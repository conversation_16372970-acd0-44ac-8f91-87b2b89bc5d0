#include "../../lvgl.h"

/*******************************************************************************
 * Size: 22 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 22 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON>ont<PERSON>wesome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_22.c --force-fast-kern-format
 ******************************************************************************/

#ifndef LV_FONT_MONTSERRAT_22
#define LV_FONT_MONTSERRAT_22 1
#endif

#if LV_FONT_MONTSERRAT_22

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t gylph_bitmap[] = {
    /* U+20 " " */

    /* U+21 "!" */
    0x4f, 0xf2, 0x3f, 0xf2, 0x3f, 0xf1, 0x2f, 0xf1,
    0x1f, 0xf0, 0x1f, 0xf0, 0xf, 0xf0, 0xf, 0xe0,
    0xf, 0xd0, 0xf, 0xd0, 0xa, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0xb0, 0x6f, 0xf5, 0x2d, 0xd1,

    /* U+22 "\"" */
    0x9f, 0x30, 0x9f, 0x39, 0xf3, 0x9, 0xf2, 0x8f,
    0x20, 0x9f, 0x28, 0xf2, 0x8, 0xf2, 0x8f, 0x10,
    0x8f, 0x17, 0xf1, 0x8, 0xf1, 0x0, 0x0, 0x0,
    0x0,

    /* U+23 "#" */
    0x0, 0x0, 0xf, 0x90, 0x0, 0x4f, 0x40, 0x0,
    0x0, 0x1, 0xf7, 0x0, 0x6, 0xf2, 0x0, 0x0,
    0x0, 0x3f, 0x50, 0x0, 0x8f, 0x0, 0x0, 0x0,
    0x5, 0xf3, 0x0, 0xa, 0xe0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x7a, 0xad,
    0xfa, 0xaa, 0xaf, 0xda, 0xa8, 0x0, 0x0, 0xbd,
    0x0, 0x0, 0xf8, 0x0, 0x0, 0x0, 0xd, 0xb0,
    0x0, 0x2f, 0x60, 0x0, 0x0, 0x0, 0xf9, 0x0,
    0x3, 0xf5, 0x0, 0x0, 0x0, 0x1f, 0x70, 0x0,
    0x5f, 0x30, 0x0, 0x4a, 0xab, 0xfc, 0xaa, 0xac,
    0xfa, 0xaa, 0x16, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x6, 0xf2, 0x0, 0xb, 0xd0,
    0x0, 0x0, 0x0, 0x8f, 0x0, 0x0, 0xdb, 0x0,
    0x0, 0x0, 0xa, 0xe0, 0x0, 0xf, 0x90, 0x0,
    0x0, 0x0, 0xcc, 0x0, 0x1, 0xf7, 0x0, 0x0,

    /* U+24 "$" */
    0x0, 0x0, 0x0, 0x65, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xca, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xef, 0xfe,
    0xc7, 0x10, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x3f, 0xf8, 0x1c, 0xa0, 0x4a, 0xa0, 0x9,
    0xfa, 0x0, 0xca, 0x0, 0x0, 0x0, 0xbf, 0x70,
    0xc, 0xa0, 0x0, 0x0, 0x9, 0xfd, 0x0, 0xca,
    0x0, 0x0, 0x0, 0x2f, 0xfe, 0x7d, 0xa0, 0x0,
    0x0, 0x0, 0x4e, 0xff, 0xfe, 0x94, 0x0, 0x0,
    0x0, 0x6, 0xbf, 0xff, 0xfd, 0x30, 0x0, 0x0,
    0x0, 0xcc, 0x9f, 0xff, 0x20, 0x0, 0x0, 0xc,
    0xa0, 0x1d, 0xf9, 0x0, 0x0, 0x0, 0xca, 0x0,
    0x7f, 0xb0, 0x42, 0x0, 0xc, 0xa0, 0x9, 0xfa,
    0xd, 0xf8, 0x20, 0xca, 0x17, 0xff, 0x40, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x28, 0xcf,
    0xff, 0xea, 0x40, 0x0, 0x0, 0x0, 0xc, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xca, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0x50, 0x0, 0x0,

    /* U+25 "%" */
    0x0, 0x6d, 0xfd, 0x40, 0x0, 0x0, 0xa, 0xf1,
    0x0, 0x6, 0xf8, 0x5a, 0xf3, 0x0, 0x0, 0x4f,
    0x60, 0x0, 0xe, 0x90, 0x0, 0xcb, 0x0, 0x0,
    0xeb, 0x0, 0x0, 0x1f, 0x50, 0x0, 0x8e, 0x0,
    0x9, 0xf1, 0x0, 0x0, 0x2f, 0x40, 0x0, 0x7e,
    0x0, 0x4f, 0x60, 0x0, 0x0, 0xf, 0x80, 0x0,
    0xbc, 0x0, 0xec, 0x0, 0x0, 0x0, 0x8, 0xf5,
    0x16, 0xf5, 0x9, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0x80, 0x3f, 0x70, 0x9f, 0xfb, 0x20,
    0x0, 0x1, 0x31, 0x0, 0xdc, 0x9, 0xf6, 0x5d,
    0xd0, 0x0, 0x0, 0x0, 0x8, 0xf2, 0x1f, 0x60,
    0x2, 0xf6, 0x0, 0x0, 0x0, 0x3f, 0x70, 0x4f,
    0x10, 0x0, 0xd9, 0x0, 0x0, 0x0, 0xdc, 0x0,
    0x6f, 0x0, 0x0, 0xbb, 0x0, 0x0, 0x8, 0xf2,
    0x0, 0x4f, 0x10, 0x0, 0xc9, 0x0, 0x0, 0x3f,
    0x80, 0x0, 0x1f, 0x50, 0x1, 0xf5, 0x0, 0x0,
    0xdd, 0x0, 0x0, 0x8, 0xe5, 0x3c, 0xd0, 0x0,
    0x8, 0xf3, 0x0, 0x0, 0x0, 0x8e, 0xfb, 0x10,

    /* U+26 "&" */
    0x0, 0x0, 0x4c, 0xee, 0xc4, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xfa, 0xaf, 0xf5, 0x0, 0x0, 0x0,
    0xf, 0xf2, 0x0, 0x2f, 0xc0, 0x0, 0x0, 0x1,
    0xfe, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0, 0xf,
    0xf2, 0x0, 0x6f, 0x90, 0x0, 0x0, 0x0, 0x8f,
    0xc1, 0x8f, 0xe1, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8, 0xdf,
    0xa0, 0x0, 0x63, 0x0, 0x3f, 0xf4, 0x1, 0xdf,
    0xa0, 0xe, 0xe0, 0xb, 0xf6, 0x0, 0x1, 0xdf,
    0xa4, 0xf9, 0x0, 0xff, 0x10, 0x0, 0x1, 0xdf,
    0xff, 0x30, 0xe, 0xf4, 0x0, 0x0, 0x1, 0xef,
    0xd0, 0x0, 0x9f, 0xe3, 0x0, 0x2, 0xaf, 0xff,
    0xa0, 0x1, 0xcf, 0xfe, 0xce, 0xff, 0xc3, 0xdf,
    0x80, 0x0, 0x6b, 0xef, 0xeb, 0x50, 0x1, 0xc2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+27 "'" */
    0x9f, 0x39, 0xf3, 0x8f, 0x28, 0xf2, 0x8f, 0x17,
    0xf1, 0x0, 0x0,

    /* U+28 "(" */
    0x0, 0x8f, 0x70, 0x1f, 0xf0, 0x8, 0xf8, 0x0,
    0xdf, 0x30, 0x2f, 0xe0, 0x6, 0xfa, 0x0, 0x8f,
    0x80, 0xb, 0xf5, 0x0, 0xcf, 0x40, 0xd, 0xf3,
    0x0, 0xef, 0x20, 0xd, 0xf3, 0x0, 0xcf, 0x40,
    0xb, 0xf5, 0x0, 0x8f, 0x70, 0x5, 0xfa, 0x0,
    0x2f, 0xe0, 0x0, 0xdf, 0x30, 0x7, 0xf8, 0x0,
    0x1f, 0xf0, 0x0, 0x8f, 0x70,

    /* U+29 ")" */
    0x1f, 0xe0, 0x0, 0x9, 0xf7, 0x0, 0x2, 0xfe,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0x7f, 0x90, 0x0,
    0x4f, 0xc0, 0x0, 0x1f, 0xf0, 0x0, 0xe, 0xf2,
    0x0, 0xd, 0xf3, 0x0, 0xc, 0xf4, 0x0, 0xb,
    0xf5, 0x0, 0xc, 0xf4, 0x0, 0xd, 0xf3, 0x0,
    0xe, 0xf2, 0x0, 0x1f, 0xf0, 0x0, 0x3f, 0xc0,
    0x0, 0x7f, 0x90, 0x0, 0xcf, 0x40, 0x2, 0xfe,
    0x0, 0x9, 0xf7, 0x0, 0x1f, 0xe0, 0x0,

    /* U+2A "*" */
    0x0, 0x4, 0xf0, 0x0, 0x0, 0x40, 0x4f, 0x0,
    0x50, 0x5f, 0xb6, 0xf4, 0xdf, 0x20, 0x4d, 0xff,
    0xfc, 0x30, 0x0, 0x8f, 0xff, 0x60, 0x4, 0xef,
    0xaf, 0xaf, 0xc2, 0x2a, 0x13, 0xf0, 0x3b, 0x0,
    0x0, 0x4f, 0x0, 0x0, 0x0, 0x1, 0x60, 0x0,
    0x0,

    /* U+2B "+" */
    0x0, 0x0, 0x6d, 0x40, 0x0, 0x0, 0x0, 0x8,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x50, 0x0,
    0x0, 0x0, 0x8, 0xf5, 0x0, 0x0, 0x6c, 0xcc,
    0xef, 0xdc, 0xcc, 0x48, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x8f, 0x50, 0x0, 0x0, 0x0,
    0x8, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x50,
    0x0, 0x0, 0x0, 0x8, 0xf5, 0x0, 0x0,

    /* U+2C "," */
    0x3, 0xb, 0xfb, 0xdf, 0xe4, 0xfb, 0x3f, 0x67,
    0xf1, 0xbb, 0x0,

    /* U+2D "-" */
    0xad, 0xdd, 0xdd, 0x2c, 0xff, 0xff, 0xf2,

    /* U+2E "." */
    0x0, 0x9, 0xf9, 0xff, 0xe8, 0xf7,

    /* U+2F "/" */
    0x0, 0x0, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0,
    0xd, 0xf1, 0x0, 0x0, 0x0, 0x3f, 0xb0, 0x0,
    0x0, 0x0, 0x9f, 0x50, 0x0, 0x0, 0x0, 0xef,
    0x0, 0x0, 0x0, 0x4, 0xfa, 0x0, 0x0, 0x0,
    0x9, 0xf5, 0x0, 0x0, 0x0, 0xe, 0xf0, 0x0,
    0x0, 0x0, 0x4f, 0x90, 0x0, 0x0, 0x0, 0xaf,
    0x40, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0, 0x0,
    0x5, 0xf9, 0x0, 0x0, 0x0, 0xa, 0xf3, 0x0,
    0x0, 0x0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x5f,
    0x80, 0x0, 0x0, 0x0, 0xbf, 0x30, 0x0, 0x0,
    0x1, 0xfd, 0x0, 0x0, 0x0, 0x6, 0xf8, 0x0,
    0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x1f,
    0xd0, 0x0, 0x0, 0x0, 0x7f, 0x70, 0x0, 0x0,
    0x0,

    /* U+30 "0" */
    0x0, 0x3, 0xae, 0xfd, 0x81, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x6, 0xff, 0x81,
    0x3, 0xbf, 0xf1, 0x0, 0xef, 0x60, 0x0, 0x0,
    0xbf, 0xa0, 0x5f, 0xd0, 0x0, 0x0, 0x3, 0xff,
    0x1a, 0xf8, 0x0, 0x0, 0x0, 0xd, 0xf5, 0xcf,
    0x50, 0x0, 0x0, 0x0, 0xaf, 0x7e, 0xf4, 0x0,
    0x0, 0x0, 0x9, 0xf9, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x9f, 0x9c, 0xf5, 0x0, 0x0, 0x0, 0xa,
    0xf7, 0xaf, 0x80, 0x0, 0x0, 0x0, 0xdf, 0x55,
    0xfd, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0x1e, 0xf6,
    0x0, 0x0, 0xb, 0xfa, 0x0, 0x6f, 0xf7, 0x10,
    0x2b, 0xff, 0x10, 0x0, 0x8f, 0xff, 0xff, 0xfe,
    0x30, 0x0, 0x0, 0x3a, 0xef, 0xd8, 0x10, 0x0,

    /* U+31 "1" */
    0xdf, 0xff, 0xfd, 0xcf, 0xff, 0xfd, 0x0, 0x5,
    0xfd, 0x0, 0x5, 0xfd, 0x0, 0x5, 0xfd, 0x0,
    0x5, 0xfd, 0x0, 0x5, 0xfd, 0x0, 0x5, 0xfd,
    0x0, 0x5, 0xfd, 0x0, 0x5, 0xfd, 0x0, 0x5,
    0xfd, 0x0, 0x5, 0xfd, 0x0, 0x5, 0xfd, 0x0,
    0x5, 0xfd, 0x0, 0x5, 0xfd, 0x0, 0x5, 0xfd,

    /* U+32 "2" */
    0x0, 0x39, 0xdf, 0xfd, 0x81, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x6f, 0xe7, 0x20, 0x15,
    0xef, 0xc0, 0x6, 0x20, 0x0, 0x0, 0x4f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xc0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x90, 0x0, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x3f, 0xf8, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xfe,

    /* U+33 "3" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x1e, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xa0, 0x0, 0x0, 0x0,
    0x9, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x46, 0x8b, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8,
    0x6, 0x0, 0x0, 0x0, 0x1e, 0xf6, 0x8f, 0xc5,
    0x10, 0x14, 0xcf, 0xe0, 0x6f, 0xff, 0xff, 0xff,
    0xfe, 0x30, 0x1, 0x7b, 0xef, 0xed, 0x81, 0x0,

    /* U+34 "4" */
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x30, 0x3, 0x63, 0x0, 0x0, 0x1, 0xef, 0x70,
    0x0, 0x8f, 0x80, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0x8, 0xf8, 0x0, 0x0, 0x7f, 0xe1, 0x0, 0x0,
    0x8f, 0x80, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x62, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x80, 0x0,

    /* U+35 "5" */
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xec,
    0x82, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x14, 0xbf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfc,
    0x7, 0x0, 0x0, 0x0, 0xc, 0xf9, 0x4f, 0xe7,
    0x20, 0x3, 0xbf, 0xf3, 0x3d, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x5a, 0xdf, 0xfd, 0x92, 0x0,

    /* U+36 "6" */
    0x0, 0x1, 0x7c, 0xef, 0xdb, 0x60, 0x0, 0x3e,
    0xff, 0xff, 0xff, 0xd0, 0x3, 0xff, 0xb3, 0x0,
    0x4, 0x40, 0xd, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x50, 0x6a, 0xcb,
    0x82, 0x0, 0xef, 0x6d, 0xff, 0xff, 0xff, 0x60,
    0xef, 0xff, 0x71, 0x2, 0xaf, 0xf4, 0xdf, 0xf4,
    0x0, 0x0, 0xa, 0xfb, 0xbf, 0xe0, 0x0, 0x0,
    0x4, 0xfe, 0x8f, 0xd0, 0x0, 0x0, 0x3, 0xfe,
    0x2f, 0xf2, 0x0, 0x0, 0x8, 0xfb, 0x9, 0xfd,
    0x40, 0x0, 0x6f, 0xf4, 0x0, 0xaf, 0xfe, 0xdf,
    0xff, 0x70, 0x0, 0x5, 0xbe, 0xfe, 0xa3, 0x0,

    /* U+37 "7" */
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x45, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x5f, 0xc0, 0x0,
    0x0, 0x6, 0xfd, 0x5, 0xfc, 0x0, 0x0, 0x0,
    0xdf, 0x60, 0x4d, 0x90, 0x0, 0x0, 0x4f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf4, 0x0, 0x0, 0x0,

    /* U+38 "8" */
    0x0, 0x2, 0x9d, 0xff, 0xd9, 0x30, 0x0, 0x0,
    0x4f, 0xff, 0xee, 0xff, 0xf7, 0x0, 0x1, 0xff,
    0xa1, 0x0, 0x19, 0xff, 0x20, 0x5, 0xfe, 0x0,
    0x0, 0x0, 0xbf, 0x80, 0x7, 0xfc, 0x0, 0x0,
    0x0, 0x8f, 0x90, 0x4, 0xfe, 0x10, 0x0, 0x0,
    0xcf, 0x70, 0x0, 0xcf, 0xc4, 0x10, 0x3b, 0xfe,
    0x10, 0x0, 0x1c, 0xff, 0xff, 0xff, 0xd2, 0x0,
    0x0, 0x8f, 0xfe, 0xcc, 0xef, 0xfa, 0x0, 0x7,
    0xfe, 0x50, 0x0, 0x3, 0xdf, 0x90, 0xe, 0xf5,
    0x0, 0x0, 0x0, 0x3f, 0xf0, 0xf, 0xf3, 0x0,
    0x0, 0x0, 0xf, 0xf2, 0xe, 0xf7, 0x0, 0x0,
    0x0, 0x4f, 0xf0, 0x7, 0xff, 0x60, 0x0, 0x5,
    0xef, 0xa0, 0x0, 0xaf, 0xff, 0xed, 0xff, 0xfc,
    0x10, 0x0, 0x4, 0xad, 0xff, 0xeb, 0x50, 0x0,

    /* U+39 "9" */
    0x0, 0x7, 0xcf, 0xfd, 0x91, 0x0, 0x0, 0x1c,
    0xff, 0xed, 0xff, 0xf4, 0x0, 0xb, 0xfd, 0x30,
    0x0, 0x8f, 0xf2, 0x2, 0xff, 0x20, 0x0, 0x0,
    0x9f, 0xb0, 0x5f, 0xd0, 0x0, 0x0, 0x3, 0xff,
    0x15, 0xfd, 0x0, 0x0, 0x0, 0x4f, 0xf4, 0x2f,
    0xf3, 0x0, 0x0, 0xb, 0xff, 0x60, 0xbf, 0xe6,
    0x10, 0x3a, 0xff, 0xf7, 0x1, 0xcf, 0xff, 0xff,
    0xf9, 0xbf, 0x70, 0x0, 0x5a, 0xcc, 0x94, 0xc,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xf6, 0x0, 0x7, 0x20, 0x1,
    0x6e, 0xfb, 0x0, 0x5, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x18, 0xce, 0xfd, 0xa4, 0x0, 0x0,

    /* U+3A ":" */
    0x8f, 0x8f, 0xfe, 0x9f, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf9, 0xff,
    0xe8, 0xf7,

    /* U+3B ";" */
    0x8f, 0x8f, 0xfe, 0x9f, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf8, 0xef,
    0xe7, 0xfc, 0x2f, 0x76, 0xf2, 0xac, 0x3, 0x20,

    /* U+3C "<" */
    0x0, 0x0, 0x0, 0x0, 0x17, 0x30, 0x0, 0x0,
    0x3, 0xaf, 0xf5, 0x0, 0x1, 0x7d, 0xff, 0xc6,
    0x0, 0x3a, 0xff, 0xe9, 0x20, 0x0, 0x7f, 0xfc,
    0x50, 0x0, 0x0, 0x8, 0xfe, 0x71, 0x0, 0x0,
    0x0, 0x18, 0xef, 0xfa, 0x40, 0x0, 0x0, 0x0,
    0x5b, 0xff, 0xe7, 0x10, 0x0, 0x0, 0x2, 0x8e,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x5, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+3D "=" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x56, 0xcc, 0xcc,
    0xcc, 0xcc, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xcc, 0xcc, 0xcc, 0xcc,
    0xc4, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x50,

    /* U+3E ">" */
    0x56, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfe, 0x92,
    0x0, 0x0, 0x0, 0x17, 0xdf, 0xfc, 0x50, 0x0,
    0x0, 0x0, 0x4a, 0xff, 0xe8, 0x20, 0x0, 0x0,
    0x1, 0x6d, 0xff, 0x40, 0x0, 0x0, 0x2, 0x8f,
    0xf5, 0x0, 0x0, 0x5c, 0xff, 0xd7, 0x0, 0x29,
    0xef, 0xfa, 0x30, 0x0, 0x7f, 0xfd, 0x71, 0x0,
    0x0, 0x8, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+3F "?" */
    0x0, 0x4a, 0xdf, 0xfd, 0x81, 0x0, 0xa, 0xff,
    0xfe, 0xff, 0xfe, 0x30, 0x8f, 0xe5, 0x0, 0x4,
    0xef, 0xc0, 0x6, 0x10, 0x0, 0x0, 0x5f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x4f, 0xf6, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x60, 0x0, 0x0, 0x0,
    0xb, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xd, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xe3, 0x0, 0x0,

    /* U+40 "@" */
    0x0, 0x0, 0x0, 0x5a, 0xdf, 0xff, 0xd9, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xef, 0xea, 0x87,
    0x8a, 0xef, 0xd4, 0x0, 0x0, 0x0, 0xa, 0xfc,
    0x40, 0x0, 0x0, 0x0, 0x4d, 0xf8, 0x0, 0x0,
    0xb, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf7, 0x0, 0x6, 0xf9, 0x0, 0x5, 0xcf, 0xfc,
    0x50, 0xfe, 0xa, 0xf3, 0x0, 0xec, 0x0, 0xa,
    0xff, 0xdc, 0xef, 0x9f, 0xe0, 0xe, 0xc0, 0x6f,
    0x40, 0x7, 0xfd, 0x20, 0x0, 0x8f, 0xfe, 0x0,
    0x6f, 0x2a, 0xf0, 0x0, 0xef, 0x20, 0x0, 0x0,
    0xaf, 0xe0, 0x1, 0xf6, 0xdc, 0x0, 0x4f, 0xb0,
    0x0, 0x0, 0x3, 0xfe, 0x0, 0xe, 0x9e, 0xa0,
    0x6, 0xf9, 0x0, 0x0, 0x0, 0xf, 0xe0, 0x0,
    0xda, 0xea, 0x0, 0x6f, 0x90, 0x0, 0x0, 0x0,
    0xfe, 0x0, 0xd, 0xad, 0xc0, 0x4, 0xfb, 0x0,
    0x0, 0x0, 0x3f, 0xe0, 0x0, 0xe9, 0xaf, 0x0,
    0xe, 0xf2, 0x0, 0x0, 0xa, 0xfe, 0x0, 0x2f,
    0x65, 0xf5, 0x0, 0x7f, 0xd2, 0x0, 0x8, 0xff,
    0xf1, 0x9, 0xf1, 0xe, 0xd0, 0x0, 0xaf, 0xfc,
    0xce, 0xf9, 0x9f, 0xec, 0xf8, 0x0, 0x6f, 0x90,
    0x0, 0x5c, 0xff, 0xc5, 0x1, 0xbf, 0xe7, 0x0,
    0x0, 0xaf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xc4, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e,
    0xfe, 0xa8, 0x89, 0xbf, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xad, 0xff, 0xec, 0x83, 0x0,
    0x0, 0x0,

    /* U+41 "A" */
    0x0, 0x0, 0x0, 0x4, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xee, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x87,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x21, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfb, 0x0, 0xaf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf4, 0x0, 0x3f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xd0, 0x0, 0xc, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x5, 0xfd, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x0, 0x0, 0x0, 0xef,
    0x40, 0x0, 0x0, 0x9, 0xff, 0xcc, 0xcc, 0xcc,
    0xef, 0xb0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x7f, 0xb0, 0x0,
    0x0, 0x0, 0xa, 0xf9, 0x0, 0x0, 0xef, 0x40,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x10, 0x5, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70, 0xc,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xe0,

    /* U+42 "B" */
    0xbf, 0xff, 0xff, 0xff, 0xeb, 0x50, 0x0, 0xbf,
    0xed, 0xdd, 0xdd, 0xff, 0xfa, 0x0, 0xbf, 0x80,
    0x0, 0x0, 0x7, 0xff, 0x50, 0xbf, 0x80, 0x0,
    0x0, 0x0, 0xbf, 0xa0, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0x8f, 0xb0, 0xbf, 0x80, 0x0, 0x0, 0x0,
    0xbf, 0x80, 0xbf, 0x80, 0x0, 0x0, 0x7, 0xff,
    0x20, 0xbf, 0xed, 0xdd, 0xdd, 0xff, 0xf4, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x10, 0xbf,
    0x80, 0x0, 0x0, 0x14, 0xcf, 0xd0, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0xe, 0xf5, 0xbf, 0x80, 0x0,
    0x0, 0x0, 0xb, 0xf8, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0xd, 0xf7, 0xbf, 0x80, 0x0, 0x0, 0x1,
    0x9f, 0xf2, 0xbf, 0xed, 0xdd, 0xdd, 0xef, 0xff,
    0x70, 0xbf, 0xff, 0xff, 0xff, 0xfd, 0x93, 0x0,

    /* U+43 "C" */
    0x0, 0x0, 0x29, 0xdf, 0xfe, 0xa5, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xfd, 0x20, 0x0,
    0xcf, 0xfa, 0x41, 0x2, 0x6e, 0xfc, 0x0, 0x9f,
    0xf5, 0x0, 0x0, 0x0, 0x9, 0x20, 0x2f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x92, 0x0, 0xc, 0xff, 0xa4, 0x10, 0x16, 0xef,
    0xc0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0x0, 0x0, 0x3, 0x9d, 0xff, 0xea, 0x50, 0x0,

    /* U+44 "D" */
    0xbf, 0xff, 0xff, 0xfe, 0xd9, 0x30, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x10, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0x38, 0xff, 0xe1, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x2d, 0xfc, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x2, 0xff, 0x60,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xc0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x2, 0xff, 0x60,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x2d, 0xfc, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0x38, 0xff, 0xe1, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x10, 0x0,
    0xbf, 0xff, 0xff, 0xfe, 0xd9, 0x30, 0x0, 0x0,

    /* U+45 "E" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfe, 0xee, 0xee, 0xee, 0x30,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0x30, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+46 "F" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0x30, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0x30, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x0,

    /* U+47 "G" */
    0x0, 0x0, 0x29, 0xdf, 0xfe, 0xb6, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xfe, 0x30, 0x0,
    0xcf, 0xfa, 0x41, 0x1, 0x5c, 0xfe, 0x0, 0x9f,
    0xf5, 0x0, 0x0, 0x0, 0x8, 0x30, 0x2f, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x10, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x1c, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf1, 0x8f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x12, 0xff, 0x50, 0x0, 0x0, 0x0,
    0xf, 0xf1, 0x9, 0xff, 0x50, 0x0, 0x0, 0x0,
    0xff, 0x10, 0xc, 0xff, 0xa4, 0x10, 0x25, 0xcf,
    0xf1, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x2, 0x9d, 0xff, 0xeb, 0x60, 0x0,

    /* U+48 "H" */
    0xbf, 0x80, 0x0, 0x0, 0x0, 0xa, 0xf8, 0xbf,
    0x80, 0x0, 0x0, 0x0, 0xa, 0xf8, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0xa, 0xf8, 0xbf, 0x80, 0x0,
    0x0, 0x0, 0xa, 0xf8, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0xa, 0xf8, 0xbf, 0x80, 0x0, 0x0, 0x0,
    0xa, 0xf8, 0xbf, 0x80, 0x0, 0x0, 0x0, 0xa,
    0xf8, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xbf,
    0x80, 0x0, 0x0, 0x0, 0xa, 0xf8, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0xa, 0xf8, 0xbf, 0x80, 0x0,
    0x0, 0x0, 0xa, 0xf8, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0xa, 0xf8, 0xbf, 0x80, 0x0, 0x0, 0x0,
    0xa, 0xf8, 0xbf, 0x80, 0x0, 0x0, 0x0, 0xa,
    0xf8, 0xbf, 0x80, 0x0, 0x0, 0x0, 0xa, 0xf8,

    /* U+49 "I" */
    0xbf, 0x8b, 0xf8, 0xbf, 0x8b, 0xf8, 0xbf, 0x8b,
    0xf8, 0xbf, 0x8b, 0xf8, 0xbf, 0x8b, 0xf8, 0xbf,
    0x8b, 0xf8, 0xbf, 0x8b, 0xf8, 0xbf, 0x8b, 0xf8,

    /* U+4A "J" */
    0x0, 0xaf, 0xff, 0xff, 0xff, 0x10, 0x9, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x10, 0x0, 0x0, 0x0, 0x1f,
    0xf1, 0x0, 0x0, 0x0, 0x1, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x1f, 0xf1, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x2f, 0xf0, 0x3, 0x50, 0x0, 0x5, 0xfe, 0x0,
    0xef, 0x70, 0x3, 0xef, 0x90, 0x8, 0xff, 0xff,
    0xff, 0xe1, 0x0, 0x4, 0xae, 0xfe, 0x91, 0x0,

    /* U+4B "K" */
    0xbf, 0x80, 0x0, 0x0, 0x0, 0x9f, 0xd1, 0xbf,
    0x80, 0x0, 0x0, 0x8, 0xfe, 0x10, 0xbf, 0x80,
    0x0, 0x0, 0x7f, 0xe2, 0x0, 0xbf, 0x80, 0x0,
    0x6, 0xff, 0x30, 0x0, 0xbf, 0x80, 0x0, 0x5f,
    0xf4, 0x0, 0x0, 0xbf, 0x80, 0x4, 0xff, 0x50,
    0x0, 0x0, 0xbf, 0x80, 0x3f, 0xf7, 0x0, 0x0,
    0x0, 0xbf, 0x83, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0xbf, 0xae, 0xff, 0xfc, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xb4, 0xff, 0x90, 0x0, 0x0, 0xbf, 0xfc,
    0x0, 0x5f, 0xf6, 0x0, 0x0, 0xbf, 0xc0, 0x0,
    0x8, 0xff, 0x30, 0x0, 0xbf, 0x80, 0x0, 0x0,
    0xbf, 0xe1, 0x0, 0xbf, 0x80, 0x0, 0x0, 0xd,
    0xfc, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x1, 0xef,
    0x90, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x3f, 0xf6,

    /* U+4C "L" */
    0xbf, 0x80, 0x0, 0x0, 0x0, 0xb, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x0,
    0xb, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0xb, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0,
    0x0, 0xb, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x80, 0x0, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0xb,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xcb, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+4D "M" */
    0xbf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xab, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xfa, 0xbf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xab, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xfa, 0xbf, 0xef, 0xa0, 0x0, 0x0, 0x0,
    0xaf, 0xef, 0xab, 0xf7, 0xef, 0x30, 0x0, 0x0,
    0x3f, 0xd7, 0xfa, 0xbf, 0x65, 0xfc, 0x0, 0x0,
    0xc, 0xf5, 0x6f, 0xab, 0xf6, 0xc, 0xf6, 0x0,
    0x5, 0xfc, 0x6, 0xfa, 0xbf, 0x60, 0x3f, 0xe0,
    0x0, 0xef, 0x30, 0x6f, 0xbb, 0xf6, 0x0, 0xaf,
    0x80, 0x7f, 0x90, 0x6, 0xfb, 0xbf, 0x60, 0x1,
    0xff, 0x3f, 0xf1, 0x0, 0x6f, 0xbb, 0xf6, 0x0,
    0x7, 0xff, 0xf7, 0x0, 0x6, 0xfb, 0xbf, 0x60,
    0x0, 0xe, 0xfd, 0x0, 0x0, 0x6f, 0xbb, 0xf6,
    0x0, 0x0, 0x5f, 0x50, 0x0, 0x6, 0xfb, 0xbf,
    0x60, 0x0, 0x0, 0x10, 0x0, 0x0, 0x6f, 0xbb,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfb,

    /* U+4E "N" */
    0xbf, 0x70, 0x0, 0x0, 0x0, 0xa, 0xf8, 0xbf,
    0xf4, 0x0, 0x0, 0x0, 0xa, 0xf8, 0xbf, 0xfe,
    0x10, 0x0, 0x0, 0xa, 0xf8, 0xbf, 0xff, 0xc0,
    0x0, 0x0, 0xa, 0xf8, 0xbf, 0xbf, 0xf9, 0x0,
    0x0, 0xa, 0xf8, 0xbf, 0x86, 0xff, 0x50, 0x0,
    0xa, 0xf8, 0xbf, 0x80, 0x9f, 0xf2, 0x0, 0xa,
    0xf8, 0xbf, 0x80, 0xc, 0xfd, 0x0, 0xa, 0xf8,
    0xbf, 0x80, 0x2, 0xef, 0xb0, 0xa, 0xf8, 0xbf,
    0x80, 0x0, 0x4f, 0xf7, 0xa, 0xf8, 0xbf, 0x80,
    0x0, 0x8, 0xff, 0x4a, 0xf8, 0xbf, 0x80, 0x0,
    0x0, 0xbf, 0xeb, 0xf8, 0xbf, 0x80, 0x0, 0x0,
    0x1e, 0xff, 0xf8, 0xbf, 0x80, 0x0, 0x0, 0x3,
    0xff, 0xf8, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x6f,
    0xf8, 0xbf, 0x80, 0x0, 0x0, 0x0, 0xa, 0xf8,

    /* U+4F "O" */
    0x0, 0x0, 0x28, 0xdf, 0xfe, 0xb6, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xfd, 0x30,
    0x0, 0x0, 0xbf, 0xfa, 0x41, 0x2, 0x7e, 0xff,
    0x40, 0x0, 0x8f, 0xf4, 0x0, 0x0, 0x0, 0x1b,
    0xfe, 0x10, 0x2f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfa, 0x8, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0xcf, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x4e, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf6, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x6c, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf4, 0x8f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x12, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xa0, 0x8, 0xff,
    0x40, 0x0, 0x0, 0x0, 0xbf, 0xe2, 0x0, 0xc,
    0xff, 0xa4, 0x10, 0x26, 0xef, 0xf4, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xd3, 0x0, 0x0,
    0x0, 0x2, 0x9d, 0xff, 0xeb, 0x60, 0x0, 0x0,

    /* U+50 "P" */
    0xbf, 0xff, 0xff, 0xfe, 0xb6, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x20, 0xbf, 0x80, 0x0,
    0x1, 0x6e, 0xfd, 0xb, 0xf8, 0x0, 0x0, 0x0,
    0x1e, 0xf6, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x9f,
    0xab, 0xf8, 0x0, 0x0, 0x0, 0x7, 0xfb, 0xbf,
    0x80, 0x0, 0x0, 0x0, 0x9f, 0xab, 0xf8, 0x0,
    0x0, 0x0, 0x1e, 0xf6, 0xbf, 0x80, 0x0, 0x1,
    0x6e, 0xfd, 0xb, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x20, 0xbf, 0xff, 0xff, 0xfe, 0xb6, 0x0, 0xb,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+51 "Q" */
    0x0, 0x0, 0x28, 0xdf, 0xfe, 0xb6, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0x0, 0x0, 0x0, 0xbf, 0xfa, 0x41, 0x2, 0x7e,
    0xff, 0x40, 0x0, 0x8, 0xff, 0x50, 0x0, 0x0,
    0x1, 0xbf, 0xe1, 0x0, 0x2f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xfa, 0x0, 0x8f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x0, 0xcf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x40, 0xef,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x60,
    0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x50, 0xcf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x40, 0x8f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x0, 0x2f, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xfa, 0x0, 0xa, 0xff, 0x30, 0x0,
    0x0, 0x0, 0xaf, 0xf2, 0x0, 0x0, 0xdf, 0xf9,
    0x30, 0x1, 0x6d, 0xff, 0x50, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x4a, 0xef, 0xff, 0xd6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xe5, 0x0, 0x7,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xec,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4a,
    0xef, 0xd7, 0x0,

    /* U+52 "R" */
    0xbf, 0xff, 0xff, 0xfe, 0xb6, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x20, 0xbf, 0x80, 0x0,
    0x1, 0x6e, 0xfd, 0xb, 0xf8, 0x0, 0x0, 0x0,
    0x1e, 0xf6, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x9f,
    0xab, 0xf8, 0x0, 0x0, 0x0, 0x7, 0xfb, 0xbf,
    0x80, 0x0, 0x0, 0x0, 0x9f, 0x9b, 0xf8, 0x0,
    0x0, 0x0, 0x1e, 0xf5, 0xbf, 0x80, 0x0, 0x1,
    0x5d, 0xfd, 0xb, 0xff, 0xee, 0xef, 0xff, 0xfd,
    0x20, 0xbf, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xb,
    0xf8, 0x0, 0x0, 0x5f, 0xe1, 0x0, 0xbf, 0x80,
    0x0, 0x0, 0xbf, 0xa0, 0xb, 0xf8, 0x0, 0x0,
    0x1, 0xef, 0x50, 0xbf, 0x80, 0x0, 0x0, 0x6,
    0xfe, 0x1b, 0xf8, 0x0, 0x0, 0x0, 0xb, 0xfa,

    /* U+53 "S" */
    0x0, 0x2, 0x9d, 0xff, 0xeb, 0x60, 0x0, 0x6,
    0xff, 0xff, 0xef, 0xff, 0xe0, 0x3, 0xff, 0x92,
    0x0, 0x4, 0xba, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xe7, 0x20, 0x0, 0x0, 0x0, 0x4, 0xef,
    0xff, 0xea, 0x50, 0x0, 0x0, 0x0, 0x6b, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0x0, 0x38, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfb, 0x4, 0x20,
    0x0, 0x0, 0x0, 0xaf, 0x90, 0xdf, 0x94, 0x0,
    0x1, 0x8f, 0xf3, 0x7, 0xff, 0xff, 0xef, 0xff,
    0xf7, 0x0, 0x1, 0x7b, 0xef, 0xfd, 0x92, 0x0,

    /* U+54 "T" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0xa,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x80, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x80, 0x0, 0x0,

    /* U+55 "U" */
    0xdf, 0x60, 0x0, 0x0, 0x0, 0xf, 0xf3, 0xdf,
    0x60, 0x0, 0x0, 0x0, 0xf, 0xf3, 0xdf, 0x60,
    0x0, 0x0, 0x0, 0xf, 0xf3, 0xdf, 0x60, 0x0,
    0x0, 0x0, 0xf, 0xf3, 0xdf, 0x60, 0x0, 0x0,
    0x0, 0xf, 0xf3, 0xdf, 0x60, 0x0, 0x0, 0x0,
    0xf, 0xf3, 0xdf, 0x60, 0x0, 0x0, 0x0, 0xf,
    0xf3, 0xdf, 0x60, 0x0, 0x0, 0x0, 0xf, 0xf3,
    0xdf, 0x60, 0x0, 0x0, 0x0, 0xf, 0xf3, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0xf, 0xf2, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0x1f, 0xf1, 0x8f, 0xc0, 0x0,
    0x0, 0x0, 0x5f, 0xe0, 0x2f, 0xf5, 0x0, 0x0,
    0x0, 0xdf, 0x80, 0xa, 0xff, 0x71, 0x0, 0x4d,
    0xfe, 0x10, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xe3,
    0x0, 0x0, 0x5, 0xbe, 0xff, 0xc7, 0x10, 0x0,

    /* U+56 "V" */
    0xc, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x70, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf1, 0x0, 0xef, 0x60, 0x0, 0x0, 0x0, 0x9,
    0xfa, 0x0, 0x8, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x30, 0x0, 0x1f, 0xf4, 0x0, 0x0, 0x0,
    0x6f, 0xc0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0,
    0xd, 0xf5, 0x0, 0x0, 0x4, 0xff, 0x10, 0x0,
    0x4, 0xfe, 0x0, 0x0, 0x0, 0xd, 0xf8, 0x0,
    0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x6f, 0xe0,
    0x0, 0x2f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x50, 0x8, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfc, 0x0, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf3, 0x6f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x9d, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf2, 0x0, 0x0, 0x0,

    /* U+57 "W" */
    0x2f, 0xf2, 0x0, 0x0, 0x0, 0xb, 0xfa, 0x0,
    0x0, 0x0, 0x2, 0xfe, 0x0, 0xdf, 0x70, 0x0,
    0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x8f,
    0x90, 0x7, 0xfc, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x40, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x2f, 0xf1,
    0x0, 0x0, 0xb, 0xfc, 0xf9, 0x0, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0xff,
    0x3f, 0xe0, 0x0, 0x0, 0x7f, 0x90, 0x0, 0x8,
    0xfb, 0x0, 0x0, 0x5f, 0xa0, 0xdf, 0x40, 0x0,
    0xc, 0xf4, 0x0, 0x0, 0x3f, 0xf1, 0x0, 0xb,
    0xf5, 0x8, 0xf9, 0x0, 0x2, 0xff, 0x0, 0x0,
    0x0, 0xef, 0x50, 0x1, 0xff, 0x0, 0x3f, 0xe0,
    0x0, 0x7f, 0xa0, 0x0, 0x0, 0x8, 0xfa, 0x0,
    0x5f, 0xa0, 0x0, 0xdf, 0x30, 0xc, 0xf5, 0x0,
    0x0, 0x0, 0x3f, 0xf0, 0xb, 0xf5, 0x0, 0x8,
    0xf9, 0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x51, 0xff, 0x0, 0x0, 0x3f, 0xe0, 0x6f, 0xb0,
    0x0, 0x0, 0x0, 0x9, 0xfa, 0x6f, 0xa0, 0x0,
    0x0, 0xdf, 0x3c, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xfb, 0xf5, 0x0, 0x0, 0x8, 0xfa, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xef,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf5,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x10, 0x0, 0x0,

    /* U+58 "X" */
    0x1e, 0xf7, 0x0, 0x0, 0x0, 0xa, 0xfc, 0x0,
    0x5f, 0xf3, 0x0, 0x0, 0x5, 0xff, 0x10, 0x0,
    0x9f, 0xd0, 0x0, 0x1, 0xef, 0x50, 0x0, 0x0,
    0xdf, 0x90, 0x0, 0xbf, 0xa0, 0x0, 0x0, 0x3,
    0xff, 0x40, 0x6f, 0xe1, 0x0, 0x0, 0x0, 0x8,
    0xfe, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xbf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x90, 0xdf,
    0xa0, 0x0, 0x0, 0x0, 0x8f, 0xd0, 0x2, 0xff,
    0x50, 0x0, 0x0, 0x4f, 0xf3, 0x0, 0x7, 0xfe,
    0x10, 0x0, 0x1e, 0xf8, 0x0, 0x0, 0xc, 0xfb,
    0x0, 0xa, 0xfd, 0x0, 0x0, 0x0, 0x1f, 0xf7,
    0x5, 0xff, 0x20, 0x0, 0x0, 0x0, 0x5f, 0xf2,

    /* U+59 "Y" */
    0xc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1,
    0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0xbf, 0x70,
    0x0, 0xaf, 0xb0, 0x0, 0x0, 0x5, 0xfd, 0x0,
    0x0, 0x1f, 0xf4, 0x0, 0x0, 0xe, 0xf4, 0x0,
    0x0, 0x7, 0xfd, 0x0, 0x0, 0x8f, 0xb0, 0x0,
    0x0, 0x0, 0xdf, 0x70, 0x1, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x4f, 0xf1, 0xa, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xfa, 0x4f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xef, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x0,

    /* U+5A "Z" */
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0x90, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+5B "[" */
    0xbf, 0xff, 0xeb, 0xfd, 0xcb, 0xbf, 0x60, 0xb,
    0xf6, 0x0, 0xbf, 0x60, 0xb, 0xf6, 0x0, 0xbf,
    0x60, 0xb, 0xf6, 0x0, 0xbf, 0x60, 0xb, 0xf6,
    0x0, 0xbf, 0x60, 0xb, 0xf6, 0x0, 0xbf, 0x60,
    0xb, 0xf6, 0x0, 0xbf, 0x60, 0xb, 0xf6, 0x0,
    0xbf, 0x60, 0xb, 0xf6, 0x0, 0xbf, 0x60, 0xb,
    0xfd, 0xcb, 0xbf, 0xff, 0xe0,

    /* U+5C "\\" */
    0xaf, 0x40, 0x0, 0x0, 0x0, 0x5f, 0x90, 0x0,
    0x0, 0x0, 0xf, 0xe0, 0x0, 0x0, 0x0, 0xa,
    0xf4, 0x0, 0x0, 0x0, 0x4, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x50, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x0,
    0x0, 0xe, 0xf0, 0x0, 0x0, 0x0, 0x8, 0xf5,
    0x0, 0x0, 0x0, 0x3, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x10, 0x0, 0x0, 0x0, 0x8f, 0x60,
    0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0x0, 0x0,
    0xd, 0xf1, 0x0, 0x0, 0x0, 0x7, 0xf7, 0x0,
    0x0, 0x0, 0x2, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x20, 0x0, 0x0, 0x0, 0x7f, 0x70, 0x0,
    0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0, 0x0, 0xc,
    0xf2,

    /* U+5D "]" */
    0x9f, 0xff, 0xf0, 0x7c, 0xcf, 0xf0, 0x0, 0x1f,
    0xf0, 0x0, 0x1f, 0xf0, 0x0, 0x1f, 0xf0, 0x0,
    0x1f, 0xf0, 0x0, 0x1f, 0xf0, 0x0, 0x1f, 0xf0,
    0x0, 0x1f, 0xf0, 0x0, 0x1f, 0xf0, 0x0, 0x1f,
    0xf0, 0x0, 0x1f, 0xf0, 0x0, 0x1f, 0xf0, 0x0,
    0x1f, 0xf0, 0x0, 0x1f, 0xf0, 0x0, 0x1f, 0xf0,
    0x0, 0x1f, 0xf0, 0x0, 0x1f, 0xf0, 0x0, 0x1f,
    0xf0, 0x7c, 0xcf, 0xf0, 0x9f, 0xff, 0xf0,

    /* U+5E "^" */
    0x0, 0x0, 0x48, 0x30, 0x0, 0x0, 0x0, 0xd,
    0xfb, 0x0, 0x0, 0x0, 0x4, 0xfc, 0xf1, 0x0,
    0x0, 0x0, 0xae, 0x2f, 0x80, 0x0, 0x0, 0x1f,
    0x80, 0xbe, 0x0, 0x0, 0x8, 0xf2, 0x4, 0xf5,
    0x0, 0x0, 0xeb, 0x0, 0xe, 0xb0, 0x0, 0x5f,
    0x50, 0x0, 0x8f, 0x20, 0xb, 0xe0, 0x0, 0x1,
    0xf8, 0x2, 0xf8, 0x0, 0x0, 0xb, 0xe0,

    /* U+5F "_" */
    0x11, 0x11, 0x11, 0x11, 0x11, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x40,

    /* U+60 "`" */
    0x1b, 0xfb, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0,
    0x6, 0xfb, 0x0,

    /* U+61 "a" */
    0x2, 0x8d, 0xff, 0xea, 0x20, 0x4, 0xff, 0xfe,
    0xef, 0xff, 0x30, 0x1d, 0x61, 0x0, 0x2c, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x30, 0x3a, 0xef, 0xff, 0xff,
    0xf3, 0x4f, 0xfc, 0x98, 0x88, 0xff, 0x3c, 0xf8,
    0x0, 0x0, 0xe, 0xf3, 0xef, 0x30, 0x0, 0x1,
    0xff, 0x3b, 0xf8, 0x0, 0x0, 0xbf, 0xf3, 0x3f,
    0xfc, 0x99, 0xef, 0xef, 0x30, 0x2a, 0xef, 0xea,
    0x2c, 0xf3,

    /* U+62 "b" */
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf1, 0x4b, 0xef, 0xd8, 0x10, 0x0, 0xff,
    0x9f, 0xff, 0xef, 0xfe, 0x40, 0xf, 0xff, 0xc3,
    0x0, 0x2b, 0xfe, 0x10, 0xff, 0xd0, 0x0, 0x0,
    0xc, 0xf9, 0xf, 0xf5, 0x0, 0x0, 0x0, 0x5f,
    0xe0, 0xff, 0x20, 0x0, 0x0, 0x1, 0xff, 0xf,
    0xf2, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0xff, 0x50,
    0x0, 0x0, 0x5, 0xfe, 0xf, 0xfd, 0x0, 0x0,
    0x0, 0xdf, 0x80, 0xff, 0xfc, 0x30, 0x3, 0xcf,
    0xe1, 0xf, 0xf8, 0xff, 0xff, 0xff, 0xe3, 0x0,
    0xff, 0x4, 0xbe, 0xfd, 0x81, 0x0, 0x0,

    /* U+63 "c" */
    0x0, 0x0, 0x7c, 0xef, 0xd8, 0x0, 0x0, 0x3d,
    0xff, 0xfe, 0xff, 0xe2, 0x1, 0xef, 0xc3, 0x0,
    0x2b, 0xf8, 0x8, 0xfc, 0x0, 0x0, 0x0, 0x50,
    0xe, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfc, 0x0, 0x0, 0x0, 0x50, 0x1, 0xef,
    0xc3, 0x0, 0x2c, 0xf9, 0x0, 0x2d, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x7c, 0xef, 0xd7, 0x0,

    /* U+64 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf0, 0x0, 0x18, 0xdf, 0xeb, 0x41, 0xff, 0x0,
    0x4e, 0xff, 0xef, 0xff, 0x9f, 0xf0, 0x1e, 0xfc,
    0x20, 0x3, 0xcf, 0xff, 0x9, 0xfd, 0x0, 0x0,
    0x0, 0xdf, 0xf0, 0xef, 0x50, 0x0, 0x0, 0x5,
    0xff, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x2f, 0xf0,
    0xff, 0x10, 0x0, 0x0, 0x2, 0xff, 0xe, 0xf4,
    0x0, 0x0, 0x0, 0x5f, 0xf0, 0x9f, 0xc0, 0x0,
    0x0, 0xc, 0xff, 0x1, 0xef, 0xa1, 0x0, 0x1a,
    0xff, 0xf0, 0x4, 0xef, 0xfc, 0xdf, 0xf9, 0xff,
    0x0, 0x1, 0x8d, 0xff, 0xb5, 0xf, 0xf0,

    /* U+65 "e" */
    0x0, 0x1, 0x8d, 0xfe, 0xb5, 0x0, 0x0, 0x3,
    0xef, 0xfd, 0xef, 0xfa, 0x0, 0x1, 0xef, 0x90,
    0x0, 0x3d, 0xf8, 0x0, 0x8f, 0x90, 0x0, 0x0,
    0x2f, 0xf1, 0xe, 0xf2, 0x0, 0x0, 0x0, 0xaf,
    0x50, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xf,
    0xf9, 0x88, 0x88, 0x88, 0x88, 0x40, 0xef, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xfd, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x1e, 0xfc, 0x40, 0x1, 0x7f,
    0x50, 0x0, 0x3e, 0xff, 0xfe, 0xff, 0xf5, 0x0,
    0x0, 0x7, 0xce, 0xfd, 0x92, 0x0,

    /* U+66 "f" */
    0x0, 0x2, 0xbf, 0xfc, 0x30, 0x0, 0xef, 0xed,
    0xf3, 0x0, 0x7f, 0xc0, 0x2, 0x0, 0x9, 0xf6,
    0x0, 0x0, 0x0, 0xbf, 0x60, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xc0, 0x8c, 0xef, 0xdc, 0xc9, 0x0,
    0xb, 0xf6, 0x0, 0x0, 0x0, 0xbf, 0x60, 0x0,
    0x0, 0xb, 0xf6, 0x0, 0x0, 0x0, 0xbf, 0x60,
    0x0, 0x0, 0xb, 0xf6, 0x0, 0x0, 0x0, 0xbf,
    0x60, 0x0, 0x0, 0xb, 0xf6, 0x0, 0x0, 0x0,
    0xbf, 0x60, 0x0, 0x0, 0xb, 0xf6, 0x0, 0x0,
    0x0, 0xbf, 0x60, 0x0, 0x0,

    /* U+67 "g" */
    0x0, 0x2, 0x8d, 0xfe, 0xc6, 0xd, 0xf2, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xbd, 0xf2, 0x3, 0xff,
    0xb2, 0x0, 0x19, 0xff, 0xf2, 0xa, 0xfa, 0x0,
    0x0, 0x0, 0x8f, 0xf2, 0xf, 0xf3, 0x0, 0x0,
    0x0, 0x1f, 0xf2, 0xf, 0xf1, 0x0, 0x0, 0x0,
    0xe, 0xf2, 0xf, 0xf3, 0x0, 0x0, 0x0, 0x1f,
    0xf2, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x9f, 0xf2,
    0x3, 0xff, 0xa2, 0x0, 0x19, 0xff, 0xf2, 0x0,
    0x5f, 0xff, 0xee, 0xff, 0xaf, 0xf2, 0x0, 0x2,
    0x9d, 0xff, 0xc5, 0xf, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x8f, 0xc0, 0x1, 0xec, 0x51, 0x0, 0x7,
    0xff, 0x60, 0x2, 0xdf, 0xff, 0xee, 0xff, 0xf9,
    0x0, 0x0, 0x5, 0xad, 0xff, 0xea, 0x40, 0x0,

    /* U+68 "h" */
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0x15,
    0xbe, 0xfd, 0x80, 0x0, 0xff, 0xbf, 0xff, 0xff,
    0xfd, 0x0, 0xff, 0xfa, 0x20, 0x6, 0xff, 0x80,
    0xff, 0xc0, 0x0, 0x0, 0x7f, 0xd0, 0xff, 0x50,
    0x0, 0x0, 0x2f, 0xf0, 0xff, 0x20, 0x0, 0x0,
    0xf, 0xf0, 0xff, 0x10, 0x0, 0x0, 0xf, 0xf1,
    0xff, 0x10, 0x0, 0x0, 0xf, 0xf1, 0xff, 0x10,
    0x0, 0x0, 0xf, 0xf1, 0xff, 0x10, 0x0, 0x0,
    0xf, 0xf1, 0xff, 0x10, 0x0, 0x0, 0xf, 0xf1,
    0xff, 0x10, 0x0, 0x0, 0xf, 0xf1,

    /* U+69 "i" */
    0x1d, 0xe2, 0x5f, 0xf6, 0xa, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf1, 0xf, 0xf1, 0xf, 0xf1,
    0xf, 0xf1, 0xf, 0xf1, 0xf, 0xf1, 0xf, 0xf1,
    0xf, 0xf1, 0xf, 0xf1, 0xf, 0xf1, 0xf, 0xf1,
    0xf, 0xf1,

    /* U+6A "j" */
    0x0, 0x0, 0xc, 0xe3, 0x0, 0x0, 0x3f, 0xf8,
    0x0, 0x0, 0x9, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf3,
    0x0, 0x0, 0xe, 0xf3, 0x0, 0x0, 0xe, 0xf3,
    0x0, 0x0, 0xe, 0xf3, 0x0, 0x0, 0xe, 0xf3,
    0x0, 0x0, 0xe, 0xf3, 0x0, 0x0, 0xe, 0xf3,
    0x0, 0x0, 0xe, 0xf3, 0x0, 0x0, 0xe, 0xf3,
    0x0, 0x0, 0xe, 0xf3, 0x0, 0x0, 0xe, 0xf3,
    0x0, 0x0, 0xe, 0xf3, 0x0, 0x0, 0xf, 0xf2,
    0x1, 0x0, 0x4f, 0xf0, 0xb, 0xfd, 0xff, 0x80,
    0x8, 0xef, 0xe8, 0x0,

    /* U+6B "k" */
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x2, 0xef, 0x80, 0xff, 0x10, 0x0, 0x3e,
    0xf8, 0x0, 0xff, 0x10, 0x3, 0xff, 0x80, 0x0,
    0xff, 0x10, 0x4f, 0xf8, 0x0, 0x0, 0xff, 0x15,
    0xff, 0x90, 0x0, 0x0, 0xff, 0x7f, 0xff, 0x80,
    0x0, 0x0, 0xff, 0xff, 0xbf, 0xf4, 0x0, 0x0,
    0xff, 0xf6, 0xa, 0xfe, 0x20, 0x0, 0xff, 0x60,
    0x0, 0xcf, 0xc0, 0x0, 0xff, 0x10, 0x0, 0x2e,
    0xf9, 0x0, 0xff, 0x10, 0x0, 0x4, 0xff, 0x50,
    0xff, 0x10, 0x0, 0x0, 0x7f, 0xf2,

    /* U+6C "l" */
    0xff, 0x1f, 0xf1, 0xff, 0x1f, 0xf1, 0xff, 0x1f,
    0xf1, 0xff, 0x1f, 0xf1, 0xff, 0x1f, 0xf1, 0xff,
    0x1f, 0xf1, 0xff, 0x1f, 0xf1, 0xff, 0x1f, 0xf1,
    0xff, 0x10,

    /* U+6D "m" */
    0xff, 0x6, 0xcf, 0xfc, 0x60, 0x3, 0xae, 0xfe,
    0xa2, 0x0, 0xff, 0xbf, 0xfd, 0xff, 0xfa, 0x6f,
    0xfe, 0xdf, 0xff, 0x30, 0xff, 0xf7, 0x0, 0x7,
    0xff, 0xfe, 0x30, 0x1, 0xcf, 0xd0, 0xff, 0xa0,
    0x0, 0x0, 0xbf, 0xf4, 0x0, 0x0, 0x2f, 0xf2,
    0xff, 0x40, 0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0,
    0xd, 0xf5, 0xff, 0x20, 0x0, 0x0, 0x6f, 0xc0,
    0x0, 0x0, 0xc, 0xf5, 0xff, 0x10, 0x0, 0x0,
    0x6f, 0xb0, 0x0, 0x0, 0xc, 0xf5, 0xff, 0x10,
    0x0, 0x0, 0x6f, 0xb0, 0x0, 0x0, 0xc, 0xf5,
    0xff, 0x10, 0x0, 0x0, 0x6f, 0xb0, 0x0, 0x0,
    0xc, 0xf5, 0xff, 0x10, 0x0, 0x0, 0x6f, 0xb0,
    0x0, 0x0, 0xc, 0xf5, 0xff, 0x10, 0x0, 0x0,
    0x6f, 0xb0, 0x0, 0x0, 0xc, 0xf5, 0xff, 0x10,
    0x0, 0x0, 0x6f, 0xb0, 0x0, 0x0, 0xc, 0xf5,

    /* U+6E "n" */
    0xff, 0x6, 0xce, 0xfd, 0x80, 0x0, 0xff, 0xbf,
    0xfd, 0xef, 0xfd, 0x0, 0xff, 0xf8, 0x0, 0x4,
    0xef, 0x80, 0xff, 0xb0, 0x0, 0x0, 0x6f, 0xd0,
    0xff, 0x40, 0x0, 0x0, 0x2f, 0xf0, 0xff, 0x20,
    0x0, 0x0, 0xf, 0xf0, 0xff, 0x10, 0x0, 0x0,
    0xf, 0xf1, 0xff, 0x10, 0x0, 0x0, 0xf, 0xf1,
    0xff, 0x10, 0x0, 0x0, 0xf, 0xf1, 0xff, 0x10,
    0x0, 0x0, 0xf, 0xf1, 0xff, 0x10, 0x0, 0x0,
    0xf, 0xf1, 0xff, 0x10, 0x0, 0x0, 0xf, 0xf1,

    /* U+6F "o" */
    0x0, 0x1, 0x7c, 0xff, 0xc7, 0x10, 0x0, 0x0,
    0x3e, 0xff, 0xef, 0xff, 0xd3, 0x0, 0x1, 0xef,
    0xb2, 0x0, 0x2b, 0xfe, 0x10, 0x8, 0xfc, 0x0,
    0x0, 0x0, 0xcf, 0x80, 0xe, 0xf5, 0x0, 0x0,
    0x0, 0x5f, 0xd0, 0xf, 0xf1, 0x0, 0x0, 0x0,
    0x2f, 0xf0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x2f,
    0xf0, 0xe, 0xf5, 0x0, 0x0, 0x0, 0x5f, 0xd0,
    0x8, 0xfd, 0x0, 0x0, 0x0, 0xdf, 0x70, 0x1,
    0xef, 0xc3, 0x0, 0x3c, 0xfd, 0x10, 0x0, 0x3d,
    0xff, 0xff, 0xff, 0xd2, 0x0, 0x0, 0x1, 0x7c,
    0xff, 0xc7, 0x0, 0x0,

    /* U+70 "p" */
    0xff, 0x5, 0xbe, 0xfd, 0x81, 0x0, 0xf, 0xfa,
    0xff, 0xdd, 0xff, 0xe4, 0x0, 0xff, 0xfb, 0x10,
    0x1, 0xaf, 0xe1, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0xcf, 0x90, 0xff, 0x50, 0x0, 0x0, 0x4, 0xfe,
    0xf, 0xf2, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0xff,
    0x20, 0x0, 0x0, 0x1, 0xff, 0xf, 0xf5, 0x0,
    0x0, 0x0, 0x5f, 0xe0, 0xff, 0xd0, 0x0, 0x0,
    0xd, 0xf8, 0xf, 0xff, 0xc3, 0x0, 0x3c, 0xfe,
    0x10, 0xff, 0x9f, 0xff, 0xff, 0xfe, 0x30, 0xf,
    0xf1, 0x4b, 0xef, 0xd8, 0x10, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+71 "q" */
    0x0, 0x1, 0x8d, 0xfe, 0xb4, 0xf, 0xf0, 0x4,
    0xef, 0xfe, 0xff, 0xf8, 0xff, 0x1, 0xef, 0xb2,
    0x0, 0x3c, 0xff, 0xf0, 0x9f, 0xc0, 0x0, 0x0,
    0xd, 0xff, 0xe, 0xf5, 0x0, 0x0, 0x0, 0x5f,
    0xf0, 0xff, 0x10, 0x0, 0x0, 0x2, 0xff, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0x2f, 0xf0, 0xef, 0x50,
    0x0, 0x0, 0x5, 0xff, 0x9, 0xfd, 0x0, 0x0,
    0x0, 0xdf, 0xf0, 0x1e, 0xfc, 0x30, 0x3, 0xcf,
    0xff, 0x0, 0x4e, 0xff, 0xff, 0xff, 0x9f, 0xf0,
    0x0, 0x18, 0xdf, 0xeb, 0x41, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,

    /* U+72 "r" */
    0xff, 0x5, 0xbe, 0x4f, 0xf8, 0xff, 0xf4, 0xff,
    0xfc, 0x41, 0xf, 0xfd, 0x0, 0x0, 0xff, 0x50,
    0x0, 0xf, 0xf3, 0x0, 0x0, 0xff, 0x10, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0xff, 0x10, 0x0, 0xf,
    0xf1, 0x0, 0x0, 0xff, 0x10, 0x0, 0xf, 0xf1,
    0x0, 0x0,

    /* U+73 "s" */
    0x0, 0x29, 0xdf, 0xfd, 0x93, 0x0, 0x5f, 0xff,
    0xde, 0xff, 0xc0, 0xe, 0xf8, 0x0, 0x1, 0x73,
    0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfa,
    0x20, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xeb, 0x72,
    0x0, 0x0, 0x28, 0xcf, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x2, 0x8f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x50, 0xd8, 0x20, 0x0, 0x4f, 0xf3, 0x4f,
    0xff, 0xfe, 0xff, 0xf9, 0x0, 0x28, 0xcf, 0xfd,
    0xb4, 0x0,

    /* U+74 "t" */
    0x0, 0x58, 0x30, 0x0, 0x0, 0xb, 0xf6, 0x0,
    0x0, 0x0, 0xbf, 0x60, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xc0, 0x8c, 0xef, 0xdc, 0xc9, 0x0, 0xb,
    0xf6, 0x0, 0x0, 0x0, 0xbf, 0x60, 0x0, 0x0,
    0xb, 0xf6, 0x0, 0x0, 0x0, 0xbf, 0x60, 0x0,
    0x0, 0xb, 0xf6, 0x0, 0x0, 0x0, 0xbf, 0x60,
    0x0, 0x0, 0xa, 0xf7, 0x0, 0x0, 0x0, 0x7f,
    0xc0, 0x2, 0x0, 0x1, 0xff, 0xfe, 0xf4, 0x0,
    0x2, 0xbe, 0xeb, 0x20,

    /* U+75 "u" */
    0x1f, 0xf0, 0x0, 0x0, 0x3, 0xfe, 0x1f, 0xf0,
    0x0, 0x0, 0x3, 0xfe, 0x1f, 0xf0, 0x0, 0x0,
    0x3, 0xfe, 0x1f, 0xf0, 0x0, 0x0, 0x3, 0xfe,
    0x1f, 0xf0, 0x0, 0x0, 0x3, 0xfe, 0x1f, 0xf0,
    0x0, 0x0, 0x3, 0xfe, 0x1f, 0xf0, 0x0, 0x0,
    0x4, 0xfe, 0x1f, 0xf1, 0x0, 0x0, 0x6, 0xfe,
    0xe, 0xf5, 0x0, 0x0, 0xc, 0xfe, 0x9, 0xfe,
    0x30, 0x0, 0x9f, 0xfe, 0x1, 0xdf, 0xfe, 0xdf,
    0xfb, 0xfe, 0x0, 0x8, 0xdf, 0xfc, 0x52, 0xfe,

    /* U+76 "v" */
    0xd, 0xf5, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x6,
    0xfc, 0x0, 0x0, 0x0, 0x6f, 0xa0, 0x0, 0xff,
    0x30, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x8f, 0x90,
    0x0, 0x3, 0xfd, 0x0, 0x0, 0x2f, 0xf1, 0x0,
    0xa, 0xf6, 0x0, 0x0, 0xb, 0xf7, 0x0, 0x1f,
    0xe0, 0x0, 0x0, 0x4, 0xfd, 0x0, 0x8f, 0x80,
    0x0, 0x0, 0x0, 0xdf, 0x40, 0xef, 0x20, 0x0,
    0x0, 0x0, 0x6f, 0xb5, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xfd, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x60, 0x0, 0x0,

    /* U+77 "w" */
    0xbf, 0x50, 0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0,
    0x6, 0xf7, 0x5f, 0xa0, 0x0, 0x0, 0x8f, 0xf5,
    0x0, 0x0, 0xc, 0xf1, 0xf, 0xf0, 0x0, 0x0,
    0xdf, 0xfb, 0x0, 0x0, 0x2f, 0xb0, 0x9, 0xf6,
    0x0, 0x3, 0xfa, 0xdf, 0x10, 0x0, 0x8f, 0x50,
    0x3, 0xfb, 0x0, 0x9, 0xf4, 0x8f, 0x70, 0x0,
    0xdf, 0x0, 0x0, 0xef, 0x10, 0xf, 0xe0, 0x2f,
    0xc0, 0x3, 0xfa, 0x0, 0x0, 0x8f, 0x60, 0x5f,
    0x80, 0xc, 0xf2, 0x9, 0xf4, 0x0, 0x0, 0x2f,
    0xc0, 0xbf, 0x20, 0x6, 0xf8, 0xe, 0xe0, 0x0,
    0x0, 0xc, 0xf3, 0xfc, 0x0, 0x0, 0xfe, 0x5f,
    0x90, 0x0, 0x0, 0x7, 0xfe, 0xf6, 0x0, 0x0,
    0xaf, 0xdf, 0x30, 0x0, 0x0, 0x1, 0xff, 0xf1,
    0x0, 0x0, 0x4f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xa0, 0x0, 0x0, 0xe, 0xf7, 0x0, 0x0,

    /* U+78 "x" */
    0x2f, 0xf4, 0x0, 0x0, 0x2f, 0xf3, 0x5, 0xfe,
    0x10, 0x0, 0xcf, 0x70, 0x0, 0x9f, 0xb0, 0x9,
    0xfb, 0x0, 0x0, 0xd, 0xf7, 0x4f, 0xe1, 0x0,
    0x0, 0x2, 0xff, 0xef, 0x30, 0x0, 0x0, 0x0,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfa,
    0x0, 0x0, 0x0, 0x5, 0xfe, 0xdf, 0x60, 0x0,
    0x0, 0x2f, 0xf4, 0x2f, 0xf3, 0x0, 0x0, 0xcf,
    0x80, 0x6, 0xfe, 0x10, 0x9, 0xfc, 0x0, 0x0,
    0xaf, 0xb0, 0x5f, 0xe1, 0x0, 0x0, 0xd, 0xf7,

    /* U+79 "y" */
    0xd, 0xf6, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x6,
    0xfc, 0x0, 0x0, 0x0, 0x6f, 0xa0, 0x0, 0xef,
    0x40, 0x0, 0x0, 0xdf, 0x30, 0x0, 0x7f, 0xb0,
    0x0, 0x4, 0xfc, 0x0, 0x0, 0x1f, 0xf2, 0x0,
    0xb, 0xf5, 0x0, 0x0, 0x9, 0xf9, 0x0, 0x2f,
    0xd0, 0x0, 0x0, 0x2, 0xff, 0x0, 0x9f, 0x70,
    0x0, 0x0, 0x0, 0xbf, 0x71, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xd7, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfc,
    0x0, 0x0, 0x0, 0x6, 0x10, 0x2e, 0xf5, 0x0,
    0x0, 0x0, 0x4f, 0xfe, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x8, 0xef, 0xd7, 0x0, 0x0, 0x0, 0x0,

    /* U+7A "z" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0x70, 0xcc, 0xcc,
    0xcc, 0xdf, 0xf5, 0x0, 0x0, 0x0, 0xc, 0xf9,
    0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0,
    0x5, 0xfe, 0x20, 0x0, 0x0, 0x2, 0xff, 0x40,
    0x0, 0x0, 0x1, 0xdf, 0x80, 0x0, 0x0, 0x0,
    0xbf, 0xb0, 0x0, 0x0, 0x0, 0x7f, 0xd1, 0x0,
    0x0, 0x0, 0x4f, 0xf3, 0x0, 0x0, 0x0, 0xe,
    0xfe, 0xcc, 0xcc, 0xcc, 0x72, 0xff, 0xff, 0xff,
    0xff, 0xfa,

    /* U+7B "{" */
    0x0, 0x9, 0xef, 0x40, 0x9, 0xff, 0xd3, 0x0,
    0xef, 0x60, 0x0, 0xf, 0xf2, 0x0, 0x0, 0xff,
    0x20, 0x0, 0xf, 0xf2, 0x0, 0x0, 0xff, 0x20,
    0x0, 0xf, 0xf2, 0x0, 0x1, 0xff, 0x10, 0x9,
    0xef, 0xb0, 0x0, 0xcf, 0xf8, 0x0, 0x0, 0x3f,
    0xf0, 0x0, 0x0, 0xff, 0x10, 0x0, 0xf, 0xf2,
    0x0, 0x0, 0xff, 0x20, 0x0, 0xf, 0xf2, 0x0,
    0x0, 0xff, 0x20, 0x0, 0xf, 0xf2, 0x0, 0x0,
    0xdf, 0x60, 0x0, 0x8, 0xff, 0xd3, 0x0, 0x8,
    0xef, 0x40,

    /* U+7C "|" */
    0xbf, 0x4b, 0xf4, 0xbf, 0x4b, 0xf4, 0xbf, 0x4b,
    0xf4, 0xbf, 0x4b, 0xf4, 0xbf, 0x4b, 0xf4, 0xbf,
    0x4b, 0xf4, 0xbf, 0x4b, 0xf4, 0xbf, 0x4b, 0xf4,
    0xbf, 0x4b, 0xf4, 0xbf, 0x4b, 0xf4, 0xbf, 0x40,

    /* U+7D "}" */
    0x9f, 0xd6, 0x0, 0x7, 0xef, 0xf4, 0x0, 0x0,
    0xaf, 0xa0, 0x0, 0x6, 0xfb, 0x0, 0x0, 0x6f,
    0xb0, 0x0, 0x6, 0xfb, 0x0, 0x0, 0x6f, 0xb0,
    0x0, 0x6, 0xfb, 0x0, 0x0, 0x5f, 0xd0, 0x0,
    0x1, 0xef, 0xd5, 0x0, 0xb, 0xff, 0x70, 0x4,
    0xfe, 0x10, 0x0, 0x5f, 0xb0, 0x0, 0x6, 0xfb,
    0x0, 0x0, 0x6f, 0xb0, 0x0, 0x6, 0xfb, 0x0,
    0x0, 0x6f, 0xb0, 0x0, 0x6, 0xfb, 0x0, 0x0,
    0xaf, 0x90, 0x7, 0xef, 0xf3, 0x0, 0x9f, 0xd5,
    0x0, 0x0,

    /* U+7E "~" */
    0x1, 0x89, 0x50, 0x0, 0x9, 0x51, 0xef, 0xff,
    0xa0, 0x2, 0xf5, 0x7f, 0x41, 0x8f, 0xd8, 0xdf,
    0x1a, 0xb0, 0x0, 0x3c, 0xfd, 0x40,

    /* U+B0 "°" */
    0x0, 0x4c, 0xfd, 0x60, 0x0, 0x4f, 0x83, 0x6f,
    0x80, 0xc, 0x80, 0x0, 0x5f, 0x0, 0xf4, 0x0,
    0x1, 0xf3, 0xd, 0x60, 0x0, 0x4f, 0x10, 0x7e,
    0x50, 0x3d, 0xa0, 0x0, 0x8f, 0xff, 0xa0, 0x0,
    0x0, 0x2, 0x10, 0x0,

    /* U+2022 "•" */
    0x0, 0x0, 0x1, 0xcf, 0xb0, 0x7f, 0xff, 0x56,
    0xff, 0xf5, 0xb, 0xfa, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x9e, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26, 0xbf,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x48, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x1, 0x5a, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x94, 0xe, 0xfd, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xfc, 0x72, 0x0, 0x0, 0xef, 0xd0,
    0x0, 0x0, 0xa, 0xff, 0xea, 0x50, 0x0, 0x0,
    0x0, 0xe, 0xfd, 0x0, 0x0, 0x0, 0xaf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xd0, 0x0,
    0x0, 0xa, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xfd, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xd0, 0x0, 0x0,
    0xa, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xfd, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0,
    0x0, 0x3, 0x54, 0xff, 0xd0, 0x0, 0x0, 0xa,
    0xff, 0x10, 0x0, 0x0, 0x5e, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xd0, 0x1, 0x69, 0x9d, 0xff,
    0x10, 0x0, 0x6, 0xff, 0xff, 0xff, 0xfc, 0x6,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x1e, 0xff,
    0xff, 0xff, 0x61, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x2a, 0xff, 0xfc, 0x50, 0x1f, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xcc,
    0xa3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F008 "" */
    0x42, 0x0, 0x78, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x87, 0x0, 0x24, 0xf8, 0x22, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x22, 0x8f, 0xff, 0xff,
    0xff, 0xb9, 0x99, 0x99, 0x99, 0x9b, 0xff, 0xff,
    0xff, 0xf9, 0x44, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x44, 0x9f, 0xf6, 0x0, 0xef, 0x30,
    0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x6f, 0xf7,
    0x0, 0xef, 0x30, 0x0, 0x0, 0x0, 0x3, 0xfe,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xfa, 0x66, 0xff,
    0x74, 0x44, 0x44, 0x44, 0x47, 0xff, 0x66, 0xaf,
    0xf6, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x6f, 0xf6, 0x0, 0xef, 0xdc, 0xcc,
    0xcc, 0xcc, 0xcd, 0xfe, 0x0, 0x6f, 0xff, 0xee,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x3, 0xff, 0xee,
    0xff, 0xfc, 0x88, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x88, 0xcf, 0xf6, 0x0, 0xef, 0x30,
    0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x6f, 0xf6,
    0x0, 0xef, 0x30, 0x0, 0x0, 0x0, 0x3, 0xfe,
    0x0, 0x6f, 0xfe, 0xcc, 0xff, 0x41, 0x11, 0x11,
    0x11, 0x14, 0xff, 0xcc, 0xef, 0xfd, 0xaa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaa, 0xdf,
    0xc6, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x6c,

    /* U+F00B "" */
    0xbf, 0xff, 0xfe, 0x31, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0x63, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x63, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x63, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x63,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x58,
    0x88, 0x87, 0x0, 0x78, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x85, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0x31, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0x63, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x63, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x63, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x53, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x47, 0x88, 0x87, 0x0,
    0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x74, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0x31, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0x63, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x63, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x63, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x52, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x37, 0x77, 0x76, 0x0, 0x57, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x74,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x16, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0x30, 0x4, 0xe8, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xf3, 0x0, 0x4f, 0xff, 0x80,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0x30, 0x0,
    0xef, 0xff, 0xf8, 0x0, 0x0, 0x2e, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x80, 0x2,
    0xef, 0xff, 0xff, 0x30, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xf8, 0x2e, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xd3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x8, 0xc4, 0x0, 0x0, 0x0, 0x2, 0xc9, 0x0,
    0x9f, 0xff, 0x40, 0x0, 0x0, 0x2e, 0xff, 0xb0,
    0xff, 0xff, 0xf4, 0x0, 0x2, 0xef, 0xff, 0xf1,
    0x7f, 0xff, 0xff, 0x40, 0x2e, 0xff, 0xff, 0x90,
    0x8, 0xff, 0xff, 0xf6, 0xef, 0xff, 0xfa, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x2, 0xef, 0xff, 0xfd, 0xff, 0xff, 0xf4, 0x0,
    0x2e, 0xff, 0xff, 0xa0, 0x8f, 0xff, 0xff, 0x40,
    0xdf, 0xff, 0xfa, 0x0, 0x8, 0xff, 0xff, 0xf0,
    0xdf, 0xff, 0xa0, 0x0, 0x0, 0x8f, 0xff, 0xe0,
    0x2e, 0xfa, 0x0, 0x0, 0x0, 0x8, 0xff, 0x30,
    0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x21, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x33, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x50, 0xe, 0xff, 0x80,
    0xb, 0xf4, 0x0, 0x0, 0x0, 0xb, 0xff, 0xe0,
    0xe, 0xff, 0x80, 0x4f, 0xff, 0x50, 0x0, 0x0,
    0x9f, 0xff, 0xe0, 0xe, 0xff, 0x80, 0x5f, 0xff,
    0xf2, 0x0, 0x3, 0xff, 0xfe, 0x30, 0xe, 0xff,
    0x80, 0x8, 0xff, 0xfc, 0x0, 0xb, 0xff, 0xf3,
    0x0, 0xe, 0xff, 0x80, 0x0, 0xaf, 0xff, 0x50,
    0x1f, 0xff, 0x90, 0x0, 0xe, 0xff, 0x80, 0x0,
    0x1f, 0xff, 0xb0, 0x6f, 0xff, 0x30, 0x0, 0xe,
    0xff, 0x80, 0x0, 0x9, 0xff, 0xf0, 0x8f, 0xff,
    0x0, 0x0, 0xe, 0xff, 0x80, 0x0, 0x5, 0xff,
    0xf2, 0xaf, 0xfd, 0x0, 0x0, 0xe, 0xff, 0x80,
    0x0, 0x3, 0xff, 0xf3, 0x9f, 0xfd, 0x0, 0x0,
    0xc, 0xff, 0x50, 0x0, 0x4, 0xff, 0xf2, 0x8f,
    0xff, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x6,
    0xff, 0xf1, 0x4f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xe0, 0xf, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x90,
    0x8, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0x20, 0x1, 0xef, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x2d, 0xff, 0xf9, 0x0, 0x0, 0x4f,
    0xff, 0xfd, 0x62, 0x1, 0x49, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7d, 0xff, 0xff, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x25, 0x65,
    0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6c,
    0xee, 0xc7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0x30, 0x2a,
    0xff, 0xff, 0xff, 0xb2, 0x3, 0x80, 0x0, 0x0,
    0x8f, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbf,
    0xf8, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x1f, 0xff, 0xff, 0xff, 0xfa, 0x55, 0xaf, 0xff,
    0xff, 0xff, 0xf2, 0x6, 0xef, 0xff, 0xff, 0x70,
    0x0, 0x7, 0xff, 0xff, 0xff, 0x60, 0x0, 0x2f,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xf3,
    0x0, 0x0, 0x3f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf3, 0x0, 0x0, 0x2f, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xf3, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0x20, 0x0, 0x1, 0xff, 0xff,
    0xf9, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xd3, 0x0,
    0x3d, 0xff, 0xff, 0xff, 0xe1, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xee, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x2f,
    0xb2, 0x9f, 0xff, 0xff, 0xff, 0xfa, 0x2a, 0xf3,
    0x0, 0x0, 0x1, 0x0, 0x2, 0xdf, 0xff, 0xfe,
    0x30, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x66,
    0x41, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xe9, 0x0,
    0x5, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xff, 0xfc, 0x10, 0x7f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xfe, 0x37, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xb4, 0xdf, 0xff, 0xbf, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xff, 0x80,
    0x1, 0xbf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xff, 0x50, 0x8f, 0x50, 0x9f, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfe, 0x30,
    0xbf, 0xff, 0x70, 0x6f, 0xff, 0xf1, 0x0, 0x0,
    0x7, 0xff, 0xfc, 0x12, 0xdf, 0xff, 0xff, 0xa0,
    0x3e, 0xff, 0xe3, 0x0, 0xa, 0xff, 0xfa, 0x3,
    0xef, 0xff, 0xff, 0xff, 0xc1, 0x2d, 0xff, 0xf6,
    0xc, 0xff, 0xf7, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe3, 0xb, 0xff, 0xf8, 0x9f, 0xf5, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x8,
    0xff, 0x50, 0x93, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x5, 0x70, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xcc, 0xcf, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0x50, 0x0, 0x9f, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xf5, 0x0, 0x9, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0x50, 0x0, 0x9f,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xf5, 0x0, 0x9, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0x40, 0x0,
    0x8f, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x2,
    0x44, 0x44, 0x40, 0x0, 0x1, 0x44, 0x44, 0x41,
    0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x22, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xee, 0xee, 0xff,
    0xff, 0xff, 0xee, 0xee, 0x20, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xfc, 0x4, 0xff, 0x40, 0xcf,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x23, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x44, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x6e, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xaf,
    0x5d, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F01C "" */
    0x0, 0x0, 0x4, 0xab, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x9f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0x50, 0x0, 0x0, 0x4f, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xfe, 0x10, 0x0, 0xd,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xfa, 0x0, 0x9, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf5, 0x3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xe1, 0xdf, 0xfa, 0x44, 0x44,
    0x20, 0x0, 0x0, 0x0, 0x34, 0x44, 0x4d, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x88,
    0x88, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xbd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x10,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0x76, 0x0, 0x0, 0x0, 0x5, 0x9c,
    0xdd, 0xb8, 0x30, 0x0, 0xf, 0xff, 0x0, 0x0,
    0x6, 0xef, 0xff, 0xff, 0xff, 0xfc, 0x40, 0xf,
    0xff, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xf, 0xff, 0x0, 0xc, 0xff, 0xff,
    0xa5, 0x34, 0x6b, 0xff, 0xff, 0xce, 0xff, 0x0,
    0xaf, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x2b, 0xff,
    0xff, 0xff, 0x5, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xd, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x7d, 0xdc, 0xbf, 0xff, 0xff,
    0x2f, 0xff, 0x20, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0x6f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0x2, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x22, 0x22, 0x22,
    0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xde,
    0xee, 0xee, 0xee, 0xe7, 0x0, 0x0, 0x0, 0x0,
    0x9e, 0xe5, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf3, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x9, 0xff, 0xe0,
    0xff, 0xff, 0xf4, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x70, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xfd, 0x0, 0xff, 0xff,
    0xff, 0xfd, 0x61, 0x0, 0x5, 0xcf, 0xff, 0xf2,
    0x0, 0xff, 0xe3, 0xef, 0xff, 0xff, 0xee, 0xff,
    0xff, 0xfe, 0x30, 0x0, 0xff, 0xf0, 0x19, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb1, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x28, 0xdf, 0xff, 0xfe, 0xa3, 0x0,
    0x0, 0x0, 0xab, 0xa0, 0x0, 0x0, 0x1, 0x33,
    0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x30, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x24, 0x44,
    0x47, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x24, 0x44, 0x47, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x75, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x2f, 0xf7, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x7f, 0xf0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xdf, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xf, 0xf3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xc, 0xfd, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1, 0xfe, 0x20, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x30, 0x0,
    0x0, 0x0, 0x9f, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x0, 0x0, 0x1, 0x0, 0x7f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf0,
    0x0, 0x5, 0xf9, 0x0, 0x9f, 0xe1, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0x0, 0x0, 0x4f, 0xfb,
    0x0, 0xdf, 0x80, 0x24, 0x44, 0x47, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x3f, 0xf7, 0x4, 0xff, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x86, 0x0,
    0x4f, 0xf1, 0xd, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x2f, 0xf7, 0x0, 0xcf, 0x60, 0x9f,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x6f,
    0xf1, 0x7, 0xf9, 0x6, 0xfa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xdf, 0x40, 0x5f, 0xb0,
    0x5f, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x1f, 0xf2, 0x5, 0xfa, 0x5, 0xfb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xd, 0xfc, 0x0, 0x9f,
    0x80, 0x7f, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1, 0xfd, 0x20, 0x1e, 0xf3, 0xb, 0xf6, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x1, 0x0, 0xa,
    0xfc, 0x1, 0xff, 0x20, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0x0, 0x0, 0xb, 0xff, 0x20, 0x8f, 0xc0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xf0, 0x0, 0x7,
    0xff, 0x40, 0x2f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0x0, 0x0, 0x19, 0x20, 0x1d, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xc0, 0x0,
    0x0, 0x0, 0x2d, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfd,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xfb, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x5, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x50, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x1, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0xd, 0xff, 0xff, 0xff, 0xdb, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x2f, 0xff, 0xff,
    0xfd, 0x10, 0xaf, 0xff, 0xff, 0xff, 0xfb, 0x56,
    0xef, 0xff, 0xff, 0xd1, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xf7, 0x2e, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0x70, 0x2, 0xed, 0x10, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x21, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xfc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcf, 0xff, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe4,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xc0, 0x0, 0x0, 0x0, 0xb, 0xd3, 0x2f,
    0xfe, 0x0, 0x0, 0x0, 0x1d, 0xff, 0x82, 0xff,
    0xe0, 0x0, 0x0, 0x2d, 0xff, 0xf9, 0x2f, 0xfe,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0x92, 0xff, 0xe0,
    0x0, 0x3e, 0xff, 0xff, 0xf9, 0x2f, 0xfe, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0x92, 0xff, 0xe0, 0x5f,
    0xff, 0xff, 0xff, 0xf9, 0x2f, 0xfe, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0x92, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x92, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x2f, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0x92, 0xff, 0xe0, 0xbf, 0xff, 0xff, 0xff,
    0xf9, 0x2f, 0xfe, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0x92, 0xff, 0xe0, 0x0, 0x8f, 0xff, 0xff, 0xf9,
    0x2f, 0xfe, 0x0, 0x0, 0x7f, 0xff, 0xff, 0x92,
    0xff, 0xe0, 0x0, 0x0, 0x6f, 0xff, 0xf9, 0x2f,
    0xfe, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x92, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x4f, 0xf5, 0x4, 0x43,
    0x0, 0x0, 0x0, 0x0, 0x13, 0x0,

    /* U+F04B "" */
    0x2, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xfb, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x91,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x60, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x30, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x10, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x40, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb2, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xfd, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2a, 0xa3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x3d, 0xff, 0xff, 0xe6, 0x0, 0x3, 0xdf, 0xff,
    0xfe, 0x60, 0xdf, 0xff, 0xff, 0xff, 0x10, 0xd,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0x30, 0xf, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0xff, 0xff, 0x40, 0xf, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0x40, 0xf, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0x40, 0xf,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0x40, 0xf, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0x40, 0xf, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0x40, 0xf, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0x40, 0xf,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0x40, 0xf, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0x40, 0xf, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0x40, 0xf, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0x40, 0xf,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0x40, 0xf, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0x40, 0xf, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0x40, 0xf, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0x30, 0xf,
    0xff, 0xff, 0xff, 0xf3, 0x8f, 0xff, 0xff, 0xfb,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xb0, 0x2, 0x44,
    0x44, 0x30, 0x0, 0x0, 0x24, 0x44, 0x43, 0x0,

    /* U+F04D "" */
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x60, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x2, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0x0,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xdc, 0x10, 0x0, 0x0, 0x0, 0xbf, 0xf3, 0x7f,
    0xfd, 0x20, 0x0, 0x0, 0xd, 0xff, 0x47, 0xff,
    0xfe, 0x30, 0x0, 0x0, 0xdf, 0xf4, 0x7f, 0xff,
    0xff, 0x40, 0x0, 0xd, 0xff, 0x47, 0xff, 0xff,
    0xff, 0x50, 0x0, 0xdf, 0xf4, 0x7f, 0xff, 0xff,
    0xff, 0x60, 0xd, 0xff, 0x47, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xdf, 0xf4, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0x8d, 0xff, 0x47, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x47, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xcd,
    0xff, 0x47, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xdf,
    0xf4, 0x7f, 0xff, 0xff, 0xff, 0xa0, 0xd, 0xff,
    0x47, 0xff, 0xff, 0xff, 0x90, 0x0, 0xdf, 0xf4,
    0x7f, 0xff, 0xff, 0x80, 0x0, 0xd, 0xff, 0x47,
    0xff, 0xff, 0x70, 0x0, 0x0, 0xdf, 0xf4, 0x7f,
    0xff, 0x60, 0x0, 0x0, 0xd, 0xff, 0x44, 0xff,
    0x50, 0x0, 0x0, 0x0, 0xcf, 0xf4, 0x2, 0x20,
    0x0, 0x0, 0x0, 0x2, 0x44, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x8, 0xea, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x1, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x56,
    0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x61,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x27, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x30,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x19, 0x20, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xe2, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xf8, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xd1,
    0x0, 0x0, 0x1d, 0xff, 0xfc, 0x10, 0x0, 0x1,
    0xdf, 0xff, 0xc1, 0x0, 0x0, 0x1d, 0xff, 0xfc,
    0x10, 0x0, 0x1, 0xdf, 0xff, 0xc1, 0x0, 0x0,
    0x1d, 0xff, 0xfc, 0x10, 0x0, 0x0, 0xbf, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,

    /* U+F054 "" */
    0x5, 0x80, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x3e,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x3, 0xef, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x3, 0xef, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xf3, 0x0, 0x0, 0x9, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x9f, 0xff, 0xf4, 0x0, 0x0, 0x9,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x9f, 0xff, 0xf4,
    0x0, 0x0, 0x9, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x8f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x1c, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x1, 0xbe, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x14, 0x55,
    0x55, 0x59, 0xff, 0xfc, 0x55, 0x55, 0x54, 0x20,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x6c, 0xdd,
    0xdd, 0xde, 0xff, 0xff, 0xdd, 0xdd, 0xdc, 0x90,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x36, 0x50, 0x0, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x43, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5b, 0xff, 0xff, 0xff, 0xea, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xef, 0xff, 0xfd, 0xce,
    0xff, 0xff, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x1b,
    0xff, 0xff, 0x71, 0x0, 0x2, 0x9f, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xfe, 0x20, 0x2,
    0x53, 0x0, 0x5f, 0xff, 0xfb, 0x0, 0x0, 0x1e,
    0xff, 0xff, 0x30, 0x0, 0x6f, 0xfc, 0x10, 0x7f,
    0xff, 0xfb, 0x0, 0xc, 0xff, 0xff, 0xa0, 0x0,
    0x6, 0xff, 0xfc, 0x0, 0xef, 0xff, 0xf8, 0x6,
    0xff, 0xff, 0xf5, 0x1, 0x3, 0xef, 0xff, 0xf4,
    0x9, 0xff, 0xff, 0xf3, 0xef, 0xff, 0xff, 0x30,
    0xbf, 0xff, 0xff, 0xff, 0x70, 0x7f, 0xff, 0xff,
    0xad, 0xff, 0xff, 0xf3, 0xa, 0xff, 0xff, 0xff,
    0xf6, 0x7, 0xff, 0xff, 0xf9, 0x3f, 0xff, 0xff,
    0x60, 0x5f, 0xff, 0xff, 0xff, 0x10, 0xaf, 0xff,
    0xfe, 0x10, 0x8f, 0xff, 0xfc, 0x0, 0xaf, 0xff,
    0xff, 0x60, 0x1f, 0xff, 0xff, 0x50, 0x0, 0xaf,
    0xff, 0xf6, 0x0, 0x6c, 0xdb, 0x40, 0xa, 0xff,
    0xff, 0x70, 0x0, 0x0, 0xaf, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xfc, 0x52, 0x12, 0x6d, 0xff,
    0xfe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x19, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x5a, 0xde, 0xfe, 0xd9,
    0x50, 0x0, 0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xd2, 0x0, 0x0, 0x0,
    0x24, 0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x60, 0x28, 0xcf, 0xff, 0xff,
    0xfb, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b,
    0xff, 0xfc, 0xff, 0xff, 0xec, 0xdf, 0xff, 0xfe,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xa3, 0x0, 0x0, 0x6e, 0xff, 0xfc, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xff, 0x70,
    0x15, 0x40, 0x1, 0xdf, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x1c, 0xff, 0xfb, 0x1f, 0xfe,
    0x50, 0x2f, 0xff, 0xff, 0x20, 0x0, 0x0, 0x7e,
    0x30, 0x0, 0x9f, 0xff, 0xef, 0xff, 0xf3, 0x8,
    0xff, 0xff, 0xd0, 0x0, 0x2, 0xff, 0xf6, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xfa, 0x3, 0xff, 0xff,
    0xf8, 0x0, 0x8, 0xff, 0xff, 0xa0, 0x0, 0x2d,
    0xff, 0xff, 0xfd, 0x1, 0xff, 0xff, 0xff, 0x0,
    0x6, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xaf, 0xff,
    0xfb, 0x2, 0xff, 0xff, 0xfe, 0x0, 0x0, 0xdf,
    0xff, 0xfd, 0x0, 0x0, 0x6, 0xff, 0xff, 0x55,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x3d, 0xff, 0xfe, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x4, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xfb, 0x10, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xff, 0xe8, 0x31, 0x20, 0x0, 0x3e,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xef, 0xff, 0xff, 0xfb, 0x0, 0x1, 0xbf, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x9c,
    0xef, 0xfd, 0x80, 0x0, 0x8, 0xff, 0xfd, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4e, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0x60,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xfd, 0x88, 0x8f, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0x80,
    0x0, 0xcf, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xf9, 0x0, 0xd, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xa0, 0x0, 0xef, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xfb,
    0x0, 0xf, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xfd, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xfd, 0xcd, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xfe, 0x30, 0x6f,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x17,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x2, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x90, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3c, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf5,
    0x0, 0x12, 0x22, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x22, 0x9f, 0xff, 0x50, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xf5, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x45, 0x55, 0xbf,
    0xff, 0x60, 0xaf, 0xff, 0xd5, 0xaf, 0xff, 0x80,
    0x0, 0x0, 0xb, 0xf8, 0xa, 0xff, 0xfe, 0x10,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x70, 0x9f,
    0xff, 0xe2, 0x0, 0x4f, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3, 0x10,
    0x0, 0x2a, 0x30, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0x41, 0xda, 0x0, 0x7f, 0xf3, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xf4, 0xd, 0xff, 0x90, 0x8f,
    0xff, 0x30, 0xef, 0xff, 0xff, 0xff, 0x50, 0x2e,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xfa, 0x67, 0x77, 0x75, 0x0, 0x0,
    0x0, 0x3, 0x77, 0xbf, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x4e, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xfc, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xfa, 0x5, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x4f, 0xff, 0xfa, 0x0, 0x5, 0xff,
    0xff, 0x70, 0x0, 0x4f, 0xff, 0xf9, 0x0, 0x0,
    0x6, 0xff, 0xff, 0x70, 0x3f, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0x7a, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x2e,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x50, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x40,

    /* U+F078 "" */
    0x9, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xcb, 0x18, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xfc, 0x9f, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xd0, 0xbf, 0xff, 0xe3,
    0x0, 0x0, 0x1, 0xcf, 0xff, 0xd1, 0x0, 0xbf,
    0xff, 0xe3, 0x0, 0x1, 0xcf, 0xff, 0xd1, 0x0,
    0x0, 0xbf, 0xff, 0xe3, 0x1, 0xcf, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xe4, 0xcf, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x61, 0x0, 0x0,
    0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x27, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xfd,
    0x10, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xd1, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xfd, 0x15, 0xbb, 0xbb,
    0xbb, 0xbb, 0xef, 0xf1, 0x0, 0x0, 0xdf, 0xfb,
    0xef, 0xdc, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf1, 0x0, 0x0, 0x9f, 0xc0, 0xef, 0xd1,
    0xdf, 0x70, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1,
    0x0, 0x0, 0x3, 0x0, 0xef, 0xd0, 0x3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xef, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0x50, 0xaf, 0xf1,
    0x19, 0x30, 0x0, 0x0, 0xef, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf6, 0xaf, 0xf3, 0xdf, 0xe0,
    0x0, 0x0, 0xef, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xef, 0xfe, 0xff, 0xd0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x17, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x6f, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x6b, 0xbb, 0xbb, 0xbb,
    0xbb, 0xba, 0x30, 0x6, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xc1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x5, 0x78, 0x88, 0x88, 0x71, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xcc,
    0xcc, 0xcc, 0xcb, 0x91, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe4,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x11, 0x11, 0xef,
    0xff, 0xf9, 0x11, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xe0, 0xdf, 0xff, 0xf8, 0xe,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xf3, 0x26,
    0x66, 0x50, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x52, 0x22, 0x25, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x6e, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xaf,
    0x5d, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xc8, 0x51, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x5, 0xb8, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x1, 0x8e, 0xff,
    0xf6, 0x0, 0x8, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xf4, 0x4d, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7b, 0xb9,
    0x74, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x1, 0x8c, 0xda, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0, 0x1d, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x80, 0xaf, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xf1, 0xef, 0xe1,
    0xc, 0xff, 0x20, 0x0, 0xbf, 0xff, 0xfe, 0x30,
    0xff, 0xd0, 0xa, 0xff, 0x30, 0xb, 0xff, 0xff,
    0xe3, 0x0, 0xbf, 0xfc, 0xbf, 0xff, 0x10, 0xcf,
    0xff, 0xfe, 0x30, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xdc, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x3, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x1, 0x2d, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x1, 0x8c,
    0xef, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0x33, 0xef,
    0xff, 0xfc, 0x0, 0x0, 0xef, 0xe1, 0xc, 0xff,
    0x20, 0x2e, 0xff, 0xff, 0xc1, 0x0, 0xff, 0xd0,
    0xa, 0xff, 0x30, 0x2, 0xef, 0xff, 0xfc, 0x10,
    0xbf, 0xfc, 0xbf, 0xff, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0xc0, 0x3f, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xd1, 0x3, 0xdf, 0xfe, 0x60,
    0x0, 0x0, 0x0, 0x4, 0x75, 0x0, 0x0, 0x1,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x12, 0x22, 0x22, 0x22, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xfe, 0xe, 0x70, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xfe, 0xe, 0xf7, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xfe, 0xe, 0xff, 0x70,
    0x1, 0x22, 0x5, 0xff, 0xff, 0xff, 0xfe, 0xe,
    0xff, 0xf2, 0xdf, 0xff, 0x25, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0xff, 0xff, 0x25, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0x25, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0x25, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0x25, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0x25, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0x25, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0x25, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0x25, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0x25, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0x25, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0x25, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0x24, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x50, 0x56,
    0x66, 0x66, 0x66, 0x66, 0x66, 0x50, 0xff, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x8b, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xb7, 0x0, 0x0, 0x0,

    /* U+F0C7 "" */
    0x2a, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x91,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfd, 0x10,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xd0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xf5, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xf6, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xf6,
    0xff, 0xfb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbc, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0xfe, 0x51, 0x3b, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xf6, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x4, 0xff, 0xff, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xda, 0xbf, 0xff, 0xff,
    0xff, 0xf6, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0,

    /* U+F0E7 "" */
    0x0, 0x3, 0x44, 0x44, 0x43, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xfe, 0x66, 0x66, 0x51, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x1, 0x22, 0x22, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0x90, 0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8c, 0xcc, 0xdf, 0xbc,
    0xfc, 0xcc, 0xc6, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xfe, 0x1, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xde, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x92,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xfc, 0x1, 0x22, 0x22, 0x22, 0x1, 0x0, 0x0,
    0xff, 0xff, 0xf8, 0xe, 0xff, 0xff, 0xfc, 0xe,
    0x70, 0x0, 0xff, 0xff, 0xf8, 0x1f, 0xff, 0xff,
    0xfc, 0xe, 0xf7, 0x0, 0xff, 0xff, 0xf8, 0x1f,
    0xff, 0xff, 0xfc, 0xe, 0xff, 0x70, 0xff, 0xff,
    0xf8, 0x1f, 0xff, 0xff, 0xfc, 0xe, 0xff, 0xf2,
    0xff, 0xff, 0xf8, 0x1f, 0xff, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0xff, 0xff, 0xf8, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xf8, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0xf8, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0xf8, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xf8, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x58, 0x88, 0x84, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x8,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0x80,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x28, 0xef, 0xf9, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x6c, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7b, 0x81, 0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x19, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xb7, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xbf, 0xfd, 0x0,
    0x8f, 0x10, 0x7f, 0x10, 0x5f, 0x30, 0x3f, 0x50,
    0x1f, 0xfc, 0xff, 0xc0, 0x7, 0xe0, 0x6, 0xf0,
    0x4, 0xf2, 0x2, 0xf4, 0x0, 0xff, 0xcf, 0xfc,
    0x0, 0x8f, 0x0, 0x7f, 0x10, 0x5f, 0x30, 0x3f,
    0x50, 0x1f, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcf,
    0xff, 0xfe, 0x66, 0xbf, 0x66, 0xaf, 0x76, 0x8f,
    0x86, 0x7f, 0xff, 0xfc, 0xff, 0xff, 0xd0, 0x6,
    0xf0, 0x5, 0xf1, 0x3, 0xf3, 0x1, 0xff, 0xff,
    0xcf, 0xff, 0xfd, 0x0, 0x7f, 0x0, 0x5f, 0x10,
    0x3f, 0x30, 0x1f, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xcf, 0xfe, 0x66, 0xcf, 0x76, 0x66, 0x66,
    0x66, 0x66, 0x9f, 0xa6, 0x7f, 0xfc, 0xff, 0xc0,
    0x7, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xf4,
    0x0, 0xff, 0xcf, 0xfc, 0x0, 0x7e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x40, 0xf, 0xfc, 0xff,
    0xfc, 0xce, 0xfc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcd,
    0xfd, 0xcc, 0xff, 0xbd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x10,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x7e, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x5, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x7, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x4, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x1, 0x22, 0x22,
    0x22, 0x7f, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xb8, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x37, 0x77, 0x77, 0x77, 0x74, 0x5, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xa0, 0xfa, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xf, 0xfa,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xa0, 0xff,
    0xfa, 0x0, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xf,
    0xff, 0xfa, 0xf, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xe3, 0x22, 0x22, 0x21, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x40,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x26, 0x9c, 0xde, 0xdc, 0xb8, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x8d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd5, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xdb, 0x99, 0x9a,
    0xcf, 0xff, 0xff, 0xff, 0xb1, 0x0, 0xa, 0xff,
    0xff, 0xfc, 0x61, 0x0, 0x0, 0x0, 0x0, 0x39,
    0xef, 0xff, 0xfe, 0x40, 0xcf, 0xff, 0xfb, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xf4, 0xaf, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xf2,
    0xa, 0xd2, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xed,
    0xb6, 0x20, 0x0, 0x0, 0x8e, 0x30, 0x0, 0x0,
    0x0, 0x6, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xfb,
    0x87, 0x89, 0xdf, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xe6, 0x0, 0x0, 0x0,
    0x3, 0xaf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xba, 0x10, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xeb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbd, 0xff, 0x50,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf9, 0xff, 0xc0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x7, 0xff, 0xfa, 0xff, 0xc0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3,
    0x7f, 0xfa, 0xff, 0xc0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x1f, 0xfa,
    0xff, 0xc0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x1f, 0xfa, 0xff, 0xc0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x5, 0xdf, 0xfa, 0xff, 0xc0, 0x78, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x7,
    0xff, 0xfa, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x19, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0x92, 0x0,

    /* U+F241 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xeb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbd, 0xff, 0x50,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf9, 0xff, 0xc0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x7, 0xff, 0xfa, 0xff, 0xc0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x3,
    0x7f, 0xfa, 0xff, 0xc0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x1f, 0xfa,
    0xff, 0xc0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0xff, 0xc0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x5, 0xdf, 0xfa, 0xff, 0xc0, 0x78, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x86, 0x0, 0x0, 0x7,
    0xff, 0xfa, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x19, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0x92, 0x0,

    /* U+F242 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xeb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbd, 0xff, 0x50,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf9, 0xff, 0xc0,
    0xef, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xfa, 0xff, 0xc0, 0xef, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x7f, 0xfa, 0xff, 0xc0, 0xef, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa,
    0xff, 0xc0, 0xef, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0xff, 0xc0,
    0xef, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xdf, 0xfa, 0xff, 0xc0, 0x78, 0x88,
    0x88, 0x88, 0x85, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xfa, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x19, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0x92, 0x0,

    /* U+F243 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xeb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbd, 0xff, 0x50,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf9, 0xff, 0xc0,
    0xef, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xfa, 0xff, 0xc0, 0xef, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x7f, 0xfa, 0xff, 0xc0, 0xef, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa,
    0xff, 0xc0, 0xef, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0xff, 0xc0,
    0xef, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xdf, 0xfa, 0xff, 0xc0, 0x78, 0x88,
    0x84, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xfa, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x19, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0x92, 0x0,

    /* U+F244 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xeb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbd, 0xff, 0x50,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf9, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xfa, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x7f, 0xfa, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xdf, 0xfa, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xfa, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x19, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0x92, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18,
    0x92, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0xdf, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x76, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xf6, 0x0, 0x4c,
    0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x33,
    0x0, 0x0, 0xd, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xd2, 0x0,
    0x4f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0x40, 0x0, 0xaf, 0xff, 0xfc, 0x1, 0xde, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x10,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xdf, 0xff,
    0xff, 0x88, 0x88, 0x9f, 0xe8, 0x88, 0x88, 0x88,
    0x88, 0x8f, 0xff, 0x91, 0x5f, 0xff, 0xf8, 0x0,
    0x0, 0x7, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xc3, 0x0, 0x4, 0xbb, 0x60, 0x0, 0x0, 0x0,
    0xeb, 0x0, 0x0, 0x0, 0x0, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x30,
    0x8, 0x88, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xc1, 0x2f, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0x9f, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x9e, 0xff, 0xff, 0xc7, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xdf, 0xff, 0xfd,
    0x20, 0x0, 0x8, 0xff, 0xff, 0xf2, 0xdf, 0xff,
    0xfe, 0x0, 0x3, 0xff, 0xff, 0xff, 0x11, 0xdf,
    0xff, 0xf9, 0x0, 0xaf, 0xff, 0xff, 0xf1, 0x1,
    0xef, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xff, 0x12,
    0x12, 0xef, 0xff, 0x53, 0xff, 0xf4, 0x7f, 0xf1,
    0x3d, 0x13, 0xff, 0xf8, 0x6f, 0xff, 0x40, 0x7f,
    0x13, 0xf5, 0xc, 0xff, 0xb8, 0xff, 0xff, 0x40,
    0x71, 0x35, 0xa, 0xff, 0xfd, 0x9f, 0xff, 0xff,
    0x40, 0x0, 0x8, 0xff, 0xff, 0xea, 0xff, 0xff,
    0xff, 0x30, 0x6, 0xff, 0xff, 0xfe, 0xaf, 0xff,
    0xff, 0xf4, 0x0, 0x6f, 0xff, 0xff, 0xe9, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x7f, 0xff, 0xfe, 0x8f,
    0xff, 0xf4, 0x6, 0x13, 0x50, 0x8f, 0xff, 0xd6,
    0xff, 0xf4, 0x7, 0xf1, 0x3f, 0x40, 0x9f, 0xfb,
    0x3f, 0xff, 0x47, 0xff, 0x13, 0xd1, 0x1d, 0xff,
    0x90, 0xff, 0xff, 0xff, 0xf1, 0x21, 0x1d, 0xff,
    0xf5, 0xa, 0xff, 0xff, 0xff, 0x20, 0x2e, 0xff,
    0xff, 0x0, 0x2f, 0xff, 0xff, 0xf2, 0x2e, 0xff,
    0xff, 0x80, 0x0, 0x6f, 0xff, 0xff, 0x4e, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0, 0x0, 0x0, 0x5, 0x9c, 0xdd,
    0xc9, 0x40, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x0, 0x2, 0x22, 0x22, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0xac, 0xcc, 0xcd, 0xff,
    0xff, 0xff, 0xfd, 0xcc, 0xcc, 0xc1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xca, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0xa, 0xff, 0xe1, 0xef, 0xf2, 0xcf,
    0xf3, 0xbf, 0xfe, 0x0, 0xa, 0xff, 0xd0, 0xdf,
    0xf0, 0xbf, 0xf1, 0x9f, 0xfe, 0x0, 0xa, 0xff,
    0xd0, 0xdf, 0xf0, 0xbf, 0xf1, 0x9f, 0xfe, 0x0,
    0xa, 0xff, 0xd0, 0xdf, 0xf0, 0xbf, 0xf1, 0x9f,
    0xfe, 0x0, 0xa, 0xff, 0xd0, 0xdf, 0xf0, 0xbf,
    0xf1, 0x9f, 0xfe, 0x0, 0xa, 0xff, 0xd0, 0xdf,
    0xf0, 0xbf, 0xf1, 0x9f, 0xfe, 0x0, 0xa, 0xff,
    0xd0, 0xdf, 0xf0, 0xbf, 0xf1, 0x9f, 0xfe, 0x0,
    0xa, 0xff, 0xd0, 0xdf, 0xf0, 0xbf, 0xf1, 0x9f,
    0xfe, 0x0, 0xa, 0xff, 0xd0, 0xdf, 0xf0, 0xbf,
    0xf1, 0x9f, 0xfe, 0x0, 0xa, 0xff, 0xd0, 0xdf,
    0xf0, 0xbf, 0xf1, 0x9f, 0xfe, 0x0, 0xa, 0xff,
    0xe1, 0xef, 0xf1, 0xcf, 0xf3, 0xaf, 0xfe, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x7b, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcb, 0x91, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xc, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf4, 0xc, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf4, 0xc, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xf4, 0xc, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xf4, 0xc, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xb9, 0x75,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x2, 0x68, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x75, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0x80, 0xcf,
    0xff, 0xc0, 0x8f, 0xff, 0xff, 0xf8, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0xc, 0xfc, 0x0,
    0xc, 0xff, 0xff, 0xf8, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x90, 0x0, 0x5f, 0xff,
    0xff, 0xf8, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xf8, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xf8, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x10, 0x0, 0xcf, 0xff,
    0xff, 0xf8, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x5, 0xf5, 0x0, 0xd, 0xff, 0xff, 0xf8,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x5f,
    0xff, 0x50, 0x2e, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xe8, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xf8, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x9, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x50,

    /* U+F7C2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xfe,
    0x70, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x2, 0xef, 0xbb, 0xfc, 0xbf, 0xdb,
    0xbf, 0xf9, 0x2, 0xef, 0xd0, 0x2f, 0x40, 0xe7,
    0x1, 0xff, 0xa2, 0xef, 0xfd, 0x2, 0xf4, 0xe,
    0x70, 0x1f, 0xfa, 0xef, 0xff, 0xd0, 0x2f, 0x40,
    0xe7, 0x1, 0xff, 0xaf, 0xff, 0xfd, 0x24, 0xf6,
    0x2e, 0x82, 0x3f, 0xfa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x99, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x9, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd5, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0x10, 0x0, 0x3, 0xed, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf1, 0x0, 0x4,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x10, 0x5, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf1, 0x6, 0xff, 0xff,
    0xf6, 0x66, 0x66, 0x66, 0x66, 0x66, 0x9f, 0xff,
    0x17, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x13,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x2, 0xef, 0xff, 0xf1, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x10, 0x0, 0x2,
    0xdf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xb8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0
};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 95, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 94, .box_w = 4, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 32, .adv_w = 138, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 57, .adv_w = 247, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 177, .adv_w = 219, .box_w = 13, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 320, .adv_w = 297, .box_w = 18, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 464, .adv_w = 241, .box_w = 15, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 592, .adv_w = 74, .box_w = 3, .box_h = 7, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 603, .adv_w = 119, .box_w = 5, .box_h = 21, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 656, .adv_w = 119, .box_w = 6, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 719, .adv_w = 141, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 760, .adv_w = 205, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 815, .adv_w = 80, .box_w = 3, .box_h = 7, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 826, .adv_w = 135, .box_w = 7, .box_h = 2, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 833, .adv_w = 80, .box_w = 3, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 839, .adv_w = 124, .box_w = 10, .box_h = 21, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 944, .adv_w = 235, .box_w = 13, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1048, .adv_w = 130, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1096, .adv_w = 202, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1192, .adv_w = 201, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1288, .adv_w = 235, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1408, .adv_w = 202, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1504, .adv_w = 217, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1600, .adv_w = 210, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1704, .adv_w = 227, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1816, .adv_w = 217, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1920, .adv_w = 80, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1938, .adv_w = 80, .box_w = 3, .box_h = 16, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 1962, .adv_w = 205, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 2023, .adv_w = 205, .box_w = 11, .box_h = 7, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 2062, .adv_w = 205, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 2123, .adv_w = 202, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2219, .adv_w = 364, .box_w = 21, .box_h = 20, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 2429, .adv_w = 258, .box_w = 18, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2573, .adv_w = 266, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2685, .adv_w = 254, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2805, .adv_w = 291, .box_w = 16, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2933, .adv_w = 236, .box_w = 12, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3029, .adv_w = 224, .box_w = 12, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3125, .adv_w = 272, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3245, .adv_w = 286, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3357, .adv_w = 109, .box_w = 3, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3381, .adv_w = 181, .box_w = 11, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3469, .adv_w = 253, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3581, .adv_w = 209, .box_w = 11, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3669, .adv_w = 336, .box_w = 17, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3805, .adv_w = 286, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3917, .adv_w = 296, .box_w = 17, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4053, .adv_w = 254, .box_w = 13, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4157, .adv_w = 296, .box_w = 18, .box_h = 19, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4328, .adv_w = 256, .box_w = 13, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4432, .adv_w = 219, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4536, .adv_w = 207, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4640, .adv_w = 278, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4752, .adv_w = 251, .box_w = 17, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4888, .adv_w = 396, .box_w = 25, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5088, .adv_w = 237, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5208, .adv_w = 228, .box_w = 16, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5336, .adv_w = 231, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5448, .adv_w = 117, .box_w = 5, .box_h = 21, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 5501, .adv_w = 124, .box_w = 10, .box_h = 21, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5606, .adv_w = 117, .box_w = 6, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5669, .adv_w = 205, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 5724, .adv_w = 176, .box_w = 11, .box_h = 3, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5741, .adv_w = 211, .box_w = 7, .box_h = 3, .ofs_x = 2, .ofs_y = 14},
    {.bitmap_index = 5752, .adv_w = 210, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5818, .adv_w = 240, .box_w = 13, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5929, .adv_w = 201, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6001, .adv_w = 240, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6112, .adv_w = 215, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6190, .adv_w = 124, .box_w = 9, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6267, .adv_w = 243, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6379, .adv_w = 240, .box_w = 12, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6481, .adv_w = 98, .box_w = 4, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6515, .adv_w = 100, .box_w = 8, .box_h = 21, .ofs_x = -3, .ofs_y = -4},
    {.bitmap_index = 6599, .adv_w = 217, .box_w = 12, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6701, .adv_w = 98, .box_w = 3, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6727, .adv_w = 372, .box_w = 20, .box_h = 12, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6847, .adv_w = 240, .box_w = 12, .box_h = 12, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6919, .adv_w = 224, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7003, .adv_w = 240, .box_w = 13, .box_h = 16, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 7107, .adv_w = 240, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 7211, .adv_w = 144, .box_w = 7, .box_h = 12, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7253, .adv_w = 176, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7319, .adv_w = 146, .box_w = 9, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7387, .adv_w = 238, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7459, .adv_w = 197, .box_w = 14, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 7543, .adv_w = 316, .box_w = 20, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7663, .adv_w = 194, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7735, .adv_w = 197, .box_w = 14, .box_h = 16, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 7847, .adv_w = 183, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7913, .adv_w = 124, .box_w = 7, .box_h = 21, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 7987, .adv_w = 105, .box_w = 3, .box_h = 21, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 8019, .adv_w = 124, .box_w = 7, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 8093, .adv_w = 205, .box_w = 11, .box_h = 4, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 8115, .adv_w = 147, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 8151, .adv_w = 111, .box_w = 5, .box_h = 5, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 8164, .adv_w = 352, .box_w = 23, .box_h = 23, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 8429, .adv_w = 352, .box_w = 22, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8616, .adv_w = 352, .box_w = 22, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8836, .adv_w = 352, .box_w = 22, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9023, .adv_w = 242, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9151, .adv_w = 352, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9404, .adv_w = 352, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9657, .adv_w = 396, .box_w = 25, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9907, .adv_w = 352, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10160, .adv_w = 396, .box_w = 25, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10373, .adv_w = 352, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10626, .adv_w = 176, .box_w = 11, .box_h = 18, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10725, .adv_w = 264, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10878, .adv_w = 396, .box_w = 25, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11153, .adv_w = 352, .box_w = 22, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11340, .adv_w = 308, .box_w = 15, .box_h = 21, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 11498, .adv_w = 308, .box_w = 20, .box_h = 24, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 11738, .adv_w = 308, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11938, .adv_w = 308, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12138, .adv_w = 308, .box_w = 15, .box_h = 21, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 12296, .adv_w = 308, .box_w = 21, .box_h = 20, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 12506, .adv_w = 220, .box_w = 12, .box_h = 20, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 12626, .adv_w = 220, .box_w = 12, .box_h = 20, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 12746, .adv_w = 308, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12946, .adv_w = 308, .box_w = 20, .box_h = 5, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 12996, .adv_w = 396, .box_w = 25, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13209, .adv_w = 440, .box_w = 28, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13531, .adv_w = 396, .box_w = 27, .box_h = 23, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 13842, .adv_w = 352, .box_w = 22, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14073, .adv_w = 308, .box_w = 19, .box_h = 12, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 14187, .adv_w = 308, .box_w = 19, .box_h = 12, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 14301, .adv_w = 440, .box_w = 28, .box_h = 18, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 14553, .adv_w = 352, .box_w = 22, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 14740, .adv_w = 352, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14993, .adv_w = 352, .box_w = 23, .box_h = 23, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 15258, .adv_w = 308, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15458, .adv_w = 308, .box_w = 20, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 15688, .adv_w = 308, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15888, .adv_w = 220, .box_w = 15, .box_h = 23, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 16061, .adv_w = 308, .box_w = 20, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16291, .adv_w = 308, .box_w = 20, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16521, .adv_w = 396, .box_w = 25, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16734, .adv_w = 352, .box_w = 24, .box_h = 23, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 17010, .adv_w = 264, .box_w = 17, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 17206, .adv_w = 440, .box_w = 28, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 17500, .adv_w = 440, .box_w = 28, .box_h = 15, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 17710, .adv_w = 440, .box_w = 28, .box_h = 15, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 17920, .adv_w = 440, .box_w = 28, .box_h = 15, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 18130, .adv_w = 440, .box_w = 28, .box_h = 15, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 18340, .adv_w = 440, .box_w = 28, .box_h = 15, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 18550, .adv_w = 440, .box_w = 28, .box_h = 18, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 18802, .adv_w = 308, .box_w = 17, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 18998, .adv_w = 308, .box_w = 20, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 19228, .adv_w = 352, .box_w = 23, .box_h = 23, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 19493, .adv_w = 440, .box_w = 28, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19731, .adv_w = 264, .box_w = 17, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 19927, .adv_w = 354, .box_w = 23, .box_h = 14, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2, 0xefa3,
    0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4, 0xefc7,
    0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015, 0xf017,
    0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074, 0xf0ab, 0xf13b, 0xf190,
    0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7, 0xf1e3, 0xf23d, 0xf254,
    0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 59, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/

/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 4, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 16, 0, 10, -8, 0, 0,
    0, 0, -19, -21, 2, 17, 8, 6,
    -14, 2, 17, 1, 15, 4, 11, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 21, 3, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 7, 0, -11, 0, 0, 0, 0,
    0, -7, 6, 7, 0, 0, -4, 0,
    -2, 4, 0, -4, 0, -4, -2, -7,
    0, 0, 0, 0, -4, 0, 0, -5,
    -5, 0, 0, -4, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    -4, 0, -5, 0, -10, 0, -43, 0,
    0, -7, 0, 7, 11, 0, 0, -7,
    4, 4, 12, 7, -6, 7, 0, 0,
    -20, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -13, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -10, -4, -17, 0, -14,
    -2, 0, 0, 0, 0, 1, 14, 0,
    -11, -3, -1, 1, 0, -6, 0, 0,
    -2, -26, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -28, -3, 13,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -14, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 12,
    0, 4, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 13, 3,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -13, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 2,
    7, 4, 11, -4, 0, 0, 7, -4,
    -12, -48, 2, 10, 7, 1, -5, 0,
    13, 0, 11, 0, 11, 0, -33, 0,
    -4, 11, 0, 12, -4, 7, 4, 0,
    0, 1, -4, 0, 0, -6, 28, 0,
    28, 0, 11, 0, 15, 5, 6, 11,
    0, 0, 0, -13, 0, 0, 0, 0,
    1, -2, 0, 2, -6, -5, -7, 2,
    0, -4, 0, 0, 0, -14, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -23, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, -19, 0, -22, 0, 0, 0,
    0, -2, 0, 35, -4, -5, 4, 4,
    -3, 0, -5, 4, 0, 0, -19, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -34, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -22, 0, 21, 0, 0, -13, 0,
    12, 0, -24, -34, -24, -7, 11, 0,
    0, -24, 0, 4, -8, 0, -5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 9, 11, -43, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 17, 0, 2, 0, 0, 0,
    0, 0, 2, 2, -4, -7, 0, -1,
    -1, -4, 0, 0, -2, 0, 0, 0,
    -7, 0, -3, 0, -8, -7, 0, -9,
    -12, -12, -7, 0, -7, 0, -7, 0,
    0, 0, 0, -3, 0, 0, 4, 0,
    2, -4, 0, 1, 0, 0, 0, 4,
    -2, 0, 0, 0, -2, 4, 4, -1,
    0, 0, 0, -7, 0, -1, 0, 0,
    0, 0, 0, 1, 0, 5, -2, 0,
    -4, 0, -6, 0, 0, -2, 0, 11,
    0, 0, -4, 0, 0, 0, 0, 0,
    -1, 1, -2, -2, 0, 0, -4, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -2, 0, -4, -4, 0,
    0, 0, 0, 0, 1, 0, 0, -2,
    0, -4, -4, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, -2, -5, 0, -5, 0, -11,
    -2, -11, 7, 0, 0, -7, 4, 7,
    10, 0, -9, -1, -4, 0, -1, -17,
    4, -2, 2, -19, 4, 0, 0, 1,
    -18, 0, -19, -3, -31, -2, 0, -18,
    0, 7, 10, 0, 5, 0, 0, 0,
    0, 1, 0, -6, -5, 0, -11, 0,
    0, 0, -4, 0, 0, 0, -4, 0,
    0, 0, 0, 0, -2, -2, 0, -2,
    -5, 0, 0, 0, 0, 0, 0, 0,
    -4, -4, 0, -2, -4, -3, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, -3, 0, -4,
    0, -2, 0, -7, 4, 0, 0, -4,
    2, 4, 4, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 2,
    0, 0, -4, 0, -4, -2, -4, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    -3, 0, 0, 0, 0, -4, -5, 0,
    -7, 0, 11, -2, 1, -11, 0, 0,
    10, -18, -18, -15, -7, 4, 0, -3,
    -23, -6, 0, -6, 0, -7, 5, -6,
    -23, 0, -10, 0, 0, 2, -1, 3,
    -2, 0, 4, 0, -11, -13, 0, -18,
    -8, -7, -8, -11, -4, -10, -1, -7,
    -10, 2, 0, 1, 0, -4, 0, 0,
    0, 2, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, -2, 0, -1, -4, 0, -6, -8,
    -8, -1, 0, -11, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 1,
    -2, 0, 0, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 17, 0, 0,
    0, 0, 0, 0, 2, 0, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    -7, 0, 0, 0, 0, -18, -11, 0,
    0, 0, -5, -18, 0, 0, -4, 4,
    0, -10, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, -6, 0,
    0, 0, 0, 4, 0, 2, -7, -7,
    0, -4, -4, -4, 0, 0, 0, 0,
    0, 0, -11, 0, -4, 0, -5, -4,
    0, -8, -9, -11, -3, 0, -7, 0,
    -11, 0, 0, 0, 0, 28, 0, 0,
    2, 0, 0, -5, 0, 4, 0, -15,
    0, 0, 0, 0, 0, -33, -6, 12,
    11, -3, -15, 0, 4, -5, 0, -18,
    -2, -5, 4, -25, -4, 5, 0, 5,
    -12, -5, -13, -12, -15, 0, 0, -21,
    0, 20, 0, 0, -2, 0, 0, 0,
    -2, -2, -4, -10, -12, -1, -33, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -2, -4, -5, 0, 0,
    -7, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -7, 0, 0, 7,
    -1, 5, 0, -8, 4, -2, -1, -9,
    -4, 0, -5, -4, -2, 0, -5, -6,
    0, 0, -3, -1, -2, -6, -4, 0,
    0, -4, 0, 4, -2, 0, -8, 0,
    0, 0, -7, 0, -6, 0, -6, -6,
    4, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 4, 0, -5, 0, -2, -4,
    -11, -2, -2, -2, -1, -2, -4, -1,
    0, 0, 0, 0, 0, -4, -3, -3,
    0, 0, 0, 0, 4, -2, 0, -2,
    0, 0, 0, -2, -4, -2, -3, -4,
    -3, 0, 3, 14, -1, 0, -10, 0,
    -2, 7, 0, -4, -15, -5, 5, 0,
    0, -17, -6, 4, -6, 2, 0, -2,
    -3, -11, 0, -5, 2, 0, 0, -6,
    0, 0, 0, 4, 4, -7, -7, 0,
    -6, -4, -5, -4, -4, 0, -6, 2,
    -7, -6, 11, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, -5,
    0, 0, -4, -4, 0, 0, 0, 0,
    -4, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -5, 0, -7, 0, 0, 0, -12, 0,
    2, -8, 7, 1, -2, -17, 0, 0,
    -8, -4, 0, -14, -9, -10, 0, 0,
    -15, -4, -14, -13, -17, 0, -9, 0,
    3, 24, -5, 0, -8, -4, -1, -4,
    -6, -10, -6, -13, -14, -8, -4, 0,
    0, -2, 0, 1, 0, 0, -25, -3,
    11, 8, -8, -13, 0, 1, -11, 0,
    -18, -2, -4, 7, -32, -5, 1, 0,
    0, -23, -4, -18, -4, -26, 0, 0,
    -25, 0, 21, 1, 0, -2, 0, 0,
    0, 0, -2, -2, -13, -2, 0, -23,
    0, 0, 0, 0, -11, 0, -3, 0,
    -1, -10, -17, 0, 0, -2, -5, -11,
    -4, 0, -2, 0, 0, 0, 0, -16,
    -4, -12, -11, -3, -6, -9, -4, -6,
    0, -7, -3, -12, -5, 0, -4, -7,
    -4, -7, 0, 2, 0, -2, -12, 0,
    7, 0, -6, 0, 0, 0, 0, 4,
    0, 2, -7, 14, 0, -4, -4, -4,
    0, 0, 0, 0, 0, 0, -11, 0,
    -4, 0, -5, -4, 0, -8, -9, -11,
    -3, 0, -7, 3, 14, 0, 0, 0,
    0, 28, 0, 0, 2, 0, 0, -5,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    -2, -7, 0, 0, 0, 0, 0, -2,
    0, 0, 0, -4, -4, 0, 0, -7,
    -4, 0, 0, -7, 0, 6, -2, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 5, 7, 3, -3, 0, -11,
    -6, 0, 11, -12, -11, -7, -7, 14,
    6, 4, -31, -2, 7, -4, 0, -4,
    4, -4, -12, 0, -4, 4, -5, -3,
    -11, -3, 0, 0, 11, 7, 0, -10,
    0, -19, -5, 10, -5, -13, 1, -5,
    -12, -12, -4, 14, 4, 0, -5, 0,
    -10, 0, 3, 12, -8, -13, -14, -9,
    11, 0, 1, -26, -3, 4, -6, -2,
    -8, 0, -8, -13, -5, -5, -3, 0,
    0, -8, -7, -4, 0, 11, 8, -4,
    -19, 0, -19, -5, 0, -12, -20, -1,
    -11, -6, -12, -10, 10, 0, 0, -5,
    0, -7, -3, 0, -4, -6, 0, 6,
    -12, 4, 0, 0, -19, 0, -4, -8,
    -6, -2, -11, -9, -12, -8, 0, -11,
    -4, -8, -7, -11, -4, 0, 0, 1,
    17, -6, 0, -11, -4, 0, -4, -7,
    -8, -10, -10, -13, -5, -7, 7, 0,
    -5, 0, -18, -4, 2, 7, -11, -13,
    -7, -12, 12, -4, 2, -33, -6, 7,
    -8, -6, -13, 0, -11, -15, -4, -4,
    -3, -4, -7, -11, -1, 0, 0, 11,
    10, -2, -23, 0, -21, -8, 8, -13,
    -24, -7, -12, -15, -18, -12, 7, 0,
    0, 0, 0, -4, 0, 0, 4, -4,
    7, 2, -7, 7, 0, 0, -11, -1,
    0, -1, 0, 1, 1, -3, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    0, 3, 11, 1, 0, -4, 0, 0,
    0, 0, -2, -2, -4, 0, 0, 0,
    1, 3, 0, 0, 0, 0, 3, 0,
    -3, 0, 13, 0, 6, 1, 1, -5,
    0, 7, 0, 0, 0, 3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 11, 0, 10, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -21, 0, -4, 6, 0, 11,
    0, 0, 35, 4, -7, -7, 4, 4,
    -2, 1, -18, 0, 0, 17, -21, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -24, 13, 49, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -21, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, -7,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, -10, 0,
    0, 1, 0, 0, 4, 45, -7, -3,
    11, 10, -10, 4, 0, 0, 4, 4,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -46, 10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -10,
    0, 0, 0, -10, 0, 0, 0, 0,
    -8, -2, 0, 0, 0, -8, 0, -4,
    0, -17, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -24, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, -4, 0, 0, -7, 0, -5, 0,
    -10, 0, 0, 0, -6, 4, -4, 0,
    0, -10, -4, -8, 0, 0, -10, 0,
    -4, 0, -17, 0, -4, 0, 0, -29,
    -7, -14, -4, -13, 0, 0, -24, 0,
    -10, -2, 0, 0, 0, 0, 0, 0,
    0, 0, -5, -6, -3, -6, 0, 0,
    0, 0, -8, 0, -8, 5, -4, 7,
    0, -2, -8, -2, -6, -7, 0, -4,
    -2, -2, 2, -10, -1, 0, 0, 0,
    -31, -3, -5, 0, -8, 0, -2, -17,
    -3, 0, 0, -2, -3, 0, 0, 0,
    0, 2, 0, -2, -6, -2, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 5, 0, 0, 0, 0, 0,
    0, -8, 0, -2, 0, 0, 0, -7,
    4, 0, 0, 0, -10, -4, -7, 0,
    0, -10, 0, -4, 0, -17, 0, 0,
    0, 0, -34, 0, -7, -13, -18, 0,
    0, -24, 0, -2, -5, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -5, -2,
    -5, 1, 0, 0, 6, -5, 0, 11,
    17, -4, -4, -11, 4, 17, 6, 8,
    -10, 4, 15, 4, 10, 8, 10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 22, 17, -6, -4, 0, -3,
    28, 15, 28, 0, 0, 0, 4, 0,
    0, 13, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 5,
    0, 0, 0, 0, -30, -4, -3, -14,
    -17, 0, 0, -24, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    5, 0, 0, 0, 0, -30, -4, -3,
    -14, -17, 0, 0, -14, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, -8, 4, 0, -4,
    3, 6, 4, -11, 0, -1, -3, 4,
    0, 3, 0, 0, 0, 0, -9, 0,
    -3, -2, -7, 0, -3, -14, 0, 22,
    -4, 0, -8, -2, 0, -2, -6, 0,
    -4, -10, -7, -4, 0, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, 5, 0, 0, 0, 0, -30,
    -4, -3, -14, -17, 0, 0, -24, 0,
    0, 0, 0, 0, 0, 18, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, 0, -11, -4, -3, 11, -3, -4,
    -14, 1, -2, 1, -2, -10, 1, 8,
    1, 3, 1, 3, -8, -14, -4, 0,
    -13, -7, -10, -15, -14, 0, -6, -7,
    -4, -5, -3, -2, -4, -2, 0, -2,
    -1, 5, 0, 5, -2, 0, 11, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, -4, -4, 0, 0,
    -10, 0, -2, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -21, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, 0, -5,
    0, 0, 0, 0, -3, 0, 0, -6,
    -4, 4, 0, -6, -7, -2, 0, -10,
    -2, -8, -2, -4, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -24, 0, 11, 0, 0, -6, 0,
    0, 0, 0, -5, 0, -4, 0, 0,
    -2, 0, 0, -2, 0, -8, 0, 0,
    15, -5, -12, -11, 2, 4, 4, -1,
    -10, 2, 5, 2, 11, 2, 12, -2,
    -10, 0, 0, -14, 0, 0, -11, -10,
    0, 0, -7, 0, -5, -6, 0, -5,
    0, -5, 0, -2, 5, 0, -3, -11,
    -4, 13, 0, 0, -3, 0, -7, 0,
    0, 5, -8, 0, 4, -4, 3, 0,
    0, -12, 0, -2, -1, 0, -4, 4,
    -3, 0, 0, 0, -14, -4, -8, 0,
    -11, 0, 0, -17, 0, 13, -4, 0,
    -6, 0, 2, 0, -4, 0, -4, -11,
    0, -4, 4, 0, 0, 0, 0, -2,
    0, 0, 4, -5, 1, 0, 0, -4,
    -2, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -22, 0, 8, 0,
    0, -3, 0, 0, 0, 0, 1, 0,
    -4, -4, 0, 0, 0, 7, 0, 8,
    0, 0, 0, 0, 0, -22, -20, 1,
    15, 11, 6, -14, 2, 15, 0, 13,
    0, 7, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 19, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

/*Store all the custom data of the font*/
static lv_font_fmt_txt_dsc_t font_dsc = {
    .glyph_bitmap = gylph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
lv_font_t lv_font_montserrat_22 = {
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 24,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0)
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if LV_FONT_MONTSERRAT_22*/
