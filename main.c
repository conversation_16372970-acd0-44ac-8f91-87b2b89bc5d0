
/**
 * @file main
 *
 */

/*********************
 *      INCLUDES
 *********************/
#define _DEFAULT_SOURCE /* needed for usleep() */
#include <stdlib.h>
#include <unistd.h>
#define SDL_MAIN_HANDLED /*To fix SDL's "undefined reference to WinMain" \
                            issue*/

#ifdef USE_PC_SIMULATOR
#include <SDL2/SDL.h>
#endif

#include "lvgl/lvgl.h"
#include "lv_drivers/display/monitor.h"
#include "lv_drivers/indev/mouse.h"
#include "lv_examples/lv_examples.h"

/******start fb device*****************/
#include <time.h>
#include <sys/time.h>
#include "lv_drivers/display/fbdev.h"
#include "lv_drivers/indev/evdev.h"
/******end fb device******************/

/*******start win********************/
#include "NetPager/win/win.h"
/*******end win*********************/


unsigned int _GetTime0()
{
    struct timespec ts;
    unsigned int ms;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    ms = (ts.tv_sec * 1000) + (ts.tv_nsec / 1000000);
    if(ms == 0)
    {
        ms = 1;
    }
    return ms;
}

#if LV_TICK_CUSTOM
/*Set in lv_conf.h as `LV_TICK_CUSTOM_SYS_TIME_EXPR`*/
uint32_t custom_tick_get(void)
{
    static int cnt=0;
    static uint64_t start_ms = 0;
    if(start_ms == 0) {
        start_ms = _GetTime0();
    }
    uint64_t now_ms;
    now_ms = _GetTime0();

    uint32_t time_ms = now_ms - start_ms;
    //printf("cnt=%d,time_ms=%d\n",cnt,time_ms);
    cnt++;
    return time_ms;
}
#endif


/**
*  @brief print memory useage state
*  @param[in] void
*  @returnval  void
*/
void memory_print(void)
{
    lv_mem_monitor_t mon;
    lv_mem_monitor(&mon);
    printf("used: %6d (%3d %%), frag: %3d %%, biggest free: %6d\n", (int)mon.total_size - mon.free_size,
                mon.used_pct,
                mon.frag_pct,
                (int)mon.free_biggest_size);
}


 void win_test1(void)
 {
     lv_obj_t* scr = lv_obj_create(NULL, NULL);
     lv_obj_set_size(scr, LV_HOR_RES, LV_VER_RES);
     static lv_style_t style;   //样式
     lv_style_reset(&style);  //重置样式释放内存
     lv_style_init(&style);
     lv_style_set_bg_color(&style, LV_STATE_DEFAULT, LV_COLOR_RED);
     lv_style_set_text_color(&style, LV_STATE_DEFAULT, LV_COLOR_WHITE);
     lv_obj_add_style(scr, LV_OBJ_PART_MAIN, &style);

     lv_obj_t* label = lv_label_create(scr, NULL);
     lv_obj_set_auto_realign(label, true);  // 自动对齐
     lv_obj_align(label, scr, LV_ALIGN_CENTER, 0, 0);
     lv_obj_add_style(label, LV_LABEL_PART_MAIN, &style);

     lv_label_set_text(label, "This is a test 1");

     lv_scr_load_anim(scr, LV_SCR_LOAD_ANIM_FADE_ON, 400, 0, true); // 动画加载屏幕并删除之前的屏幕
     printf("win test 1 is created ");
     memory_print();  //内存打印
 }


void win_test2(void)
 {
     lv_obj_t* scr = lv_obj_create(NULL, NULL);
     lv_obj_set_size(scr, LV_HOR_RES, LV_VER_RES);
     static lv_style_t style;  //样式
     lv_style_reset(&style);  //重置样式释放内存
     lv_style_init(&style);
     lv_style_set_bg_color(&style, LV_STATE_DEFAULT, LV_COLOR_BLUE);
     lv_style_set_text_color(&style, LV_STATE_DEFAULT, LV_COLOR_WHITE);
     lv_obj_add_style(scr, LV_OBJ_PART_MAIN, &style);

     lv_obj_t* label = lv_label_create(scr, NULL);
     lv_obj_set_auto_realign(label, true);  // 自动对齐
     lv_obj_align(label, scr, LV_ALIGN_CENTER, 0, 0);
     lv_obj_add_style(label, LV_LABEL_PART_MAIN, &style);

     lv_label_set_text(label, "This is a test 2");
     
     lv_scr_load_anim(scr, LV_SCR_LOAD_ANIM_FADE_ON, 400, 0,true);  // 动画加载屏幕并删除之前的屏幕
     printf("win test 2 is created ");
     memory_print();  //内存打印
 }


/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *  STATIC PROTOTYPES
 **********************/
static void hal_init_sdl(void);
static void hal_init_fb(void);
static int tick_thread(void *data);
static void memory_monitor(lv_task_t *param);

/**********************
 *  STATIC VARIABLES
 **********************/
lv_indev_t *kb_indev;

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

int main(int argc, char **argv)
{
  (void)argc; /*Unused*/
  (void)argv; /*Unused*/

  InitWinStack();			//初始化窗体栈

  /*Initialize netpager system*/
  system_init();

  /*Initialize the HAL (display, input devices, tick) for LVGL*/
#if USE_PC_SIMULATOR
  /*Initialize LVGL*/
  lv_init();
  hal_init_sdl();
#elif USE_ASM9260
  lv_init();
  hal_init_fb();
#elif defined(USE_SSD212) || defined(USE_SSD202)
  if (0 != sstar_lv_init()) {
        printf("ERR: sstar_lv_init failed.\n");
        return -1;
    }
    //设置背光(放到此处是因为ssd初始化disp后会重新加载config.ini的PWM设置)
	  init_backlight_module();
#endif


#if 1
  //lv_demo_widgets();
  //lv_demo_printer();
  //lv_demo_benchmark();

  font_loading();			//初始化freetype字体
  login_win_start();
  //control_win_start();
 //lv_demo_printer();
#else
  lv_freetype_init(64); /*Cache max 64 glyphs*/

  /*Create a font*/
  static lv_font_t font1;
  lv_freetype_font_init(&font1, "./lv_freetype/MicrosoftYaHeiSemilight-01.ttf", 32);

  /*Create style with the new font*/
  static lv_style_t style;
  lv_style_init(&style);
  lv_style_set_text_font(&style, LV_STATE_DEFAULT, &font1);

  /*Create a label with the new style*/
  lv_obj_t * label = lv_label_create(lv_scr_act(), NULL);
  lv_obj_add_style(label, LV_LABEL_PART_MAIN, &style);
  lv_label_set_text(label, "你好Hello world,广州市花都区新华街交通东路");
  //lv_obj_add_style(tv, LV_TABVIEW_PART_TAB_BG,&style); /* 设置样式 */
#endif


  while (1) {
    /* Periodically call the lv_task handler.
     * It could be done in a timer interrupt or an OS task too.*/
	      pthread_mutex_lock(&lvglMutex);
        unsigned int curr = _GetTime0();
        lv_task_handler();
        pthread_mutex_unlock(&lvglMutex);
        unsigned int time_diff = _GetTime0() - curr;
        if (time_diff < 10) {
            usleep(( 10 - time_diff ) * 1000);
        }
    #if 0
    static uint16_t i;
        if (++i > 100) {
            i = 0;
            static bool flag;
            if (flag)
            {
                flag = false;
                win_test2();
            }
            else
            {
                flag = true;
                win_test1();
            }
      }
    #endif
  }

  return 0;
}

/**********************
 *   STATIC FUNCTIONS
 **********************/

/**
 * Initialize the Hardware Abstraction Layer (HAL) for the Littlev graphics
 * library
 */

#ifdef USE_PC_SIMULATOR
static void hal_init_sdl(void) {
  /* Use the 'monitor' driver which creates window on PC's monitor to simulate a display*/
  monitor_init();

  /*Create a display buffer*/
  static lv_disp_buf_t disp_buf1;
  static lv_color_t buf1_1[1280 * 120];
  lv_disp_buf_init(&disp_buf1, buf1_1, NULL, g_screen_width * 120);

  /*Create a display*/
  lv_disp_drv_t disp_drv;
  lv_disp_drv_init(&disp_drv); /*Basic initialization*/
  disp_drv.buffer = &disp_buf1;
  disp_drv.flush_cb = monitor_flush;
  lv_disp_drv_register(&disp_drv);

  /* Add the mouse as input device
   * Use the 'mouse' driver which reads the PC's mouse*/
  mouse_init();
  lv_indev_drv_t indev_drv;
  lv_indev_drv_init(&indev_drv); /*Basic initialization*/
  indev_drv.type = LV_INDEV_TYPE_POINTER;

  /*This function will be called periodically (by the library) to get the mouse position and state*/
  indev_drv.read_cb = mouse_read;
  lv_indev_t *mouse_indev = lv_indev_drv_register(&indev_drv);

#if 0
  /*Set a cursor for the mouse*/
  LV_IMG_DECLARE(mouse_cursor_icon); /*Declare the image file.*/
  lv_obj_t * cursor_obj = lv_img_create(lv_scr_act(), NULL); /*Create an image object for the cursor */
  lv_img_set_src(cursor_obj, &mouse_cursor_icon);           /*Set the image source*/
  lv_indev_set_cursor(mouse_indev, cursor_obj);             /*Connect the image  object to the driver*/
#endif
  /* Tick init.
   * You have to call 'lv_tick_inc()' in periodically to inform LittelvGL about
   * how much time were elapsed Create an SDL thread to do this*/
  SDL_CreateThread(tick_thread, "tick", NULL);

  /* Optional:
   * Create a memory monitor task which prints the memory usage in
   * periodically.*/
  lv_task_create(memory_monitor, 5000, LV_TASK_PRIO_MID, NULL);
}



/**
 * A task to measure the elapsed time for LVGL
 * @param data unused
 * @return never return
 */
static int tick_thread(void *data) {
  (void)data;

  while (1) {
    SDL_Delay(5);   /*Sleep for 5 millisecond*/
    lv_tick_inc(5); /*Tell LittelvGL that 5 milliseconds were elapsed*/
  }

  return 0;
}

#endif

#if USE_ASM9260
#define DISP_BUF_SIZE (1024*600)
/**
 * Initialize the Hardware Abstraction Layer (HAL) for the Littlev graphics
 * library
 */
static void hal_init_fb(void) {
  /*Linux frame buffer device init*/
      fbdev_init();

      /*A small buffer for LittlevGL to draw the screen's content*/
      static lv_color_t buf[DISP_BUF_SIZE];   //asm9260硬件不支持双缓存，如果采用两个缓冲区，速度会减半

      /*Initialize a descriptor for the buffer*/
      static lv_disp_buf_t disp_buf;
      lv_disp_buf_init(&disp_buf, buf, NULL, DISP_BUF_SIZE);

      /*Initialize and register a display driver*/
      lv_disp_drv_t disp_drv;
      lv_disp_drv_init(&disp_drv);
      disp_drv.buffer   = &disp_buf;
      disp_drv.flush_cb = fbdev_flush;
      lv_disp_drv_register(&disp_drv);

      lv_indev_drv_t indev_drv;
      lv_indev_drv_init(&indev_drv);/*Basic initialization*/
      evdev_init();
      indev_drv.type=LV_INDEV_TYPE_POINTER;/*See below.*/
      indev_drv.read_cb=evdev_read;/*See below.*/
      lv_indev_drv_register(&indev_drv);/*Register the driver in LittlevGL*/

}
#endif

/**
 * Print the memory usage periodically
 * @param param
 */
static void memory_monitor(lv_task_t *param) {
  (void)param; /*Unused*/

  lv_mem_monitor_t mon;
  lv_mem_monitor(&mon);
  printf("used: %6d (%3d %%), frag: %3d %%, biggest free: %6d\n",
         (int)mon.total_size - mon.free_size, mon.used_pct, mon.frag_pct,
         (int)mon.free_biggest_size);
}


