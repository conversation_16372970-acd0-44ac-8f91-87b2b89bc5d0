/*
 * sstar_disp.c
 */

#include <stdio.h>
#include <string.h>

#include "mi_panel_datatype.h"
#include "mi_panel.h"
#include "mi_disp_datatype.h"
#include "mi_disp.h"

#include "sstar_disp.h"

#if defined(USE_SSD202)
//#include "SAT070CP50_TTL_1024x600.h"
#include "JLL101QS31A_ILI9881C_1024x800_MIPI.h"
#endif


#define MAKE_YUYV_VALUE(y,u,v)  ((y) << 24) | ((u) << 16) | ((y) << 8) | (v)
#define YUYV_BLACK              MAKE_YUYV_VALUE(0,128,128)

int disp_init_ok=0;
int sstar_disp_init(unsigned int dev, const char *interface, unsigned int width, unsigned int height)
{
    #if USE_SSD202
    if(MIPI_POWER_GetDirection())
    {
        ssd20x_mipi_powerOn(1);
        usleep(10000);
    }
    #endif

    MI_DISP_PubAttr_t stPubAttr;
    MI_DISP_VideoLayerAttr_t stLayerAttr;
    MI_DISP_InputPortAttr_t stInputPortAttr;
    #ifdef CHIP_i2m
    MI_PANEL_LinkType_e eLinkType;
    #else
    MI_PANEL_IntfType_e eLinkType;
    #endif
    memset(&stPubAttr, 0, sizeof(MI_DISP_PubAttr_t));
    stPubAttr.u32BgColor = YUYV_BLACK;

    memset(&stLayerAttr, 0, sizeof(MI_DISP_VideoLayerAttr_t));
    stLayerAttr.stVidLayerDispWin.u16X = 0;
    stLayerAttr.stVidLayerDispWin.u16Y = 0;
    stLayerAttr.stVidLayerDispWin.u16Width = width;
    stLayerAttr.stVidLayerDispWin.u16Height = height;
    stLayerAttr.stVidLayerSize.u16Width = width;
    stLayerAttr.stVidLayerSize.u16Height = height;
    stLayerAttr.ePixFormat = E_MI_SYS_PIXEL_FRAME_YUV_SEMIPLANAR_420;

    memset(&stInputPortAttr, 0, sizeof(MI_DISP_InputPortAttr_t));
    stInputPortAttr.stDispWin.u16X = 0;
    stInputPortAttr.stDispWin.u16Y = 0;
    stInputPortAttr.stDispWin.u16Width = width;
    stInputPortAttr.stDispWin.u16Height = height;
    stInputPortAttr.u16SrcWidth = width;
    stInputPortAttr.u16SrcHeight = height;

    if (0 == strcmp(interface, "ttl")) {
        stPubAttr.eIntfSync = E_MI_DISP_OUTPUT_USER;
    #ifndef CHIP_i2m
        stPubAttr.eIntfType = E_MI_DISP_INTF_TTL;
        eLinkType = E_MI_PNL_INTF_TTL;
    #endif
    }
    else if(0 == strcmp(interface, "mipi"))
    {
        stPubAttr.eIntfSync = E_MI_DISP_OUTPUT_USER;
    #ifndef CHIP_i2m
        stPubAttr.eIntfType = E_MI_DISP_INTF_MIPIDSI;
        eLinkType = E_MI_PNL_INTF_MIPI_DSI;
    #endif
    }

#if defined(USE_SSD202)
    stPubAttr.stSyncInfo.u16Vact = stPanelParam.u16Height;
    stPubAttr.stSyncInfo.u16Vbb = stPanelParam.u16VSyncBackPorch;
    stPubAttr.stSyncInfo.u16Vfb = stPanelParam.u16VTotal - (stPanelParam.u16VSyncWidth +
                                                                  stPanelParam.u16Height + stPanelParam.u16VSyncBackPorch);
    stPubAttr.stSyncInfo.u16Hact = stPanelParam.u16Width;
    stPubAttr.stSyncInfo.u16Hbb = stPanelParam.u16HSyncBackPorch;
    stPubAttr.stSyncInfo.u16Hfb = stPanelParam.u16HTotal - (stPanelParam.u16HSyncWidth +
                                                                  stPanelParam.u16Width + stPanelParam.u16HSyncBackPorch);
    stPubAttr.stSyncInfo.u16Bvact = 0;
    stPubAttr.stSyncInfo.u16Bvbb = 0;
    stPubAttr.stSyncInfo.u16Bvfb = 0;
    stPubAttr.stSyncInfo.u16Hpw = stPanelParam.u16HSyncWidth;
    stPubAttr.stSyncInfo.u16Vpw = stPanelParam.u16VSyncWidth;
    stPubAttr.stSyncInfo.u32FrameRate = stPanelParam.u16DCLK * 1000000 / (stPanelParam.u16HTotal * stPanelParam.u16VTotal);
    stPubAttr.eIntfSync = E_MI_DISP_OUTPUT_USER;
    stPubAttr.eIntfType = E_MI_DISP_INTF_LCD;
    eLinkType = stPanelParam.eLinkType;
#endif

    if (MI_SUCCESS != MI_DISP_SetPubAttr(dev, &stPubAttr)) {
        printf("ERR %s -> [%d]\n", __FILE__, __LINE__);
        goto DISP_ENABLE_ERR;
    }
    if (MI_SUCCESS != MI_DISP_Enable(dev)) {
        printf("ERR %s -> [%d]\n", __FILE__, __LINE__);
        goto DISP_ENABLE_ERR;
    }
    if (MI_SUCCESS != MI_DISP_BindVideoLayer(0, dev)) {
        printf("ERR %s -> [%d]\n", __FILE__, __LINE__);
        //goto DISP_BIND_LAYER_ERR;
    }
    if (MI_SUCCESS != MI_DISP_SetVideoLayerAttr(0, &stLayerAttr)) {
        printf("ERR %s -> [%d]\n", __FILE__, __LINE__);
        goto DISP_SET_LAYER_ATTR_ERR;
    }
    if (MI_SUCCESS != MI_DISP_EnableVideoLayer(0)) {
        printf("ERR %s -> [%d]\n", __FILE__, __LINE__);
        goto DISP_ENABLE_LAYER_ERR;
    }
    if (MI_SUCCESS != MI_DISP_SetInputPortAttr(0, 0, &stInputPortAttr)) {
        printf("ERR %s -> [%d]\n", __FILE__, __LINE__);
        goto DISP_SET_PORT_ERR;
    }

    #if 0   //视频层数据不需要现在开启
    if (MI_SUCCESS != MI_DISP_EnableInputPort(0, 0)) {
        printf("ERR %s -> [%d]\n", __FILE__, __LINE__);
        goto DISP_ENABLE_PORT_ERR;
    }
    #endif

	if (MI_SUCCESS != MI_PANEL_Init(eLinkType)) {
        printf("ERR %s -> [%d], eLinkType: %d, interface: %s\n", __FILE__, __LINE__, eLinkType, interface);
        goto PANEL_INIT_ERR;
    }

#if defined(USE_SSD202)
    MI_PANEL_SetPanelParam(&stPanelParam);
    if(eLinkType == E_MI_PNL_LINK_MIPI_DSI)
    {
        printf("Set mipi dsi condfig...\n");
        MI_PANEL_SetMipiDsiConfig(&stMipiDsiConfig_JLL101QS31A_ILI9881C);
    }
#endif

disp_init_ok=1;

    return 0;
PANEL_INIT_ERR:
	MI_DISP_DisableInputPort(0, 0);
DISP_ENABLE_PORT_ERR:
DISP_SET_PORT_ERR:
    MI_DISP_DisableVideoLayer(0);
DISP_ENABLE_LAYER_ERR:
DISP_SET_LAYER_ATTR_ERR:
    MI_DISP_UnBindVideoLayer(0, dev);
DISP_BIND_LAYER_ERR:
    MI_DISP_Disable(dev);
DISP_ENABLE_ERR:

    return -1;
}

void sstar_disp_deinit(unsigned int dev, const char *interface)
{
	if (0 == strcmp(interface, "ttl") || 0 == strcmp(interface, "mipi")) {
        MI_PANEL_DeInit();
    }
    //MI_DISP_DisableInputPort(0, 0);
    MI_DISP_DisableVideoLayer(0);
    MI_DISP_UnBindVideoLayer(0, dev);
    MI_DISP_Disable(dev);
}


#if defined(USE_SSD202)
void ssd20x_getpanel_wh(int *width, int *height)
{
#ifndef SUPPORT_HDMI
    MI_DISP_PubAttr_t stPubAttr;
    MI_DISP_GetPubAttr(0,&stPubAttr);
    *width = stPubAttr.stSyncInfo.u16Hact;
    *height = stPubAttr.stSyncInfo.u16Vact;
#else
    MI_DISP_VideoLayerAttr_t stLayerAttr;
    MI_DISP_GetVideoLayerAttr(0, &stLayerAttr);
    *width = stLayerAttr.stVidLayerDispWin.u16Width;
    *height = stLayerAttr.stVidLayerDispWin.u16Height;
#endif
    printf("ssd20x_getpanel_wh = [%d %d]\n", *width, *height);
}




void ssd20x_mipi_powerDown()
{
    printf("ssd20x_mipi_powerDown......\n");
    //延迟退出
	if(!disp_init_ok)
	{
		printf("disp init is not OK,delay 500ms\n");
		usleep(500000);
	}
    MIPI_POWER_SWITCH(0); 
}

void ssd20x_mipi_powerOn()
{
    printf("ssd20x_mipi_powerOn......\n");
    MIPI_POWER_SWITCH(1);
}

void ssd20x_mipi_IsPowerPinOutPut()
{
    printf("ssd20x_mipi_powerOn......\n");
    MIPI_POWER_SWITCH(1);
}
#endif