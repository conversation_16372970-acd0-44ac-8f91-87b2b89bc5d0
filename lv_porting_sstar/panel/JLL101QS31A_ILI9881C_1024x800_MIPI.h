#ifndef JLL101QS31A_ILI9881C
#define JLL101QS31A_ILI9881C

#include "mi_panel_datatype.h"

#define FLAG_DELAY            0xFE
#define FLAG_END_OF_TABLE     0xFF   // END OF REGISTERS MARKER

#define HPW (20)
#define HBP (132)
#define HFP (80)
#define VPW (16)
#define VBP (16)
#define VFP (16)

#define FPS (60)
#define HDA (800)
#define VDA (1280)

MI_PANEL_ParamConfig_t stPanelParam =
{
    "JLL101QS31A", // const char *m_pPanelName;                ///<  PanelName
    0, //MS_U8 m_bPanelDither :1;                 ///<  PANEL_DITHER, keep the setting
    E_MI_PNL_LINK_MIPI_DSI, //MHAL_DISP_ApiPnlLinkType_e m_ePanelLinkType   :4;  ///<  PANEL_LINK

    ///////////////////////////////////////////////
    // Board related setting
    ///////////////////////////////////////////////
    1,  //MS_U8 m_bPanelDualPort  :1;              ///<  VOP_21[8], MOD_4A[1],    PANEL_DUAL_PORT, refer to m_bPanelDoubleClk
    0,  //MS_U8 m_bPanelSwapPort  :1;              ///<  MOD_4A[0],               PANEL_SWAP_PORT, refer to "LVDS output app note" A/B channel swap
    0,  //MS_U8 m_bPanelSwapOdd_ML    :1;          ///<  PANEL_SWAP_ODD_ML
    0,  //MS_U8 m_bPanelSwapEven_ML   :1;          ///<  PANEL_SWAP_EVEN_ML
    0,  //MS_U8 m_bPanelSwapOdd_RB    :1;          ///<  PANEL_SWAP_ODD_RB
    0,  //MS_U8 m_bPanelSwapEven_RB   :1;          ///<  PANEL_SWAP_EVEN_RB

    0,  //MS_U8 m_bPanelSwapLVDS_POL  :1;          ///<  MOD_40[5], PANEL_SWAP_LVDS_POL, for differential P/N swap
    0,  //MS_U8 m_bPanelSwapLVDS_CH   :1;          ///<  MOD_40[6], PANEL_SWAP_LVDS_CH, for pair swap
    0,  //MS_U8 m_bPanelPDP10BIT      :1;          ///<  MOD_40[3], PANEL_PDP_10BIT ,for pair swap
    1,  //MS_U8 m_bPanelLVDS_TI_MODE  :1;          ///<  MOD_40[2], PANEL_LVDS_TI_MODE, refer to "LVDS output app note"

    ///////////////////////////////////////////////
    // For TTL Only
    ///////////////////////////////////////////////
    0,  //MS_U8 m_ucPanelDCLKDelay;                ///<  PANEL_DCLK_DELAY
    0,  //MS_U8 m_bPanelInvDCLK   :1;              ///<  MOD_4A[4],                   PANEL_INV_DCLK
    0,  //MS_U8 m_bPanelInvDE     :1;              ///<  MOD_4A[2],                   PANEL_INV_DE
    0,  //MS_U8 m_bPanelInvHSync  :1;              ///<  MOD_4A[12],                  PANEL_INV_HSYNC
    0,  //MS_U8 m_bPanelInvVSync  :1;              ///<  MOD_4A[3],                   PANEL_INV_VSYNC

    ///////////////////////////////////////////////
    // Output driving current setting
    ///////////////////////////////////////////////
    // driving current setting (0x00=4mA, 0x01=6mA, 0x02=8mA, 0x03=12mA)
    1,  //MS_U8 m_ucPanelDCKLCurrent;              ///<  define PANEL_DCLK_CURRENT
    1,  //MS_U8 m_ucPanelDECurrent;                ///<  define PANEL_DE_CURRENT
    1,  //MS_U8 m_ucPanelODDDataCurrent;           ///<  define PANEL_ODD_DATA_CURRENT
    1,  //MS_U8 m_ucPanelEvenDataCurrent;          ///<  define PANEL_EVEN_DATA_CURRENT

    ///////////////////////////////////////////////
    // panel on/off timing
    ///////////////////////////////////////////////
    30,  //MS_U16 m_wPanelOnTiming1;                ///<  time between panel & data while turn on power
    400,  //MS_U16 m_wPanelOnTiming2;                ///<  time between data & back light while turn on power
    80,  //MS_U16 m_wPanelOffTiming1;               ///<  time between back light & data while turn off power
    30,  //MS_U16 m_wPanelOffTiming2;               ///<  time between data & panel while turn off power

    ///////////////////////////////////////////////
    // panel timing spec.
    ///////////////////////////////////////////////
    // sync related
    HPW,  //MS_U8 m_ucPanelHSyncWidth;               ///<  VOP_01[7:0], PANEL_HSYNC_WIDTH
    HBP,  //MS_U8 m_ucPanelHSyncBackPorch;           ///<  PANEL_HSYNC_BACK_PORCH, no register setting, provide value for query only

                                             ///<  not support Manuel VSync Start/End now
                                             ///<  VOP_02[10:0] VSync start = Vtt - VBackPorch - VSyncWidth
                                             ///<  VOP_03[10:0] VSync end = Vtt - VBackPorch
    VPW,  //MS_U8 m_ucPanelVSyncWidth;               ///<  define PANEL_VSYNC_WIDTH
    VBP,  //MS_U8 m_ucPanelVBackPorch;               ///<  define PANEL_VSYNC_BACK_PORCH

    // DE related
    (HPW+HBP),  //MS_U16 m_wPanelHStart;                   ///<  VOP_04[11:0], PANEL_HSTART, DE H Start (PANEL_HSYNC_WIDTH + PANEL_HSYNC_BACK_PORCH)
    (VPW+VBP),  //MS_U16 m_wPanelVStart;                   ///<  VOP_06[11:0], PANEL_VSTART, DE V Start
    HDA,  //MS_U16 m_wPanelWidth;                    ///< PANEL_WIDTH, DE width (VOP_05[11:0] = HEnd = HStart + Width - 1)
    VDA,  //MS_U16 m_wPanelHeight;                   ///< PANEL_HEIGHT, DE height (VOP_07[11:0], = Vend = VStart + Height - 1)

    // DClk related
    0,  //MS_U16 m_wPanelMaxHTotal;                ///<  PANEL_MAX_HTOTAL. Reserved for future using.
    (HDA+HPW+HBP+HFP),  //MS_U16 m_wPanelHTotal;                   ///<  VOP_0C[11:0], PANEL_HTOTAL
    0,  //MS_U16 m_wPanelMinHTotal;                ///<  PANEL_MIN_HTOTAL. Reserved for future using.

    0,  //MS_U16 m_wPanelMaxVTotal;                ///<  PANEL_MAX_VTOTAL. Reserved for future using.
    (VDA+VPW+VBP+VFP),  //MS_U16 m_wPanelVTotal;                   ///<  VOP_0D[11:0], PANEL_VTOTAL
    0,  //MS_U16 m_wPanelMinVTotal;                ///<  PANEL_MIN_VTOTAL. Reserved for future using.

    0,  //MS_U8 m_dwPanelMaxDCLK;                  ///<  PANEL_MAX_DCLK. Reserved for future using.
    ((unsigned long)(VDA+VPW+VBP+VFP)*(HDA+HPW+HBP+HFP)*FPS/1048576),  //MS_U8 m_dwPanelDCLK;                     ///<  LPLL_0F[23:0], PANEL_DCLK          ,{0x3100_10[7:0], 0x3100_0F[15:0]}
    0,  //MS_U8 m_dwPanelMinDCLK;                  ///<  PANEL_MIN_DCLK. Reserved for future using.
                                             ///<  spread spectrum
    0,  //MS_U16 m_wSpreadSpectrumStep;            ///<  move to board define, no use now.
    0,  //MS_U16 m_wSpreadSpectrumSpan;            ///<  move to board define, no use now.

    160,  //MS_U8 m_ucDimmingCtl;                    ///<  Initial Dimming Value
    255,  //MS_U8 m_ucMaxPWMVal;                     ///<  Max Dimming Value
    80,  //MS_U8 m_ucMinPWMVal;                     ///<  Min Dimming Value

    0,  //MS_U8 m_bPanelDeinterMode   :1;          ///<  define PANEL_DEINTER_MODE,  no use now
    E_MI_PNL_ASPECT_RATIO_WIDE,  //MHAL_DISP_PnlAspectRatio_e m_ucPanelAspectRatio; ///<  Panel Aspect Ratio, provide information to upper layer application for aspect ratio setting.
  /*
    *
    * Board related params
    *
    *  If a board ( like BD_MST064C_D01A_S ) swap LVDS TX polarity
    *    : This polarity swap value =
    *      (LVDS_PN_SWAP_H<<8) | LVDS_PN_SWAP_L from board define,
    *  Otherwise
    *    : The value shall set to 0.
    */
    0,  //MS_U16 m_u16LVDSTxSwapValue;
    E_MI_PNL_TI_8BIT_MODE,  //MHAL_DISP_ApiPnlTiBitMode_e m_ucTiBitMode;                         ///< MOD_4B[1:0], refer to "LVDS output app note"
    E_MI_PNL_OUTPUT_8BIT_MODE,  //MHAL_DISP_ApiPnlOutPutFormatBitMode_e m_ucOutputFormatBitMode;

    0,  //MS_U8 m_bPanelSwapOdd_RG    :1;          ///<  define PANEL_SWAP_ODD_RG
    0,  //MS_U8 m_bPanelSwapEven_RG   :1;          ///<  define PANEL_SWAP_EVEN_RG
    0,  //MS_U8 m_bPanelSwapOdd_GB    :1;          ///<  define PANEL_SWAP_ODD_GB
    0,  //MS_U8 m_bPanelSwapEven_GB   :1;          ///<  define PANEL_SWAP_EVEN_GB

    /**
    *  Others
    */
    1,  //MS_U8 m_bPanelDoubleClk     :1;             ///<  LPLL_03[7], define Double Clock ,LVDS dual mode
    0x001c848e,  //MS_U32 m_dwPanelMaxSET;                     ///<  define PANEL_MAX_SET
    0x0018eb59,  //MS_U32 m_dwPanelMinSET;                     ///<  define PANEL_MIN_SET
    E_MI_PNL_CHG_VTOTAL,  //MHAL_DISP_ApiPnlOutTimingMode_e m_ucOutTimingMode;   ///<Define which panel output timing change mode is used to change VFreq for same panel
    0,  //MS_U8 m_bPanelNoiseDith     :1;             ///<  PAFRC mixed with noise dither disable
    (MI_PANEL_ChannelSwapType_e)2,
    (MI_PANEL_ChannelSwapType_e)4,
    (MI_PANEL_ChannelSwapType_e)3,
    (MI_PANEL_ChannelSwapType_e)1,
    (MI_PANEL_ChannelSwapType_e)0
};

MI_U8 JLL101QS31A_INIT_CMD[] =
{
    0xFF, 3, 0x98, 0x81, 0x03, 

    0x01, 0x01, 0x00,
    0x02, 0x01, 0x00,
    0x03, 0x01, 0x53,
    0x04, 0x01, 0xD3,
    0x05, 0x01, 0x00,
    0x06, 0x01, 0x0D,
    0x07, 0x01, 0x08,
    0x08, 0x01, 0x00,
    0x09, 0x01, 0x00,
    0x0A, 0x01, 0x00,
    0x0B, 0x01, 0x00,
    0x0C, 0x01, 0x00,
    0x0D, 0x01, 0x00,
    0x0E, 0x01, 0x00,
    0x0F, 0x01, 0x28,
    0x10, 0x01, 0x28,
    0x11, 0x01, 0x00,
    0x12, 0x01, 0x00,
    0x13, 0x01, 0x00,
    0x14, 0x01, 0x00,
    0x15, 0x01, 0x00,
    0x16, 0x01, 0x00,
    0x17, 0x01, 0x00,
    0x18, 0x01, 0x00,
    0x19, 0x01, 0x00,
    0x1A, 0x01, 0x00,
    0x1B, 0x01, 0x00,
    0x1C, 0x01, 0x00,
    0x1D, 0x01, 0x00,
    0x1E, 0x01, 0x40,
    0x1F, 0x01, 0x80,
    0x20, 0x01, 0x06,
    0x21, 0x01, 0x01,
    0x22, 0x01, 0x00,
    0x23, 0x01, 0x00,
    0x24, 0x01, 0x00,
    0x25, 0x01, 0x00,
    0x26, 0x01, 0x00,
    0x27, 0x01, 0x00,
    0x28, 0x01, 0x33,
    0x29, 0x01, 0x33,
    0x2A, 0x01, 0x00,
    0x2B, 0x01, 0x00,
    0x2C, 0x01, 0x00,
    0x2D, 0x01, 0x00,
    0x2E, 0x01, 0x00,
    0x2F, 0x01, 0x00,
    0x30, 0x01, 0x00,
    0x31, 0x01, 0x00,
    0x32, 0x01, 0x00,
    0x33, 0x01, 0x00,
    0x34, 0x01, 0x03,
    0x35, 0x01, 0x00,
    0x36, 0x01, 0x00,
    0x37, 0x01, 0x00,
    0x38, 0x01, 0x96,
    0x39, 0x01, 0x00,
    0x3A, 0x01, 0x00,
    0x3B, 0x01, 0x00,
    0x3C, 0x01, 0x00,
    0x3D, 0x01, 0x00,
    0x3E, 0x01, 0x00,
    0x3F, 0x01, 0x00,
    0x40, 0x01, 0x00,
    0x41, 0x01, 0x00,
    0x42, 0x01, 0x00,
    0x43, 0x01, 0x00,
    0x44, 0x01, 0x00,
    
    0x50, 0x01, 0x00,
    0x51, 0x01, 0x23,
    0x52, 0x01, 0x45,
    0x53, 0x01, 0x67,
    0x54, 0x01, 0x89,
    0x55, 0x01, 0xAB,
    0x56, 0x01, 0x01,
    0x57, 0x01, 0x23,
    0x58, 0x01, 0x45,
    0x59, 0x01, 0x67,
    0x5A, 0x01, 0x89,
    0x5B, 0x01, 0xAB,
    0x5C, 0x01, 0xCD,
    0x5D, 0x01, 0xEF,

    0x5E, 0x01, 0x00,
    0x5F, 0x01, 0x08,
    0x60, 0x01, 0x08,
    0x61, 0x01, 0x06,
    0x62, 0x01, 0x06,
    0x63, 0x01, 0x01,
    0x64, 0x01, 0x01,
    0x65, 0x01, 0x00,
    0x66, 0x01, 0x00,
    0x67, 0x01, 0x02,
    0x68, 0x01, 0x15,
    0x69, 0x01, 0x15,
    0x6A, 0x01, 0x14,
    0x6B, 0x01, 0x14,
    0x6C, 0x01, 0x0D,
    0x6D, 0x01, 0x0D,
    0x6E, 0x01, 0x0C,
    0x6F, 0x01, 0x0C,
    0x70, 0x01, 0x0F,
    0x71, 0x01, 0x0F,
    0x72, 0x01, 0x0E,
    0x73, 0x01, 0x0E,
    0x74, 0x01, 0x02,

    0x75, 0x01, 0x08,
    0x76, 0x01, 0x08,
    0x77, 0x01, 0x06,
    0x78, 0x01, 0x06,
    0x79, 0x01, 0x01,
    0x7A, 0x01, 0x01,
    0x7B, 0x01, 0x00,
    0x7C, 0x01, 0x00,
    0x7D, 0x01, 0x02,
    0x7E, 0x01, 0x15,
    0x7F, 0x01, 0x15,
    0x80, 0x01, 0x14,
    0x81, 0x01, 0x14,
    0x82, 0x01, 0x0D,
    0x83, 0x01, 0x0D,
    0x84, 0x01, 0x0C,
    0x85, 0x01, 0x0C,
    0x86, 0x01, 0x0F,
    0x87, 0x01, 0x0F,
    0x88, 0x01, 0x0E,
    0x89, 0x01, 0x0E,
    0x8A, 0x01, 0x02,

    0xFF, 0x03, 0x98, 0x81, 0x04,
    0x6E, 0x01, 0x2B,
    0x6F, 0x01, 0x37,
    0x3A, 0x01, 0xA4,
    0x8D, 0x01, 0x1A,
    0x87, 0x01, 0xBA,
    0xB2, 0x01, 0xD1,
    0x88, 0x01, 0x0B,
    0x38, 0x01, 0x01,
    0x39, 0x01, 0x00,
    0xB5, 0x01, 0x07,
    0x31, 0x01, 0x75,
    0x3B, 0x01, 0x98,

    0xFF, 0x03, 0x98, 0x81, 0x01,
    0x22, 0x01, 0x0A,
    0x31, 0x01, 0x00,
    0x53, 0x01, 0x40,
    0x55, 0x01, 0x40,
    0x50, 0x01, 0x99,
    0x51, 0x01, 0x94,
    0x60, 0x01, 0x10,
    0x62, 0x01, 0x20,

    0xA0, 0x01, 0x00,
    0xA1, 0x01, 0x00,
    0xA2, 0x01, 0x15,
    0xA3, 0x01, 0x14,
    0xA4, 0x01, 0x1B,
    0xA5, 0x01, 0x2F,
    0xA6, 0x01, 0x25,
    0xA7, 0x01, 0x24,
    0xA8, 0x01, 0x80,
    0xA9, 0x01, 0x1F,
    0xAA, 0x01, 0x2C,
    0xAB, 0x01, 0x6C,
    0xAC, 0x01, 0x16,
    0xAD, 0x01, 0x14,
    0xAE, 0x01, 0x4D,
    0xAF, 0x01, 0x20,
    0xB0, 0x01, 0x29,
    0xB1, 0x01, 0x4F,
    0xB2, 0x01, 0x5F,
    0xB3, 0x01, 0x23,

    0xC0, 0x01, 0x00,
    0xC1, 0x01, 0x2E,
    0xC2, 0x01, 0x3B,
    0xC3, 0x01, 0x15,
    0xC4, 0x01, 0x16,
    0xC5, 0x01, 0x28,
    0xC6, 0x01, 0x1A,
    0xC7, 0x01, 0x1C,
    0xC8, 0x01, 0xA7,
    0xC9, 0x01, 0x1B,
    0xCA, 0x01, 0x28,
    0xCB, 0x01, 0x92,
    0xCC, 0x01, 0x1F,
    0xCD, 0x01, 0x1C,
    0xCE, 0x01, 0x4B,
    0xCF, 0x01, 0x1F,
    0xD0, 0x01, 0x28,
    0xD1, 0x01, 0x4E,
    0xD2, 0x01, 0x5C,
    0xD3, 0x01, 0x23,
    
    /*for test pattern by riu_w 101e 0d 0x1000 20220425*/
#if 0
    0xFF, 3, 0x98, 0x81, 0x04, 
    0x2F, 1, 0x31,
#endif
    0xFF, 3, 0x98, 0x81, 0x00, 
    0x11, 01, 0, 
    FLAG_DELAY, FLAG_DELAY, 120,
    0x29, 01, 0, 
    0x35, 01, 0, 
    FLAG_DELAY, FLAG_DELAY, 20,
    FLAG_END_OF_TABLE, FLAG_END_OF_TABLE,

};

MI_PANEL_MipiDsiConfig_t stMipiDsiConfig_JLL101QS31A_ILI9881C =
{
    //HsTrail HsPrpr HsZero ClkHsPrpr ClkHsExit ClkTrail ClkZero ClkHsPost DaHsExit ContDet
    5,      5,     7,     5,       7,             4,      16,      11,       7,       0,
    //Lpx   TaGet  TaSure  TaGo
    16,   26,    24,     50,

    //Hac,  Hpw,  Hbp,  Hfp,  Vac,  Vpw, Vbp, Vfp,  Bllp, Fps
    HDA,  HPW,  HBP, HFP, VDA, VPW,  VBP, VFP,  0,    FPS,

    E_MI_PNL_MIPI_DSI_LANE_4,      // MIPnlMipiDsiLaneMode_e enLaneNum;
    E_MI_PNL_MIPI_DSI_RGB888,      // MIPnlMipiDsiFormat_e enFormat;
    E_MI_PNL_MIPI_DSI_SYNC_PULSE,  // MIPnlMipiDsiCtrlMode_e enCtrl;

    JLL101QS31A_INIT_CMD,
    sizeof(JLL101QS31A_INIT_CMD),
    1, 0x01AF, 0x01B9, 0x80D2, 8,
};

#endif //JLL101QS31A_ILI9881C