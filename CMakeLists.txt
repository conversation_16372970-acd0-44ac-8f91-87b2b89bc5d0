cmake_minimum_required(VERSION 3.10)

#[[message等级
(无) = 重要消息；
 STATUS = 非重要消息；
 WARNING = CMake 警告, 会继续执行；
 AUTHOR_WARNING = CMake 警告 (dev), 会继续执行；
 SEND_ERROR = CMake 错误, 继续执行，但是会跳过生成的步骤；
 FATAL_ERROR = CMake 错误, 终止所有处理过程；
]]
if ("${CMAKE_BUILD_TYPE}" STREQUAL "i386")
    set(DST_BIN demo)
    add_definitions(-DUSE_PC_SIMULATOR=1)

    add_definitions(-D_GNU_SOURCE)
    add_definitions(-D__USE_XOPEN)
    add_definitions(-D_XOPEN_SOURCE)

    set(CMAKE_C_FLAGS "-O3 -g2")

    include_directories("/home/<USER>/project/dlna/x86_64/cross/include/upnp")
    include_directories("/usr/include/freetype2")
    #link_directories必须要放到add_executable前面，因为这个命令只对后续命令生效。
    link_directories("/home/<USER>/project/dlna/x86_64/cross/lib")

elseif ("${CMAKE_BUILD_TYPE}" STREQUAL "arm")
    set(DST_BIN Pager)
    add_definitions(-DUSE_ASM9260=1)
    set(TOOLCHAIN_DIR /home/<USER>/project/toolchain/arm/4.3.2/bin)
    set(CMAKE_SYSTEM_NAME Linux)
    set(CMAKE_SYSTEM_PROCESSOR arm)
    set(CMAKE_C_COMPILER ${TOOLCHAIN_DIR}/arm-linux-gcc)
    set(CMAKE_CXX_COMPILER ${TOOLCHAIN_DIR}/arm-linux-g++)
    set(CMAKE_C_FLAGS "-O3 -g0")

    include_directories(
        "/home/<USER>/project/dlna/arm/cross/include/upnp"
        "/home/<USER>/eclipse-workspace/thirdparty/cross/include/freetype2"
    )
    #link_directories必须要放到add_executable前面，因为这个命令只对后续命令生效。
    link_directories(
        "/home/<USER>/eclipse-workspace/thirdparty/cross/lib"
        "/home/<USER>/project/dlna/arm/cross/lib"
    )

elseif ("${CMAKE_BUILD_TYPE}" STREQUAL "armv7")
    set(DST_BIN PagerSSD)
    add_definitions(-DUSE_SSD212=1)
    
    add_definitions(-D_GNU_SOURCE)
    add_definitions(-D__USE_XOPEN)
    add_definitions(-D_XOPEN_SOURCE)

    set(TOOLCHAIN_DIR /home/<USER>/project/toolchain/arm/gcc-sigmastar-9.1.0-2020.07-x86_64_arm-linux-gnueabihf/bin)
    set(CMAKE_SYSTEM_NAME Linux)
    set(CMAKE_SYSTEM_PROCESSOR arm)
    set(CMAKE_C_COMPILER ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-gcc)
    set(CMAKE_CXX_COMPILER ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-g++)
    set(CMAKE_AR ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-ar)
    set(CMAKE_AS ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-as)
    set(CMAKE_LD  ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-ld)
    set(CMAKE_NM ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-nm)
    set(CMAKE_STRIP  ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-strip)
    set(CMAKE_C_FLAGS "-O3 -g0 -mfpu=neon-vfpv4 -mfloat-abi=hard")

    set(ALKAID_PROJ /home/<USER>/project/exdisk/sigmastar/ssd212/V015/project)

    #include $(ALKAID_PROJ)/configs/current.configs
    set(PRODUCT disp)
    set(CHIP p3)
    set(TOOLCHAIN glibc)
    set(TOOLCHAIN_VERSION 9.1.0)
    add_definitions(-DCHIP_${CHIP})

    include_directories(
        ${ALKAID_PROJ}/release/include
    )
    #link_directories必须要放到add_executable前面，因为这个命令只对后续命令生效。
    link_directories(
        ${ALKAID_PROJ}/release/${PRODUCT}/${CHIP}/common/${TOOLCHAIN}/${TOOLCHAIN_VERSION}/mi_libs/dynamic
    )
    link_directories(
        ${ALKAID_PROJ}/release/${PRODUCT}/${CHIP}/common/${TOOLCHAIN}/${TOOLCHAIN_VERSION}/ex_libs/dynamic
    )

    include_directories(
        "/home/<USER>/project/dlna/armv7/cross/include/upnp"
        "/home/<USER>/eclipse-workspace/thirdparty/armv7_cross/include/freetype2"
        "/home/<USER>/project/ffmpeg/armv7/include"
        "/home/<USER>/project/curl/armv7/cross/include"
    )
    #link_directories必须要放到add_executable前面，因为这个命令只对后续命令生效。
    link_directories(
        "/home/<USER>/eclipse-workspace/thirdparty/armv7_cross/lib"
        "/home/<USER>/project/dlna/armv7/cross/lib"
        "/home/<USER>/project/ffmpeg/armv7/lib"
        "/home/<USER>/project/curl/armv7/cross/lib"
    )

elseif ("${CMAKE_BUILD_TYPE}" STREQUAL "armv7_debug")
    set(DST_BIN PagerSSD)
    add_definitions(-DUSE_SSD212=1)
    add_definitions(-DDEBUG_CORE_MODE=1)
    
    add_definitions(-D_GNU_SOURCE)
    add_definitions(-D__USE_XOPEN)
    add_definitions(-D_XOPEN_SOURCE)

    set(TOOLCHAIN_DIR /home/<USER>/project/toolchain/arm/gcc-sigmastar-9.1.0-2020.07-x86_64_arm-linux-gnueabihf/bin)
    set(CMAKE_SYSTEM_NAME Linux)
    set(CMAKE_SYSTEM_PROCESSOR arm)
    set(CMAKE_C_COMPILER ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-gcc)
    set(CMAKE_CXX_COMPILER ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-g++)
    set(CMAKE_AR ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-ar)
    set(CMAKE_AS ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-as)
    set(CMAKE_LD  ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-ld)
    set(CMAKE_NM ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-nm)
    set(CMAKE_STRIP  ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-strip)
    set(CMAKE_C_FLAGS "-O3 -g2 -mfpu=neon-vfpv4 -mfloat-abi=hard")

    set(ALKAID_PROJ /home/<USER>/project/exdisk/sigmastar/ssd212/V015/project)

    #include $(ALKAID_PROJ)/configs/current.configs
    set(PRODUCT disp)
    set(CHIP p3)
    set(TOOLCHAIN glibc)
    set(TOOLCHAIN_VERSION 9.1.0)
    add_definitions(-DCHIP_${CHIP})

    include_directories(
        ${ALKAID_PROJ}/release/include
    )
    #link_directories必须要放到add_executable前面，因为这个命令只对后续命令生效。
    link_directories(
        ${ALKAID_PROJ}/release/${PRODUCT}/${CHIP}/common/${TOOLCHAIN}/${TOOLCHAIN_VERSION}/mi_libs/dynamic
    )
    link_directories(
        ${ALKAID_PROJ}/release/${PRODUCT}/${CHIP}/common/${TOOLCHAIN}/${TOOLCHAIN_VERSION}/ex_libs/dynamic
    )

    include_directories(
        "/home/<USER>/project/dlna/armv7/cross/include/upnp"
        "/home/<USER>/eclipse-workspace/thirdparty/armv7_cross/include/freetype2"
        "/home/<USER>/project/ffmpeg/armv7/include"
        "/home/<USER>/project/curl/armv7/cross/include"
    )
    #link_directories必须要放到add_executable前面，因为这个命令只对后续命令生效。
    link_directories(
        "/home/<USER>/eclipse-workspace/thirdparty/armv7_cross/lib"
        "/home/<USER>/project/dlna/armv7/cross/lib"
        "/home/<USER>/project/ffmpeg/armv7/lib"
        "/home/<USER>/project/curl/armv7/cross/lib"
    )

elseif ("${CMAKE_BUILD_TYPE}" STREQUAL "ssd202")
    set(DST_BIN PagerSSD202)
    add_definitions(-DUSE_SSD202=1)
    
    add_definitions(-D_GNU_SOURCE)
    add_definitions(-D__USE_XOPEN)
    add_definitions(-D_XOPEN_SOURCE)

    set(TOOLCHAIN_DIR /home/<USER>/project/toolchain/arm/gcc-arm-8.2-2018.08-x86_64-arm-linux-gnueabihf/bin)
    set(CMAKE_SYSTEM_NAME Linux)
    set(CMAKE_SYSTEM_PROCESSOR arm)
    set(CMAKE_C_COMPILER ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-gcc)
    set(CMAKE_CXX_COMPILER ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-g++)
    set(CMAKE_AR ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-ar)
    set(CMAKE_AS ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-as)
    set(CMAKE_LD  ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-ld)
    set(CMAKE_NM ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-nm)
    set(CMAKE_STRIP  ${TOOLCHAIN_DIR}/arm-linux-gnueabihf-strip)
    set(CMAKE_C_FLAGS "-O3 -g0 -mfpu=neon-vfpv4 -mfloat-abi=hard")

    set(ALKAID_PROJ /home/<USER>/project/exdisk/sigmastar/ssd202/V050/project)

    #include $(ALKAID_PROJ)/configs/current.configs
    set(PRODUCT nvr)
    set(CHIP i2m)
    set(TOOLCHAIN glibc)
    set(TOOLCHAIN_VERSION 8.2.1)
    add_definitions(-DCHIP_${CHIP})

    include_directories(
        ${ALKAID_PROJ}/release/include
    )
    #link_directories必须要放到add_executable前面，因为这个命令只对后续命令生效。
    link_directories(
        ${ALKAID_PROJ}/release/${PRODUCT}/${CHIP}/common/${TOOLCHAIN}/${TOOLCHAIN_VERSION}/mi_libs/dynamic
    )
    link_directories(
        ${ALKAID_PROJ}/release/${PRODUCT}/${CHIP}/common/${TOOLCHAIN}/${TOOLCHAIN_VERSION}/ex_libs/dynamic
    )

    include_directories(
        "/home/<USER>/project/dlna/ssd202/cross/include/upnp"
        "/home/<USER>/eclipse-workspace/thirdparty/ssd202_cross/include/freetype2"
        "/home/<USER>/project/ffmpeg/ssd202/include"
        "/home/<USER>/project/curl/ssd202/cross/include"
        "/home/<USER>/project/rtsp/myRtsp/rtspliblive/include"
        "/home/<USER>/project/speex/cross_ssd202/include"
    )
    #link_directories必须要放到add_executable前面，因为这个命令只对后续命令生效。
    link_directories(
        "/home/<USER>/eclipse-workspace/thirdparty/ssd202_cross/lib"
        "/home/<USER>/project/dlna/ssd202/cross/lib"
        "/home/<USER>/project/ffmpeg/ssd202/lib"
        "/home/<USER>/project/curl/ssd202/cross/lib"
        "/home/<USER>/project/rtsp/myRtsp/rtspliblive/src"
        "/home/<USER>/project/speex/cross_ssd202/lib"
    )

else()
    MESSAGE(FATAL_ERROR "Please specify the Platform!")
endif()

project(NetPager)
#set(CMAKE_C_STANDARD 11)#C11
#set(CMAKE_CXX_STANDARD 17)#C17
#set(CMAKE_CXX_STANDARD_REQUIRED ON)


# 5. 头文件
include_directories(${PROJECT_SOURCE_DIR})
# 5.1. 定义函数，用于递归添加头文件
function(include_sub_directories_recursively root_dir)
    if (IS_DIRECTORY ${root_dir})               # 当前路径是一个目录吗，是的话就加入到包含目录
        message("include dir: " ${root_dir})
        include_directories(${root_dir})
    endif()

    file(GLOB ALL_SUB RELATIVE ${root_dir} ${root_dir}/*) # 获得当前目录下的所有文件，让如ALL_SUB列表中
    foreach(sub ${ALL_SUB})
        if (IS_DIRECTORY ${root_dir}/${sub})
            include_sub_directories_recursively(${root_dir}/${sub}) # 对子目录递归调用，包含
        endif()
    endforeach()
endfunction()
# 5.2. 添加头文件
include_sub_directories_recursively(${PROJECT_SOURCE_DIR}/NetPager) # 对子目录递归调用，包含

if ("${CMAKE_BUILD_TYPE}" STREQUAL "armv7" OR "${CMAKE_BUILD_TYPE}" STREQUAL "armv7_debug")
    include_sub_directories_recursively(${PROJECT_SOURCE_DIR}/lv_porting_sstar) # 对子目录递归调用，包含
    file(GLOB_RECURSE INCLUDES "lv_drivers/*.h" "lv_examples/*.h" "lv_freetype/*.h" "lvgl/*.h" "lv_porting_sstar/*.h")
    file(GLOB_RECURSE SOURCES  "lv_drivers/*.c" "lv_examples/*.c" "lv_freetype/*.c" "lvgl/*.c" "NetPager/*.c" "lv_porting_sstar/*.c")
    list(FILTER INCLUDES EXCLUDE REGEX "NetPager/network/src/ffplayer")
    list(FILTER SOURCES EXCLUDE REGEX "NetPager/network/src/ffplayer")
elseif ("${CMAKE_BUILD_TYPE}" STREQUAL "i386" OR "${CMAKE_BUILD_TYPE}" STREQUAL "arm")
    file(GLOB_RECURSE INCLUDES "lv_drivers/*.h" "lv_examples/*.h" "lv_freetype/*.h" "lvgl/*.h")
    file(GLOB_RECURSE SOURCES  "lv_drivers/*.c" "lv_examples/*.c" "lv_freetype/*.c" "lvgl/*.c" "NetPager/*.c")
    list(FILTER INCLUDES EXCLUDE REGEX "NetPager/network/src/ffplayer")
    list(FILTER SOURCES EXCLUDE REGEX "NetPager/network/src/ffplayer")
elseif ("${CMAKE_BUILD_TYPE}" STREQUAL "ssd202")
    include_sub_directories_recursively(${PROJECT_SOURCE_DIR}/lv_porting_sstar) # 对子目录递归调用，包含
    file(GLOB_RECURSE INCLUDES "lv_drivers/*.h" "lv_examples/*.h" "lv_freetype/*.h" "lvgl/*.h" "lv_porting_sstar/*.h")
    file(GLOB_RECURSE SOURCES  "lv_drivers/*.c" "lv_examples/*.c" "lv_freetype/*.c" "lvgl/*.c" "NetPager/*.c" "lv_porting_sstar/*.c")
endif()

#SET(EXECUTABLE_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/bin)
SET(EXECUTABLE_OUTPUT_PATH ${PROJECT_SOURCE_DIR})

add_executable(${DST_BIN} main.c mouse_cursor_icon.c ${SOURCES} ${INCLUDES})
#target_link_libraries(${DST_BIN} PRIVATE SDL2 m pthread /usr/lib/x86_64-linux-gnu/libfreetype.so /home/<USER>/project/dlna/x86_64/cross/lib/libixml.so /home/<USER>/project/dlna/x86_64/cross/lib/libupnp.so)

if ("${CMAKE_BUILD_TYPE}" STREQUAL "i386")
    target_link_libraries(${DST_BIN} PRIVATE SDL2 m rt pthread freetype ixml upnp)
elseif ("${CMAKE_BUILD_TYPE}" STREQUAL "arm")
    target_link_libraries(${DST_BIN} PRIVATE m rt pthread freetype ixml upnp)
elseif ("${CMAKE_BUILD_TYPE}" STREQUAL "armv7" OR "${CMAKE_BUILD_TYPE}" STREQUAL "armv7_debug")
    target_link_libraries(${DST_BIN} PRIVATE m rt pthread freetype ixml upnp curl cam_os_wrapper cam_fs_wrapper mi_sys mi_common mi_panel mi_disp mi_gfx mi_ao mi_ai SRC_LINUX APC_LINUX AED_LINUX AEC_LINUX avcodec avutil swresample avformat)
    elseif ("${CMAKE_BUILD_TYPE}" STREQUAL "ssd202")
    target_link_libraries(${DST_BIN} PRIVATE m rt pthread freetype ixml upnp curl cam_os_wrapper cam_fs_wrapper mi_sys mi_common mi_panel mi_disp mi_gfx mi_ao mi_ai mi_vdec avcodec avutil swresample avformat swscale libRtspStream.a stdc++ speexdsp)    
endif()

add_custom_target (run COMMAND ${EXECUTABLE_OUTPUT_PATH}/${DST_BIN})
