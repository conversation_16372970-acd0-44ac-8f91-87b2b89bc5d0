/**
 * @file mouse.c
 *
 */

/*********************
 *      INCLUDES
 *********************/
#include "mouse.h"

#include <time.h>
#include <sys/time.h>
#include "NetPager/win/win.h"
#if USE_MOUSE != 0

/*********************
 *      DEFINES
 *********************/
#ifndef MONITOR_ZOOM
#define MONITOR_ZOOM    1
#endif

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *  STATIC PROTOTYPES
 **********************/

/**********************
 *  STATIC VARIABLES
 **********************/
static bool left_button_down = false;
static int16_t last_x = 0;
static int16_t last_y = 0;

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

/**
 * Initialize the mouse
 */
void mouse_init(void)
{

}

struct timeval t_start,t_end;
int press_x,press_y,release_x,release_y,isPressed;
/**
 * Get the current position and state of the mouse
 * @param indev_drv pointer to the related input device driver
 * @param data store the mouse data here
 * @return false: because the points are not buffered, so no more data to be read
 */
bool mouse_read(lv_indev_drv_t * indev_drv, lv_indev_data_t * data)
{
    (void) indev_drv;      /*Unused*/

    /*Store the collected data*/
    data->point.x = last_x;
    data->point.y = last_y;
    data->state = left_button_down ? LV_INDEV_STATE_PR : LV_INDEV_STATE_REL;
    if(isPressed == 0 && data->state == LV_INDEV_STATE_PR)
    {
    	//按下
    	isPressed = 1;
    	press_x=data->point.x;
    	press_y=data->point.y;
    	release_x=0;
    	release_y=0;
    }
    else if(isPressed == 1 && data->state == LV_INDEV_STATE_REL)
    {
    	//弹起
    	release_x=data->point.x;
    	release_y=data->point.y;
    	if(abs(release_y-press_y)>=180 && abs(release_x-press_x) <=120)		//Y轴偏移超过180,且X轴偏移不超过120,认为是手势操作
    	{
    		if(release_y>press_y)
    		{
    			printf("GESTURE DOWN\n");	//PRE
    			if(GetCurrentWin() == WIN_CONTROL)
    			{
    				control_win_current_page--;
    				control_win_update(control_page_type,1);
    			}
    		}
    		else
    		{
    			printf("GESTURE UP\n");	//NEXT
    			if(GetCurrentWin() == WIN_CONTROL)
    			{
    				control_win_current_page++;
    				control_win_update(control_page_type,1);
    			}
    		}
    	}
    	isPressed=0;
    	press_x=0;
    	press_y=0;
    }
    return false;
}

/**
 * It will be called from the main SDL thread
 */
void mouse_handler(SDL_Event * event)
{
    switch(event->type) {
        case SDL_MOUSEBUTTONUP:
            if(event->button.button == SDL_BUTTON_LEFT)
            {
                left_button_down = false;
                //printf("Touch:up\n");
            }
            break;
        case SDL_MOUSEBUTTONDOWN:
            if(event->button.button == SDL_BUTTON_LEFT) {
                left_button_down = true;
                last_x = event->motion.x / MONITOR_ZOOM;
                last_y = event->motion.y / MONITOR_ZOOM;
                printf("Touch:x=%d,y=%d\n",last_x,last_y);
                //gettimeofday(&t_start, NULL);

                //printf("Start time: %ld us", t_start.tv_usec);
            }
            break;
        case SDL_MOUSEMOTION:
            last_x = event->motion.x / MONITOR_ZOOM;
            last_y = event->motion.y / MONITOR_ZOOM;
            break;

        case SDL_FINGERUP:
            left_button_down = false;
            last_x = LV_HOR_RES * event->tfinger.x / MONITOR_ZOOM;
            last_y = LV_VER_RES * event->tfinger.y / MONITOR_ZOOM;
            break;
        case SDL_FINGERDOWN:
            left_button_down = true;
            last_x = LV_HOR_RES * event->tfinger.x / MONITOR_ZOOM;
            last_y = LV_VER_RES * event->tfinger.y / MONITOR_ZOOM;
            break;
        case SDL_FINGERMOTION:
            last_x = LV_HOR_RES * event->tfinger.x / MONITOR_ZOOM;
            last_y = LV_VER_RES * event->tfinger.y / MONITOR_ZOOM;
            break;
    }

}

/**********************
 *   STATIC FUNCTIONS
 **********************/

#endif
