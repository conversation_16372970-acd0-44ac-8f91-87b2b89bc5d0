/**
 * @file evdev.c
 *
 */

/*********************
 *      INCLUDES
 *********************/
#include "evdev.h"
#if USE_EVDEV != 0 || USE_BSD_EVDEV

#include <stdio.h>
#include <unistd.h>
#include <fcntl.h>
#if USE_BSD_EVDEV
#include <dev/evdev/input.h>
#else
#include <linux/input.h>
#endif

#include "NetPager/win/win.h"
#include "NetPager/network/include/const.h"

#if !defined(ABS_MT_POSITION_X) && !defined(ABS_MT_POSITION_Y)
#define ABS_MT_POSITION_X	0x35	/* Center X touch position */
#define ABS_MT_POSITION_Y	0x36	/* Center Y touch position */
#endif
/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *  STATIC PROTOTYPES
 **********************/
int map(int x, int in_min, int in_max, int out_min, int out_max);

/**********************
 *  STATIC VARIABLES
 **********************/
int evdev_fd;
int evdev_root_x;
int evdev_root_y;
int evdev_button;

int evdev_key_val;

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

/**
 * Initialize the evdev interface
 */
void evdev_init(void)
{
#if USE_BSD_EVDEV
    evdev_fd = open(EVDEV_NAME, O_RDWR | O_NOCTTY);
#else
    evdev_fd = open(EVDEV_NAME, O_RDWR | O_NOCTTY | O_NDELAY);
#endif
    if(evdev_fd == -1) {
        perror("unable open evdev interface:");
        return;
    }

#if USE_BSD_EVDEV
    fcntl(evdev_fd, F_SETFL, O_NONBLOCK);
#else
    fcntl(evdev_fd, F_SETFL, O_ASYNC | O_NONBLOCK);
#endif

    evdev_root_x = 0;
    evdev_root_y = 0;
    evdev_key_val = 0;
    evdev_button = LV_INDEV_STATE_REL;
}
/**
 * reconfigure the device file for evdev
 * @param dev_name set the evdev device filename
 * @return true: the device file set complete
 *         false: the device file doesn't exist current system
 */
bool evdev_set_file(char* dev_name)
{ 
     if(evdev_fd != -1) {
        close(evdev_fd);
     }
#if USE_BSD_EVDEV
     evdev_fd = open(dev_name, O_RDWR | O_NOCTTY);
#else
     evdev_fd = open(dev_name, O_RDWR | O_NOCTTY | O_NDELAY);
#endif

     if(evdev_fd == -1) {
        perror("unable open evdev interface:");
        return false;
     }

#if USE_BSD_EVDEV
     fcntl(evdev_fd, F_SETFL, O_NONBLOCK);
#else
     fcntl(evdev_fd, F_SETFL, O_ASYNC | O_NONBLOCK);
#endif

     evdev_root_x = 0;
     evdev_root_y = 0;
     evdev_key_val = 0;
     evdev_button = LV_INDEV_STATE_REL;

     return true;
}

//add for test
#if 1
int press_x,press_y,release_x,release_y,isPressed;
#endif
//end

/**
 * Get the current position and state of the evdev
 * @param data store the evdev data here
 * @return false: because the points are not buffered, so no more data to be read
 */
bool evdev_read(lv_indev_drv_t * drv, lv_indev_data_t * data)
{
    struct input_event in;

    while(read(evdev_fd, &in, sizeof(struct input_event)) > 0) {
        if(in.type == EV_REL) {
            if(in.code == REL_X)
				#if EVDEV_SWAP_AXES
					evdev_root_y += in.value;
				#else
					evdev_root_x += in.value;
				#endif
            else if(in.code == REL_Y)
				#if EVDEV_SWAP_AXES
					evdev_root_x += in.value;
				#else
					evdev_root_y += in.value;
				#endif
        } else if(in.type == EV_ABS) {
            if(in.code == ABS_X)
				#if EVDEV_SWAP_AXES
					evdev_root_y = in.value;
				#else
					evdev_root_x = in.value;
				#endif
            else if(in.code == ABS_Y)
				#if EVDEV_SWAP_AXES
					evdev_root_x = in.value;
				#else
					evdev_root_y = in.value;
				#endif
            else if(in.code == ABS_MT_POSITION_X)
                                #if EVDEV_SWAP_AXES
                                        evdev_root_y = in.value;
                                #else
                                        evdev_root_x = in.value;
                                #endif
            else if(in.code == ABS_MT_POSITION_Y)
                                #if EVDEV_SWAP_AXES
                                        evdev_root_x = in.value;
                                #else
                                        evdev_root_y = in.value;
                                #endif
            //add for asm9260t
            else if(in.code == ABS_PRESSURE)
			   evdev_button = in.value;
            //end
        } else if(in.type == EV_KEY) {
            if(in.code == BTN_MOUSE || in.code == BTN_TOUCH) {
                if(in.value == 0)
                    evdev_button = LV_INDEV_STATE_REL;
                else if(in.value == 1)
                    evdev_button = LV_INDEV_STATE_PR;
            } else if(drv->type == LV_INDEV_TYPE_KEYPAD) {
		data->state = (in.value) ? LV_INDEV_STATE_PR : LV_INDEV_STATE_REL;
		switch(in.code) {
			case KEY_BACKSPACE:
				data->key = LV_KEY_BACKSPACE;
				break;
			case KEY_ENTER:
				data->key = LV_KEY_ENTER;
				break;
			case KEY_UP:
				data->key = LV_KEY_UP;
				break;
			case KEY_LEFT:
				data->key = LV_KEY_PREV;
				break;
			case KEY_RIGHT:
				data->key = LV_KEY_NEXT;
				break;
			case KEY_DOWN:
				data->key = LV_KEY_DOWN;
				break;
			default:
				data->key = 0;
				break;
		}
		evdev_key_val = data->key;
		evdev_button = data->state;
		return false;
	    }
        }
    }

    if(drv->type == LV_INDEV_TYPE_KEYPAD) {
        /* No data retrieved */
        data->key = evdev_key_val;
	data->state = evdev_button;
	return false;
    }
    if(drv->type != LV_INDEV_TYPE_POINTER)
        return false;
    /*Store the collected data*/

#if EVDEV_CALIBRATE
    data->point.x = map(evdev_root_x, EVDEV_HOR_MIN, EVDEV_HOR_MAX, 0, lv_disp_get_hor_res(drv->disp));
    data->point.y = map(evdev_root_y, EVDEV_VER_MIN, EVDEV_VER_MAX, 0, lv_disp_get_ver_res(drv->disp));
#else
    data->point.x = evdev_root_x;
    data->point.y = evdev_root_y;
#endif

    data->state = evdev_button;
	//printf("ori x=%d,y=%d\n",data->point.x,data->point.y);
    if(data->point.x < 0)
      data->point.x = 0;
    if(data->point.y < 0)
      data->point.y = 0;
	

    #if defined(USE_SSD212) || defined(USE_SSD202) //SSD202,mipi屏幕是正贴，无需翻转
    #if USE_SSD212
    int rotation_value=Get_Screen_Rotations_Value();
    #elif USE_SSD202
	//判断触摸屏型号(1：GT9271-睛灵龙-旋转180度,2：GT911-杭州魔方-不需要旋转)
	static int touch_id=1;
	char product[8]={0};
    FILE *fp = fopen("/sys/devices/soc0/soc/1f223000.i2c0/i2c-0/0-005d/input/input0/id/product", "r");
    if (fp != NULL) {
		if (fgets(product, sizeof(product), fp) != NULL) {
			// 删除换行符
			product[strcspn(product, "\n")] = '\0';
			// 判断字符，并返回相应的值
			if (strcmp(product, "2437") == 0) {
				touch_id = 1;
			} else if (strcmp(product, "038f") == 0) {
				touch_id = 2;
			}
		}
		fclose(fp);
    }
    int rotation_value=2; //翻转180度
	if(touch_id == 2)
	{
		rotation_value=0;//不需要旋转
	}
    #endif

    if(rotation_value == 1) {//90
        lv_coord_t tmp = data->point.x;
        data->point.x = data->point.y;
        data->point.y = g_screen_height - tmp - 1;
    }
    else if(rotation_value == 2) {//180
        data->point.x = g_screen_width - data->point.x - 1;
        data->point.y = g_screen_height - data->point.y - 1;
    }
    else if(rotation_value == 3) {//270
        lv_coord_t tmp = data->point.y;
        data->point.y = data->point.x;
        data->point.x = g_screen_width - tmp - 1;
    }
    #endif

	//printf("real x=%d,y=%d\n",data->point.x,data->point.y);

#if 1
	int currentWin=GetCurrentWin();
    if(isPressed == 0 && data->state == LV_INDEV_STATE_PR)
	{
		Contrl_LCDBacklight(1);

		//按下
		isPressed = 1;
		press_x=data->point.x;
		press_y=data->point.y;
		release_x=0;
		release_y=0;

		if(currentWin == WIN_CONTROL || currentWin == WIN_MUSICLIST)
		{
			if(currentWin == WIN_CONTROL)
			{
				/****************解决控制界面音量调节问题******************/
				if(IS_DISP_RES_1024)
            	{
					if(press_x>=970 && (150 <= press_y <= 408) )
					{
						data->point.x=995;
					}
				}
				else if(IS_DISP_RES_1280)
            	{
					if(press_x>=1210 && (200 <= press_y <= 550) )
					{
						data->point.x=1235;
					}
				}
				else if(IS_DISP_RES_800)
				{
					if(press_x>=742 && (120 <= press_y <= 336) )
					{
						data->point.x=764;
					}
				}
				/****************解决控制界面音量控制问题******************/
			}

			/****************扩大底部控制栏按钮点击区域****************/
			if(IS_DISP_RES_1024)
            {
				if(press_x<=955 && press_y>=550 )
				{
					data->point.y=550;
				}
			}
			else if(IS_DISP_RES_1280)
            {
				if(press_x<=1195 && press_y>=700 )
				{
					data->point.y=735;
				}
			}
			else if(IS_DISP_RES_800)
			{
				if(press_x<=735 && press_y>=456 )
				{
					data->point.y=456;
				}
			}
			/****************扩大底部控制栏按钮点击区域****************/
		}

		if(IsValidWin(WIN_CONTROL))
		{
			/****************扩大返回按钮点击区域****************/
			int y_limit=45;
			if(IS_DISP_RES_1280)
				y_limit=60;
			if(press_x>=2 && press_y>=2 && press_x<=90 && press_y<=y_limit)
			{
				data->point.x=30;
				data->point.y=20;
			}
			/****************扩大返回按钮点击区域****************/

			/****************扩大分组扩展按钮点击区域****************/
			if(press_x>=160 && press_y>=2 && press_x<=255 && press_y<=y_limit)
			{
				data->point.x=208;
				data->point.y=24;
			}
			/****************扩大分组扩展按钮点击区域****************/
		}

	}
    else if(data->state == LV_INDEV_STATE_PR)	//因为state没有这么快变化,按下后到释放前按下坐标要一致才能触发点击事件
    {
		if(currentWin == WIN_CONTROL || currentWin == WIN_MUSICLIST)
		{
			/****************扩大底部控制栏按钮点击区域****************/
			if(IS_DISP_RES_1024)
            {
				if(press_x<=955 && press_y>=550 )
				{
					data->point.y=550;
				}
			}
			else if(IS_DISP_RES_1280)
            {
				if(press_x<=1195 && press_y>=700 )
				{
					data->point.y=735;
				}
			}
			else if(IS_DISP_RES_800)
			{
				if(press_x<=735 && press_y>=456 )
				{
					data->point.y=456;
				}
			}
			/****************扩大底部控制栏按钮点击区域****************/
		}

		if(IsValidWin(WIN_CONTROL))
		{
			/****************扩大返回按钮点击区域****************/
			int y_limit=45;
			if(IS_DISP_RES_1280)
				y_limit=60;
			if(press_x>=2 && press_y>=2 && press_x<=90 && press_y<=y_limit)
			{
				data->point.x=30;
				data->point.y=20;
			}
			/****************扩大返回按钮点击区域****************/

			/****************扩大分组扩展按钮点击区域****************/
			if(press_x>=160 && press_y>=2 && press_x<=255 && press_y<=y_limit)
			{
				data->point.x=208;
				data->point.y=24;
			}
			/****************扩大分组扩展按钮点击区域****************/
		}

    }
	else if(isPressed == 1 && data->state == LV_INDEV_STATE_REL)
	{
		//弹起
		release_x=data->point.x;
		release_y=data->point.y;


		if(currentWin == WIN_CONTROL || currentWin == WIN_MUSICLIST)
		{
			if(currentWin == WIN_CONTROL)
			{
				/****************解决控制界面音量控制问题******************/
				if(IS_DISP_RES_1024)
            	{
					if(release_x>=970 && (150 <= release_y <= 408) )
					{
						data->point.x=995;
					}
				}
				else if(IS_DISP_RES_1280)
            	{
					if(release_x>=1210 && (200 <= release_y <= 550) )
					{
						data->point.x=1235;
					}
				}
				else if(IS_DISP_RES_800)
				{
					if(press_x>=742 && (120 <= press_y <= 336) )
					{
						data->point.x=764;
					}
				}
				/****************解决控制界面音量调节问题******************/
			}
			/****************扩大底部控制栏按钮点击区域****************/
			if(IS_DISP_RES_1024)
            {
				if(release_x<=955 && release_y>=550 )
				{
					data->point.y=550;
				}
			}
			else if(IS_DISP_RES_1280)
            {
				if(release_x<=1195 && release_y>=700 )
				{
					data->point.y=735;
				}
			}
			else if(IS_DISP_RES_800)
			{
				if(press_x<=735 && press_y>=456 )
				{
					data->point.y=456;
				}
			}
			/****************扩大底部控制栏按钮点击区域****************/
		}
		#if 0
		if(abs(release_y-press_y)>=180 && abs(release_x-press_x) <=120)		//Y轴偏移超过180,且X轴偏移不超过120,认为是手势操作
		{
			if(release_y>press_y)
			{
				printf("GESTURE DOWN\n");	//PRE
				if(GetCurrentWin() == WIN_CONTROL)
				{
					if(press_x<970)	//超过区域为音量控制
					{
						control_win_current_page--;
						control_win_update(control_page_type,1);
					}
				}
			}
			else
			{
				printf("GESTURE UP\n");	//NEXT
				if(GetCurrentWin() == WIN_CONTROL)
				{
					if(press_x<965)	//超过区域为音量控制
					{
						control_win_current_page++;
						control_win_update(control_page_type,1);
					}
				}
			}
		}
		#endif

		isPressed=0;
		press_x=0;
		press_y=0;
	}

#endif

    return false;
}

/**********************
 *   STATIC FUNCTIONS
 **********************/
int map(int x, int in_min, int in_max, int out_min, int out_max)
{
  return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

#endif