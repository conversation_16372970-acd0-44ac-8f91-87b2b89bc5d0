/**
 * @file lv_ex_widgets.h
 *
 */

#ifndef LV_EX_WIDGETS_H
#define LV_EX_WIDGETS_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_ex_arc_1(void);
void lv_ex_arc_2(void);
void lv_ex_bar_1(void);
void lv_ex_btn_1(void);
void lv_ex_btn_2(void);
void lv_ex_btnmatrix_1(void);
void lv_ex_calendar_1(void);
void lv_ex_canvas_1(void);
void lv_ex_canvas_2(void);
void lv_ex_checkbox_1(void);
void lv_ex_chart_1(void);
void lv_ex_chart_2(void);
void lv_ex_cont_1(void);
void lv_ex_cpicker_1(void);
void lv_ex_cpicker_2(void);
void lv_ex_dropdown_1(void);
void lv_ex_dropdown_2(void);
void lv_ex_gauge_1(void);
void lv_ex_img_1(void);
void lv_ex_img_2(void);
void lv_ex_img_3(void);
void lv_ex_imgbtn_1(void);
void lv_ex_keyboard_1(void);
void lv_ex_label_1(void);
void lv_ex_label_2(void);
void lv_ex_label_3(void);
void lv_ex_led_1(void);
void lv_ex_line_1(void);
void lv_ex_list_1(void);
void lv_ex_linemeter_1(void);
void lv_ex_msgbox_1(void);
void lv_ex_msgbox_2(void);
void lv_ex_obj_1(void);
void lv_ex_page_1(void);
void lv_ex_spinner_1(void);
void lv_ex_roller_1(void);
void lv_ex_slider_1(void);
void lv_ex_slider_2(void);
void lv_ex_spinbox_1(void);
void lv_ex_switch_1(void);
void lv_ex_textarea_1(void);
void lv_ex_textarea_2(void);
void lv_ex_textarea_3(void);  
void lv_ex_objmask_1(void);
void lv_ex_objmask_2(void);
void lv_ex_table_1(void);
void lv_ex_tabview_1(void);
void lv_ex_tileview_1(void);
void lv_ex_win_1(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /*LV_EX_WIDGETS_H*/
