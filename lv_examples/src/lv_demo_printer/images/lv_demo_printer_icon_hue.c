#include "lvgl/lvgl.h"
#include "../../../lv_examples.h"
#if LV_USE_DEMO_PRINTER

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG_LV_DEMO_PRINTER_ICON_HUE
#define LV_ATTRIBUTE_IMG_LV_DEMO_PRINTER_ICON_HUE
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_IMG_LV_DEMO_PRINTER_ICON_HUE uint8_t lv_demo_printer_icon_hue_map[] = {
#if LV_COLOR_DEPTH == 1 || LV_COLOR_DEPTH == 8
  /*Pixel format: Alpha 8 bit, Red: 3 bit, Green: 3 bit, Blue: 2 bit*/
  0x26, 0x00, 0x26, 0x00, 0x26, 0x00, 0x46, 0x00, 0x46, 0x00, 0x26, 0x00, 0x26, 0x00, 0x46, 0x3c, 0x4a, 0x8f, 0x4a, 0xc0, 0x4a, 0xe4, 0x4b, 0xf3, 0x4f, 0xe7, 0x4f, 0xc4, 0x4f, 0x94, 0x57, 0x47, 0x5b, 0x00, 0x57, 0x00, 0x5b, 0x00, 0x5b, 0x00, 0x5b, 0x00, 0x5b, 0x00, 0x5b, 0x00, 
  0x26, 0x00, 0x26, 0x00, 0x26, 0x00, 0x46, 0x00, 0x46, 0x00, 0x26, 0x43, 0x46, 0xc0, 0x46, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4b, 0xff, 0x4f, 0xff, 0x4f, 0xff, 0x4f, 0xff, 0x57, 0xff, 0x5b, 0xcc, 0x57, 0x4f, 0x5b, 0x00, 0x3b, 0x00, 0x5b, 0x00, 0x5b, 0x00, 0x5b, 0x00, 
  0x26, 0x00, 0x26, 0x00, 0x26, 0x00, 0x46, 0x00, 0x46, 0x87, 0x46, 0xff, 0x4a, 0xff, 0x46, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4b, 0xff, 0x4f, 0xff, 0x4f, 0xff, 0x53, 0xff, 0x5b, 0xff, 0x57, 0xff, 0x57, 0xff, 0x5b, 0x98, 0x3b, 0x00, 0x5b, 0x00, 0x5b, 0x00, 0x5b, 0x00, 
  0x26, 0x00, 0x26, 0x00, 0x26, 0x00, 0x46, 0x98, 0x4a, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4b, 0xff, 0x4f, 0xff, 0x4f, 0xff, 0x57, 0xff, 0x5b, 0xff, 0x57, 0xff, 0x5b, 0xff, 0x3b, 0xff, 0x5b, 0xaf, 0x5b, 0x00, 0x5b, 0x00, 0x5b, 0x00, 
  0x46, 0x00, 0x46, 0x00, 0x46, 0x87, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4b, 0xff, 0x4f, 0xff, 0x4f, 0xff, 0x5b, 0xff, 0x57, 0xff, 0x5b, 0xff, 0x3b, 0xff, 0x5b, 0xff, 0x5b, 0xff, 0x5b, 0xa0, 0x3b, 0x00, 0x3b, 0x00, 
  0x46, 0x00, 0x46, 0x34, 0x4a, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4b, 0xff, 0x4f, 0xff, 0x53, 0xff, 0x5b, 0xff, 0x57, 0xff, 0x5b, 0xff, 0x5b, 0xff, 0x5b, 0xff, 0x5b, 0xff, 0x3a, 0xff, 0x3a, 0x4c, 0x3a, 0x00, 
  0x46, 0x00, 0x46, 0xd0, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x4a, 0xff, 0x4b, 0xff, 0x4f, 0xff, 0x57, 0xff, 0x5b, 0xff, 0x5b, 0xff, 0x3b, 0xff, 0x5b, 0xff, 0x3b, 0xff, 0x3a, 0xff, 0x3a, 0xff, 0x3a, 0xe8, 0x3a, 0x00, 
  0x46, 0x37, 0x4a, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x4f, 0xff, 0x57, 0xff, 0x5b, 0xff, 0x5b, 0xff, 0x5b, 0xff, 0x3a, 0xff, 0x3a, 0xff, 0x3a, 0xff, 0x3a, 0xff, 0x3a, 0xff, 0x3a, 0x50, 
  0x66, 0x94, 0x66, 0xff, 0x66, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x4a, 0xff, 0x4a, 0xff, 0x53, 0xff, 0x5b, 0xff, 0x5b, 0xff, 0x5b, 0xff, 0x3a, 0xff, 0x3a, 0xff, 0x3a, 0xff, 0x3a, 0xff, 0x5a, 0xff, 0x59, 0xff, 0x59, 0xac, 
  0x66, 0xc7, 0x66, 0xff, 0x66, 0xff, 0x66, 0xff, 0x66, 0xff, 0x66, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x46, 0xff, 0x4a, 0xff, 0x57, 0xff, 0x5b, 0xff, 0x3a, 0xff, 0x3a, 0xff, 0x3a, 0xff, 0x5a, 0xff, 0x59, 0xff, 0x59, 0xff, 0x59, 0xff, 0x59, 0xff, 0x59, 0xd7, 
  0x66, 0xe8, 0x66, 0xff, 0x66, 0xff, 0x66, 0xff, 0x66, 0xff, 0x66, 0xff, 0x66, 0xff, 0x66, 0xff, 0x66, 0xff, 0x4a, 0xff, 0x26, 0xff, 0x2b, 0xff, 0x3b, 0xff, 0x3a, 0xff, 0x39, 0xff, 0x39, 0xff, 0x59, 0xff, 0x59, 0xff, 0x59, 0xff, 0x59, 0xff, 0x59, 0xff, 0x59, 0xff, 0x59, 0xec, 
  0x86, 0xf7, 0x86, 0xff, 0x86, 0xff, 0x86, 0xff, 0x86, 0xff, 0x86, 0xff, 0x86, 0xff, 0x86, 0xff, 0x86, 0xff, 0x86, 0xff, 0x62, 0xff, 0x8e, 0xff, 0x7d, 0xff, 0x79, 0xff, 0x79, 0xff, 0x79, 0xff, 0x79, 0xff, 0x79, 0xff, 0x79, 0xff, 0x79, 0xff, 0x79, 0xff, 0x79, 0xff, 0x79, 0xf4, 
  0xa5, 0xec, 0xa5, 0xff, 0xa5, 0xff, 0xa5, 0xff, 0x85, 0xff, 0x85, 0xff, 0x85, 0xff, 0xa5, 0xff, 0xa5, 0xff, 0xa5, 0xff, 0xc0, 0xff, 0xf0, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xdd, 0xff, 0xd9, 0xff, 0xb9, 0xff, 0xb9, 0xff, 0xb9, 0xff, 0xb9, 0xff, 0xb9, 0xff, 0xb9, 0xff, 0xb9, 0xef, 
  0x85, 0xcb, 0x85, 0xff, 0x85, 0xff, 0x85, 0xff, 0xa5, 0xff, 0xa5, 0xff, 0xa5, 0xff, 0xa5, 0xff, 0xc4, 0xff, 0xc4, 0xff, 0xe5, 0xff, 0xf1, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xdd, 0xff, 0xdd, 0xff, 0xb9, 0xff, 0xb9, 0xff, 0x99, 0xff, 0x99, 0xff, 0xb9, 0xd8, 
  0x85, 0x9b, 0x85, 0xff, 0xa5, 0xff, 0xa5, 0xff, 0xc5, 0xff, 0xc4, 0xff, 0xa4, 0xff, 0xc5, 0xff, 0xc5, 0xff, 0xe5, 0xff, 0xe9, 0xff, 0xf1, 0xff, 0xf9, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xdd, 0xff, 0xdd, 0xff, 0xb9, 0xff, 0xb9, 0xb3, 
  0xa5, 0x40, 0xa5, 0xff, 0xc4, 0xff, 0xc4, 0xff, 0xa5, 0xff, 0xa5, 0xff, 0xc5, 0xff, 0xc5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe9, 0xff, 0xf1, 0xff, 0xf5, 0xff, 0xf9, 0xff, 0xfd, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xdd, 0x5c, 
  0xc4, 0x00, 0xc5, 0xdc, 0xa5, 0xff, 0xa5, 0xff, 0xc5, 0xff, 0xc5, 0xff, 0xc5, 0xff, 0xc5, 0xff, 0xe5, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xf1, 0xff, 0xf5, 0xff, 0xf9, 0xff, 0xf9, 0xff, 0xfd, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xf3, 0xfd, 0x00, 
  0xa5, 0x00, 0xa5, 0x43, 0xa5, 0xff, 0xc5, 0xff, 0xe5, 0xff, 0xc5, 0xff, 0xc5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xf1, 0xff, 0xf5, 0xff, 0xf9, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0x5c, 0xfd, 0x00, 
  0xc5, 0x00, 0xc5, 0x00, 0xc5, 0x9b, 0xe5, 0xff, 0xc5, 0xff, 0xc5, 0xff, 0xe5, 0xff, 0xe9, 0xff, 0xe5, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xf1, 0xff, 0xf5, 0xff, 0xf5, 0xff, 0xfd, 0xff, 0xf9, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfc, 0xff, 0xfd, 0xb4, 0xfd, 0x00, 0xfd, 0x00, 
  0xc5, 0x00, 0xe4, 0x00, 0xc5, 0x00, 0xc5, 0xaf, 0xc5, 0xff, 0xc5, 0xff, 0xe9, 0xff, 0xe5, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xed, 0xff, 0xf1, 0xff, 0xf5, 0xff, 0xf5, 0xff, 0xf9, 0xff, 0xfd, 0xff, 0xf9, 0xff, 0xfd, 0xff, 0xfd, 0xff, 0xfd, 0xc4, 0xfd, 0x00, 0xfc, 0x00, 0xfc, 0x00, 
  0xc5, 0x00, 0xc4, 0x00, 0xc5, 0x00, 0xc5, 0x00, 0xc5, 0x9f, 0xe5, 0xff, 0xe9, 0xff, 0xe5, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xed, 0xff, 0xf1, 0xff, 0xf5, 0xff, 0xf5, 0xff, 0xf9, 0xff, 0xfd, 0xff, 0xf9, 0xff, 0xf9, 0xff, 0xfd, 0xaf, 0xfd, 0x00, 0xfd, 0x00, 0xfd, 0x00, 0xfd, 0x00, 
  0xc5, 0x00, 0xc4, 0x00, 0xc5, 0x00, 0xc5, 0x00, 0xe5, 0x00, 0xe9, 0x58, 0xe5, 0xd7, 0xe5, 0xff, 0xed, 0xff, 0xed, 0xff, 0xed, 0xff, 0xf1, 0xff, 0xf5, 0xff, 0xf5, 0xff, 0xf5, 0xff, 0xf9, 0xff, 0xf9, 0xe0, 0xf9, 0x64, 0xfd, 0x00, 0xfd, 0x00, 0xfd, 0x00, 0xfd, 0x00, 0xfd, 0x00, 
  0xc5, 0x00, 0xc4, 0x00, 0xc5, 0x00, 0xc5, 0x00, 0xe5, 0x00, 0xe5, 0x00, 0xe5, 0x00, 0xe9, 0x54, 0xed, 0xa4, 0xed, 0xcf, 0xed, 0xe8, 0xf1, 0xf3, 0xf5, 0xeb, 0xf5, 0xd3, 0xf5, 0xa8, 0xf9, 0x5f, 0xfd, 0x04, 0xf9, 0x00, 0xfd, 0x00, 0xfd, 0x00, 0xfd, 0x00, 0xfd, 0x00, 0xfd, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0xd1, 0x20, 0x00, 0xb0, 0x20, 0x00, 0xf1, 0x28, 0x00, 0x71, 0x39, 0x00, 0x51, 0x31, 0x00, 0x51, 0x31, 0x00, 0x31, 0x31, 0x00, 0x51, 0x31, 0x3c, 0xf3, 0x31, 0x8f, 0x13, 0x3a, 0xc0, 0xd3, 0x39, 0xe4, 0x94, 0x3a, 0xf3, 0x56, 0x33, 0xe7, 0x16, 0x33, 0xc4, 0xd5, 0x32, 0x94, 0xfb, 0x3c, 0x47, 0xdd, 0x3d, 0x00, 0x7d, 0x3d, 0x00, 0xda, 0x3d, 0x00, 0x38, 0x36, 0x00, 0x18, 0x36, 0x00, 0x18, 0x36, 0x00, 0x18, 0x36, 0x00, 
  0xd1, 0x20, 0x00, 0xb0, 0x20, 0x00, 0xf1, 0x28, 0x00, 0x71, 0x39, 0x00, 0x31, 0x31, 0x00, 0x51, 0x31, 0x43, 0x71, 0x31, 0xc0, 0x71, 0x31, 0xff, 0xf3, 0x39, 0xff, 0x34, 0x3a, 0xff, 0xf3, 0x39, 0xff, 0x94, 0x3a, 0xff, 0x56, 0x33, 0xff, 0xf5, 0x32, 0xff, 0x76, 0x3b, 0xff, 0x9c, 0x3d, 0xff, 0xbd, 0x3d, 0xcc, 0x7d, 0x3d, 0x4f, 0xda, 0x3d, 0x00, 0x37, 0x36, 0x00, 0x18, 0x36, 0x00, 0x18, 0x36, 0x00, 0x18, 0x36, 0x00, 
  0xb1, 0x20, 0x00, 0xb0, 0x20, 0x00, 0xf1, 0x28, 0x00, 0x71, 0x39, 0x00, 0x71, 0x39, 0x87, 0x91, 0x39, 0xff, 0x91, 0x31, 0xff, 0x71, 0x31, 0xff, 0xb2, 0x39, 0xff, 0x34, 0x3a, 0xff, 0xf3, 0x39, 0xff, 0x94, 0x3a, 0xff, 0x56, 0x33, 0xff, 0xb5, 0x32, 0xff, 0x39, 0x3c, 0xff, 0xfe, 0x45, 0xff, 0x7d, 0x45, 0xff, 0x7d, 0x45, 0xff, 0xf9, 0x35, 0x98, 0x18, 0x36, 0x00, 0x18, 0x36, 0x00, 0x18, 0x36, 0x00, 0x18, 0x36, 0x00, 
  0xd1, 0x20, 0x00, 0xb0, 0x18, 0x00, 0xf1, 0x28, 0x00, 0x91, 0x41, 0x98, 0x92, 0x41, 0xff, 0x91, 0x39, 0xff, 0x91, 0x31, 0xff, 0x71, 0x31, 0xff, 0x91, 0x31, 0xff, 0x13, 0x3a, 0xff, 0xf3, 0x39, 0xff, 0x94, 0x3a, 0xff, 0x56, 0x33, 0xff, 0xd5, 0x32, 0xff, 0xfb, 0x3c, 0xff, 0xfe, 0x45, 0xff, 0x7d, 0x45, 0xff, 0xda, 0x3d, 0xff, 0x37, 0x36, 0xff, 0x18, 0x36, 0xaf, 0x19, 0x36, 0x00, 0x1a, 0x36, 0x00, 0x1a, 0x36, 0x00, 
  0x31, 0x31, 0x00, 0x31, 0x31, 0x00, 0x51, 0x39, 0x87, 0x91, 0x39, 0xff, 0x91, 0x39, 0xff, 0x91, 0x41, 0xff, 0x91, 0x39, 0xff, 0x91, 0x31, 0xff, 0x71, 0x31, 0xff, 0xf3, 0x39, 0xff, 0xf3, 0x39, 0xff, 0x94, 0x3a, 0xff, 0x15, 0x33, 0xff, 0x76, 0x3b, 0xff, 0x9d, 0x3d, 0xff, 0x9e, 0x45, 0xff, 0xbc, 0x3d, 0xff, 0x38, 0x36, 0xff, 0x18, 0x36, 0xff, 0x1a, 0x36, 0xff, 0x19, 0x36, 0xa0, 0xf4, 0x35, 0x00, 0xf5, 0x35, 0x00, 
  0x52, 0x49, 0x00, 0x52, 0x49, 0x34, 0x92, 0x51, 0xff, 0x92, 0x41, 0xff, 0x91, 0x39, 0xff, 0x91, 0x39, 0xff, 0x91, 0x41, 0xff, 0x91, 0x39, 0xff, 0x71, 0x31, 0xff, 0xb2, 0x39, 0xff, 0xf3, 0x39, 0xff, 0x94, 0x3a, 0xff, 0xd5, 0x32, 0xff, 0x39, 0x3c, 0xff, 0xfe, 0x45, 0xff, 0x7d, 0x45, 0xff, 0x18, 0x36, 0xff, 0x38, 0x36, 0xff, 0x1b, 0x36, 0xff, 0x17, 0x36, 0xff, 0xcf, 0x35, 0xff, 0xac, 0x35, 0x4c, 0xad, 0x35, 0x00, 
  0x52, 0x49, 0x00, 0x92, 0x49, 0xd0, 0x92, 0x51, 0xff, 0x92, 0x51, 0xff, 0x92, 0x41, 0xff, 0x91, 0x39, 0xff, 0x91, 0x39, 0xff, 0x91, 0x39, 0xff, 0x71, 0x31, 0xff, 0x91, 0x31, 0xff, 0xf3, 0x39, 0xff, 0x94, 0x3a, 0xff, 0xf5, 0x32, 0xff, 0xdb, 0x3c, 0xff, 0xde, 0x45, 0xff, 0xf9, 0x3d, 0xff, 0x39, 0x36, 0xff, 0x1b, 0x36, 0xff, 0xf5, 0x35, 0xff, 0xad, 0x35, 0xff, 0xac, 0x35, 0xff, 0xae, 0x35, 0xe8, 0xae, 0x35, 0x00, 
  0x51, 0x49, 0x37, 0x92, 0x49, 0xff, 0x92, 0x49, 0xff, 0x92, 0x49, 0xff, 0x92, 0x51, 0xff, 0x92, 0x49, 0xff, 0x91, 0x41, 0xff, 0x91, 0x39, 0xff, 0x91, 0x39, 0xff, 0x71, 0x31, 0xff, 0xb2, 0x39, 0xff, 0x74, 0x3a, 0xff, 0x97, 0x33, 0xff, 0x7d, 0x45, 0xff, 0xdb, 0x3d, 0xff, 0x3a, 0x36, 0xff, 0x19, 0x36, 0xff, 0xd1, 0x35, 0xff, 0xac, 0x35, 0xff, 0xae, 0x2d, 0xff, 0xaf, 0x2d, 0xff, 0xaf, 0x2d, 0xff, 0xae, 0x35, 0x50, 
  0x4f, 0x69, 0x94, 0x70, 0x61, 0xff, 0x71, 0x59, 0xff, 0x92, 0x51, 0xff, 0x92, 0x49, 0xff, 0x92, 0x49, 0xff, 0x92, 0x49, 0xff, 0x92, 0x41, 0xff, 0x91, 0x39, 0xff, 0x71, 0x39, 0xff, 0x92, 0x31, 0xff, 0x33, 0x32, 0xff, 0x3a, 0x3c, 0xff, 0x1e, 0x46, 0xff, 0x3a, 0x36, 0xff, 0x16, 0x36, 0xff, 0xaf, 0x35, 0xff, 0xad, 0x2d, 0xff, 0xaf, 0x2d, 0xff, 0xae, 0x35, 0xff, 0xcd, 0x35, 0xff, 0xcb, 0x3d, 0xff, 0xc9, 0x45, 0xac, 
  0x4f, 0x69, 0xc7, 0x4f, 0x71, 0xff, 0x4f, 0x71, 0xff, 0x4f, 0x69, 0xff, 0x70, 0x61, 0xff, 0x71, 0x59, 0xff, 0x92, 0x51, 0xff, 0x92, 0x49, 0xff, 0x92, 0x41, 0xff, 0x71, 0x39, 0xff, 0x51, 0x31, 0xff, 0x34, 0x32, 0xff, 0x1d, 0x3d, 0xff, 0x7c, 0x36, 0xff, 0xf4, 0x2d, 0xff, 0xad, 0x2d, 0xff, 0xad, 0x35, 0xff, 0xad, 0x3d, 0xff, 0xcb, 0x3d, 0xff, 0xc9, 0x45, 0xff, 0xc8, 0x4d, 0xff, 0xc8, 0x4d, 0xff, 0xc9, 0x4d, 0xd7, 
  0x70, 0x61, 0xe8, 0x70, 0x61, 0xff, 0x70, 0x61, 0xff, 0x70, 0x69, 0xff, 0x70, 0x69, 0xff, 0x70, 0x69, 0xff, 0x70, 0x61, 0xff, 0x71, 0x59, 0xff, 0x92, 0x59, 0xff, 0x93, 0x49, 0xff, 0x32, 0x31, 0xff, 0x79, 0x1a, 0xff, 0xfc, 0x1d, 0xff, 0xf0, 0x25, 0xff, 0x8a, 0x2d, 0xff, 0xaa, 0x2d, 0xff, 0xaa, 0x35, 0xff, 0xa9, 0x3d, 0xff, 0xa8, 0x3d, 0xff, 0xa9, 0x3d, 0xff, 0xa9, 0x35, 0xff, 0xa9, 0x35, 0xff, 0xa9, 0x35, 0xec, 
  0x2d, 0x79, 0xf7, 0x2d, 0x79, 0xff, 0x2d, 0x79, 0xff, 0x2d, 0x79, 0xff, 0x2d, 0x79, 0xff, 0x2d, 0x79, 0xff, 0x2d, 0x79, 0xff, 0x0d, 0x79, 0xff, 0x0e, 0x79, 0xff, 0x2e, 0x71, 0xff, 0x4d, 0x70, 0xff, 0x6e, 0x7b, 0xff, 0x2b, 0x6f, 0xff, 0xc7, 0x5d, 0xff, 0xc8, 0x65, 0xff, 0xe8, 0x65, 0xff, 0xe8, 0x6d, 0xff, 0x08, 0x6e, 0xff, 0x08, 0x6e, 0xff, 0x08, 0x6e, 0xff, 0x08, 0x6e, 0xff, 0x08, 0x6e, 0xff, 0x08, 0x6e, 0xf4, 
  0xc9, 0x90, 0xec, 0xc9, 0x90, 0xff, 0xc9, 0x90, 0xff, 0xca, 0x90, 0xff, 0xca, 0x90, 0xff, 0xcb, 0x88, 0xff, 0xca, 0x90, 0xff, 0xc9, 0x98, 0xff, 0xc7, 0x98, 0xff, 0xc5, 0xa8, 0xff, 0x24, 0xc8, 0xff, 0xa1, 0xfb, 0xff, 0xe3, 0xff, 0xff, 0x47, 0xdf, 0xff, 0xa7, 0xc6, 0xff, 0x87, 0xb6, 0xff, 0x47, 0xa6, 0xff, 0x47, 0x9e, 0xff, 0x47, 0xa6, 0xff, 0x67, 0xae, 0xff, 0x67, 0xae, 0xff, 0x67, 0xae, 0xff, 0x67, 0xae, 0xef, 
  0xea, 0x88, 0xcb, 0xeb, 0x88, 0xff, 0xcb, 0x88, 0xff, 0xeb, 0x88, 0xff, 0xea, 0x90, 0xff, 0xe8, 0x98, 0xff, 0xe6, 0xa8, 0xff, 0x05, 0xa9, 0xff, 0x04, 0xb9, 0xff, 0xe4, 0xc8, 0xff, 0x24, 0xe9, 0xff, 0x84, 0xfb, 0xff, 0xa4, 0xfe, 0xff, 0xe4, 0xff, 0xff, 0x66, 0xf7, 0xff, 0x07, 0xdf, 0xff, 0xe7, 0xce, 0xff, 0xa7, 0xbe, 0xff, 0x67, 0xa6, 0xff, 0x47, 0x96, 0xff, 0x28, 0x8e, 0xff, 0x47, 0x96, 0xff, 0x47, 0x9e, 0xd8, 
  0xeb, 0x88, 0x9b, 0xea, 0x90, 0xff, 0xe8, 0x98, 0xff, 0xe6, 0xa8, 0xff, 0x05, 0xb1, 0xff, 0x04, 0xb1, 0xff, 0x04, 0xb1, 0xff, 0x04, 0xb9, 0xff, 0xe5, 0xc8, 0xff, 0x05, 0xd9, 0xff, 0xc4, 0xe9, 0xff, 0xc5, 0xf3, 0xff, 0xc4, 0xfd, 0xff, 0x04, 0xff, 0xff, 0xa4, 0xff, 0xff, 0x66, 0xf7, 0xff, 0x27, 0xe7, 0xff, 0x27, 0xe7, 0xff, 0x27, 0xe7, 0xff, 0xe7, 0xd6, 0xff, 0xa7, 0xbe, 0xff, 0x67, 0xa6, 0xff, 0x47, 0x9e, 0xb3, 
  0xe6, 0xa8, 0x40, 0x05, 0xb1, 0xff, 0x04, 0xb9, 0xff, 0x04, 0xb9, 0xff, 0x05, 0xa9, 0xff, 0x05, 0xb1, 0xff, 0x05, 0xc1, 0xff, 0xe5, 0xd0, 0xff, 0x25, 0xd1, 0xff, 0x45, 0xe9, 0xff, 0x24, 0xea, 0xff, 0xe4, 0xf3, 0xff, 0x65, 0xfd, 0xff, 0x44, 0xfe, 0xff, 0x04, 0xff, 0xff, 0xc4, 0xff, 0xff, 0x65, 0xf7, 0xff, 0x27, 0xe7, 0xff, 0x07, 0xdf, 0xff, 0x27, 0xe7, 0xff, 0x47, 0xef, 0xff, 0x27, 0xe7, 0xff, 0xe7, 0xd6, 0x5c, 
  0x04, 0xb1, 0x00, 0x05, 0xb1, 0xdc, 0x05, 0xb1, 0xff, 0x05, 0xa9, 0xff, 0x05, 0xb9, 0xff, 0x05, 0xd1, 0xff, 0x05, 0xd1, 0xff, 0x05, 0xc9, 0xff, 0x65, 0xe1, 0xff, 0xa5, 0xe9, 0xff, 0xa4, 0xea, 0xff, 0xe4, 0xf3, 0xff, 0x25, 0xfd, 0xff, 0x24, 0xfe, 0xff, 0x84, 0xfe, 0xff, 0x44, 0xff, 0xff, 0xa4, 0xff, 0xff, 0x84, 0xff, 0xff, 0x46, 0xef, 0xff, 0x07, 0xdf, 0xff, 0x07, 0xdf, 0xff, 0x27, 0xe7, 0xf3, 0x27, 0xe7, 0x00, 
  0xe5, 0xa8, 0x00, 0xe5, 0xa8, 0x43, 0x05, 0xa9, 0xff, 0x05, 0xc1, 0xff, 0x05, 0xd1, 0xff, 0x05, 0xd1, 0xff, 0xe5, 0xc8, 0xff, 0x65, 0xe1, 0xff, 0x45, 0xe9, 0xff, 0x04, 0xea, 0xff, 0xe4, 0xf2, 0xff, 0xc4, 0xf3, 0xff, 0xe5, 0xfc, 0xff, 0xc4, 0xfd, 0xff, 0xa4, 0xfe, 0xff, 0x84, 0xfe, 0xff, 0x84, 0xff, 0xff, 0x84, 0xff, 0xff, 0x84, 0xff, 0xff, 0x45, 0xf7, 0xff, 0x07, 0xdf, 0xff, 0x08, 0xd7, 0x5c, 0x07, 0xdf, 0x00, 
  0x05, 0xc1, 0x00, 0x05, 0xc1, 0x00, 0x05, 0xc9, 0x9b, 0x05, 0xd1, 0xff, 0x05, 0xd1, 0xff, 0xe5, 0xc8, 0xff, 0x45, 0xd9, 0xff, 0xa5, 0xe9, 0xff, 0x45, 0xe1, 0xff, 0x84, 0xea, 0xff, 0xe4, 0xf2, 0xff, 0xc4, 0xf3, 0xff, 0x05, 0xfd, 0xff, 0x45, 0xfd, 0xff, 0x84, 0xfe, 0xff, 0x44, 0xfe, 0xff, 0xc4, 0xfe, 0xff, 0xa4, 0xff, 0xff, 0x84, 0xff, 0xff, 0x84, 0xff, 0xff, 0x65, 0xff, 0xb4, 0x46, 0xef, 0x00, 0x45, 0xef, 0x00, 
  0xe4, 0xd0, 0x00, 0xe4, 0xd0, 0x00, 0xe4, 0xd0, 0x00, 0x05, 0xc9, 0xaf, 0x05, 0xc9, 0xff, 0x25, 0xd1, 0xff, 0x85, 0xe9, 0xff, 0x65, 0xe9, 0xff, 0x85, 0xe1, 0xff, 0xe4, 0xf2, 0xff, 0xc4, 0xf2, 0xff, 0xc4, 0xf3, 0xff, 0x25, 0xfd, 0xff, 0xc5, 0xfc, 0xff, 0x24, 0xfe, 0xff, 0xa4, 0xfe, 0xff, 0x44, 0xfe, 0xff, 0x24, 0xff, 0xff, 0xa4, 0xff, 0xff, 0x64, 0xff, 0xc4, 0x84, 0xff, 0x00, 0x84, 0xff, 0x00, 0x84, 0xff, 0x00, 
  0xe4, 0xc8, 0x00, 0xc4, 0xc8, 0x00, 0xe4, 0xc8, 0x00, 0x05, 0xc9, 0x00, 0x05, 0xc9, 0x9f, 0x85, 0xe1, 0xff, 0xa5, 0xe9, 0xff, 0x45, 0xe1, 0xff, 0x04, 0xea, 0xff, 0x44, 0xf3, 0xff, 0xa4, 0xf2, 0xff, 0xc4, 0xf3, 0xff, 0x45, 0xfd, 0xff, 0xa5, 0xfc, 0xff, 0xa4, 0xfd, 0xff, 0xc4, 0xfe, 0xff, 0x44, 0xfe, 0xff, 0x64, 0xfe, 0xff, 0x44, 0xff, 0xaf, 0x84, 0xff, 0x00, 0x64, 0xff, 0x00, 0x64, 0xff, 0x00, 0x64, 0xff, 0x00, 
  0xe4, 0xc8, 0x00, 0xe4, 0xc8, 0x00, 0xe4, 0xc8, 0x00, 0x05, 0xc9, 0x00, 0x25, 0xd1, 0x00, 0x84, 0xe9, 0x58, 0x85, 0xe1, 0xd7, 0x45, 0xe1, 0xff, 0x84, 0xea, 0xff, 0x44, 0xf3, 0xff, 0xa4, 0xf2, 0xff, 0xc4, 0xf3, 0xff, 0x45, 0xfd, 0xff, 0xc5, 0xfc, 0xff, 0x25, 0xfd, 0xff, 0x84, 0xfe, 0xff, 0x84, 0xfe, 0xe0, 0x44, 0xfe, 0x64, 0x04, 0xff, 0x00, 0xa5, 0xff, 0x00, 0x64, 0xff, 0x00, 0x64, 0xff, 0x00, 0x64, 0xff, 0x00, 
  0xe4, 0xc8, 0x00, 0xe4, 0xc8, 0x00, 0xe4, 0xc8, 0x00, 0x05, 0xc9, 0x00, 0x25, 0xd1, 0x00, 0x84, 0xe9, 0x00, 0x64, 0xe1, 0x00, 0x85, 0xe1, 0x54, 0xe4, 0xf2, 0xa4, 0x24, 0xf3, 0xcf, 0xa4, 0xf2, 0xe8, 0xc4, 0xf3, 0xf3, 0x45, 0xfd, 0xeb, 0xe5, 0xfc, 0xd3, 0xc5, 0xfc, 0xa8, 0x24, 0xfe, 0x5f, 0xa4, 0xfe, 0x04, 0x44, 0xfe, 0x00, 0x04, 0xff, 0x00, 0xa5, 0xff, 0x00, 0x64, 0xff, 0x00, 0x64, 0xff, 0x00, 0x64, 0xff, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0x20, 0xd1, 0x00, 0x20, 0xb0, 0x00, 0x28, 0xf1, 0x00, 0x39, 0x71, 0x00, 0x31, 0x51, 0x00, 0x31, 0x51, 0x00, 0x31, 0x31, 0x00, 0x31, 0x51, 0x3c, 0x31, 0xf3, 0x8f, 0x3a, 0x13, 0xc0, 0x39, 0xd3, 0xe4, 0x3a, 0x94, 0xf3, 0x33, 0x56, 0xe7, 0x33, 0x16, 0xc4, 0x32, 0xd5, 0x94, 0x3c, 0xfb, 0x47, 0x3d, 0xdd, 0x00, 0x3d, 0x7d, 0x00, 0x3d, 0xda, 0x00, 0x36, 0x38, 0x00, 0x36, 0x18, 0x00, 0x36, 0x18, 0x00, 0x36, 0x18, 0x00, 
  0x20, 0xd1, 0x00, 0x20, 0xb0, 0x00, 0x28, 0xf1, 0x00, 0x39, 0x71, 0x00, 0x31, 0x31, 0x00, 0x31, 0x51, 0x43, 0x31, 0x71, 0xc0, 0x31, 0x71, 0xff, 0x39, 0xf3, 0xff, 0x3a, 0x34, 0xff, 0x39, 0xf3, 0xff, 0x3a, 0x94, 0xff, 0x33, 0x56, 0xff, 0x32, 0xf5, 0xff, 0x3b, 0x76, 0xff, 0x3d, 0x9c, 0xff, 0x3d, 0xbd, 0xcc, 0x3d, 0x7d, 0x4f, 0x3d, 0xda, 0x00, 0x36, 0x37, 0x00, 0x36, 0x18, 0x00, 0x36, 0x18, 0x00, 0x36, 0x18, 0x00, 
  0x20, 0xb1, 0x00, 0x20, 0xb0, 0x00, 0x28, 0xf1, 0x00, 0x39, 0x71, 0x00, 0x39, 0x71, 0x87, 0x39, 0x91, 0xff, 0x31, 0x91, 0xff, 0x31, 0x71, 0xff, 0x39, 0xb2, 0xff, 0x3a, 0x34, 0xff, 0x39, 0xf3, 0xff, 0x3a, 0x94, 0xff, 0x33, 0x56, 0xff, 0x32, 0xb5, 0xff, 0x3c, 0x39, 0xff, 0x45, 0xfe, 0xff, 0x45, 0x7d, 0xff, 0x45, 0x7d, 0xff, 0x35, 0xf9, 0x98, 0x36, 0x18, 0x00, 0x36, 0x18, 0x00, 0x36, 0x18, 0x00, 0x36, 0x18, 0x00, 
  0x20, 0xd1, 0x00, 0x18, 0xb0, 0x00, 0x28, 0xf1, 0x00, 0x41, 0x91, 0x98, 0x41, 0x92, 0xff, 0x39, 0x91, 0xff, 0x31, 0x91, 0xff, 0x31, 0x71, 0xff, 0x31, 0x91, 0xff, 0x3a, 0x13, 0xff, 0x39, 0xf3, 0xff, 0x3a, 0x94, 0xff, 0x33, 0x56, 0xff, 0x32, 0xd5, 0xff, 0x3c, 0xfb, 0xff, 0x45, 0xfe, 0xff, 0x45, 0x7d, 0xff, 0x3d, 0xda, 0xff, 0x36, 0x37, 0xff, 0x36, 0x18, 0xaf, 0x36, 0x19, 0x00, 0x36, 0x1a, 0x00, 0x36, 0x1a, 0x00, 
  0x31, 0x31, 0x00, 0x31, 0x31, 0x00, 0x39, 0x51, 0x87, 0x39, 0x91, 0xff, 0x39, 0x91, 0xff, 0x41, 0x91, 0xff, 0x39, 0x91, 0xff, 0x31, 0x91, 0xff, 0x31, 0x71, 0xff, 0x39, 0xf3, 0xff, 0x39, 0xf3, 0xff, 0x3a, 0x94, 0xff, 0x33, 0x15, 0xff, 0x3b, 0x76, 0xff, 0x3d, 0x9d, 0xff, 0x45, 0x9e, 0xff, 0x3d, 0xbc, 0xff, 0x36, 0x38, 0xff, 0x36, 0x18, 0xff, 0x36, 0x1a, 0xff, 0x36, 0x19, 0xa0, 0x35, 0xf4, 0x00, 0x35, 0xf5, 0x00, 
  0x49, 0x52, 0x00, 0x49, 0x52, 0x34, 0x51, 0x92, 0xff, 0x41, 0x92, 0xff, 0x39, 0x91, 0xff, 0x39, 0x91, 0xff, 0x41, 0x91, 0xff, 0x39, 0x91, 0xff, 0x31, 0x71, 0xff, 0x39, 0xb2, 0xff, 0x39, 0xf3, 0xff, 0x3a, 0x94, 0xff, 0x32, 0xd5, 0xff, 0x3c, 0x39, 0xff, 0x45, 0xfe, 0xff, 0x45, 0x7d, 0xff, 0x36, 0x18, 0xff, 0x36, 0x38, 0xff, 0x36, 0x1b, 0xff, 0x36, 0x17, 0xff, 0x35, 0xcf, 0xff, 0x35, 0xac, 0x4c, 0x35, 0xad, 0x00, 
  0x49, 0x52, 0x00, 0x49, 0x92, 0xd0, 0x51, 0x92, 0xff, 0x51, 0x92, 0xff, 0x41, 0x92, 0xff, 0x39, 0x91, 0xff, 0x39, 0x91, 0xff, 0x39, 0x91, 0xff, 0x31, 0x71, 0xff, 0x31, 0x91, 0xff, 0x39, 0xf3, 0xff, 0x3a, 0x94, 0xff, 0x32, 0xf5, 0xff, 0x3c, 0xdb, 0xff, 0x45, 0xde, 0xff, 0x3d, 0xf9, 0xff, 0x36, 0x39, 0xff, 0x36, 0x1b, 0xff, 0x35, 0xf5, 0xff, 0x35, 0xad, 0xff, 0x35, 0xac, 0xff, 0x35, 0xae, 0xe8, 0x35, 0xae, 0x00, 
  0x49, 0x51, 0x37, 0x49, 0x92, 0xff, 0x49, 0x92, 0xff, 0x49, 0x92, 0xff, 0x51, 0x92, 0xff, 0x49, 0x92, 0xff, 0x41, 0x91, 0xff, 0x39, 0x91, 0xff, 0x39, 0x91, 0xff, 0x31, 0x71, 0xff, 0x39, 0xb2, 0xff, 0x3a, 0x74, 0xff, 0x33, 0x97, 0xff, 0x45, 0x7d, 0xff, 0x3d, 0xdb, 0xff, 0x36, 0x3a, 0xff, 0x36, 0x19, 0xff, 0x35, 0xd1, 0xff, 0x35, 0xac, 0xff, 0x2d, 0xae, 0xff, 0x2d, 0xaf, 0xff, 0x2d, 0xaf, 0xff, 0x35, 0xae, 0x50, 
  0x69, 0x4f, 0x94, 0x61, 0x70, 0xff, 0x59, 0x71, 0xff, 0x51, 0x92, 0xff, 0x49, 0x92, 0xff, 0x49, 0x92, 0xff, 0x49, 0x92, 0xff, 0x41, 0x92, 0xff, 0x39, 0x91, 0xff, 0x39, 0x71, 0xff, 0x31, 0x92, 0xff, 0x32, 0x33, 0xff, 0x3c, 0x3a, 0xff, 0x46, 0x1e, 0xff, 0x36, 0x3a, 0xff, 0x36, 0x16, 0xff, 0x35, 0xaf, 0xff, 0x2d, 0xad, 0xff, 0x2d, 0xaf, 0xff, 0x35, 0xae, 0xff, 0x35, 0xcd, 0xff, 0x3d, 0xcb, 0xff, 0x45, 0xc9, 0xac, 
  0x69, 0x4f, 0xc7, 0x71, 0x4f, 0xff, 0x71, 0x4f, 0xff, 0x69, 0x4f, 0xff, 0x61, 0x70, 0xff, 0x59, 0x71, 0xff, 0x51, 0x92, 0xff, 0x49, 0x92, 0xff, 0x41, 0x92, 0xff, 0x39, 0x71, 0xff, 0x31, 0x51, 0xff, 0x32, 0x34, 0xff, 0x3d, 0x1d, 0xff, 0x36, 0x7c, 0xff, 0x2d, 0xf4, 0xff, 0x2d, 0xad, 0xff, 0x35, 0xad, 0xff, 0x3d, 0xad, 0xff, 0x3d, 0xcb, 0xff, 0x45, 0xc9, 0xff, 0x4d, 0xc8, 0xff, 0x4d, 0xc8, 0xff, 0x4d, 0xc9, 0xd7, 
  0x61, 0x70, 0xe8, 0x61, 0x70, 0xff, 0x61, 0x70, 0xff, 0x69, 0x70, 0xff, 0x69, 0x70, 0xff, 0x69, 0x70, 0xff, 0x61, 0x70, 0xff, 0x59, 0x71, 0xff, 0x59, 0x92, 0xff, 0x49, 0x93, 0xff, 0x31, 0x32, 0xff, 0x1a, 0x79, 0xff, 0x1d, 0xfc, 0xff, 0x25, 0xf0, 0xff, 0x2d, 0x8a, 0xff, 0x2d, 0xaa, 0xff, 0x35, 0xaa, 0xff, 0x3d, 0xa9, 0xff, 0x3d, 0xa8, 0xff, 0x3d, 0xa9, 0xff, 0x35, 0xa9, 0xff, 0x35, 0xa9, 0xff, 0x35, 0xa9, 0xec, 
  0x79, 0x2d, 0xf7, 0x79, 0x2d, 0xff, 0x79, 0x2d, 0xff, 0x79, 0x2d, 0xff, 0x79, 0x2d, 0xff, 0x79, 0x2d, 0xff, 0x79, 0x2d, 0xff, 0x79, 0x0d, 0xff, 0x79, 0x0e, 0xff, 0x71, 0x2e, 0xff, 0x70, 0x4d, 0xff, 0x7b, 0x6e, 0xff, 0x6f, 0x2b, 0xff, 0x5d, 0xc7, 0xff, 0x65, 0xc8, 0xff, 0x65, 0xe8, 0xff, 0x6d, 0xe8, 0xff, 0x6e, 0x08, 0xff, 0x6e, 0x08, 0xff, 0x6e, 0x08, 0xff, 0x6e, 0x08, 0xff, 0x6e, 0x08, 0xff, 0x6e, 0x08, 0xf4, 
  0x90, 0xc9, 0xec, 0x90, 0xc9, 0xff, 0x90, 0xc9, 0xff, 0x90, 0xca, 0xff, 0x90, 0xca, 0xff, 0x88, 0xcb, 0xff, 0x90, 0xca, 0xff, 0x98, 0xc9, 0xff, 0x98, 0xc7, 0xff, 0xa8, 0xc5, 0xff, 0xc8, 0x24, 0xff, 0xfb, 0xa1, 0xff, 0xff, 0xe3, 0xff, 0xdf, 0x47, 0xff, 0xc6, 0xa7, 0xff, 0xb6, 0x87, 0xff, 0xa6, 0x47, 0xff, 0x9e, 0x47, 0xff, 0xa6, 0x47, 0xff, 0xae, 0x67, 0xff, 0xae, 0x67, 0xff, 0xae, 0x67, 0xff, 0xae, 0x67, 0xef, 
  0x88, 0xea, 0xcb, 0x88, 0xeb, 0xff, 0x88, 0xcb, 0xff, 0x88, 0xeb, 0xff, 0x90, 0xea, 0xff, 0x98, 0xe8, 0xff, 0xa8, 0xe6, 0xff, 0xa9, 0x05, 0xff, 0xb9, 0x04, 0xff, 0xc8, 0xe4, 0xff, 0xe9, 0x24, 0xff, 0xfb, 0x84, 0xff, 0xfe, 0xa4, 0xff, 0xff, 0xe4, 0xff, 0xf7, 0x66, 0xff, 0xdf, 0x07, 0xff, 0xce, 0xe7, 0xff, 0xbe, 0xa7, 0xff, 0xa6, 0x67, 0xff, 0x96, 0x47, 0xff, 0x8e, 0x28, 0xff, 0x96, 0x47, 0xff, 0x9e, 0x47, 0xd8, 
  0x88, 0xeb, 0x9b, 0x90, 0xea, 0xff, 0x98, 0xe8, 0xff, 0xa8, 0xe6, 0xff, 0xb1, 0x05, 0xff, 0xb1, 0x04, 0xff, 0xb1, 0x04, 0xff, 0xb9, 0x04, 0xff, 0xc8, 0xe5, 0xff, 0xd9, 0x05, 0xff, 0xe9, 0xc4, 0xff, 0xf3, 0xc5, 0xff, 0xfd, 0xc4, 0xff, 0xff, 0x04, 0xff, 0xff, 0xa4, 0xff, 0xf7, 0x66, 0xff, 0xe7, 0x27, 0xff, 0xe7, 0x27, 0xff, 0xe7, 0x27, 0xff, 0xd6, 0xe7, 0xff, 0xbe, 0xa7, 0xff, 0xa6, 0x67, 0xff, 0x9e, 0x47, 0xb3, 
  0xa8, 0xe6, 0x40, 0xb1, 0x05, 0xff, 0xb9, 0x04, 0xff, 0xb9, 0x04, 0xff, 0xa9, 0x05, 0xff, 0xb1, 0x05, 0xff, 0xc1, 0x05, 0xff, 0xd0, 0xe5, 0xff, 0xd1, 0x25, 0xff, 0xe9, 0x45, 0xff, 0xea, 0x24, 0xff, 0xf3, 0xe4, 0xff, 0xfd, 0x65, 0xff, 0xfe, 0x44, 0xff, 0xff, 0x04, 0xff, 0xff, 0xc4, 0xff, 0xf7, 0x65, 0xff, 0xe7, 0x27, 0xff, 0xdf, 0x07, 0xff, 0xe7, 0x27, 0xff, 0xef, 0x47, 0xff, 0xe7, 0x27, 0xff, 0xd6, 0xe7, 0x5c, 
  0xb1, 0x04, 0x00, 0xb1, 0x05, 0xdc, 0xb1, 0x05, 0xff, 0xa9, 0x05, 0xff, 0xb9, 0x05, 0xff, 0xd1, 0x05, 0xff, 0xd1, 0x05, 0xff, 0xc9, 0x05, 0xff, 0xe1, 0x65, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0xa4, 0xff, 0xf3, 0xe4, 0xff, 0xfd, 0x25, 0xff, 0xfe, 0x24, 0xff, 0xfe, 0x84, 0xff, 0xff, 0x44, 0xff, 0xff, 0xa4, 0xff, 0xff, 0x84, 0xff, 0xef, 0x46, 0xff, 0xdf, 0x07, 0xff, 0xdf, 0x07, 0xff, 0xe7, 0x27, 0xf3, 0xe7, 0x27, 0x00, 
  0xa8, 0xe5, 0x00, 0xa8, 0xe5, 0x43, 0xa9, 0x05, 0xff, 0xc1, 0x05, 0xff, 0xd1, 0x05, 0xff, 0xd1, 0x05, 0xff, 0xc8, 0xe5, 0xff, 0xe1, 0x65, 0xff, 0xe9, 0x45, 0xff, 0xea, 0x04, 0xff, 0xf2, 0xe4, 0xff, 0xf3, 0xc4, 0xff, 0xfc, 0xe5, 0xff, 0xfd, 0xc4, 0xff, 0xfe, 0xa4, 0xff, 0xfe, 0x84, 0xff, 0xff, 0x84, 0xff, 0xff, 0x84, 0xff, 0xff, 0x84, 0xff, 0xf7, 0x45, 0xff, 0xdf, 0x07, 0xff, 0xd7, 0x08, 0x5c, 0xdf, 0x07, 0x00, 
  0xc1, 0x05, 0x00, 0xc1, 0x05, 0x00, 0xc9, 0x05, 0x9b, 0xd1, 0x05, 0xff, 0xd1, 0x05, 0xff, 0xc8, 0xe5, 0xff, 0xd9, 0x45, 0xff, 0xe9, 0xa5, 0xff, 0xe1, 0x45, 0xff, 0xea, 0x84, 0xff, 0xf2, 0xe4, 0xff, 0xf3, 0xc4, 0xff, 0xfd, 0x05, 0xff, 0xfd, 0x45, 0xff, 0xfe, 0x84, 0xff, 0xfe, 0x44, 0xff, 0xfe, 0xc4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0x84, 0xff, 0xff, 0x84, 0xff, 0xff, 0x65, 0xb4, 0xef, 0x46, 0x00, 0xef, 0x45, 0x00, 
  0xd0, 0xe4, 0x00, 0xd0, 0xe4, 0x00, 0xd0, 0xe4, 0x00, 0xc9, 0x05, 0xaf, 0xc9, 0x05, 0xff, 0xd1, 0x25, 0xff, 0xe9, 0x85, 0xff, 0xe9, 0x65, 0xff, 0xe1, 0x85, 0xff, 0xf2, 0xe4, 0xff, 0xf2, 0xc4, 0xff, 0xf3, 0xc4, 0xff, 0xfd, 0x25, 0xff, 0xfc, 0xc5, 0xff, 0xfe, 0x24, 0xff, 0xfe, 0xa4, 0xff, 0xfe, 0x44, 0xff, 0xff, 0x24, 0xff, 0xff, 0xa4, 0xff, 0xff, 0x64, 0xc4, 0xff, 0x84, 0x00, 0xff, 0x84, 0x00, 0xff, 0x84, 0x00, 
  0xc8, 0xe4, 0x00, 0xc8, 0xc4, 0x00, 0xc8, 0xe4, 0x00, 0xc9, 0x05, 0x00, 0xc9, 0x05, 0x9f, 0xe1, 0x85, 0xff, 0xe9, 0xa5, 0xff, 0xe1, 0x45, 0xff, 0xea, 0x04, 0xff, 0xf3, 0x44, 0xff, 0xf2, 0xa4, 0xff, 0xf3, 0xc4, 0xff, 0xfd, 0x45, 0xff, 0xfc, 0xa5, 0xff, 0xfd, 0xa4, 0xff, 0xfe, 0xc4, 0xff, 0xfe, 0x44, 0xff, 0xfe, 0x64, 0xff, 0xff, 0x44, 0xaf, 0xff, 0x84, 0x00, 0xff, 0x64, 0x00, 0xff, 0x64, 0x00, 0xff, 0x64, 0x00, 
  0xc8, 0xe4, 0x00, 0xc8, 0xe4, 0x00, 0xc8, 0xe4, 0x00, 0xc9, 0x05, 0x00, 0xd1, 0x25, 0x00, 0xe9, 0x84, 0x58, 0xe1, 0x85, 0xd7, 0xe1, 0x45, 0xff, 0xea, 0x84, 0xff, 0xf3, 0x44, 0xff, 0xf2, 0xa4, 0xff, 0xf3, 0xc4, 0xff, 0xfd, 0x45, 0xff, 0xfc, 0xc5, 0xff, 0xfd, 0x25, 0xff, 0xfe, 0x84, 0xff, 0xfe, 0x84, 0xe0, 0xfe, 0x44, 0x64, 0xff, 0x04, 0x00, 0xff, 0xa5, 0x00, 0xff, 0x64, 0x00, 0xff, 0x64, 0x00, 0xff, 0x64, 0x00, 
  0xc8, 0xe4, 0x00, 0xc8, 0xe4, 0x00, 0xc8, 0xe4, 0x00, 0xc9, 0x05, 0x00, 0xd1, 0x25, 0x00, 0xe9, 0x84, 0x00, 0xe1, 0x64, 0x00, 0xe1, 0x85, 0x54, 0xf2, 0xe4, 0xa4, 0xf3, 0x24, 0xcf, 0xf2, 0xa4, 0xe8, 0xf3, 0xc4, 0xf3, 0xfd, 0x45, 0xeb, 0xfc, 0xe5, 0xd3, 0xfc, 0xc5, 0xa8, 0xfe, 0x24, 0x5f, 0xfe, 0xa4, 0x04, 0xfe, 0x44, 0x00, 0xff, 0x04, 0x00, 0xff, 0xa5, 0x00, 0xff, 0x64, 0x00, 0xff, 0x64, 0x00, 0xff, 0x64, 0x00, 
#endif
#if LV_COLOR_DEPTH == 32
  0x85, 0x17, 0x22, 0x00, 0x84, 0x14, 0x1f, 0x00, 0x87, 0x1d, 0x28, 0x00, 0x8b, 0x2d, 0x3b, 0x00, 0x8a, 0x28, 0x33, 0x00, 0x89, 0x27, 0x2f, 0x00, 0x87, 0x23, 0x2e, 0x00, 0x8a, 0x2a, 0x32, 0x3c, 0x98, 0x3e, 0x34, 0x8f, 0x9a, 0x41, 0x35, 0xc0, 0x96, 0x3a, 0x36, 0xe4, 0xa3, 0x4f, 0x35, 0xf3, 0xb2, 0x69, 0x34, 0xe7, 0xad, 0x61, 0x33, 0xc4, 0xa9, 0x5a, 0x32, 0x94, 0xd8, 0x9d, 0x38, 0x47, 0xec, 0xb9, 0x3c, 0x00, 0xe9, 0xac, 0x3b, 0x00, 0xd1, 0xb9, 0x35, 0x00, 0xbd, 0xc3, 0x31, 0x00, 0xc3, 0xc0, 0x32, 0x00, 0xc3, 0xc0, 0x32, 0x00, 0xc3, 0xc0, 0x32, 0x00, 
  0x85, 0x17, 0x22, 0x00, 0x84, 0x14, 0x1f, 0x00, 0x87, 0x1d, 0x28, 0x00, 0x8b, 0x2d, 0x3b, 0x00, 0x8a, 0x26, 0x32, 0x00, 0x89, 0x2a, 0x30, 0x43, 0x8a, 0x2d, 0x33, 0xc0, 0x89, 0x2e, 0x34, 0xff, 0x96, 0x3d, 0x36, 0xff, 0x9d, 0x45, 0x36, 0xff, 0x96, 0x3b, 0x36, 0xff, 0xa3, 0x4f, 0x35, 0xff, 0xb2, 0x69, 0x34, 0xff, 0xa9, 0x5b, 0x34, 0xff, 0xb4, 0x6b, 0x35, 0xff, 0xe4, 0xaf, 0x3c, 0xff, 0xe6, 0xb4, 0x3c, 0xcc, 0xeb, 0xab, 0x3c, 0x4f, 0xd3, 0xb8, 0x36, 0x00, 0xbc, 0xc3, 0x30, 0x00, 0xc3, 0xc0, 0x32, 0x00, 0xc3, 0xc0, 0x32, 0x00, 0xc3, 0xc0, 0x32, 0x00, 
  0x85, 0x16, 0x21, 0x00, 0x84, 0x13, 0x1e, 0x00, 0x86, 0x1c, 0x27, 0x00, 0x8b, 0x2d, 0x3a, 0x00, 0x8b, 0x2b, 0x38, 0x87, 0x8b, 0x30, 0x35, 0xff, 0x8b, 0x31, 0x34, 0xff, 0x88, 0x2c, 0x34, 0xff, 0x91, 0x36, 0x35, 0xff, 0x9d, 0x44, 0x36, 0xff, 0x96, 0x3b, 0x36, 0xff, 0xa3, 0x4f, 0x35, 0xff, 0xb2, 0x69, 0x34, 0xff, 0xa5, 0x54, 0x33, 0xff, 0xc6, 0x84, 0x37, 0xff, 0xee, 0xbe, 0x3d, 0xff, 0xe6, 0xad, 0x3d, 0xff, 0xe9, 0xac, 0x3e, 0xff, 0xcb, 0xbc, 0x34, 0x98, 0xbe, 0xc2, 0x30, 0x00, 0xc3, 0xc0, 0x32, 0x00, 0xc3, 0xc0, 0x32, 0x00, 0xc3, 0xc0, 0x32, 0x00, 
  0x85, 0x17, 0x20, 0x00, 0x83, 0x14, 0x1b, 0x00, 0x85, 0x1b, 0x25, 0x00, 0x8c, 0x2f, 0x3d, 0x98, 0x8d, 0x31, 0x3f, 0xff, 0x8c, 0x2f, 0x3a, 0xff, 0x8b, 0x2f, 0x32, 0xff, 0x89, 0x2d, 0x34, 0xff, 0x8c, 0x30, 0x34, 0xff, 0x9a, 0x41, 0x36, 0xff, 0x97, 0x3c, 0x36, 0xff, 0xa3, 0x4f, 0x35, 0xff, 0xb2, 0x67, 0x33, 0xff, 0xa8, 0x59, 0x33, 0xff, 0xd7, 0x9c, 0x3a, 0xff, 0xed, 0xbb, 0x3d, 0xff, 0xec, 0xab, 0x3e, 0xff, 0xd4, 0xb7, 0x37, 0xff, 0xbc, 0xc3, 0x30, 0xff, 0xc1, 0xc0, 0x32, 0xaf, 0xc8, 0xc1, 0x32, 0x00, 0xd1, 0xc2, 0x33, 0x00, 0xd1, 0xc2, 0x33, 0x00, 
  0x8a, 0x24, 0x32, 0x00, 0x8a, 0x23, 0x31, 0x00, 0x8b, 0x2a, 0x36, 0x87, 0x8c, 0x30, 0x3b, 0xff, 0x8c, 0x2f, 0x3c, 0xff, 0x8c, 0x2f, 0x3e, 0xff, 0x8c, 0x2f, 0x38, 0xff, 0x8b, 0x2f, 0x32, 0xff, 0x89, 0x2c, 0x34, 0xff, 0x96, 0x3c, 0x35, 0xff, 0x99, 0x3e, 0x37, 0xff, 0xa3, 0x4f, 0x35, 0xff, 0xac, 0x60, 0x33, 0xff, 0xb4, 0x6b, 0x35, 0xff, 0xe5, 0xb1, 0x3c, 0xff, 0xee, 0xb0, 0x3e, 0xff, 0xdd, 0xb3, 0x3a, 0xff, 0xbd, 0xc3, 0x30, 0xff, 0xc0, 0xc1, 0x31, 0xff, 0xd2, 0xc2, 0x33, 0xff, 0xc5, 0xc0, 0x32, 0xa0, 0xa4, 0xbc, 0x30, 0x00, 0xa6, 0xbc, 0x30, 0x00, 
  0x8d, 0x28, 0x46, 0x00, 0x8e, 0x2a, 0x4a, 0x34, 0x90, 0x32, 0x4e, 0xff, 0x8d, 0x2f, 0x40, 0xff, 0x8b, 0x2f, 0x38, 0xff, 0x8c, 0x2f, 0x3c, 0xff, 0x8c, 0x2f, 0x3d, 0xff, 0x8b, 0x2f, 0x35, 0xff, 0x88, 0x2b, 0x32, 0xff, 0x91, 0x36, 0x35, 0xff, 0x99, 0x3e, 0x37, 0xff, 0xa3, 0x50, 0x35, 0xff, 0xa8, 0x59, 0x32, 0xff, 0xc5, 0x83, 0x36, 0xff, 0xf2, 0xbc, 0x3e, 0xff, 0xe5, 0xae, 0x3d, 0xff, 0xc2, 0xc0, 0x32, 0xff, 0xc1, 0xc3, 0x31, 0xff, 0xd6, 0xc2, 0x33, 0xff, 0xbb, 0xbf, 0x32, 0xff, 0x7b, 0xb7, 0x2e, 0xff, 0x61, 0xb4, 0x2d, 0x4c, 0x66, 0xb5, 0x2d, 0x00, 
  0x8e, 0x29, 0x45, 0x00, 0x8f, 0x2f, 0x4c, 0xd0, 0x90, 0x30, 0x4f, 0xff, 0x8f, 0x2f, 0x4f, 0xff, 0x8d, 0x2f, 0x44, 0xff, 0x8c, 0x2f, 0x39, 0xff, 0x8c, 0x2f, 0x3a, 0xff, 0x8c, 0x2f, 0x3b, 0xff, 0x89, 0x2d, 0x33, 0xff, 0x8c, 0x30, 0x33, 0xff, 0x97, 0x3b, 0x36, 0xff, 0xa3, 0x50, 0x35, 0xff, 0xab, 0x5e, 0x32, 0xff, 0xd7, 0x9a, 0x39, 0xff, 0xf4, 0xb8, 0x40, 0xff, 0xcb, 0xbb, 0x35, 0xff, 0xc5, 0xc5, 0x30, 0xff, 0xd5, 0xc2, 0x33, 0xff, 0xa6, 0xbc, 0x30, 0xff, 0x69, 0xb5, 0x2d, 0xff, 0x63, 0xb4, 0x2d, 0xff, 0x72, 0xb6, 0x2e, 0xe8, 0x73, 0xb6, 0x2d, 0x00, 
  0x8c, 0x29, 0x49, 0x37, 0x92, 0x31, 0x49, 0xff, 0x93, 0x30, 0x46, 0xff, 0x91, 0x2f, 0x4a, 0xff, 0x90, 0x2f, 0x4f, 0xff, 0x8e, 0x2f, 0x49, 0xff, 0x8c, 0x2f, 0x3d, 0xff, 0x8c, 0x2f, 0x3a, 0xff, 0x8b, 0x2f, 0x38, 0xff, 0x89, 0x2c, 0x32, 0xff, 0x92, 0x36, 0x36, 0xff, 0xa0, 0x4b, 0x35, 0xff, 0xb7, 0x6f, 0x34, 0xff, 0xec, 0xad, 0x3e, 0xff, 0xdb, 0xba, 0x39, 0xff, 0xcd, 0xc6, 0x31, 0xff, 0xc6, 0xc1, 0x32, 0xff, 0x8c, 0xb9, 0x2f, 0xff, 0x61, 0xb4, 0x2d, 0xff, 0x6d, 0xb5, 0x2c, 0xff, 0x7b, 0xb6, 0x2a, 0xff, 0x79, 0xb6, 0x2a, 0xff, 0x71, 0xb6, 0x2e, 0x50, 
  0x7c, 0x28, 0x67, 0x94, 0x82, 0x2d, 0x61, 0xff, 0x88, 0x2d, 0x57, 0xff, 0x8d, 0x2f, 0x4e, 0xff, 0x92, 0x30, 0x47, 0xff, 0x93, 0x30, 0x49, 0xff, 0x91, 0x2f, 0x4b, 0xff, 0x8d, 0x2f, 0x42, 0xff, 0x8c, 0x2f, 0x3a, 0xff, 0x88, 0x2b, 0x35, 0xff, 0x8d, 0x31, 0x34, 0xff, 0x9a, 0x44, 0x34, 0xff, 0xcd, 0x85, 0x39, 0xff, 0xf0, 0xc1, 0x3d, 0xff, 0xd2, 0xc4, 0x31, 0xff, 0xaf, 0xbf, 0x31, 0xff, 0x76, 0xb6, 0x2d, 0xff, 0x67, 0xb3, 0x29, 0xff, 0x77, 0xb6, 0x2a, 0xff, 0x72, 0xb6, 0x2e, 0xff, 0x66, 0xb7, 0x34, 0xff, 0x58, 0xb7, 0x3b, 0xff, 0x4c, 0xb7, 0x41, 0xac, 
  0x7a, 0x28, 0x6b, 0xc7, 0x79, 0x29, 0x6f, 0xff, 0x79, 0x29, 0x6e, 0xff, 0x7c, 0x2a, 0x69, 0xff, 0x82, 0x2b, 0x60, 0xff, 0x88, 0x2d, 0x57, 0xff, 0x8e, 0x2f, 0x50, 0xff, 0x92, 0x30, 0x4b, 0xff, 0x92, 0x30, 0x42, 0xff, 0x8c, 0x2d, 0x38, 0xff, 0x87, 0x2a, 0x31, 0xff, 0x9d, 0x44, 0x34, 0xff, 0xea, 0xa1, 0x3c, 0xff, 0xe0, 0xcd, 0x33, 0xff, 0x9d, 0xbe, 0x2a, 0xff, 0x6c, 0xb4, 0x29, 0xff, 0x65, 0xb4, 0x2e, 0xff, 0x67, 0xb6, 0x35, 0xff, 0x58, 0xb7, 0x3b, 0xff, 0x4b, 0xb7, 0x42, 0xff, 0x43, 0xb7, 0x46, 0xff, 0x41, 0xb7, 0x47, 0xff, 0x45, 0xb7, 0x45, 0xd7, 
  0x83, 0x2c, 0x63, 0xe8, 0x83, 0x2d, 0x63, 0xff, 0x83, 0x2d, 0x63, 0xff, 0x82, 0x2c, 0x65, 0xff, 0x80, 0x2c, 0x68, 0xff, 0x80, 0x2c, 0x68, 0xff, 0x83, 0x2d, 0x62, 0xff, 0x89, 0x2e, 0x5b, 0xff, 0x8f, 0x30, 0x55, 0xff, 0x95, 0x31, 0x49, 0xff, 0x8f, 0x23, 0x30, 0xff, 0xc7, 0x4e, 0x15, 0xff, 0xe0, 0xbb, 0x17, 0xff, 0x7e, 0xbd, 0x1e, 0xff, 0x54, 0xb1, 0x26, 0xff, 0x54, 0xb3, 0x2c, 0xff, 0x4f, 0xb4, 0x33, 0xff, 0x45, 0xb4, 0x37, 0xff, 0x43, 0xb4, 0x38, 0xff, 0x48, 0xb4, 0x36, 0xff, 0x4b, 0xb4, 0x34, 0xff, 0x4c, 0xb4, 0x34, 0xff, 0x4b, 0xb4, 0x34, 0xec, 
  0x6b, 0x24, 0x77, 0xf7, 0x6b, 0x24, 0x77, 0xff, 0x6b, 0x24, 0x77, 0xff, 0x6b, 0x24, 0x77, 0xff, 0x6b, 0x24, 0x77, 0xff, 0x6a, 0x24, 0x77, 0xff, 0x6a, 0x23, 0x79, 0xff, 0x6c, 0x22, 0x79, 0xff, 0x70, 0x22, 0x76, 0xff, 0x71, 0x23, 0x73, 0xff, 0x69, 0x07, 0x6e, 0xff, 0x73, 0x6c, 0x7c, 0xff, 0x5a, 0xe3, 0x6c, 0xff, 0x37, 0xb9, 0x59, 0xff, 0x3d, 0xba, 0x5e, 0xff, 0x3d, 0xbc, 0x63, 0xff, 0x40, 0xbe, 0x67, 0xff, 0x43, 0xbf, 0x68, 0xff, 0x44, 0xbf, 0x67, 0xff, 0x43, 0xbf, 0x67, 0xff, 0x43, 0xbf, 0x67, 0xff, 0x43, 0xbf, 0x67, 0xff, 0x43, 0xbf, 0x67, 0xf4, 
  0x4c, 0x19, 0x92, 0xec, 0x4c, 0x19, 0x92, 0xff, 0x4c, 0x19, 0x92, 0xff, 0x4d, 0x18, 0x91, 0xff, 0x52, 0x18, 0x8e, 0xff, 0x55, 0x18, 0x8a, 0xff, 0x52, 0x18, 0x8d, 0xff, 0x48, 0x19, 0x95, 0xff, 0x3a, 0x1a, 0x9b, 0xff, 0x2c, 0x18, 0xa6, 0xff, 0x1e, 0x03, 0xc9, 0xff, 0x0a, 0x73, 0xff, 0xff, 0x15, 0xff, 0xff, 0xff, 0x36, 0xe9, 0xdb, 0xff, 0x3c, 0xd5, 0xc0, 0xff, 0x3a, 0xd0, 0xb1, 0xff, 0x3a, 0xca, 0xa0, 0xff, 0x3b, 0xc8, 0x99, 0xff, 0x3a, 0xca, 0x9e, 0xff, 0x3a, 0xcc, 0xa5, 0xff, 0x3a, 0xcd, 0xa7, 0xff, 0x3a, 0xcd, 0xa7, 0xff, 0x3a, 0xcd, 0xa7, 0xef, 
  0x54, 0x1b, 0x8b, 0xcb, 0x58, 0x1b, 0x88, 0xff, 0x5c, 0x1a, 0x85, 0xff, 0x58, 0x1b, 0x88, 0xff, 0x4e, 0x1b, 0x91, 0xff, 0x40, 0x1d, 0x9c, 0xff, 0x32, 0x1e, 0xa5, 0xff, 0x26, 0x1f, 0xaa, 0xff, 0x1e, 0x1f, 0xb9, 0xff, 0x20, 0x1b, 0xcb, 0xff, 0x22, 0x24, 0xe6, 0xff, 0x24, 0x71, 0xf9, 0xff, 0x1f, 0xd5, 0xff, 0xff, 0x22, 0xfb, 0xff, 0xff, 0x32, 0xee, 0xf1, 0xff, 0x3c, 0xe1, 0xda, 0xff, 0x3b, 0xdc, 0xcb, 0xff, 0x3a, 0xd6, 0xbb, 0xff, 0x3b, 0xce, 0xa4, 0xff, 0x3c, 0xc7, 0x93, 0xff, 0x3d, 0xc5, 0x8c, 0xff, 0x3c, 0xc7, 0x90, 0xff, 0x3c, 0xc9, 0x97, 0xd8, 
  0x57, 0x1b, 0x89, 0x9b, 0x4e, 0x1b, 0x90, 0xff, 0x40, 0x1d, 0x9b, 0xff, 0x32, 0x1e, 0xa7, 0xff, 0x25, 0x1f, 0xb1, 0xff, 0x1e, 0x20, 0xb3, 0xff, 0x20, 0x1f, 0xaf, 0xff, 0x24, 0x20, 0xbc, 0xff, 0x27, 0x1e, 0xca, 0xff, 0x27, 0x20, 0xdb, 0xff, 0x23, 0x39, 0xec, 0xff, 0x25, 0x78, 0xf4, 0xff, 0x24, 0xb7, 0xfd, 0xff, 0x1f, 0xe2, 0xff, 0xff, 0x21, 0xf6, 0xfc, 0xff, 0x2d, 0xeb, 0xee, 0xff, 0x3a, 0xe4, 0xe1, 0xff, 0x3b, 0xe5, 0xe3, 0xff, 0x37, 0xe4, 0xe0, 0xff, 0x38, 0xdd, 0xce, 0xff, 0x3a, 0xd6, 0xba, 0xff, 0x3b, 0xce, 0xa4, 0xff, 0x3c, 0xc8, 0x95, 0xb3, 
  0x2f, 0x1d, 0xa7, 0x40, 0x27, 0x1f, 0xaf, 0xff, 0x1e, 0x20, 0xb6, 0xff, 0x20, 0x20, 0xb5, 0xff, 0x25, 0x1f, 0xab, 0xff, 0x27, 0x1f, 0xaf, 0xff, 0x27, 0x21, 0xc4, 0xff, 0x27, 0x1e, 0xcd, 0xff, 0x27, 0x24, 0xd4, 0xff, 0x25, 0x2a, 0xe6, 0xff, 0x23, 0x46, 0xeb, 0xff, 0x24, 0x7d, 0xf4, 0xff, 0x26, 0xae, 0xfb, 0xff, 0x22, 0xca, 0xfe, 0xff, 0x22, 0xe2, 0xfc, 0xff, 0x20, 0xf8, 0xfd, 0xff, 0x27, 0xec, 0xf4, 0xff, 0x36, 0xe3, 0xde, 0xff, 0x3c, 0xe0, 0xd7, 0xff, 0x38, 0xe5, 0xe4, 0xff, 0x37, 0xe7, 0xe9, 0xff, 0x38, 0xe3, 0xdf, 0xff, 0x39, 0xdd, 0xcf, 0x5c, 
  0x20, 0x1f, 0xb4, 0x00, 0x25, 0x1f, 0xb1, 0xdc, 0x27, 0x1f, 0xad, 0xff, 0x27, 0x1f, 0xa8, 0xff, 0x27, 0x20, 0xb6, 0xff, 0x27, 0x21, 0xcd, 0xff, 0x27, 0x1f, 0xcf, 0xff, 0x27, 0x20, 0xcc, 0xff, 0x25, 0x2c, 0xe3, 0xff, 0x25, 0x33, 0xe8, 0xff, 0x23, 0x53, 0xec, 0xff, 0x24, 0x7c, 0xf4, 0xff, 0x27, 0xa3, 0xfa, 0xff, 0x23, 0xc3, 0xfd, 0xff, 0x21, 0xd0, 0xfe, 0xff, 0x24, 0xea, 0xf9, 0xff, 0x20, 0xf6, 0xfc, 0xff, 0x22, 0xef, 0xfc, 0xff, 0x30, 0xe7, 0xe7, 0xff, 0x3c, 0xe0, 0xd5, 0xff, 0x3a, 0xe1, 0xd7, 0xff, 0x38, 0xe3, 0xde, 0xf3, 0x38, 0xe5, 0xe3, 0x00, 
  0x25, 0x1e, 0xaa, 0x00, 0x26, 0x1e, 0xa9, 0x43, 0x28, 0x1f, 0xab, 0xff, 0x27, 0x20, 0xbe, 0xff, 0x27, 0x21, 0xd1, 0xff, 0x27, 0x21, 0xcf, 0xff, 0x27, 0x1e, 0xc6, 0xff, 0x26, 0x2c, 0xdd, 0xff, 0x25, 0x2a, 0xe7, 0xff, 0x24, 0x3f, 0xe7, 0xff, 0x22, 0x5d, 0xef, 0xff, 0x24, 0x7a, 0xf3, 0xff, 0x27, 0x9e, 0xfa, 0xff, 0x24, 0xb7, 0xfb, 0xff, 0x21, 0xd3, 0xff, 0xff, 0x22, 0xd1, 0xfd, 0xff, 0x24, 0xf1, 0xf7, 0xff, 0x22, 0xf2, 0xfa, 0xff, 0x20, 0xf0, 0xfe, 0xff, 0x2a, 0xea, 0xef, 0xff, 0x39, 0xe1, 0xda, 0xff, 0x3d, 0xdf, 0xd4, 0x5c, 0x3b, 0xe0, 0xd6, 0x00, 
  0x25, 0x20, 0xc0, 0x00, 0x25, 0x20, 0xbe, 0x00, 0x26, 0x20, 0xc5, 0x9b, 0x27, 0x22, 0xd2, 0xff, 0x27, 0x21, 0xce, 0xff, 0x27, 0x1d, 0xc6, 0xff, 0x26, 0x27, 0xd5, 0xff, 0x25, 0x33, 0xe9, 0xff, 0x25, 0x28, 0xe3, 0xff, 0x24, 0x4f, 0xeb, 0xff, 0x22, 0x5e, 0xef, 0xff, 0x24, 0x7a, 0xf3, 0xff, 0x27, 0xa1, 0xfa, 0xff, 0x25, 0xa7, 0xfa, 0xff, 0x22, 0xd2, 0xfd, 0xff, 0x21, 0xc9, 0xfe, 0xff, 0x23, 0xda, 0xfb, 0xff, 0x24, 0xf5, 0xf7, 0xff, 0x24, 0xef, 0xf8, 0xff, 0x20, 0xf0, 0xfe, 0xff, 0x26, 0xed, 0xf5, 0xb4, 0x2d, 0xe8, 0xea, 0x00, 0x2c, 0xe9, 0xeb, 0x00, 
  0x22, 0x1d, 0xd0, 0x00, 0x20, 0x1c, 0xd1, 0x00, 0x22, 0x1d, 0xcf, 0x00, 0x27, 0x22, 0xcc, 0xaf, 0x28, 0x1f, 0xc7, 0xff, 0x27, 0x23, 0xcf, 0xff, 0x25, 0x32, 0xe7, 0xff, 0x25, 0x2d, 0xe5, 0xff, 0x25, 0x31, 0xe4, 0xff, 0x23, 0x5e, 0xef, 0xff, 0x22, 0x59, 0xee, 0xff, 0x24, 0x7a, 0xf3, 0xff, 0x27, 0xa6, 0xfa, 0xff, 0x27, 0x99, 0xf9, 0xff, 0x23, 0xc5, 0xfc, 0xff, 0x21, 0xd4, 0xfe, 0xff, 0x22, 0xc7, 0xfe, 0xff, 0x23, 0xe3, 0xfa, 0xff, 0x24, 0xf5, 0xf7, 0xff, 0x24, 0xed, 0xf8, 0xc4, 0x22, 0xef, 0xfb, 0x00, 0x1f, 0xf0, 0xfe, 0x00, 0x1f, 0xf0, 0xfd, 0x00, 
  0x21, 0x1c, 0xcb, 0x00, 0x20, 0x1a, 0xcb, 0x00, 0x22, 0x1d, 0xcc, 0x00, 0x27, 0x21, 0xcb, 0x00, 0x27, 0x21, 0xcc, 0x9f, 0x25, 0x2f, 0xe2, 0xff, 0x25, 0x33, 0xe8, 0xff, 0x25, 0x27, 0xe1, 0xff, 0x24, 0x40, 0xe8, 0xff, 0x23, 0x68, 0xf1, 0xff, 0x22, 0x55, 0xed, 0xff, 0x24, 0x7a, 0xf3, 0xff, 0x27, 0xa8, 0xfb, 0xff, 0x27, 0x94, 0xf8, 0xff, 0x24, 0xb5, 0xfb, 0xff, 0x21, 0xd8, 0xfe, 0xff, 0x22, 0xca, 0xfd, 0xff, 0x22, 0xcc, 0xfd, 0xff, 0x24, 0xea, 0xf9, 0xaf, 0x24, 0xf1, 0xf8, 0x00, 0x24, 0xee, 0xf8, 0x00, 0x23, 0xee, 0xf8, 0x00, 0x23, 0xee, 0xf8, 0x00, 
  0x21, 0x1c, 0xcb, 0x00, 0x20, 0x1b, 0xcb, 0x00, 0x22, 0x1d, 0xcc, 0x00, 0x28, 0x20, 0xc9, 0x00, 0x26, 0x25, 0xd2, 0x00, 0x24, 0x31, 0xe8, 0x58, 0x25, 0x2f, 0xe4, 0xd7, 0x25, 0x29, 0xe2, 0xff, 0x24, 0x51, 0xec, 0xff, 0x23, 0x69, 0xf1, 0xff, 0x22, 0x55, 0xed, 0xff, 0x24, 0x7a, 0xf3, 0xff, 0x26, 0xa8, 0xfb, 0xff, 0x27, 0x98, 0xf9, 0xff, 0x25, 0xa5, 0xf9, 0xff, 0x22, 0xd0, 0xfd, 0xff, 0x22, 0xd0, 0xfd, 0xe0, 0x22, 0xc7, 0xfe, 0x64, 0x23, 0xe0, 0xfa, 0x00, 0x25, 0xf4, 0xf7, 0x00, 0x24, 0xee, 0xf8, 0x00, 0x23, 0xee, 0xf8, 0x00, 0x23, 0xee, 0xf8, 0x00, 
  0x21, 0x1c, 0xcb, 0x00, 0x20, 0x1b, 0xcb, 0x00, 0x22, 0x1d, 0xcc, 0x00, 0x28, 0x20, 0xc9, 0x00, 0x26, 0x24, 0xd1, 0x00, 0x24, 0x30, 0xe6, 0x00, 0x24, 0x2c, 0xe4, 0x00, 0x25, 0x31, 0xe4, 0x54, 0x23, 0x5d, 0xee, 0xa4, 0x23, 0x65, 0xf0, 0xcf, 0x22, 0x55, 0xed, 0xe8, 0x24, 0x7a, 0xf3, 0xf3, 0x26, 0xa8, 0xfb, 0xeb, 0x26, 0x9d, 0xf9, 0xd3, 0x26, 0x9a, 0xf9, 0xa8, 0x22, 0xc5, 0xfc, 0x5f, 0x22, 0xd4, 0xfe, 0x04, 0x22, 0xc9, 0xfe, 0x00, 0x23, 0xe2, 0xfa, 0x00, 0x25, 0xf3, 0xf7, 0x00, 0x24, 0xee, 0xf8, 0x00, 0x23, 0xee, 0xf8, 0x00, 0x23, 0xee, 0xf8, 0x00, 
#endif
};

const lv_img_dsc_t lv_demo_printer_icon_hue = {
  .header.always_zero = 0,
  .header.w = 23,
  .header.h = 23,
  .data_size = 529 * LV_IMG_PX_SIZE_ALPHA_BYTE,
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .data = lv_demo_printer_icon_hue_map,
};

#endif /*LV_USE_DEMO_PRINTER*/
