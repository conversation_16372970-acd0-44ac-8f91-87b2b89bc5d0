#ifndef __CONST_H_
#define __CONST_H_

#include "Auxdio_protocol.h"

#if defined(USE_SSD212) || defined(USE_SSD202)
extern int  g_device_moduleId;	//设备型号ID
#endif
extern int g_screen_width,g_screen_height;  //屏幕尺寸

#if USE_ASM9260
#define     IS_DISP_RES_1024        true
#define     IS_DISP_RES_1280        false
#define     IS_DISP_RES_800         false
#define     IS_DISP_RES_600         false
#else
#define     IS_DISP_RES_1024        (g_screen_width == 1024)    //1024x600
#define     IS_DISP_RES_1280        (g_screen_width == 1280)    //1280x800
#define     IS_DISP_RES_800         (g_screen_width == 800)     //800x480
#define     IS_DISP_RES_600         (g_screen_width == 600)    //600x1024
#endif

#define     XRatio_1024_to_1280(x)    ((x * 1280 + 1023) / 1024)
#define     YRatio_600_to_800(x)    ((x * 800 + 599) / 600)

#define 	CHINESE 		        0
#define 	ENGLISH 		        1
#define 	ZHTW 		            2
#define     DEFAULT_LANGUAGE        CHINESE


#define APP_ALL_GENERAL             0
#define APP_AISP_GENERAL            1
#define APP_JUSBE_GENERAL           2
#define APP_JUSBE_MIXER             3
#define APP_SCGJ_GENERAL            4
#define APP_LZY_TRANSMITTER         5
#define APP_LZY_WAN                 6
#define APP_LZY_NEW_TRANSMITTER     7   //龙之音新版发射机支持歌曲上传
#define APP_LZY_NEW_WAN             8   //龙之音新版寻呼台支持歌曲上传
#define APP_C6A1                    9
#define APP_C5A1                    10  //C5A1伟声客户定制告警声
#define APP_AIPU_GENERAL            11  //艾普
#define APP_AIPU_MIXER              12  //调音台
#define APP_BLUE_GENERAL            50  //蓝色，测试用
#define APP_CYAN_GENERAL            51  //青色，测试用
#define APP_RED_GENERAL             52  //红色，测试用

#define APP_LZY_COMMERCIAL_STANDARD    80  //青色，龙之音商用版本-话筒
#define APP_LZY_COMMERCIAL_TRANSMITTER 81  //青色，龙之音商用版本-发射机

#define APP_TYPE APP_AIPU_MIXER

#define IS_AIPU_MIXER                (APP_TYPE == APP_AIPU_MIXER)

#define LZY_COMMERCIAL_VERISON      (APP_TYPE == APP_LZY_COMMERCIAL_STANDARD || APP_TYPE == APP_LZY_COMMERCIAL_TRANSMITTER)

#define IS_TRANSMITTER              (APP_TYPE==APP_LZY_TRANSMITTER || APP_TYPE==APP_LZY_NEW_TRANSMITTER || APP_TYPE==APP_LZY_COMMERCIAL_TRANSMITTER)
#if defined(USE_SSD212) || defined(USE_SSD202)
#define IS_LZY_NEW_TRANSMITTER_OR_PAGER      (APP_TYPE==APP_LZY_NEW_TRANSMITTER || APP_TYPE==APP_LZY_NEW_WAN)
    #if IS_LZY_NEW_TRANSMITTER_OR_PAGER
    #define IS_LZY_LIMIT_PAGING_DURATION_30MIN    1
    #define IS_LZY_LIMIT_PAGING_DURATION_120MIN    0
    #endif
#else
#define IS_LZY_NEW_TRANSMITTER_OR_PAGER      0
#endif
#define IS_APP_HIDE_LOGIN_ACCOUNT   (APP_TYPE==APP_LZY_TRANSMITTER || APP_TYPE==APP_LZY_WAN || APP_TYPE==APP_LZY_NEW_TRANSMITTER || APP_TYPE==APP_LZY_NEW_WAN ||\
                                    APP_TYPE==APP_LZY_COMMERCIAL_TRANSMITTER)
#define IS_APP_LZY_VERSION          (APP_TYPE==APP_LZY_TRANSMITTER || APP_TYPE==APP_LZY_WAN || APP_TYPE==APP_LZY_NEW_TRANSMITTER || APP_TYPE==APP_LZY_NEW_WAN)
#define IS_APP_SUPPORT_CALL        1//!(APP_TYPE==APP_LZY_TRANSMITTER || APP_TYPE==APP_LZY_WAN || APP_TYPE==APP_LZY_NEW_TRANSMITTER || APP_TYPE==APP_LZY_NEW_WAN)

#if (APP_TYPE == APP_ALL_GENERAL)
#define CUSTOMER_ID   0
#define CUSTOMER_FUNCTION   "A0"
#elif (APP_TYPE == APP_AISP_GENERAL)
#define CUSTOMER_ID   1
#define CUSTOMER_FUNCTION   "A0"
#define SUPPORT_AUTO_TRIGGER     0          //打开自动触发功能支持
#define DISABLE_UDISK_FUNCTION   0          //关闭U盘支持
#define HIDE_CONTROL_BACK_BUTTON 0          //隐藏控制页面的返回按钮
#define DISBALE_NETWORK_SETTING_AND_FIXED_STATIC  0      //禁止网络设置（固定为静态IP）
#elif (APP_TYPE == APP_SCGJ_GENERAL)
#define CUSTOMER_ID   2
#define CUSTOMER_FUNCTION   "A7"
#elif (APP_TYPE == APP_JUSBE_GENERAL)
#define CUSTOMER_ID   3
#define CUSTOMER_FUNCTION   "A0"
#elif (APP_TYPE == APP_JUSBE_MIXER)
#define CUSTOMER_ID   3
#define CUSTOMER_FUNCTION   "A1"
#elif (APP_TYPE == APP_LZY_TRANSMITTER)
#define CUSTOMER_ID   4
#define CUSTOMER_FUNCTION   "A1"
#elif (APP_TYPE == APP_LZY_WAN)
#define CUSTOMER_ID   4
#define CUSTOMER_FUNCTION   "A2"
#elif (APP_TYPE == APP_LZY_NEW_TRANSMITTER)
#define CUSTOMER_ID   4
#define CUSTOMER_FUNCTION   "B1"
#elif (APP_TYPE == APP_LZY_NEW_WAN)
#define CUSTOMER_ID   4
#define CUSTOMER_FUNCTION   "B2"
#elif (APP_TYPE == APP_C6A1)
#define CUSTOMER_ID   6
#define CUSTOMER_FUNCTION   "A1"
#elif (APP_TYPE == APP_C5A1)
#define CUSTOMER_ID   5
#define CUSTOMER_FUNCTION   "A1"
#elif (APP_TYPE == APP_AIPU_GENERAL)
#define CUSTOMER_ID   8
#define CUSTOMER_FUNCTION   "A0"
#elif (APP_TYPE == APP_AIPU_MIXER)
#define CUSTOMER_ID   8
#define CUSTOMER_FUNCTION   "B0"
#elif (APP_TYPE == APP_BLUE_GENERAL)
#define CUSTOMER_ID   9
#define CUSTOMER_FUNCTION   "A0"
#elif (APP_TYPE == APP_CYAN_GENERAL)
#define CUSTOMER_ID   9
#define CUSTOMER_FUNCTION   "A1"
#elif (APP_TYPE == APP_RED_GENERAL)
#define CUSTOMER_ID   9
#define CUSTOMER_FUNCTION   "A2"

#elif (APP_TYPE == APP_LZY_COMMERCIAL_STANDARD)
#define CUSTOMER_ID   4
#define CUSTOMER_FUNCTION   "C0"
#define LZY_COMMERCIAL_LCD_800_480   1          //是否是5寸LCD，是的话注意关闭USBDISK
#elif (APP_TYPE == APP_LZY_COMMERCIAL_TRANSMITTER)
#define CUSTOMER_ID   4
#define CUSTOMER_FUNCTION   "C1"
#define LZY_COMMERCIAL_LCD_800_480   1          //是否是5寸LCD，是的话注意关闭USBDISK
#endif

#if defined(USE_SSD212) || defined(USE_SSD202)
#define DEFAULT_MODULE_ID   1
#endif

#if USE_SSD212
#define VERSION    "V2.5.0703"              /*本地软件版本*/
#elif USE_SSD202
#define VERSION    "V2.5.0326"              /*本地软件版本*/
#else
#define VERSION    "V2.5.0703"              /*本地软件版本*/
#endif

#define SUPPORT_TCP_DEFAULT      1       //打开主机转发寻呼台音频数据流的支持
#define LIMIT_MINIMUM_FUNCTION   0       //屏蔽控制类别、对讲、本地监听、网络音乐、udisk

//非调音台版本，还是启用G722，因为旧版本终端需要升级才能支持G722.1
#if (APP_TYPE == APP_JUSBE_MIXER)
#define SUPPORT_CODEC_G722       0      //支持G722编解码
#define ENABLE_CALL_FUNCTION     0      //打开对讲功能
#else
#define SUPPORT_CODEC_G722       1      //支持G722编解码
#define ENABLE_CALL_FUNCTION     1      //打开对讲功能
#endif

#if SUPPORT_CODEC_G722
    #define SUPPORT_CODEC_G722_1     0      //支持G722.1编解码
#else
    #define SUPPORT_CODEC_G722_1     1      //支持G722.1编解码
#endif

#define DEFAULT_PC_WAN_MODE_TEST    1       //PC模拟器默认WAN模式(不影响发行版)
#define ENABLE_LISTEN_FUNCTION   0     //打开监听功能

#define PC_MULTI_PROCESS_TEST       0       //PC端多进程测试（多开）

/***********************************************************/
#define CLOSE_WATCHDOG 0				//1关闭开门狗  0开启开门狗（默认）
#define test_NOI2C 0					//去掉I2C读写(裸机测试时为1去除，默认为0不去除)

#define NETWORK_VPN_INTERNET		0		//VPN INTERNET环境，g_Is_tcp_real_internet固定为1
/***********************************************************/


/***********************************************************
 * 	音量
*/
#define DEFAULT_ZONE_PAGING_VOLUME      80
#define DEFAULT_LISTEN_OUTPUT_VOLUME    80
#define DEFAULT_LISTEN_CALL_VOLUME      80
#define DEFAULT_LISTEN_RING_VOLUME      80

/***********************************************************
 * 	设备节点
*/
#define DSP                 "/dev/dsp"
#define WATDOG_DEV          "/dev/watchdog"
#define WDT_TIMEOUT         30  /*看门狗超时重启时间，单位为秒*/

#define NET_LOCAL_DEV_NAME	"eth0"
#define NET_4G_DEV_NAME	    "eth1"
/***********************************************************/

#define IS_SERVER_CONNECTED (g_host_device_TimeOut!=-1)

#define INI_SECTION_BASIC   		    "Basic"
#define INI_SETCION_MFR 			    "Manufacturer"
#define INI_SETCION_DSP_Firmware 	    "Firmware"

#if defined(USE_SSD212) || defined(USE_SSD202)
#define CONFIG_DIR_PATH				    "/customer/App/Config"
#define BASIC_INI_FILE 	                "/customer/App/Config/config_basic.ini"
#define MFR_CONFIG_INI_FILE 	        "/customer/App/Config/config_man.ini"
#define DSP_FIRMWARE_CONFIG_INI_FILE    "/customer/App/Config/dsp_firmware_config"

#define FILE_DEVICE_ALIAS               "/customer/App/device_alias"//设备名称
#define MUSICLISTFILE                   "/customer/App/MusicList"	//歌曲列表文件
#define RECORD_INFO_FILE                "/customer/App/RecordInfo"	//记录文件
#define USERINFOFILE                    "/customer/App/Userinfo"	//用户信息文件
#define ZONEINFOFILE                    "/customer/App/Zoneinfo"	//分区文件
#define GROUPINFOFILE                   "/customer/App/Groupinfo"	//分组文件

#define UPDATE_SAVE_PATH                "/customer/App/Update.temp"
#define UPDATE_REAL_PATH                "/customer/App/Update.tar.gz"
#else
#define BASIC_INI_FILE 	                "/mnt/yaffs2/config.ini"
#define MFR_CONFIG_INI_FILE 	        "/mnt/yaffs2/config_man.ini"

#define FILE_DEVICE_ALIAS               "/mnt/yaffs2/device_alias"  //设备名称
#define MUSICLISTFILE                   "/mnt/yaffs2/MusicList"	    //歌曲列表文件
#define RECORD_INFO_FILE                "/mnt/yaffs2/RecordInfo"	//记录文件
#define USERINFOFILE                    "/mnt/yaffs2/Userinfo"	    //用户信息文件
#define ZONEINFOFILE                    "/mnt/yaffs2/Zoneinfo"		//分区文件
#define GROUPINFOFILE                   "/mnt/yaffs2/Groupinfo"	    //分组文件

#define UPDATE_SAVE_PATH                "/mnt/yaffs2/Update.temp"
#define UPDATE_REAL_PATH                "/mnt/yaffs2/Update.tar.gz"
#endif


#endif