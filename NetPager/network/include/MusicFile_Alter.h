#ifndef MUSICFILE_ALTER_H_
#define MUSICFILE_ALTER_H_

int usb_init_success_flag;
/*************************************************
 * 	系统扫描音频文件部分  - 文件操作宏定义
 */

char* Get_DirName_From_Path(char *path);
char* Check_FileName(char *path);

char* StringSubU(const char* string,int start,int number);



/*******目录下的歌曲数量(只用于保存USB插入时解码端首先发过来的歌曲数目，用于分配空间）************/

#if 0
typedef struct
{
	int FileNum;
}usb_sd_filenum;

typedef struct
{
	int USB_DirNum;
	int SD_DirNum;
	usb_sd_filenum USB_Dir[20];
	usb_sd_filenum SD_Dir[20];
}USB_SD_num;
USB_SD_num REC_USB_SD_NUM;

/*************************************************
 * 	系统扫描音频文件部分  - 外部调用函数
 */

int Init_MusicFile_Space();
void Free_MusicFile_USBSpace();
int Init_NetDisk_MusicFile_Space();
void Free_NetDisk_MusicFile_Space();

#endif

#endif /* MUSICFILE_H_ */
