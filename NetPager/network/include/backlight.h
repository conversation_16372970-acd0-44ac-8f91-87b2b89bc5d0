#ifndef BACKLIGHT_H_
#define BACKLIGHT_H_

/*********************************************************************
 * DEFINITION
 */
#define MODE_PWM                 1
#define MODE_COUNTER             2
#define MODE_CAPTURE             3
#define MODE_CAPTURE_WITH_DT     4

#define ERROR                    -1
#define SUCCEED                  0

/*********************************************************************
 * LOCAL FUNCTION
 */
static int open_pwmnode(void);
static void close_pwmnode(void);

/*********************************************************************
 * GLOBAL FUNCTION
 */
int init_backlight_module(void);
int backlight_adjust(int level);


#define MAX_BRIGHT_LEVEL   8
#define DEFAULT_BRIGHT_LEVEL MAX_BRIGHT_LEVEL-1

extern int backlight_level;

#endif /*BACKLIGHT_H_*/

