#ifndef XML_H
#define XML_H

#include "pthread.h"


extern char g_xml_groupInfo_url[256];
extern char g_xml_Playlist_url[256];
extern char g_xml_zoneInfo_url[256];
extern char g_xml_audiocollectorInfo_url[256];
extern char g_xml_userInfo_url[256];
extern char g_xml_pagerInfo_url[256];


extern int g_xml_groupInfo_download_flag;
extern int g_xml_playlist_download_flag;
extern int g_xml_zoneInfo_download_flag;
extern int g_xml_audioCollectorInfo_download_flag;
extern int g_xml_userInfo_download_flag;
extern int g_xml_pagerInfo_download_flag;

extern pthread_mutex_t XMLDownloadMutex;
extern pthread_mutex_t MusicListMutex;
extern pthread_mutex_t GroupListMutex;
extern pthread_mutex_t ZoneInfoMutex;
extern pthread_mutex_t UserInfoMutex;
extern pthread_mutex_t PagerInfoMutex;

#if ENABLE_CALL_FUNCTION
extern pthread_mutex_t PagerInfoMutex;
#endif

extern char g_Audio_Collector_Info_dateTime[32];	//音频采集器信息更新日期(主机下发)

int XML_Playlist_Thread();

#endif

