#ifndef I2S_H_
#define I2S_H_

#include "sysconf.h"

/*********PAGING*************/

#if SUPPORT_CODEC_G722
#if defined(USE_SSD212) || defined(USE_SSD202)
#define RATE                32000   //采样频率
#define FMT                 16      //量化位数
#define CHANNELS            1       //声道数目
#define RSIZE               (512*2) //buf的大小
#else
#define RATE                 22050   //采样频率
#define FMT                 16      //量化位数
#define CHANNELS             1       //声道数目
#define RSIZE                (512*2) //buf的大小
#endif
#else
#define RATE                 32000   //采样频率
#define FMT                 16      //量化位数
#define CHANNELS             1       //声道数目
#define RSIZE                (640*2) //buf的大小
#endif

/*********PAGING*************/

#if ENABLE_CALL_FUNCTION
/*********PAGING*************/
#define RATE_CALL                 16000   //采样频率
#define FMT_CALL                  16      //量化位数
#define CHANNELS_CALL             1       //声道数目
#define RSIZE_PAGER_CALL          (512) //buf的大小 //512/(16000*2)=16ms

#define FRAME_PAGER_CALL          RSIZE_PAGER_CALL/2    //每帧256个采样点

#if SUPPORT_CODEC_G722
#define G722_BUFFER_SIZE         RSIZE/4
#define G722_CALL_BUFFER_SIZE    RSIZE_PAGER_CALL/4
#endif


#define RX_CALL_BUF_PKG_MAX       20

typedef struct
{
    char rx_call_data[RX_CALL_BUF_PKG_MAX][1400];
    int rx_call_len[RX_CALL_BUF_PKG_MAX];
    char rx_call_valid[RX_CALL_BUF_PKG_MAX];
    char rx_call_write_pos;
    char rx_call_read_pos;
}stRx_call_stream;

extern stRx_call_stream rx_call_stream;

/*********PAGING*************/
#endif



#if ENABLE_LISTEN_FUNCTION

#define RATE_MONITOR                 44100   //采样频率
#define FMT_MONITOR                  16      //量化位数
#define CHANNELS_MONITOR             1       //声道数目
#define RSIZE_MONITOR                (512*2) //buf的大小




#define RX_MONITOR_BUF_PKG_MAX       40

typedef struct
{
    char rx_monitor_data[RX_MONITOR_BUF_PKG_MAX][1200];
    int rx_monitor_len[RX_MONITOR_BUF_PKG_MAX];
    char rx_monitor_valid[RX_MONITOR_BUF_PKG_MAX];
    char rx_monitor_write_cnt;
    char rx_monitor_read_cnt;
    int  rx_monitor_samplerate;
    int rx_monitor_stream_cnt;
}stRx_monitor_stream;

extern stRx_monitor_stream rx_monitor_stream;

#endif



extern int g_send_decode_pcm_type;

extern pthread_mutex_t call_data_mutex;


/***********************************************************
 * 	I2S - 解码位
*/
#define AUDIO_FORMAT_16BIT  16         /*16bits*/
#define AUDIO_FORMAT_24BIT  24         /*24bits*/
#define AUDIO_FORMAT_32BIT  32         /*32bits*/

/***********************************************************
 * 	I2S - 比特率
*/
#define AUDIO_RATE_16     	16000      /*16000hz*/
#define AUDIO_RATE_44_1     44100      /*44100hz*/
#define AUDIO_RATE_48       48000      /*48000hz*/
#define AUDIO_RATE_96       96000      /*96000hz*/
#define AUDIO_RATE_115      115200     /*115200hz*/
#define AUDIO_RATE_192      192000     /*192000hz*/


/***********************************************************
 * 	I2S - 通道数
*/
#define AUDIO_CHANNEL_1   1
#define AUDIO_CHANNEL_2	  2

int PcmAdjustmentVolume(short *samples,int numSamples,float factor);
void PcmMixed(short *samples1,short *samples2,int numSamples,float factor1,float factor2);
int open_and_init_dsp(int rate,int afmt,int channels);
void close_dsp();
int write_i2s_data(char *buf,int len);

#endif
