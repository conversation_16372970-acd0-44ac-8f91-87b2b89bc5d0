#ifndef SAVEUSERINFO_H_
#define SAVEUSERINFO_H_

#include "const.h"
//#include "SaveZoneInfo.h"

/***********************************************************
 * 	SaveZone - 用户信息 宏定义
*/
#define MAX_USER_NUM				1000   		   //最大用户数

#define SUPER_USER_NAME             "admin"        // 超级用户名称


//用户拥有权限划分
typedef enum
{
    USER_LIMITS_NONE = 0x0000,                   // 无权限
    USER_LIMITS_PLAYLIST = 0x0001,               // 播放列表管理
    USER_LIMITS_TIMER = 0x0002,                  // 定时管理
    USER_LIMITS_USE_AUDIO_COLLECTOR = 0x0004,    // 使用音频采集器

    USER_LIMITS_ALL          = 0xFFFF            //全部权限
}LimitCode;

typedef struct
{
	int id;							//用户id
	unsigned char name[16+1];		//用户名
	unsigned char password[16+1];	//主密码
	unsigned char password2[16+1];	//记住密码					//默认密码为空，或admin均可。
	unsigned char authority;		//管理权限（1可以进入设置界面）
	unsigned char uuid[36+1];		//用户uuid
	unsigned char playmode;			//播放模式
	unsigned char sys_authority;	//系统权限（暂时没用到，备用）
	unsigned char passwordRemember;	//是否记住密码
	int ZoneCount;					//包含的分区数
	int LimitCode;					//权限码
	unsigned char *ZoneMac;			//分区MAC集合
}USERINFO_DETAIL;


typedef struct
{
	int TotalUser;		//用户数
	int CurrentUserIndex;		//当前用户索引
	unsigned char DateTime[32];	    //更新日期
	unsigned char CurrentUserName[16+1];	//当前用户名称
	USERINFO_DETAIL userInfo[MAX_USER_NUM];
}USERINFO;
USERINFO m_stUser_Info;



int Set_Save_User_Info(int id,unsigned char *name, unsigned char *password, unsigned char authority, unsigned char passwordRemember);
int Delete_User_Info(int nID);
int GetUserIndexByUserName(unsigned char *name);

#endif /* SAVEUSERINFO_H_ */
