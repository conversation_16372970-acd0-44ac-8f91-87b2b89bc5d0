#ifndef __COM_BP1048_H__
#define __COM_BP1048_H__

enum
{
	BP1048_FW_NORMAL_TYPE=1,
	BP1048_FW_UDISK_TYPE=2
};

#define set_dsp_volume(x)  	   Bp1048_Send_Set_Volume(x);
#define set_dsp_sampleRate(x)  Bp1048_Send_Set_SampleRate(x);
#define set_dsp_source(x)	   Bp1048_Send_Set_Source(x);

#define BP1048_UART_DATA_LENGTH_MAX	   256
#define BP1048_UART_RECEIVE_BUFFER_MAX (6+BP1048_UART_DATA_LENGTH_MAX)
#define BP1048_FIRMEARE_UPGRADE_ONCE_SIZE	2048
#define BP1048_UART_SEND_UPGRADE_BUFFER_MAX (6+BP1048_FIRMEARE_UPGRADE_ONCE_SIZE+10)

#define BP1048_UART_FRAME_HEAD1	0xAA
#define BP1048_UART_FRAME_HEAD2	0x55
#define BP1048_UART_FRAME_HEAD	(BP1048_UART_FRAME_HEAD2<<8)+BP1048_UART_FRAME_HEAD1

typedef struct {
	unsigned short FrameHead;		 //起始码(0x55AA)
	unsigned char Command; 			 //命令字
	unsigned short Data_Len; 		 //数据长度
	unsigned char Data_content[BP1048_UART_DATA_LENGTH_MAX];//数据内容
	unsigned char sum;				 //校验码
}st_bp1048_pro;



typedef struct {
	unsigned char uuid[8];			//UUID
	unsigned char firmware_type;	//固件类型
	unsigned char version[10];		//version
}st_bp1048_info;

extern st_bp1048_info g_bp1048_info;

extern unsigned char g_bp1048_IsInit;
extern unsigned char g_stc_IsInit;		//STC单片是否初始化（只有SSD平台发射机才用到）


void Bp1048_Command_Proc( unsigned char cmd, unsigned short datalen,unsigned char *data );
void Bp1048_uart_send(unsigned char cmd, unsigned short datalen,unsigned char *data);
unsigned char Bp1048_get_sampleRate_index(int sampleRate);
void Bp1048_receive_UpgradeInfo(unsigned short datalen,unsigned char *rcbuf);

void Bp1048_receive_ChipInfo(unsigned short datalen,unsigned char *rcbuf);

#define BP1048_CMD_CHIP_INFO		0x00
#define BP1048_CMD_FEATURES			0x01
#define BP1048_CMD_SAMPLERATE		0x02
#define BP1048_CMD_BOOT				0x03
#define BP1048_CMD_REBOOT			0x04
#define BP1048_CMD_SOURCE			0x11
#define BP1048_CMD_VOLUME			0x12
#define BP1048_CMD_BT_INFO			0x13
#define BP1048_CMD_SET_RP			0x18
#define BP1048_CMD_CUSTOM_SIGNAL	0x19
#define BP1048_CMD_ENTER_CALL		0x20
#define BP1048_CMD_UPGRADE			0xFE


//U盘播放相关
#define BP1048_CMD_QUERY_USB_PLUG_INFO		0x51
#define BP1048_CMD_SET_USB_PLAY_STATUS	0x52
#define BP1048_CMD_QUERY_USB_DETAIL_STATUS	0x53

#define BP1048_CMD_SET_USB_PLAY_MODE	0x55

#define BP1048_CMD_QUERY_USB_DIR		0x57
#define BP1048_CMD_QUERY_USB_FILE		0x58

#define BP1048_CMD_SET_USB_PLAY_FILE	0x59

#define BP1048_UDISK_PLAY_MODE_SINGLE_LOOP	0X02
#define BP1048_UDISK_PLAY_MODE_LIST_LOOP	0X03

enum{
	BP1048_CHANNEL_Line5_L,
	BP1048_CHANNEL_Line5_R,
	BP1048_CHANNEL_MIC1,
	BP1048_CHANNEL_MIC2,
	BP1048_CHANNEL_I2S0_IN_L,
	BP1048_CHANNEL_I2S0_IN_R,
	BP1048_CHANNEL_I2S1_IN_L,
	BP1048_CHANNEL_I2S1_IN_R,
	BP1048_CHANNEL_I2S0_OUT_L,
	BP1048_CHANNEL_I2S0_OUT_R,
	BP1048_CHANNEL_I2S1_OUT_L,
	BP1048_CHANNEL_I2S1_OUT_R,
	BP1048_CHANNEL_DAC0_L,
	BP1048_CHANNEL_DAC0_R,
	BP1048_CHANNEL_BT_L,
	BP1048_CHANNEL_BT_R,
	BP1048_MAX_CHANNEL_COUNT
};


//自定义，非协议部分
enum{
	BP1048_OUTPUT_CHANNEL_DAC0_L,
	BP1048_OUTPUT_CHANNEL_DAC0_R,
	BP1048_OUTPUT_CHANNEL_I2S0_OUT_L,
	BP1048_OUTPUT_CHANNEL_I2S0_OUT_R,
	BP1048_OUTPUT_CHANNEL_I2S1_OUT_L,
	BP1048_OUTPUT_CHANNEL_I2S1_OUT_R,
	BP1048_MAX_OUTPUT_CHANNEL_COUNT
};


//自定义，非协议部分
#define BP1048_SOURCE_DECODE_DAC_I2S0_STEREO_MUSIC		0x01
#define BP1048_SOURCE_DECODE_DAC_I2S0_STEREO_OTHER		0x02
#define BP1048_SOURCE_DECODE_DAC_I2S0_MONO_MUSIC		0x04
#define BP1048_SOURCE_DECODE_DAC_I2S0_MONO_OTHER		0x08
#define BP1048_SOURCE_DECODE_DAC_LINE					0x10
#define BP1048_SOURCE_DECODE_DAC_MIC1					0x20
#define BP1048_SOURCE_DECODE_DAC_BT						0x40
#define BP1048_SOURCE_DECODE_DAC_100V					0x80
#define BP1048_SOURCE_DECODE_I2S0_MIC2					0x100

#define BP1048_SOURCE_DECODE_DAC_MIC2					0x200

#define BP1048_SOURCE_PAGER_I2S0_MIC1					0x1000
#define BP1048_SOURCE_PAGER_I2S0_LINE					0x2000
#define BP1048_SOURCE_PAGER_DAC_I2S						0x4000
#define BP1048_SOURCE_PAGER_I2S0_BT						0x8000

#define BP1048_SOURCE_AUDIO_COLLECTOR_I2S0_MIC1			0x10000
#define BP1048_SOURCE_AUDIO_COLLECTOR_I2S0_MIC2			0x20000
#define BP1048_SOURCE_AUDIO_COLLECTOR_I2S1_LINEL		0x40000
#define BP1048_SOURCE_AUDIO_COLLECTOR_I2S1_LINER		0x80000


#define BP1048_SOURCE_DECODE_PAGER_MIC					BP1048_SOURCE_PAGER_I2S0_LINE  | BP1048_SOURCE_PAGER_I2S0_MIC1 | BP1048_SOURCE_DECODE_I2S0_MIC2
#define BP1048_SOURCE_DECODE_PAGER_MIC_LISTENING		BP1048_SOURCE_PAGER_I2S0_LINE  | BP1048_SOURCE_PAGER_I2S0_MIC1 | BP1048_SOURCE_DECODE_I2S0_MIC2 | BP1048_SOURCE_DECODE_DAC_LINE | BP1048_SOURCE_DECODE_DAC_MIC1
#define BP1048_SOURCE_DECODE_PAGER_MUSIC				BP1048_SOURCE_PAGER_I2S0_BT
#define BP1048_SOURCE_DECODE_PAGER_MUSIC_LISTENING		BP1048_SOURCE_PAGER_I2S0_BT	   | BP1048_SOURCE_DECODE_DAC_BT
#define BP1048_SOURCE_DECODE_PAGER_MIC_MUSIC			BP1048_SOURCE_PAGER_I2S0_LINE  | BP1048_SOURCE_PAGER_I2S0_MIC1 | BP1048_SOURCE_DECODE_I2S0_MIC2 | BP1048_SOURCE_PAGER_I2S0_BT
#define BP1048_SOURCE_DECODE_PAGER_MIC_MUSIC_LISTENING	BP1048_SOURCE_PAGER_I2S0_LINE  | BP1048_SOURCE_PAGER_I2S0_MIC1 | BP1048_SOURCE_DECODE_I2S0_MIC2 | BP1048_SOURCE_PAGER_I2S0_BT | \
												        BP1048_SOURCE_DECODE_DAC_LINE  | BP1048_SOURCE_DECODE_DAC_MIC1 | BP1048_SOURCE_DECODE_DAC_MIC2 | BP1048_SOURCE_DECODE_DAC_BT
														
#define BP1048_SOURCE_DECODE_PAGER_ONLY_LINE				BP1048_SOURCE_PAGER_I2S0_LINE
#define BP1048_SOURCE_DECODE_PAGER_ONLY_LINE_LISTENING		BP1048_SOURCE_PAGER_I2S0_LINE | BP1048_SOURCE_DECODE_DAC_LINE

#define BP1048_SOURCE_DECODE_PAGER_CALL					BP1048_SOURCE_PAGER_I2S0_LINE | BP1048_SOURCE_PAGER_I2S0_MIC1 | BP1048_SOURCE_DECODE_I2S0_MIC2 | BP1048_SOURCE_PAGER_DAC_I2S


//佳比调音台版本
#define BP1048_SOURCE_SOUND_CONTROL				BP1048_SOURCE_DECODE_DAC_LINE | BP1048_SOURCE_PAGER_I2S0_LINE

#define BP1048_CHANNEL_GAIN_0db				0
#define BP1048_CHANNEL_GAIN_DOWN(x)			(BP1048_CHANNEL_GAIN_0db+x)

#define I2S_MUSIC_PLAY_GAIN		BP1048_CHANNEL_GAIN_DOWN(10)
#define I2S_OTHER_GAIN			BP1048_CHANNEL_GAIN_DOWN(0)
#define MIX_AUX_GAIN			BP1048_CHANNEL_GAIN_DOWN(24)
#define SINGLE_AUX_GAIN			BP1048_CHANNEL_GAIN_DOWN(0)


typedef struct {
	unsigned char contain_channel_valid[BP1048_MAX_OUTPUT_CHANNEL_COUNT][BP1048_MAX_CHANNEL_COUNT];
	unsigned char contain_channel_gain[BP1048_MAX_OUTPUT_CHANNEL_COUNT][BP1048_MAX_CHANNEL_COUNT];
	unsigned char output_channel_vaild[BP1048_MAX_OUTPUT_CHANNEL_COUNT];
}st_bp1048_output_channel_info;




enum
{
	BP1048_AUDIO_MODULE_ADC0_L,					//LINE5 L
	BP1048_AUDIO_MODULE_ADC0_R,					//LINE5 R
	BP1048_AUDIO_MODULE_ADC1_L,					//MIC1
	BP1048_AUDIO_MODULE_ADC1_R,					//MIC2
	BP1048_AUDIO_MODULE_DAC0_L,					//DAC_L
	BP1048_AUDIO_MODULE_DAC0_R,					//DAC_R
	BP1048_AUDIO_MODULE_BT,						//BT
	BP1048_AUDIO_MODULE_MAX,
};



typedef struct {
	unsigned char module_switch[BP1048_AUDIO_MODULE_MAX];	//模块开关
	unsigned short module_gain[BP1048_AUDIO_MODULE_MAX];	//模块增益
}st_bp1048_firmware_feature_info;
extern st_bp1048_firmware_feature_info bp1048_firmware_feature;

#define BP1048_BLUETOOTH_NAME_MAX_LENGTH		33
#define BP1048_BLUETOOTH_PASSWORD_MAX_LENGTH	4
typedef struct {
	unsigned char name[BP1048_BLUETOOTH_NAME_MAX_LENGTH];			//蓝牙名称
	unsigned char hasPassword;										//是否有密码
	unsigned char password[BP1048_BLUETOOTH_PASSWORD_MAX_LENGTH];	//蓝牙密码
}st_bp1048_bt_info;
extern st_bp1048_bt_info bp1048_bt_info;






typedef struct {
	unsigned char IsStart;			//UUID
	unsigned char Upgrade_process;	//0 ready  1 upgrading 2 end
	unsigned int pkg_index;						//包序号（从1开始）
	unsigned char upgrade_valid;		//每发送一个包，需要收到应答才能发下一个包
	unsigned long file_length;			//升级文件总大小
	unsigned int pkg_count;			//升级文件总包数
}st_bp1048_upgrade_info;

extern st_bp1048_upgrade_info bp1048_upgrade_info;


extern const st_bp1048_firmware_feature_info bp1048_default_firmware_feature;
extern const st_bp1048_bt_info  bp1048_default_bt_info;

void Dsp_init();


extern int sampleRate_array[9];

#define BP1048_MUSICFILENAMELEN	48+1
#define BP1048_MUSICDIRNAMELEN	48+1
#define BP1048_MUSICUSBDIRMAX	30
#define BP1048_MUSICUSBFILEMAX	100
/*************************************************
 * 	系统扫描音频文件部分  - 音乐结构体定义
 */
typedef struct
{
    char cName[BP1048_MUSICFILENAMELEN];    //歌曲名称
	int  realId;					   		//对应的实际文件ID
}BP1048_FILENAME;

typedef struct
{
	int id;									//目录id
	int FileStartIndex;						//歌曲起始id
    char cDirName[BP1048_MUSICDIRNAMELEN];	//目录路径
    BP1048_FILENAME *musicfile;            	//对应目录下的歌曲文件相关信息
    int nFileNum;                   		//对应文件夹下的歌曲数
	unsigned char ready_flag;				//准备就绪标志
}BP1048_MUSICDIR;

typedef struct
{
    int DirNum;		   						 		//usb目录个数
    BP1048_MUSICDIR SongDir[BP1048_MUSICUSBDIRMAX]; //存贮SD对应目录下的相关信息
	unsigned char ready_flag;						//准备就绪标志
	unsigned char IsGetDirInfo;
}BP1048_MUSICLIST;



typedef struct {
	unsigned char IsPlug;
	unsigned char playStatus;
	unsigned char playMode;
	unsigned int current_playFileId;				//当前播放的文件id
	BP1048_MUSICLIST musicList;
}st_bp1048_usb_info;

extern st_bp1048_usb_info bp1048_usb_info;

void free_udisk_musiclist();
void Bp1048_Send_Query_Usb_MusicList();

void BP1048_Send_Set_Usb_Play_File(int fileId);
void BP1048_Send_Set_Usb_Play_Mode(int playMode);
void Bp1048_Send_Set_Usb_PlayStatus(unsigned char playStatus);
void Bp1048_Send_Query_Usb_Plug();
void Bp1048_Send_Query_Usb_PlayStatus();

void BP1048_Send_Enter_CALL(int isCall,int echoLevel,int nsLevel);
#endif
