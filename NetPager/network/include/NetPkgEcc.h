#ifndef NETPKGECC_H
#define NETPKGECC_H

/***********网络纠错数据结构体****************************/

struct NetPkgNode
{
	unsigned char *send_buf;			//数据包
	int sendlen;						//数据包长度
	int cmd_word;						//命令字
	long int send_ip;					//发送目标ip;
	int port;							//发送端口
	int timeCount;						//超时计数
	struct NetPkgNode *preNode;			//上一节点地址
	struct NetPkgNode *nextNode;		//下一节点地址
};

typedef struct
{
	int PkgCount;				//需侦测的包计数
	struct NetPkgNode *pkgList;	//包详细信息(指针形式，采用链表存储)
}st_NETPKGECC;
st_NETPKGECC m_stNetPkgEcc;






void NetPkg_Check_Thread();
void Add_NetPkgEcc( unsigned char *send_buf,int len,int cmd_word,long int ip,int port,int mutex_flag );
void Del_NetPkgEcc( int cmd_word,int ip,int port );
void NetResponse_update(int cmd_word,int ip,int port,unsigned char *send_buf,int len);
#endif
