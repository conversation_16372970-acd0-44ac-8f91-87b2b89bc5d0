
#ifndef UART_H_
#define UART_H_

#include "com_bp1048.h"


/*********************************************************************
 * MACROS
 */
#define _DEBUG_UART_                	 0   // 调试信息打印，0-不打印，1-打印
#define MAX_UART_BUF_SIZE                512 // 缓存大小

/*波特率定义*/
#define BAUD_4800					4800
#define BAUD_9600 		            9600
#define BAUD_19200 		            19200
#define BAUD_38400 		            38400
#define BAUD_57600					57600
#define BAUD_115200 	            115200
#define BAUD_460800					406800

/*串口设备定义*/
#define UARTDEV0                    "/dev/ttyS0"
#define UARTDEV1                    "/dev/ttyS1"
#define UARTDEV2                    "/dev/ttyS2"
#define UARTDEV3                    "/dev/ttyS3"
#define UARTDEV5                    "/dev/ttyS5"
#define UARTDEV6                    "/dev/ttyS6"

/*串口号定义*/
#define COM0                        0x00
#define COM1                        0x01
#define COM2                        0x02
#define COM3                        0x03
#define COM4                        0x04
#define COM5                        0x05
#define COM6                        0x06



unsigned char Checksum(unsigned char cmd,int len,unsigned char *buf);
int Init_Serial_Port(char *Dev, int Speed, int NumDataBits, char Parity, int NumStopBits);

#endif /* UART_H_ */

