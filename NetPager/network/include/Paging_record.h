#ifndef PAGING_RECORD_H
#define PAGING_RECORD_H

#include "time.h"
#include "const.h"

#define MAX_RECORD_NUM 256

//注：文件名为record/id.wav
typedef struct
{
	unsigned char id;			//记录id
	int duration;				//持续时间 秒为单位
	struct tm t_TimerP;			//记录的时间
	char userName[16];	//记录的用户名(此处不用用户id，考虑到用户名可能会被删除)
}RECORDINFO_DETAIL;


typedef struct
{
	unsigned char recordNum;							//录音数目
	RECORDINFO_DETAIL recordInfo[MAX_RECORD_NUM];		//记录信息
}RECORDINFO;
RECORDINFO m_stRecord_Info;



int Set_Record_Info(unsigned char id,char* userName,int duration,struct tm t_TimerP);

#endif
