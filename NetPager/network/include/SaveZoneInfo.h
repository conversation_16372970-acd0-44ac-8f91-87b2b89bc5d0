#ifndef SAVEZONEINFO_H_
#define SAVEZONEINFO_H_

#define MAX_ZONE_NUM	4000
#define MAX_PAGER_NUM	300

typedef enum
{
	DF_BLUETOOTH		=   0x00000001,	// 蓝牙设备
    DF_LOCAL_MONITOR	=   0x00000002, // 本地监听设备
    DF_CALL             =   0x00000004, // 对讲
	DF_VIDEO            =   0x00000008, // 可视
}Device_Feature;

/***********************************************************
 * 	设备分区	-	结构体保存 - 分区信息
*/


typedef struct
{
	int g_zone_id;			   				//分区ID
	unsigned char g_zone_name[32+1];	       //分区名字
	long int g_zone_ip;				   	   //分区IP	32位 4个字节 0x00000000			注：Linux下PC编译器int默认为long int类型
	unsigned char g_zone_mac[6];		   //MAC地址 6个字节
	unsigned char g_zone_source;	       //分区音源
	unsigned char g_zone_media_name[64];   //分区节目名
	unsigned char g_zone_vol; 		       //分区音量
	unsigned char g_zone_conection;	       //分区在线状态
	unsigned char g_zone_isSelect;		   //是否选中
	unsigned char g_zone_isHide;		   //是否选中
	unsigned char g_zone_DeviceType;	   //设备类型
	unsigned char g_zone_device_feature;   //设备特性
	unsigned char g_zone_playStatus;	   //播放状态
	unsigned char g_zone_offline_count;	   //掉线次数
}ZONEINFO_DETAIL;


typedef struct
{
	int ExistTotalZone;									//已存在(已上过线)的分区数
	int OnlineTotalZone;								//在线分区数
	ZONEINFO_DETAIL zoneInfo[MAX_ZONE_NUM];				//分区信息
	unsigned char DateTime[32];					 //更新日期
}ZONEINFO;
ZONEINFO m_stZone_Info;




typedef struct
{
	int onlineZone[MAX_ZONE_NUM];			//在线的分区ID
}ONLINEZONE;
ONLINEZONE m_stOnlineZone_Info;						//在线分区信息






//由于分组是由主机一次性发送过来的，所以不存在在线与掉线，故不采用实时检测判断。每次接收到分组信息后重新更新所有分组信息
typedef struct
{
	unsigned char g_group_id;					 //分组ID
	unsigned char g_group_realId[40];	         //分组实际ID
	int ZoneNum;						 		 //包含的分区数量
	int AccountZoneNum;							 //本账户分区数量
	unsigned char g_group_name[32];	       		 //分组名字
	unsigned char g_group_UserName[32];		     //分组所属账户名称
	int g_zone_contain[MAX_ZONE_NUM];	 		 //包含的分区号
	unsigned char g_zone_mac[6*MAX_ZONE_NUM];	 //包含的分区号
	unsigned char g_group_isSelect;		   		 //是否选中
	unsigned char g_group_isHide;				 //是否隐藏
}GROUP_DETAIL;

typedef struct
{
	unsigned char TotalGroup;					 //在线分组
	unsigned char DateTime[32];					 //更新日期
	GROUP_DETAIL *groupInfo;					 //分组信息
}GROUPINFO;
GROUPINFO m_stGroup_Info;


#define MAX_VIDEO_PACKETS 6
typedef struct
{
	unsigned int pkgId[MAX_VIDEO_PACKETS];				//收到的视频包id
	unsigned char subPkgId[MAX_VIDEO_PACKETS];			//收到的视频分包id
	unsigned char streamBuf[MAX_VIDEO_PACKETS][1500];	//包缓存
	unsigned int  streamLen[MAX_VIDEO_PACKETS];			//包大小
	unsigned char nextPkgIndex;							//下一次保存视频包的位置
	unsigned int expectNextPkgId;						//期望的下一个包id 							
}VIDEO_CALL_STREAM;



//寻呼台信息，用户对讲
typedef struct
{
	int id;										//寻呼台ID（可不用）
	unsigned char mac[6];					 	//寻呼台MAC
	long int ip;							 	//寻呼台IP
	unsigned char name[32];	       		 		//寻呼台名字
	unsigned char source;	       				//寻呼台音源
	unsigned char conection;	       			//寻呼台在线状态
	unsigned char isSelect;		   		 		//寻呼台是否选中
	unsigned char isSupportCall;				//是否支持对讲
	unsigned char isSupportVideo;				//是否支持可视
	unsigned char call_status;					//寻呼台对讲状态
}PAGER_DETAIL;

typedef struct
{
	unsigned int TotalPager;					 //总共寻呼台
	int OnlinePager;							//在线寻呼台
	PAGER_DETAIL pagerInfo[MAX_PAGER_NUM];		//寻呼台详细信息
	unsigned char DateTime[32];	    			//更新日期

	unsigned char  self_isCallingParty;       //是否为主叫方     ( 1为主叫方  0为被叫方 )
    unsigned char  self_callMac[6];		   	  //记录主叫方或被叫方的MAC
    unsigned char  self_audioCoding;          //语音编码
    unsigned char  selt_enableNat;            //开启NAT后由主机转发双方音频流
    unsigned short self_audioPort;            //关闭NAT后有效
	unsigned char  self_callStatus;			  //自身的对讲状态
	unsigned char  other_callStatus;          //对方的对讲状态
	unsigned char  other_isSupportVideo;      //对方是否支持视频
	unsigned char  self_video_callStatus;	  //自身的视频对讲状态
	unsigned char  other_video_callStatus;    //对方的视频对讲状态
	unsigned char  other_deviceModel;	      //对方设备型号
	unsigned int   self_mediaTimeStamp;		  //自身的媒体时间戳
	VIDEO_CALL_STREAM stVideoStream;		  //视频流结构体

}PAGERINFO;
PAGERINFO m_stPager_Info;



enum
{
	CALL_STATUS_FREE,						//空闲
	CALL_STATUS_WAIT_CALLED_ANSWER,			//被叫方响铃中
	CALL_STATUS_NO_RESPONSE,				//无人应答
	CALL_STATUS_BUSY,						//被叫方繁忙，无法接听（如寻呼）
	CALL_STATUS_REJECT,						//被叫方拒绝接听（手动挂断）	0x04
	CALL_STATUS_CODECES_NOT_SUPPORT,		//不支持的语音编码	0x05
	CALL_STATUS_CONNECT,					//被叫方已接听	0x06
	CALL_STATUS_HANGUP						//任意一方已挂断	0x07
};

/**********以下为视频对讲状态*/
//请求对讲，
enum
{
	VIDEO_CALL_STATUS_FREE,						//空闲
	//VIDEO_CALL_STATUS_REQUEST_VIDEO,			//请求视频对讲（请求方才有此状态，被请求方收到后直接进入尝试打开RTSP状态，拒绝除外）
	VIDEO_CALL_STATUS_TRY_OPEN_RTSP,			//正在尝试打开RTSP
	VIDEO_CALL_STATUS_RTSP_SEND_PARM,			//已发送RTSP视频流参数
	VIDEO_CALL_STATUS_ALL_READY					//一切准备就绪，对方可以开始播放RTSP并转发给服务器
};

/*********以上为视频对讲状态*/





typedef struct
{
	unsigned char  self_isListeningParty;     //是否为监听端     ( 1为监听端  0为被监听端 )
    unsigned char  self_listenedMac[6];		  //记录被监听端的MAC
	unsigned char  self_listenedType;	  	 //记录监听类型
    unsigned char  self_audioCoding;          //语音编码
    unsigned char  selt_enableNat;            //开启NAT后由主机转发双方音频流
    unsigned short self_audioPort;            //关闭NAT后有效
	unsigned char  self_listenStatus;		  //自身的对讲状态
}LISTENINFO;
LISTENINFO m_stListen_Info;


enum
{
	LISTEN_STATUS_STOP,					//开始监听
	LISTEN_STATUS_START				    //停止监听
};

enum
{
	LISTEN_TYPE_OUTPUT=1,				//输出监听
	LISTEN_TYPE_SCENE=2				    //现场监听
};


int Set_Zone_Info(unsigned char g_zone_id,unsigned char *g_zone_name, long int g_zone_ip,unsigned char g_zone_mac[6], unsigned char g_zone_source, unsigned char *g_zone_media_name, unsigned char g_zone_vol,
		unsigned char g_zone_conection);
int Set_Group_Info(unsigned char g_group_id,unsigned char zoneNum,unsigned char *g_group_name,unsigned char *g_zone_mac);


int Get_group_ori_index_by_realId(char *id);
int Get_group_real_index_by_realId(char *id);
int Get_Group_ori_index_by_realIndex(int realIndex);


void Set_Zone_Online_status(long int ip,int status);
int Get_ZoneIndex_By_Ip(long int ip);
int Get_ZoneIndex_By_MAC(char *mac);

//根据音源id获取音源名
char* Get_Source_Name_ById(unsigned char id);
//设备是否支持对讲
unsigned char IsSupportCallDevice(unsigned char deviceFeature);
//设备是否支持可视
unsigned char IsSupportVideoDevice(unsigned char deviceFeature);


int OS_Init_Group_M();
int Save_Group_Info();
int Scan_Group_Info();
int OS_Init_Zone_M();
int Save_Zone_Info();
int Scan_Zone_Info();

#endif /* SAVEZONEINFO_H_ */

