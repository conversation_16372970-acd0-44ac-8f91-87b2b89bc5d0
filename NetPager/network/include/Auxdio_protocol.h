#ifndef AUXDIO_PROTOCOL_H_
#define AUXDIO_PROTOCOL_H_

#include "const.h"

#define SEND_REPEAT_COUNT	3


/****************网络模式声明************************/
#define NETWORK_MODE_LAN	1
#define NETWORK_MODE_WAN	2


/************ 网络协议 ******************/
#define DEVICE_MODEL_HOST   	0x01
#define DEVICE_MODEL_NETWORK_TOOLS		0X02	//网络配置工具
#define	DEVICE_MODEL_PAGING_A 	0x03        //寻呼台A
#define	DEVICE_MODEL_PAGING_B 	0x04        //寻呼台B
#define	DEVICE_MODEL_PAGING_C 	0x14        //寻呼台C
#define DEVICE_MODEL_ZONE_A		0x05
#define DEVICE_MODEL_ZONE_B		0x06
#define DEVICE_MODEL_ZONE_C		0x07
#define DEVICE_MODEL_ZONE_D		0x0A
#define DEVICE_MODEL_ZONE_E		0x10
#define DEVICE_MODEL_ZONE_F		0x1C

//播放状态
#define SONG_PLAYING          0x01  // 播放
#define SONG_SUSPEND          0x02  // 暂停
#define SONG_STOP             0x04  // 停止

//设置/查询定义
#define CMD_SET	 	0x08	//设置
#define CMD_QUERY	0x80    //查询

//静音定义
#define CMD_NOMUTE	0x01	//非静音
#define CMD_MUTE	0x10	//静音

//IP属性
#define IP_ASSIGN_DHCP 		0
#define IP_ASSIGN_STATIC	1

//更新文件
#define XML_FILE_GROUP  			0x01
#define XML_FILE_MUSICLIST			0x02
#define XML_FILE_TIMER				0x03
#define XML_FILE_ZONEINFO			0x04
#define XML_FILE_AUDIO_COLLECTOR	0x05
#define XML_FILE_FIRE_COLLECTOR		0x06
#define XML_FILE_USER				0x09
#define XML_FILE_SEQUENCE_POWER     0x0A
#define XML_FILE_PAGER    	 		0x0B

//同步状态
#define SYNC_STATUS_ING				0x01	//正在同步
#define sync_status_FAIL			0x02	//同步失败

//XML更新状态
#define XML_FILE_NEWEST				0x01	//最新文件
#define XML_FILE_BUSY				0x02	//忙碌
#define XML_FILE_FAIL				0x03	//更新失败
#define XML_FILE_SUCCEED			0x04	//更新成功


/*************************************************
 * 	播放模式定义
 */
#define SINGLE_PLAY          0x01  // 单曲播放
#define SINGLE_LOOP          0x02  // 单曲循环
#define SEQUENCY_PLAY        0x03  // 顺序播放
#define LIST_LOOP            0x04  // 列表循环
#define SHUFFLE              0x05  // 随机播放


/*************************************************
 * 	语音编码类型
 */
#define DECODE_STANDARD_PCM          0x00  // 标准PCM
#define DECODE_G711         		 0x01  // G711
#define DECODE_G722         		 0x02  // G722
#define DECODE_G722_1         		 0x03  // G722.1


/*************************************************
 * 	视频编码类型
 */
enum {
    VIDEO_CODECS_UNKNOWN=0x00,      //未知编码
    VIDEO_CODECS_H264=0x01,         //H264编码
    VIDEO_CODECS_H265=0x02          //H265编码
};



/*************************************************
 * 	分布集中模式定义
 */
#define WORK_MODE_DISTRIBUTIONAL 0x01 //分布模式(默认)	0x01
#define WORK_MODE_CONCENTRATED	 0x02 //集中模式		0x02


/*************************************************
 * 	包属性定义
 */
#define PACKAGE_TYPE_NORMAL		0x00  //普通包
#define PACKAGE_TYPE_NEED_RELAY	0x01  //分控发往主机由主机转发的包
#define PACKAGE_TYPE_HOST_RELAY 0x02  //主机转发分控的包


#if LZY_COMMERCIAL_VERISON
//组播
#define CMD_SEND_SEARCH_ZONE_INFO		0xFFAE		//组播		主机或寻呼台主动发送搜索设备 注：当寻呼台收到此消息时需应答
#define CMD_RECV_ZONE_ONLINE_INFO		0xFFAA		//组播		终端在线应答或终端间隔一定时间主动发送在线信息
#define CMD_OFFLINE                     0xFFEE      //终端掉线通知
#else
//组播
#define CMD_SEND_SEARCH_ZONE_INFO		0xFFFF		//组播		主机或寻呼台主动发送搜索设备 注：当寻呼台收到此消息时需应答
#define CMD_RECV_ZONE_ONLINE_INFO		0xFFFA		//组播		终端在线应答或终端间隔一定时间主动发送在线信息
#define CMD_OFFLINE                     0xFFFE      //终端掉线通知
#endif

#define CMD_SEND_PAGING_NOTIFY			0x0001		//单播		通知终端开始寻呼
#define CMD_SEND_PCM_DATA				0x0002		//组播		发送PCM码流
#define CMD_TIME_SYNC					0x0003		//组播		由主机定时发送	//需应答给主机
#define CMD_SET_ALIAS        			0x0004		//设置设备别名
#define CMD_SET_ZONE_VOL				0X0005		//组播		设置/查询终端音量
#define CMD_GET_ZONE_DETAIL_INFO		0X0006		//组播		查询终端信息(音量、节目源、节目名称)
#define CMD_VERSION_QUERY     			0X0007		//单播/组播	响应主机的查询版本信息命令
#define CMD_FIRWARE_UPDATE				0x0008		//单播/组播	得到升级文件信息，并响应主机本机升级进度，此处因为不能立即应答，所以需要记录主机的地址
#define CMD_FIRWARE_UPDATE_PROGRESS		0x0009		//单播/组播	升级进度
#define CMD_SEND_SONG_URL				0x000A		//单播		发送歌曲地址(网络)
#define CMD_SET_ZONE_MUTE_STATUS		0x000C		//单播		设置/查询终端静音状态
#define CMD_SEND_UPDATE_INFO			0x0008		//测试发送终端升级

#define CMD_GET_HOST_FILE_SYNC_INFO		0x0010	//主机向终端请求更新文件
#define CMD_SEND_HOST_MUSICLIST_SYNC_PROGRESS	0x0012  //向主机发送同步进度
#define CMD_CONTROL_REBOOT		 				0x001C	//主机向终端发送重启指令
#define CMD_QUERY_FILE               			0x000E	//主机向终端查询存储在本地的文件信息
#define CMD_PLAY_LOCAL_MUSIC        		    0x0016	//主机向终端请求播放本地歌曲
#define CMD_PLAY_RING							0x0015	//主机向终端请求播放钟声
#define CMD_SET_ZONE_PLAYSTATUS					0x000B	//主机控制终端播放状态
#define CMD_SET_PLAYMODE						0x0017	//主机设置终端播放模式
#define CMD_SET_ZONE_IDLE_STATUS				0x0018	//主机设置终端空闲模式
#define CMD_TIME_SYN_FIRST     					0x0019	//开机请求时间同步
#define CMD_SET_ZONE_MAC	 					0x001A	//主机请求重新分配MAC
#define CMD_SEND_OFFLINE_PAGING_AGAIN	 		0x001B	//主机向终端发送再次寻呼指令
#define CMD_CONTROL_FORMAT 	 					0x001D		//主机向设备请求清除数据
#define CMD_SEND_HOST_ZONE_PAGING_OFFLINE 	 	0x001E		//寻呼台向主机发送终端寻呼中途掉线信息(组播***************)


#define CMD_HOST_QUERY_SET_WORK_MODE 		0x0031  //主机向终端设置网络模式
#define CMD_HOST_QUERY_SET_IP_INFO 		0x0032  //主机向终端设置IP属性

//记录文件
#define CMD_HOST_QUERY_RECORD_LIST 		0x0033		//主机获取设备记录文件列表
#define CMD_SEND_RECORD_FILE_CONTENT	0x0034		//终端向主机发送记录文件内容

#define TEST_COMMAND							0x1111		//test


#define CMD_QUERY_FLASH_INFO 	 0x0022			//主机向设备请求查询FLASH信息
#define CMD_QUERY_MAC_INFO 	 	 0x0023			//主机向设备请求查询/设置MAC地址
#define CMD_QUERY_TIME_INFO 	 0x0024			//主机向设备请求查询设备日期时间


#define CMD_HOST_SEND_AUDIO_COLLECTOR_DEVICE_LIST	0x0037	//主机向其他控制设备下发音频采集器设备列表


#define CMD_QUERY_SET_WORK_MODE 					0x0026		//查询/设置工作模式
#define CMD_PAGING_SEND_HOST_PLAY_SOURCE			0x0027		//寻呼机/移动设备/分控软件请求主机播放节目源（集中模式）
#define CMD_PAGING_SEND_HOST_SELECTED_ZONE		    0x003B		//寻呼台/移动设备/分控设备向主机发送选中的分区（集中模式）


#define CMD_SEND_PAGING_NOTIFY_MULTICAST			0x0040		//通知终端开始寻呼(组播)

#define CMD_TCP_HOST_SEND_ZONE_DETAIL				0x0042		//TCP模式下接收主机下发的分区状态命令

#define CMD_SEND_PAGING_NOTIFY_TCP					0x0045		//通知终端开始寻呼(TCP模式)
#define CMD_SEND_PCM_DATA_TCP						0x0046		//发送PCM码流(TCP模式)
#define CMD_SEND_OFFLINE_PAGING_AGAIN_TCP	 		0x0047		//主机向终端发送再次寻呼指令
#define CMD_SEND_MULTICAST_CMD	 					0x0059		//主机或者控制端发送组播命令给终端

#define CMD_HOST_SET_DSP_FIRMWARE_FEATURE	 		0x0060		//主机或配置工具向终端查询/设置DSP固件功能特性（组播）

#define CMD_ALL_PAGER_STATUS        				0x0064		//获取所有寻呼台设备的状态 
#define CMD_CALLING_INVITATION						0x0065		//对讲设备主叫方发起对讲邀请
#define CMD_CALLED_RESPONSE							0x0066		//对讲设备被叫方应答邀请
#define CMD_CALLED_STATUS							0x0067		//对讲设备状态反馈
#define CMD_CALLING_AUDIOSTREAM						0x0068		//对讲设备音频流传输

#define CMD_LISTEN_EVENT            				0x0071		//监听设备发起监听
#define CMD_LISTEN_RESPONSE         				0x0072      //被监听设备应答
#define CMD_LISTEN_STREAM_UPLOAD    				0x0073		//监听设备上传音频流
#define CMD_LISTEN_STATUS							0x0074		//监听设备发送监听状态

#define CMD_SEND_HOST_ACCOUNT						0X0079		//控制设备向主机发送登录账户
#define CMD_HOST_SET_SERIAL_NUMBER                  0x007B 		//主机或配置工具向终端获取设备序列号（组播)


#define CMD_SEND_HOST_GET_ACCOUNT_STORAGE_CAPACITY  0x0090 		//寻呼台获取账户存储容量
#define CMD_SEND_HOST_REQUEST_UPLOAD_SONG_FILE      0x0091      //寻呼台请求上传歌曲文件
#define CMD_SEND_HOST_NOTIFY_UPLOAD_STATUS          0x0092      //寻呼台通知服务器上传状态
#define CMD_SEND_HOST_REQUEST_DELETE_SONG           0x0094      //寻呼台对本地歌曲进行删除操作

#define CMD_HOST_SET_BROADCAST_PAGING               0x0095      //主机控制寻呼台发起广播寻呼


#define CMD_CALL_REQUEST_VIDEO                      0x0098      //对讲设备请求发起视频流
#define CMD_CALL_VIDEO_PARM                         0x0099      //对讲设备发送视频流参数
#define CMD_CALL_VIDEO_STATUS                       0x009A      //对讲设备发送视频对讲状态
#define CMD_CALL_VIDEO_STREAM                       0x009B      //对讲设备视频流传输

#define CMD_NETWORK_MODE_MULTICAST			        0x00B2		 //主机向终端设置网络模式(组播)


#define TEST_SONG_URL 	"http://************:65500/program/common/pop/andy.mp3"
#define TEST_RING_URL 	"http://************:65500/program/common/bells/钟声.mp3"

/******PCM码流传输开始/结束标记*******/
#define PCM_SEND_START		1
#define PCM_SEND_END		0

#define PAYLOAD_START        8



//节目源定义
#define SOURCE_NULL			0x00
#define SOURCE_AUX			0x01
#define SOURCE_LOCAL_PLAY	0x02
#define SOURCE_NET_PLAY		0x02
#define SOURCE_TIMING		0x03
#define SOURCE_LOCAL_MIC	0x04
#define SOURCE_CALL			0x05
#define SOURCE_FIRE_ALARM	0x08
#define SOURCE_NET_PAGING	0x09
#define SOURCE_100V			0x0A
#define SOURCE_OFFLINE		0xFF

#define SOURCE_AUDIO_COLLECTOR_MIN	0x51	//音频采集器0x51 - 0x78，支持10台音频采集器，共40个音源
#define SOURCE_AUDIO_COLLECTOR_MAX	0x78	//音频采集器0x51 - 0x78，支持10台音频采集器，共40个音源

#define SEND_RECORD_FILE_PKG_MAX 1024	//记录文件发送包大小,1K

#define NET_PACKAGE_MIN_SIZE	9		//网络包最小字节数

/***********************************************************
 * 	播放模式 宏定义
*/
#define PLAY_ONCE   	0x01    //单曲播放
#define PLAY_ONCE_LOOP  0x02	//单曲循环
#define PLAY_SEQUENCE   0x03	//顺序播放
#define PLAY_TAB_LOOP   0x04	//列表循环
#define PLAY_RANDOM     0x05	//随机播放

/***********************************************************
 *  BP1048播放状态 宏定义
*/
#define BP1048_MUSIC_STATUS_PLAY 		0x01	//播放
#define BP1048_MUSIC_STATUS_PAUSE	    0x02	//暂停
#define BP1048_MUSIC_STATUS_STOP		0x00	//停止

/***********************************************************
 * 	寻呼类型 宏定义
*/
#define PAGING_TYPE_MIC 		0x01	//MIC寻呼
#define PAGING_TYPE_MUSIC		0x03	//音乐寻呼

//寻呼状态定义
#define PAGING_STOP                  0x80
#define PAGING_START                 0x08
#define CALLING_START                0x09

/***********************************************************
 *  错误值
*/
#define SUCCEED             0    /*函数返回成功*/
#define ERROR               -1   /*函数返回错误*/
#define MEM_ERR             -2   /*内存分配失败*/
#define TRANS_ERROE			-3	 /*传递参数错误*/
#define FILE_ERROE			-4	 /*传递参数错误*/

//包数据宏定义
#define GET_PKG_CMD(p)	(((p[0]<<8)|p[1])&0xFFFF)
#define GET_PKG_LENGHT(p)	(((p[6]<<8)|p[7])&0xFFFF)
#define GET_PKG_DATA_POINT(p) (&p[8])

#endif
