//********************************************************************
// Copyright (C) 2009 ~ 2010 mingming-killer
// Licence: GPL
//
// 项目: Funcex
// 文件: StringEx.h
// 版本: V 1.0.0
//
// 说明: 扩展原来C 语言的字符串处理函数, 加入类似
//  		    MFC 和STL 的字符串处理功能
// 
// 作者: mingming-killer
// 		    Email: <EMAIL>
// 
// 日期: 2009.10.22
// 
//********************************************************************

#ifndef STRINGEX_H
#define STRINGEX_H




//*******************************************************************
//***********************  数据结构定义 ***********************
//*******************************************************************


//******************************************************************



//*****************************************************************
//************************  接口函数  **************************
//*****************************************************************

//****************************************************
// 原型	: int strexInsert (char* pstrOrg, int nPos, char* pstrInsert)
// 参数	: pstrOrg:  原字符串指针
//  			  nPos: 插入位置索引(插入到哪个位置), 从0 开始算.
//  			  pstrInsert: 插入的字符串指针
// 返回值: int: STREX_OK: 插入成功
//  			  	 STREX_ERR_NULLPOINT: 传入空指针
// 说明	: 将一个字符串插入到另外一个字符的指定位置;
// 			  注意是指定位置, 不是指定位置之后.
// 			  例如strexInsert ("Test is good", 8, "not") ; 结果是: Test is not good
//  			  注意要保证原字符串的内存长度能够容纳插入的字符串,
// 		         以及不能超过STREX_MAXCHAR .
//*****************************************************
extern int strexInsert (char* pstrOrg, int nPos, char* pstrInsert);


//****************************************************
// 原型	: int strexDelete (char* pstrOrg, int nPos, int nDel)
// 参数	: pstrOrg:  原字符串指针
//  			  nPos: 删除位置的索引(从哪个位置开始删), 从0 开始算.
//  			  nDel: 删除字符的个数
// 返回值: int: STREX_OK:删除功能
//  			  	 STREX_ERR_NULLPOINT: 传入空指针
//  				 STREX_ERR_OVERRANGE: 指定的位置越界
// 说明	: 删除字符串指定位置的指定个数个字符.
// 			  注意传入字符串长度不能超过STREX_MAXCHAR
//*****************************************************
extern int strexDelete (char* pstrOrg, int nPos, int nDel);


//****************************************************
// 原型	: int strexFind (char* pstrOrg, char* pstrFind)
// 参数	: pstrOrg:  原字符串指针
//  			  pstrFind: 要查找的字符串
// 返回值: int: 查找字符串第一次出现的位置索引(从0开始算). 
//                    返回-1 则表示没有找到
// 说明	: 在字符串中查找第一次出现子串的位置
//*****************************************************
extern int strexFind (char* pstrOrg, char* pstrFind);


//****************************************************
// 原型	: int strexFindRev (char* pstrOrg, char* pstrFind)
// 参数	: pstrOrg:  原字符串指针
//  			  pstrFind: 要查找的字符串
// 		   	  pnCount: 保存匹配目标字符串次数的变量的指针,
//					  如果传入NULL 则忽略此参数
// 返回值: int: 查找字符串最后一次出现的位置索引(从0开始算). 
//                    返回-1 则表示没有找到; 
//  			  STREX_ERR_GENERAL 代表其他错误.
// 说明	: 在字符串中查找最后一次出现子串的位置.
//*****************************************************
extern int strexFindRev (char* pstrOrg, char* pstrFind, int* pnCount);


//****************************************************
// 原型	: int strexSet (char* pstrOrg, char* pstrFind)
// 参数	: pstrOrg:  原字符串指针
//  			  pstrFind: 匹配目标字符串
//  			  pstrSet: 替换的字符串 
// 返回值: int: 匹配目标字符串的次数. 
//                    返回STREX_ERR_GENERAL 代表其他错误.
// 说明	: 在字符串中查找最后一次出现子串的位置.
//*****************************************************
extern int strexSet (char* pstrOrg, char* pstrFind, char* pstrSet);


//*****************************************************************




//*****************************************************************
//*********************  常量宏定义  **************************
//*****************************************************************

#define STREX_MAXCHAR  	2048


// 返回值定义
enum strex_ret_en
{
	STREX_ERR_GENERAL = -10,          // 一般错误
	
	STREX_OK = 0,                              // 操作成功
	STREX_ERR_NOMEMORY, 	           // 申请内存失败
	STREX_ERR_NULLPOINT,              // 指针为空
	STREX_ERR_OVERRANGE,             // 越界
	

} STREX_RET;

//*****************************************************************



#endif  // end ifdef


