/*
 * TimerP.h
 *  @encod:             UTF-8
 *  @Created on:        2013-8-30
 *  @Author:            ysl
 */
#ifndef TIMERP_H_
#define TIMERP_H_


//#include <sys/time.h>

extern char weekday_str[20];
extern char time_str[20];
extern char date_str[20];

extern const char WeekDay_CH[7][10];
extern const char WeekDay_EN[7][10];

typedef struct
{
	int	sec;		/* Seconds: 0-59 (K&R says 0-61?) */
	int	min;		/* Minutes: 0-59 */
	int	hour;	/* Hours since midnight: 0-23 */
	int	day;	/* Day of the month: 1-31 */
	int	mon;		/* Months *since* january: 0-11 */
	int	year;	/* Years since 1900 */
	int	weekday;	/* Days since Sunday (0-6) */
}_CURRENT_TIME_;

_CURRENT_TIME_ CURRENT_TIME;


/*设定时间显示函数*/
struct tm* GetSystemTime(time_t *tp);
void SetSystemTime(struct tm Time_tm);
int GetCurrentTime_s();


/*测试函数*/
void SysTimeSet_test();

#endif /* TIMERP_H_ */
