#ifndef _SONGUPLOAD_H_
#define _SONGUPLOAD_H_
#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <unistd.h>
#include "const.h"

#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)

enum
{
    UPLOAD_STATUS_FREE             = 0,    //空闲状态
    UPLOAD_STATUS_START            = 1,    //开始上传
    UPLOAD_STATUS_SUCCEED          = 2,    //上传成功
    UPLOAD_STATUS_STOP_MANUAL      = 3,    //手动停止上传
    UPLOAD_STATUS_FAILED_NORMAL    = 4,    //上传失败(普通失败)
    UPLOAD_STATUS_FAILED_FORMAT    = 5,    //上传失败(格式不符，歌曲异常)
    UPLOAD_STATUS_FAILED_NO_SPACE  = 6,    //上传失败(存储空间不足)
};

enum
{
    UPLOAD_EVENT_SUCCEED             = 1,    //上传完成
    UPLOAD_EVENT_CANCELED            = 2,    //取消上传
};

typedef struct
{
    unsigned long long storageCapacity;        //总存储空间
    unsigned long long storageSpaceUsed;       //已用空间
    unsigned long long storageSpaceRemaining;  //剩余空间
    int compress_bitrate;       //压缩比特率
    char uploadUrl[256];        //上传地址
    char uploadSongName[128];   //待上传的歌曲文件名
    char uploadListID[64];      //待上传的播放列表ID
    char uploadSongPath[256];   //待上传的歌曲文件路径
    unsigned long  uploadSongSize;          //待上传的歌曲文件大小(MB)
    unsigned long  uploadSongCompressSize; //待上传的歌曲文件预估压缩大小(MB)
    unsigned int uploadStatus;             //上传状态
    unsigned int uploadProgress;           //上传进度
}st_songUpload;

extern st_songUpload songUploadInfo;

/* curl上传文件 */
//static int curl_upload_file(const char* url, const char* filePath);
int Start_Upload_File_Thread();
#endif

#endif