#ifndef _SYSCONF_H_
#define _SYSCONF_H_
#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <unistd.h>

#include "const.h"

#include "network_client.h"
#include "backlight.h"
#include "bell.h"
#include "alarm.h"
#include "call_ring.h"
#include "com_bp1048.h"
#include "language_config.h"
#include "xml.h"
#include "SaveZoneInfo.h"
#include "SaveUserInfo.h"
#if defined(USE_SSD212) || defined(USE_SSD202)
#include "audio_params.h"
#include "module4G.h"
#include "mi_audio.h"
#include "checkUSB.h"
#include "udiskPlay.h"
#endif

#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
#include "songUpload.h"
#endif


enum {
	SSD_HARDWARE_LCD_1024_600		=	0x0001,
	SSD_HARDWARE_LCD_800_480		=   0x0002,
	SSD_HARDWARE_LCD_1280_800		=   0x0004,
	SSD_HARDWARE_LCD_600_1024		=   0x0008,
	SSD_HARDWARE_LCD_Rotation_180	=   0x0800,
	SSD_HARDWARE_LCD_Rotation_270	=   0x1000,
};


/****************变量声明***************************/

extern int g_screen_width, g_screen_height;


extern int language;
extern int Paging_status;

extern char SOFT_VERSION[50];

extern unsigned char network_init_flag;	   //网络初始化完成标志
extern int eth_link_status;		//有线网卡连接状态

extern signed char g_host_device_TimeOut;            	//主机离线计数,-1代表已经离线
extern signed char host_ready_offline_flag;			   // 主机即将离线标志

extern int g_mic_sensitivity;	//麦克风灵敏度
extern int g_mic_multiplier_val;//麦克风灵敏度量化数值
extern int g_paging_vol;	//寻呼音量

extern int g_listen_vol;	//监听音量

#if ENABLE_CALL_FUNCTION
extern int g_call_vol;	//对讲音量
extern int g_ring_vol;	//铃声音量
#endif

extern int g_Signal_Timeout_level;  //信号检测时间等级（1~6）
extern const unsigned long g_Signal_Timeout_array[6];
extern char ipAddress[20];
extern int g_system_work_mode;	//系统工作模式(默认分布模式)
extern unsigned char MAC_ADDR[6];   //MAC ADDR

#if SUPPORT_AUTO_TRIGGER
//自动触发
extern int g_auto_trigger_enable;		   //开启自动触发
extern char g_auto_trigger_groupId[40];  //自动触发分组ID
extern char g_AuxIn_signal;				  //线路输入是否有信号
extern int  g_paging_mode;				  // 0-手动寻呼 1-AUX自动信号触发
#endif

//背光时间
extern int g_backlightTimeout;
//背光时间计数
extern int g_backlight_timer_count;
extern const unsigned long g_backlight_threshold_array[6];

//MIC超时时间
extern int g_Mic_TimeOut;
//MIC启用
extern int g_Mic_enable;

extern int paging_alarm_press_flag;	//消防警报按键
extern int paging_alarm_valid_flag;	//消防警报有效标志

extern char g_device_serialNum[20];    //设备序列号

extern int g_isSupportCall;				//是否支持对讲
extern int g_isSupportPagerCall;		//是否支持寻呼台之间对讲（在g_isSupportCall=1且收到pager.Xml的情况下支持）

extern int g_isShowPlayListDirAndSongAdmin;	//话筒任意用户可以看到管理员创建的歌曲列表

//话筒优先
extern int g_AuxIn_enable;

//播放模式
extern int g_PlayMode;

//dsp音量
extern int g_dsp_volume;

//密码访问设置页面
extern int enable_password_access_settings;

extern unsigned char g_device_alias[128];	//设备别名

extern int g_system_work_mode;	//系统工作模式(默认分布模式)

extern int g_Enable_local_listening;				//开启本地监听
extern int g_paging_type;		//呼叫类型 1-寻呼台MIC寻呼	2-APP广播寻呼  3-寻呼台音乐广播

extern int g_Is_tcp_real_internet;      //TCP模式是否处于internet公网上（主机IP是169开头或者192开头代表是内网TCP）

/****************变量声明***************************/


extern int g_network_mode;	//网络模式


extern int  g_IP_Assign;					//IP分配方式(static or DHCP)
extern char  g_Static_ip_address[32];		//静态IP地址
extern char g_Subnet_Mask[32];				//子网掩码
extern char g_GateWay[32];			//网关
extern char g_Primary_DNS[32];				//主DNS服务器
extern char g_Alternative_DNS[32];			//备用DNS服务器



/*音频采集器设备列表结构体*/
struct s_audio_collector_info {
	unsigned char id;	//音源id
	char name[32];		//音源名称
};

struct s_audio_collector_list
{
	int totalNum;
	struct s_audio_collector_info *list;
};

extern struct s_audio_collector_list Audio_collector_list;

extern unsigned char IPV4_Tpye_Flag;
extern int TCP_sockfd;				//tcp连接描述符
extern 	char g_host_addr_domain_ip[48];


#define ARRAY_LEN(array) \
    (sizeof(array)/sizeof(array[0]))

/***********************************************************
 * 	config - 系统命令调用函数
*/
char *strupr(char *str);
unsigned char get_system_source();
int i2s_init(int rate, int afmt);
int Init_watdog();
bool IsFileExist(const char *file_path);
unsigned long Get_File_Size(const char *path);
void System_Reboot();
bool WildcardFileSearch(const char* pattern,const char* dirPath,char *dstFileName);
int Exec_System_CMD(const char * cmdstring);
int Popen_CMD(const char *m_cmd);
int pox_system(const char *cmd_line);


#if defined(USE_SSD212) || defined(USE_SSD202)
int Get_Screen_Rotations_Value();
int Get_Screen_LCD_type_Value(bool forceUpdate);
#endif

#endif
