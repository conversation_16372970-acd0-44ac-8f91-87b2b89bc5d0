#ifndef NETWORK_CLIENT_H_
#define NETWORK_CLIENT_H_
#include<sys/socket.h>
#include<arpa/inet.h>
#include<netinet/in.h>


/*************************************************
 * 	UDP - 客服端  - 宏定义
 */
#define _DEBUG_CLIENT_	0		//打印权限
#define IFNAME 			"eth0"	//网卡
#define IFNAME_PC1		"ens33"
#define IFNAME_PC2		"ens32"
#define UpNet_SENDNUM 	3   	//最多重复上线广播次数
#define READ_IDNUM 		8   	//读取id次数
#define CLIENT_BUF_SIZE 1450	//最大缓存数据


extern long int g_host_ip;	//主机IP地址(UDP)
extern long int g_networktools_ip;	//配置工具地址(UDP)

#define Send_MULTI_SOCKET_SEND_PCM 1
#define Send_MULTI_SOCKET_SEND_COMMAND 2
#define Send_MULTI_SOCKET_SEND_ONLINE  3
#define Send_UNICAST 4
#define Send_UDP_RESET 5
#define Send_UDP_VILLAGE 6


void nif_init();
void socket_join_multicast_membership();
static void Process_Multi_Host_Pkg(unsigned char *Pkg, int Pkg_Length,struct sockaddr_in socketAddr );
static void Process_Unicast_Pkg(unsigned char *Pkg, int Pkg_Length,struct sockaddr_in socketAddr);
static void Process_Multi_Terminal_Pkg(unsigned char *Pkg, int Pkg_Length,struct sockaddr_in socketAddr);
unsigned char Calculate_XorDat(unsigned char *Data, int Length);
void Socket_close();

//UDP数据包发送
int UDP_SendData(int socketType,char *SendBuf,int Tx_Len,long int unicastIp);
//发送命令组合
int Network_Send_Compose_CMD(unsigned char *sendBuf,unsigned int cmd,unsigned char deviceType,int Datalen,unsigned char *Data);
// 寻呼前发送UDP单播通知分区
int SendToZone_Paging_Ready_Cmd(long int ip,int flag);
//寻呼前发送UDP单播通知所有已选分区
int SendToZone_Paging_Ready_Cmd_select(int flag,int type);
//寻呼前发送UDP组播通知所有已选分区
int SendToZone_Paging_Ready_Cmd_select_multicast(int flag,int type);
//发送PCM码流
int SendToZone_PCM_Data_Cmd( unsigned char* data,int len );
//主动搜索分区命令-发送组播
int SendToZone_Search_Cmd();
//将int型IP转换为字符串
char* IPIntToChar(long int ip);
//将char型IP转换为长整型数据
long int IPcharToInt(char *ip);
//设置终端音量
int SendToZone_SetQueryVol(unsigned char type,long int ip,unsigned char vol );
//应答主机升级信息
int respond_pkg_update_status_code(unsigned char code);
//应答主机升级进度
int send_download_rate_of_progress(unsigned char progress);
//获取主机推送的升级信息
void pkg_get_update_info(unsigned char *rxbuf);
//应答主机查询固件版本
int pkg_query_firmware_version();

//应答主机XML文件更新状态
int send_host_xml_update_status(int file_type,int update_status);
//模拟主机升级
int SendToZone_UpdateInfo( long int ip );

//时间同步
void pkg_set_local_time(unsigned char *rxbuf);

//主机向终端请求播放钟声
int host_play_ring(long int ip);

//获取详细分区信息
static void pkg_get_zone_detail_info(unsigned char *rxbuf,long int ip);

//将域名转换为IP地址，如果输入为IP地址转出来也为IP地址
static void convert_domain_to_ip(unsigned char *domain);

//主机控制终端播放状态
int send_terminal_set_playstatus(char playstatus,long int ip);

//应答主机切换至空闲状态
int response_cmd_set_zone_idle_status(char *rxbuf);

//主机请求重新设置终端MAC
int host_set_terminal_mac(long int ip);

//分区信息刷新线程
int Zone_Info_Update_Thread();

//主机控制终端重启
int host_control_reboot(char *rxbuf);

//主机向设备请求清除数据
int host_control_format(char *rxbuf);

//主机向终端发送再次寻呼指令
int SendToZone_offline_Paging_Again(char *ZoneMac);

//寻呼台向主机发送终端寻呼中途掉线信息
int SendTo_Host_Zone_offline_Info(char *ip,char *startTime,char *endTime);

//设置设备别名
void pkg_set_device_alias(unsigned char *rxbuf);

//超时命令复位
int send_device_reset();

//主机向设备请求查询FLASH信息
void HOST_QUERY_FLASH_INFO(unsigned char *pkg_data);
//主机向设备请求查询/设置MAC地址
void HOST_QUERY_SET_MAC_INFO(unsigned char *pkg_data);
//主机向设备请求查询设备日期时间
void HOST_QUERY_TIME_INFO(unsigned char *pkg_data);

//空负载应答包
void respond_null_payload_pkg(unsigned char *rxbuf,long int ip);

//主机向其他控制设备下发音频采集器设备列表
void HOST_SEND_AUDIO_COLLECTOR_DEVICE_LIST(unsigned char *rxbuf);

//主机向终端设置IP属性
void HOST_QUERY_SET_IP_INFO(unsigned char *pkg_data);

//寻呼台/移动设备/分控设备向主机发送选中的分区（集中模式）
void Paging_Send_ZoneList_To_Host_Concentrated();

// 寻呼机/移动设备/分控软件请求主机播放节目源（集中模式）
void PAGING_SEND_HOST_PLAY_SOURCE(int type,char *ListId,char *MusicName);

//主机向设备查询/设置工作模式
void HOST_QUERY_SET_WORK_MODE(unsigned char *pkg_data);

//主机应答设置分区信息命令
void Host_Response_ZoneList_Concentrated(unsigned char *pkg_data);

//主机应答播放音乐(集中模式)
void Host_Response_Play_Music_Concentrated(unsigned char *pkg_data);

//主机应答/设置播放模式(集中模式)
void Host_Response_SET_Play_Mode_Concentrated(unsigned char *pkg_data);

//应答主机向终端查询存储在本地的文件信息
int response_send_host_xml_file_info(char *rxbuf,int file_type);

//向UDP客户端发送组播命令字数据
void host_udp_multicast_send_cmd_data(unsigned char *udp_buf, int send_len);

//TCP模式下接收主机下发的分区状态命令
int TCP_HANDLE_CMD_RECV_ZONE_DETAIL_INFO(unsigned char *Pkg);

//主机或配置工具向终端查询/设置DSP固件功能特性（组播）
void Host_Set_Dsp_Firmware_Feature(unsigned char *pkg_data);

//主机或配置工具向终端获取设备序列号（组播)
void Host_Set_SerialNumber(unsigned char *pkg_data);

int Send_callStatus_feedback(int status);

int Send_listen_event(unsigned char *listenedMac,unsigned char event,unsigned char type);
int Recv_listen_event(unsigned char *Pkg);
int Recv_Listen_Audio_Stream(unsigned char *Pkg);
int Send_listen_status();


//控制设备主机发送账户信息
int SendTo_Host_Account_Info();

//龙之音测试
int Send_host_get_account_storageCapacity(const char *userAccount);
int recv_get_account_storageCapacity(unsigned char *Pkg);
int Send_host_request_upload_song(char *songName,char *listID,int songSize,int songSize_compressed);
int recv_request_upload_song(unsigned char *Pkg);
int Send_host_notfify_upload_status(unsigned char event,char *listID,char *songName);
int recv_upload_song_status(unsigned char *Pkg);
int Send_host_request_delete_song(unsigned char event,char *listID,char *songName);
int recv_request_delete_song(unsigned char *Pkg);

int host_set_broadcast_paging(unsigned char *Pkg);
int send_host_set_broadcast_paging_result(int result);
#endif /* NETWORK_CLIENT_H_ */
