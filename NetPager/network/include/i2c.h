#ifndef _I2C_H_
#define _I2C_H_

#include<linux/i2c.h>
#include<linux/i2c-dev.h>
#include<stdbool.h>

#if defined(USE_SSD212) || defined(USE_SSD202)

#define I2C0_FILE_NAME "/dev/i2c-0"
#define I2C1_FILE_NAME "/dev/i2c-1"

bool i2c1_init();
bool i2c1_deinit();
bool i2c1_write(unsigned char slave_addr, unsigned char reg_addr, unsigned char value);
bool i2c1_read(unsigned char slave_addr, unsigned char reg_addr, unsigned char *value);
static bool i2c_write(int fd,unsigned char slave_addr, unsigned char reg_addr, unsigned char value);
static bool i2c_read(int fd, unsigned char slave_addr, unsigned char reg_addr, unsigned char *value);
#endif

/*********************************************************************
 * MACROS
 */
#define I2C_DEV_1       "/dev/i2c-1" //I2C设备名
#define I2C_DEV_0       "/dev/i2c-0" //I2C设备名

#define _I2C_DEBUG_     1       //调试标志，0-不打印调试信息，1-打印调试信息
#define SET_TIMEOUT     1       //I2C等待超时时间设置
#define SET_RETRIES     1       //重发次数

extern int g_i2c_fd;
/*********************************************************************
 * LOCAL FUNCTIONS
 */
void Init_i2c_mutex();
int open_i2cdev(char *interface);
void close_i2cdev(void);
int write_1byte_to_i2cdev(unsigned char dev_addr, int reg_addr, int data);
char read_1byte_from_i2cdev(unsigned char dev_addr, int reg_addr);
int write_nbyte_to_i2cdev(unsigned char dev_addr, int reg_addr, unsigned int nbyte, unsigned char *data);
int read_nbyte_from_i2cdev(unsigned char dev_addr, int reg_addr, unsigned int nbyte, unsigned char *data);

#endif
