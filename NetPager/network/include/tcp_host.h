/*
 * tcp_host.h
 *
 *  Created on: 2017-5-26
 *      Author: Administrator
 */

#ifndef TCP_HOST_H_
#define TCP_HOST_H_


#define TCP_MAX_RECV_SIZE 	1450		//MTU

struct tcp_client
{
    unsigned char pkg_data[TCP_MAX_RECV_SIZE];
    unsigned short pkg_len;
};


void host_tcp_send_data(unsigned char *send_buf, int send_len);
static void process_wan_tcp_pkg(unsigned char *Pkg);

extern int udp_wan_paging_socket;
extern char g_host_tcp_addr_domain[64];
extern char g_host_tcp_prase_ipAddress[16];		//解析后的主机IP
extern int g_host_tcp_port;		//主机TCP端口

extern int g_current_connect_tcp_id;  //当前连接的tcp_id，默认是1-表示主服务器，2表示备用服务器
extern char g_host_tcp_addr_domain2[64];	//主机地址2(域名或者IP)
extern int g_host_tcp_port2;				//主机TCP端口2

extern struct sockaddr_in Tcp_Server_addr;
extern int TCP_sockfd;
extern int g_tcp_connect_status;	//	与主机的TCP连接状态
extern int g_exit_tcp_flag;			//  退出TCP标志
extern int g_tcp_reconnect_flag;	//  TCP重连标志



#endif /* TCP_HOST_H_ */
