#ifndef GPIO_H_
#define GPIO_H_

/*****************************
 * 	GPIO	-	宏定义
 */
#define GPIO_IOC_MAGIC   'G'
#define IOCTL_GPIO_SETPINMUX	_IOW(GPIO_IOC_MAGIC, 0, int)
#define IOCTL_GPIO_REVPINMUX    _IOW(GPIO_IOC_MAGIC, 1, int)
#define IOCTL_GPIO_SETVALUE     _IOW(GPIO_IOC_MAGIC, 2, int)
#define IOCTL_GPIO_GETVALUE     _IOR(GPIO_IOC_MAGIC, 3, int)
#define GPIO_NODE		"/dev/gpio"

struct as9260_gpio_arg {
	int port;
    int pin;
	int data;
	int pinmuxback;
};
struct as9260_gpio_arg localArg;

/*****************************
 * 	GPIO - 获取模块id号
 */
int Write_Set_GPIO_Data(int u_port, int u_pin, int u_data);

int Get_Gpio_Key_Value(int keyId);
#if (IS_TRANSMITTER)
int Get_Gpio_Tx_Switch_value();
#endif
void NetLedCtrl(unsigned char IsEnable);
#endif /* GPIO_H_ */
