#ifndef PAGING_WIN_H_
#define PAGING_WIN_H_

#include "SaveZoneInfo.h"

#define SHOW_TYPE_ZONE	0
#define SHOW_TYPE_GROUP	1




//正在寻呼的分区信息

typedef struct
{
	unsigned char ZoneMac[6];
	char StartTime[32];
	char EndTime[32];
	char IsPaging_fail;
}_ZoneInfo;


typedef struct
{
	int ZoneNum;
	_ZoneInfo ZoneInfo[MAX_ZONE_NUM];
}_PagingZoneInfo;

_PagingZoneInfo PagingZoneInfo;



#define MAX_CONCENTRATED_CMD 10		//集中模式/TCP下允许同时进行发送的包数量

//选中分区结构体，用于集中模式下发送命令前通知主机(只要操作分区，则视为分区改变，此时需要重新生成Zone_Identify)
typedef struct
{
	int Zone_Num;	//选中的分区数量
	int Zone_id[MAX_ZONE_NUM];
	unsigned char Zone_MAC[6*MAX_ZONE_NUM];	//最大512个分区
	unsigned char Zone_Identify;		//分区标识
	unsigned char Host_RX_Zone_Flag;			//主机应答分区信息标志(分区标识匹配时才为1)
	unsigned char Host_respone_PkgId[3];		//主机应答Pkg_id

	unsigned char Last_CMD_Pkg_Data[MAX_CONCENTRATED_CMD][1450];	//最后一个命令包数据部分
	int Last_CMD[MAX_CONCENTRATED_CMD];			    				//最后一个命令包命令字
	int Last_CMD_Pkg_len[MAX_CONCENTRATED_CMD];				//最后一个命令包数据负载长度
	int Last_CMD_Pkg_SendCount[MAX_CONCENTRATED_CMD];				//发送计数


	unsigned char Last_play_ListId[64];		//最后一次播放列表id
	unsigned char Last_play_MusicName[128];	//最后一次播放歌曲名

}_st_Concentrated_Info;

_st_Concentrated_Info st_Concentrated_Info;


unsigned char IsZone_Group_Select(unsigned char type,unsigned char id);
void Zone_Group_SelectOrCancel(unsigned char type,int id);
void Reset_data();
int get_concentrated_pkg_valid_pos();
void set_concentrated_pkg_cmd(int pkgCmd,unsigned char *sendBuf,unsigned char sendLen);
void clear_concentrated_pkg_pos(int pkg_cmd);

#endif
