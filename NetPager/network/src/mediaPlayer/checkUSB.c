#if defined(USE_SSD212) || defined(USE_SSD202)
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <sys/un.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <linux/types.h>
#include <linux/netlink.h>
#include <errno.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <pthread.h>
#include <ftw.h>
#include <sys/mount.h>

#include "checkUSB.h"
#include "MusicFile.h"
#include "sysconf.h"

/***********************************************************
 * U盘手动挂载线程检测模块	-	U盘状态定义
*/
int g_udisk_mount_status = UDISK_STATUS_NOT_FOUND;

char* letter_array[]={"a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"};

/*********************************************************************
 * @fn      init_hotplug_sock
 *
 * @brief   探测设备节点 函数
 *
 * @param
 *
 * @return  有设备出现   - 大于0
 * 			没有设备	- 小于0
 */
static int init_hotplug_sock()
{
    const int buffersize = 1024;
    int ret;

    struct sockaddr_nl snl;
    bzero(&snl, sizeof(struct sockaddr_nl));
    snl.nl_family = AF_NETLINK;
    snl.nl_pid = getpid();
    snl.nl_groups = 1;

    int s = socket(PF_NETLINK, SOCK_DGRAM, NETLINK_KOBJECT_UEVENT);
    if (s == -1)
    {
        perror("ERROR: socket");
        return -1;
    }
    setsockopt(s, SOL_SOCKET, SO_RCVBUF, &buffersize, sizeof(buffersize));

    ret = bind(s, (struct sockaddr *)&snl, sizeof(struct sockaddr_nl));
    if (ret < 0)
    {
        perror("ERROR: bind");
        close(s);
        return -1;
    }

    return s;
}

/*********************************************************************
 * @fn      first_check_usb_status
 *
 * @brief   获取U盘状态(只是首次开机获取,执行一次）
 *
 * @param
 *
 * @return  插入返回  - 1
 * 			拔出返回  - 0
 */
static int first_check_usb_status()
{
    char usb_partion[256]={0};
    FILE *fp;
    fp = fopen("/proc/partitions", "r");
    if (fp == NULL)
	{
    	printf("\nERROR:open /proc/partitions error\n");
    	return 0;
	}

    while (!feof(fp))
	{
    	fgets(usb_partion, 256, fp);
    	/*验证 /dev下的USB设备节点*/
    	if((strstr(usb_partion,"sda")!=0) || (strstr(usb_partion,"sdb")!=0)||(strstr(usb_partion,"sdc")!=0)
    			||(strstr(usb_partion,"sdd")!=0) ||(strstr(usb_partion,"sde")!=0))
    	{
    		fclose(fp);
    		return 1;
    	}
    	memset(usb_partion, 0, sizeof(usb_partion));
	}

    fclose(fp);
    return 0;
}


/*********************************************************************
 * @fn      fn
 *
 * @brief   检测fn信息
 *
 * @param	const char * file:
 * 		 	const struct stat  *sb:
 * 		 	int flag:
 *
 * @return  返回 - 0
 */
static int fn(const char * file, const struct stat  *sb, int flag)
{
	sb=sb;
	if(flag == FTW_D)
	{
		printf("%s  -- directory\n",file);
	}
	return 0;
}

int CheckUsb_Mount(char *specialfile,char *dir,char *filesystemtype,long flags,const void *string_flags,int times)
{
	int status,ni;
	
	for(ni=0;ni<times;ni++)
	{
		status=mount(specialfile,dir,filesystemtype,flags,string_flags);
//		system("/bin/mkdir /mnt/usb > /dev/null 2>&1 || true");
//		status=system("/bin/mount -o sync -o noatime -o nodiratime -t vfat /dev/sda /mnt/usb > /dev/null 2>&1 || true");
//ZDEBUG("/bin/mount -o sync -o noatime -o nodiratime -t vfat /dev/sda /mnt/usb > /dev/null 2>&1 || true");
		if(status)
		{
			//printf("specialfile= %s,dir= %s,filesystemtype=%s\n",specialfile,dir,filesystemtype);
			//perror("USB mount fail:");
		}
		else
		{
			//ftw("/mnt/udisk",fn,500);
			printf("USB mount %s to %s success!\n",specialfile,dir);
			break;
		}

		//sleep(1);
	}
	
	return status;
}


int udisk_mount()
{
	int mountOK=0;
	int i,k;
	char MountDevice[20]={0};
	udisk_umount();
	for(i=0;i<sizeof(letter_array)/sizeof(letter_array[0]);i++)
	{
		memset(MountDevice,0,sizeof(MountDevice));
		sprintf(MountDevice,"/dev/sd%s",letter_array[i]);
		char charset[32] = {0};
		#if 1
		sprintf(charset,"iocharset=utf8");
		#else
		sprintf(charset,"iocharset=cp936");
		#endif
		if(!CheckUsb_Mount(MountDevice,(char *)UDISK_MOUNT_DIR,(char *)"vfat",0,charset,1))
		{
			mountOK=1;
			break;
		}
		for(k=0;k<6;k++)
		{
			memset(MountDevice,0,sizeof(MountDevice));
			sprintf(MountDevice,"/dev/sd%s%d",letter_array[i],k);
			if(!CheckUsb_Mount(MountDevice,(char *)UDISK_MOUNT_DIR,(char *)"vfat",0,charset,1))
			{
				mountOK=1;
				break;
			}
		}
		if(mountOK == 1)
			break;
	}
	return mountOK;
}


int udisk_umount()
{
	int status;
	int umountOK=0;

	status=umount((char *)UDISK_MOUNT_DIR);
	if(status)
	{
		perror(" USB umount:");
		status=umount2((char *)UDISK_MOUNT_DIR,MNT_DETACH);
		if(status)
		{
			perror(" USB umount2:");
		}
		else
		{
			umountOK=1;
			printf("USB unmount2 success!\n");
		}
	}
	else
	{
		printf(" USB unmount success!\n");
		umountOK=1;
	}
	
	
	return umountOK;
}

/*********************************************************************
 * @fn      check_usb_status
 *
 * @brief   获取U盘状态(回调函数）
 *
 * @param
 *
 * @return  n
 */
void *check_usb_status()
{
    if(first_check_usb_status())
    {
		#if _DEBUG_USB_
    	printf("Udisk insert\n");
		#endif
		Set_Udisk_Mount_Status(UDISK_STATUS_HOTPLUG_INSERT);
		if(udisk_mount())
		{
			Set_Udisk_Mount_Status(UDISK_STATUS_MOUNT_OK);

			if(Get_Udisk_Mount_Status()==UDISK_STATUS_MOUNT_OK)
				Set_Udisk_Mount_Status(UDISK_STATUS_SCAN_OK);
		}
		else
		{
			Set_Udisk_Mount_Status(UDISK_STATUS_MOUNT_FAILED);
		}
    }
	else
	{
		#if _DEBUG_USB_
		printf("Udisk not found!\n");
		#endif
	}

    while(1)
    {   
        if(Get_Udisk_Mount_Status() == UDISK_STATUS_HOTPLUG_INSERT)
        {
			//延时500ms再挂载
			int i=0;
			for(i=0;i<10;i++)
			{
				usleep(50000);
				if(Get_Udisk_Mount_Status()!=UDISK_STATUS_HOTPLUG_INSERT)
					break;
			}
			if(Get_Udisk_Mount_Status()!=UDISK_STATUS_HOTPLUG_INSERT)
				continue;

			if(udisk_mount())
			{
				Set_Udisk_Mount_Status(UDISK_STATUS_MOUNT_OK);
				if(Get_Udisk_Mount_Status()==UDISK_STATUS_MOUNT_OK)
					Set_Udisk_Mount_Status(UDISK_STATUS_SCAN_OK);
			}
			else
			{
				Set_Udisk_Mount_Status(UDISK_STATUS_MOUNT_FAILED);
			}
        }
        else if(Get_Udisk_Mount_Status() == UDISK_STATUS_HOTPLUG_UNPLUG)
        {
			Set_Udisk_Mount_Status(UDISK_STATUS_NOT_FOUND);
			//如果有问题，可以考虑先不卸载，而是等U盘下次插入后再处理
			usleep(200000);
			udisk_umount();
        }
        usleep(100000);
    }
}

/*********************************************************************
 * @fn      check_usb_status_thread
 *
 * @brief   启动检查U盘是否插入线程
 *
 * @param
 *
 * @return  none
 */
void check_usb_status_thread()
{
    pthread_t pid;
    pthread_attr_t Pthread_Attr;
    pthread_attr_init(&Pthread_Attr);
    pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pid, &Pthread_Attr, (void *)check_usb_status, NULL);
    pthread_attr_destroy(&Pthread_Attr);
}

/*********************************************************************
 * @fn      Init_Usb_Check_Status_M
 *
 * @brief   初始化usb手动挂载模块
 *
 * @param
 *
 * @return  none
 */
void Init_Usb_Check_Status_M()
{
	check_usb_status_thread();
}

/*********************************************************************
 * @fn      Get_Udisk_Mount_Status
 *
 * @brief   获取usb挂载状态
 *
 * @param
 *
 * @return  挂载状态标志
 */
int Get_Udisk_Mount_Status()
{
	return g_udisk_mount_status;
}

/*********************************************************************
 * @fn      Set_Udisk_Mount_Status
 *
 * @brief   设置挂载 u盘 状态
 *
 * @param	int u_UMount_Status: USB挂载状态
 *
 * @return  none
 */
void Set_Udisk_Mount_Status(int u_UMount_Status)
{
	if(u_UMount_Status == g_udisk_mount_status)
	{
		return;
	}
	if(u_UMount_Status == UDISK_STATUS_HOTPLUG_INSERT && g_udisk_mount_status> UDISK_STATUS_HOTPLUG_UNPLUG)
	{
		return;
	}
	if(u_UMount_Status == UDISK_STATUS_HOTPLUG_UNPLUG && g_udisk_mount_status <= UDISK_STATUS_HOTPLUG_UNPLUG)
	{
		return;
	}

	if((u_UMount_Status == UDISK_STATUS_MOUNT_FAILED || u_UMount_Status == UDISK_STATUS_MOUNT_OK ) &&\
		 g_udisk_mount_status != UDISK_STATUS_HOTPLUG_INSERT)
	{
		return;
	}

	if(u_UMount_Status == UDISK_STATUS_SCAN_OK && g_udisk_mount_status != UDISK_STATUS_MOUNT_OK)
	{
		return;
	}

	g_udisk_mount_status = u_UMount_Status;
	printf("Set_Udisk_Mount_Status:%d\n",u_UMount_Status);

	if(u_UMount_Status == UDISK_STATUS_MOUNT_OK)
	{
		udisk_set_plug_status(1);
	}
	else if(u_UMount_Status == UDISK_STATUS_NOT_FOUND)
	{
		udisk_set_plug_status(0);
	}
}

#endif