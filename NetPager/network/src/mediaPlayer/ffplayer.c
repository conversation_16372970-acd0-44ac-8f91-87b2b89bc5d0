#if defined(USE_SSD212) || defined(USE_SSD202)

#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include <stdlib.h>
#include <pthread.h>
#include <unistd.h>
#include <signal.h>

#include "mi_common.h"
#include "mi_sys.h"
#include "mi_ao.h"

#include "audio_params.h"
#include "audioProcess.h"

#include "sysconf.h"

#include "ffplayer.h"

//#define DUMP_FILE

#define ExecFunc(result, value)\
    if (result != value)\
    {\
        printf("[%s %d]exec function failed\n", __FUNCTION__, __LINE__);\
        return -1;\
    }\


#define COMPOSE_ID(a,b,c,d) ((a) | ((b)<<8) | ((c)<<16) | ((d)<<24))
#define WAV_RIFF COMPOSE_ID('R','I','F','F')
#define WAV_WAVE COMPOSE_ID('W','A','V','E')
#define WAV_FMT COMPOSE_ID('f','m','t',' ')
#define WAV_DATA COMPOSE_ID('d','a','t','a')

static MI_S32 AoDevId = 0;
static MI_S32 AoChn = 0;

static bool g_bExit = false;
static bool g_bPlayThreadRun = false;
bool g_bDecodeDone = false;
static pthread_t g_playThread = 0;

#ifdef DUMP_FILE
static FILE *fp = NULL;
#endif

int insert_wave_header(FILE* fp, long data_len, int sampleRate, int chnCnt)
{
    int len;
    WaveHeader* header;
    WaveChunkHeader* chunk;
    WaveFmtBody* body;

    fseek(fp, 0, SEEK_SET);        //写到wav文件的开始处

    len = sizeof(WaveHeader)+sizeof(WaveFmtBody)+sizeof(WaveChunkHeader)*2;
    char* buf = (char*)malloc(len);
    header = (WaveHeader*)buf;
    header->magic = WAV_RIFF;
    header->length = data_len + sizeof(WaveFmtBody)+sizeof(WaveChunkHeader)*2 + 4;
    header->type = WAV_WAVE;

    chunk = buf+sizeof(WaveHeader);
    chunk->type = WAV_FMT;
    chunk->length = 16;

    body = (WaveFmtBody*)(buf+sizeof(WaveHeader)+sizeof(WaveChunkHeader));
    body->format = (u_short)0x0001;      		//编码方式为pcm
    body->channels = (u_short)chnCnt;	 		//(u_short)0x02;      //声道数为2
    body->sample_fq = sampleRate;		 		//44100;             //采样频率为44.1k
    body->byte_p_sec = sampleRate * chnCnt * 2;	//176400;           //每秒所需字节数 44100*2*2=采样频率*声道*采样位数 
    body->byte_p_spl = (u_short)0x4;     		//对齐无意义
    body->bit_p_spl = (u_short)16;       		//采样位数16bit=2Byte


    chunk = (WaveChunkHeader*)(buf+sizeof(WaveHeader)+sizeof(WaveChunkHeader)+sizeof(WaveFmtBody));
    chunk->type = WAV_DATA;
    chunk->length = data_len;

    fwrite(buf, 1, len, fp);
    free(buf);
    return 0;
}

void *mp3DecodeProc(void *pParam)
{
    AudioState *is = (AudioState *)pParam;
    AVPacket *packet = av_packet_alloc();
    AVFrame *frame = av_frame_alloc();
    uint8_t *out[] = { is->audio_buf };
    int data_size = 0, got_frame = 0;
    int wavDataLen = 0;
#ifdef DUMP_FILE    
    int file_data_size = 0;
#endif

    while(g_bPlayThreadRun)    //1.2 循环读取mp3文件中的数据帧
    {
        if(udiskInfo.playStatus != BP1048_MUSIC_STATUS_PLAY)
        {
            usleep(100000);
            continue;
        }
        if (av_read_frame(is->pFormatCtx, packet) < 0)
        {
            printf("av_read_frame <0!\n");
            av_packet_unref(packet);
            break;
        }
        if(packet->stream_index != is->sndindex)
        {
            av_packet_unref(packet);
            continue;
        }
        if(avcodec_decode_audio4(is->sndCodecCtx, frame, &got_frame, packet) < 0) //1.3 解码数据帧
        {
            printf("avcodec_decode_audio4<0!\n");
            av_packet_unref(packet);
            break;
        }

        if(got_frame <= 0) /* No data yet, get more frames */
        {
            av_packet_unref(packet);
            continue;
        }
        data_size = av_samples_get_buffer_size(NULL, is->sndCodecCtx->channels, frame->nb_samples, is->sndCodecCtx->sample_fmt, 1);
        //1.4下面将ffmpeg解码后的数据帧转为我们需要的数据(关于"需要的数据"下面有解释)
        if(NULL==is->swr_ctx)
        {
            if(is->swr_ctx != NULL)
                swr_free(&is->swr_ctx);
            printf("frame: channnels=%d,format=%d, sample_rate=%d", frame->channels, frame->format, frame->sample_rate);
            is->swr_ctx = swr_alloc_set_opts(NULL, AV_CH_LAYOUT_STEREO, AV_SAMPLE_FMT_S16, WAV_SAMPLERATE, av_get_default_channel_layout(frame->channels), frame->format, frame->sample_rate, 0, NULL);
            if(is->swr_ctx == NULL)
            {
                printf("swr_ctx == NULL");
            }
            swr_init(is->swr_ctx);
        }
        #if 0
        int dst_nb_samples = av_rescale_rnd(swr_get_delay(is->swr_ctx, frame->sample_rate) +
                                frame->nb_samples, WAV_SAMPLERATE, frame->sample_rate, AV_ROUND_UP);
        wavDataLen = swr_convert(is->swr_ctx, out, dst_nb_samples, (const uint8_t **)frame->extended_data, frame->nb_samples);
        #endif
        //如果固定采样帧，那么一个解码帧数据量超出一次swr_convert的数量，所以需要分多次

        wavDataLen = swr_convert(is->swr_ctx, out, MI_AUDIO_SAMPLE_PER_FRAME, (const uint8_t **)frame->extended_data, frame->nb_samples);
        if(wavDataLen<=0)
        {
            av_packet_unref(packet);
            continue;
        }
        //printf("wavDataLen=%d\n",wavDataLen);
        //先降10dB，再设置输出DAC参数
        //ffmpeg比较特别，如果歌曲是单声道，那么转换为立体声后会每个声道的音量会下降
        //所以需要判断声道数，如果是立体声，降-10dB；如果是单声道，降-7dB
        int16_t  progcess_stero[2048]={0};
        memcpy(progcess_stero,is->audio_buf,MI_AUDIO_SAMPLE_PER_FRAME*2*2);
        int SamplesPreFrame = MI_AUDIO_SAMPLE_PER_FRAME;
        int i=0;
        int stero_gain_val =  1295;     // -10dB
        int mono_gain_val =  1900;     // -7dB
        int gain_process = (frame->channels == 1) ? mono_gain_val:stero_gain_val;
        #if 1
        for(i=0; i<SamplesPreFrame; i++)
        {
            progcess_stero[2 * i + 0] = limit_value_16bit( ((((int32_t)progcess_stero[2 * i + 0]) * gain_process + 2048) >> 12) );
            progcess_stero[2 * i + 1] = limit_value_16bit( ((((int32_t)progcess_stero[2 * i + 1]) * gain_process + 2048) >> 12) );
        }

        int16_t mono_buf[1024]={0};
        StereoToMono(progcess_stero,SamplesPreFrame,mono_buf);
        //立体声转单声道并发送至终端
        int sysSource = get_system_source();
        if(sysSource == SOURCE_NET_PAGING && g_paging_type == PAGING_TYPE_MUSIC)
            send_paging_stream(mono_buf,wavDataLen*2);

        //500/3.162=158mv,158mv/1.585=100mv，功放端3.75V

        //监听音量
        int t_sysvol=mSysVol[(unsigned int)(g_listen_vol)];
        if(t_sysvol)    //如果非0，那么放大3dB，兼容8欧输出
        {
            t_sysvol = t_sysvol*1.42;
        }

        for(i=0; i<SamplesPreFrame; i++)
        {
            progcess_stero[2 * i + 0] = limit_value_16bit( (((((int32_t)progcess_stero[2 * i + 0]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L] + 2048) >> 12 );
            progcess_stero[2 * i + 1] = limit_value_16bit( (((((int32_t)progcess_stero[2 * i + 1]) * 4096 + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R] + 2048) >> 12 );
        }

        #endif


        MI_AUDIO_Frame_t stAoSendFrame;
        memset(&stAoSendFrame, 0x0, sizeof(MI_AUDIO_Frame_t));
        #if USE_SSD212
        stAoSendFrame.u32Len[0] = wavDataLen * 2 * 2;
        #elif USE_SSD202
        stAoSendFrame.u32Len = wavDataLen * 2 * 2;
        #endif
        stAoSendFrame.apVirAddr[0] = (unsigned char*)progcess_stero;//(unsigned char*)is->audio_buf;
        stAoSendFrame.apVirAddr[1] = NULL;
        int s32Ret = MI_SUCCESS;
        
        pthread_mutex_lock(&mutex_audio_out);
        if(mi_ao_init_flag && mi_ao_sampleRate==32000)
        {
            do
            {
                s32Ret = MI_AO_SendFrame(AoDevId, AoChn, &stAoSendFrame, -1);

            }
            while (s32Ret == MI_AO_ERR_NOBUF);
        }
        pthread_mutex_unlock(&mutex_audio_out);

        while(wavDataLen>0)
        {
            wavDataLen = swr_convert(is->swr_ctx, out, MI_AUDIO_SAMPLE_PER_FRAME, NULL, 0);
            //printf("wavDataLen2=%d\n",wavDataLen);
            if(wavDataLen<=0)
            {
                break;
            }
            //先降10dB，再设置输出DAC参数
            memcpy(progcess_stero,is->audio_buf,MI_AUDIO_SAMPLE_PER_FRAME*2*2);
            i=0;
            #if 1
            for(i=0; i<SamplesPreFrame; i++)
            {
                progcess_stero[2 * i + 0] = limit_value_16bit( ((((int32_t)progcess_stero[2 * i + 0]) * gain_process + 2048) >> 12) );
                progcess_stero[2 * i + 1] = limit_value_16bit( ((((int32_t)progcess_stero[2 * i + 1]) * gain_process + 2048) >> 12) );
            }

            StereoToMono(progcess_stero,SamplesPreFrame,mono_buf);
            //立体声转单声道并发送至终端
            if(sysSource == SOURCE_NET_PAGING && g_paging_type == PAGING_TYPE_MUSIC)
                send_paging_stream(mono_buf,wavDataLen*2);

            for(i=0; i<SamplesPreFrame; i++)
            {
                progcess_stero[2 * i + 0] = limit_value_16bit( (((((int32_t)progcess_stero[2 * i + 0]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L] + 2048) >> 12 );
                progcess_stero[2 * i + 1] = limit_value_16bit( (((((int32_t)progcess_stero[2 * i + 1]) * 4096 + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R] + 2048) >> 12 );
            }

            #endif

            memset(&stAoSendFrame, 0x0, sizeof(MI_AUDIO_Frame_t));
            #if USE_SSD212
            stAoSendFrame.u32Len[0] = wavDataLen * 2 * 2;
            #elif USE_SSD202
            stAoSendFrame.u32Len = wavDataLen * 2 * 2;
            #endif
            stAoSendFrame.apVirAddr[0] = (unsigned char*)progcess_stero;//(unsigned char*)is->audio_buf;
            stAoSendFrame.apVirAddr[1] = NULL;
            s32Ret = MI_SUCCESS;

            pthread_mutex_lock(&mutex_audio_out);
            if(mi_ao_init_flag && mi_ao_sampleRate==32000)
            {
                do
                {
                    s32Ret = MI_AO_SendFrame(AoDevId, AoChn, &stAoSendFrame, -1);

                }
                while (s32Ret == MI_AO_ERR_NOBUF);
            }
            pthread_mutex_unlock(&mutex_audio_out);
        }

        av_packet_unref(packet);
    }

	if (is->swr_ctx != NULL)
		swr_free(&is->swr_ctx);
    if(packet)
    {
        av_packet_free(&packet);
        packet=NULL;
    }
    av_frame_free(&frame);
    //如果是强行退出，那么播放完成标志不为true，不自动下一曲
    if(g_bPlayThreadRun)
        g_bDecodeDone = true;

    printf("Play End...\n");

    return NULL;
}

int init_ffplayer(AudioState* is, char* filepath)
{
    int i=0;
    int ret;
    is->sndindex = -1;

    g_bDecodeDone = false;

#ifdef DUMP_FILE
    int wavHeaderLen = 0;
#endif

    if(NULL == filepath)
    {
        printf("input file is NULL");
        return -1;
    }

#if 0
    avcodec_register_all();
    //avfilter_register_all();
    av_register_all();
#endif
    is->pFormatCtx = avformat_alloc_context();

    if(avformat_open_input(&is->pFormatCtx, filepath, NULL, NULL)!=0)
        return -1;

    if(avformat_find_stream_info(is->pFormatCtx, NULL)<0 || is->pFormatCtx->nb_streams == 0)
        return -1;
    av_dump_format(is->pFormatCtx,0, 0, 0);
    //is->videoindex = av_find_best_stream(is->pFormatCtx, AVMEDIA_TYPE_VIDEO, is->videoindex, -1, NULL, 0); 
    is->sndindex = av_find_best_stream(is->pFormatCtx, AVMEDIA_TYPE_AUDIO,-1, -1, NULL, 0);
    printf("videoindex=%d, sndindex=%d\n", is->videoindex, is->sndindex);
    if(is->sndindex == 0)
    {
        is->sndCodecCtx = is->pFormatCtx->streams[is->sndindex]->codec;
        is->sndCodec = avcodec_find_decoder(is->sndCodecCtx->codec_id);
        if(is->sndCodec == NULL)
        {
            printf("Codec not found\n");
            return -1;
        }
        if(avcodec_open2(is->sndCodecCtx, is->sndCodec, NULL) < 0)
            return -1;
    }
    else
    {
        printf("is->sndindex != 0,error!\n");
        return -1;
    }

#ifdef DUMP_FILE
	fp = fopen("./test.wav", "wb+");
	if (fp)
	{
		wavHeaderLen = sizeof(WaveHeader) + sizeof(WaveFmtBody) + sizeof(WaveChunkHeader) * 2;
		fseek(fp, wavHeaderLen, SEEK_SET);
		printf("wavHeaderLen=%d", wavHeaderLen);
	}
#endif

    g_bPlayThreadRun = true;
    ret = pthread_create(&g_playThread, NULL, mp3DecodeProc, (void *)is);
    if (ret != 0) 
    {
        printf("pthread_create mp3DecodeProc failed!\n");
    }
    return 0;
}

void deinit_ffplayer(AudioState* is)
{
    g_bPlayThreadRun = false;
    printf("deinit_ffplayer1...\n");
    if (g_playThread)
    {
        printf("deinit_ffplayer2...\n");
        pthread_join(g_playThread, NULL);
        g_playThread = NULL;
    }
    
    if(is!=NULL)
    {
        printf("deinit_ffplayer3...\n");
        avcodec_close(is->sndCodecCtx);
        //avcodec_free_context(is->sndCodecCtx);
        avformat_close_input(&is->pFormatCtx);
    }
}

void signalHandler(int signo)
{
    switch (signo)
    {
        case SIGINT:
            printf("catch exit signal\n");
            g_bExit = true;
            break;
        default:
            break;
    }
}

int init_ao(signed short channels, unsigned int dwSamplesPerSec, int s32AoVolume)
{
    MI_AUDIO_Attr_t stAoSetAttr, stAoGetAttr;
    MI_AO_AdecConfig_t stAoSetAdecConfig, stAoGetAdecConfig;
    MI_AO_VqeConfig_t stAoSetVqeConfig, stAoGetVqeConfig;
    MI_AO_ChnParam_t stAoChnParam;
    MI_U32 u32DmaBufSize;

    memset(&stAoSetAttr, 0x0, sizeof(MI_AUDIO_Attr_t));
    stAoSetAttr.eBitwidth = E_MI_AUDIO_BIT_WIDTH_16;
    stAoSetAttr.eWorkmode = E_MI_AUDIO_MODE_I2S_MASTER;
    stAoSetAttr.WorkModeSetting.stI2sConfig.bSyncClock = FALSE;
    stAoSetAttr.WorkModeSetting.stI2sConfig.eFmt = E_MI_AUDIO_I2S_FMT_I2S_MSB;
    stAoSetAttr.WorkModeSetting.stI2sConfig.eMclk = E_MI_AUDIO_I2S_MCLK_0;
    stAoSetAttr.eSamplerate = (MI_AUDIO_SampleRate_e)dwSamplesPerSec;
    //stAoSetAttr.u32PtNumPerFrm = 1024;//MI_AUDIO_SAMPLE_PER_FRAME;
    stAoSetAttr.u32PtNumPerFrm = dwSamplesPerSec / (int)E_MI_AUDIO_SAMPLE_RATE_8000 * MI_AUDIO_SAMPLE_PER_FRAME;//MI_AUDIO_SAMPLE_PER_FRAME;
    stAoSetAttr.u32ChnCnt = 1;

    if (channels == 2)
    {
        stAoSetAttr.eSoundmode = E_MI_AUDIO_SOUND_MODE_STEREO;
    }
    else if (channels == 1)
    {
        stAoSetAttr.eSoundmode = E_MI_AUDIO_SOUND_MODE_MONO;
    }

    ExecFunc(MI_AO_SetPubAttr(AoDevId, &stAoSetAttr), MI_SUCCESS);
    ExecFunc(MI_AO_GetPubAttr(AoDevId, &stAoGetAttr), MI_SUCCESS);
    ExecFunc(MI_AO_Enable(AoDevId), MI_SUCCESS);
    ExecFunc(MI_AO_EnableChn(AoDevId, AoChn), MI_SUCCESS);
    #if USE_SSD212
    ExecFunc(MI_AO_SetVolume(AoDevId, 0, s32AoVolume, E_MI_AO_GAIN_FADING_OFF), MI_SUCCESS);
    #elif USE_SSD202
    ExecFunc(MI_AO_SetVolume(AoDevId, s32AoVolume), MI_SUCCESS);
    #endif

    printf("ao init, volume value is %d\n", s32AoVolume);

    return 0;
}

void deinit_ao()
{
    MI_AO_ChnState_t stChnState;
    do
    {
        MI_AO_QueryChnStat(AoDevId, AoChn, &stChnState);

        //printf("busyNum = %d\n", stChnState.u32ChnBusyNum);

        if (stChnState.u32ChnBusyNum <= MI_AUDIO_SAMPLE_PER_FRAME)
            break;
	    
	usleep(10000);
    }
    while (1);

    MI_AO_DisableChn(AoDevId, AoChn);
    MI_AO_Disable(AoDevId);
}

#if 0
int main(int argc, char **argv)
{
    int ret = 0;
    int volume = -10;
    AudioState* is = (AudioState*) av_mallocz(sizeof(AudioState));

    signal(SIGINT, signalHandler);

    if (argc < 2)
    {
        printf("please input a mp3 file!\n");
        printf("eg: ./mp3Player [file] [volume] , the default volume is -10\n");
        return -1;
    }

    if (argc > 2)
    {
        volume = atoi(argv[2]);
    }

    if (init_ao(2, WAV_SAMPLERATE, volume) != 0)
    {
        printf("init ao error\n");
        return -1;
    }
    
    if( (ret=init_ffplayer(is, argv[1])) != 0)
    {
        printf("init_ffmpeg error");
        goto ao_deinit;
    }

    while (1)
    {
        if (g_bExit || g_bDecodeDone)
        {
            break;
        }

        usleep(30000);
    }

    deinit_ffplayer(is);

ao_deinit:    
    deinit_ao();

    av_free(is);

    return 0;
}
#endif





int ffmpeg_get_media_song_bitRate(char *filename)
{
    int ret=0;
    int bitRate=0;
    AVFormatContext *fmt_ctx = NULL;

    // 打开输入媒体文件
    if ((ret = avformat_open_input(&fmt_ctx, filename, NULL, NULL)) < 0) {
        av_log(NULL, AV_LOG_ERROR, "Cannot open input file\n");
        return 0;
    }

    // 探测媒体文件信息
    if ((ret = avformat_find_stream_info(fmt_ctx, NULL)) < 0) {
        av_log(NULL, AV_LOG_ERROR, "Cannot find stream information\n");
        avformat_close_input(&fmt_ctx);
        return 0;
    }

    // 判断是否为 MP3 音频文件
    AVStream *stream = fmt_ctx->streams[0];
    if (stream->codecpar->codec_type == AVMEDIA_TYPE_AUDIO && stream->codecpar->codec_id == AV_CODEC_ID_MP3) {
        av_log(NULL, AV_LOG_INFO, "File is a valid MP3 audio file\n");


        //获取元信息，album、artist、title、等
        //AVDictionaryEntry *tag = NULL;
        //while (tag = av_dict_get(fmt_ctx->metadata, "", tag, AV_DICT_IGNORE_SUFFIX))
        //{
        //    qDebug()<<tag->key<<tag->value;
        //}
        #if 0
        //时长
        //duration/AV_TIME_BASE单位为秒
        qDebug()<<"duration"<<guard.formatCtx->duration/(AV_TIME_BASE/1000.0)<<"ms";
        //文件格式，如wav
        qDebug()<<"format"<<guard.formatCtx->iformat->name<<":"<<guard.formatCtx->iformat->long_name;
        //帧数
        qDebug()<<"n stream"<<guard.formatCtx->nb_streams;
        #endif
        //获取容器的比特率
        bitRate=fmt_ctx->bit_rate/1000; //kbps
    }
    // 判断是否为 WAV 音频文件
    else if(stream->codecpar->codec_type == AVMEDIA_TYPE_AUDIO && stream->codecpar->codec_id == AV_CODEC_ID_PCM_S16LE)
    {
        const char *format_name = fmt_ctx->iformat->name;
        if (strcmp(format_name, "wav") != 0) {
            av_log(NULL, AV_LOG_INFO, "File is a valid WAV audio file\n");
        }
        else{
            bitRate=fmt_ctx->bit_rate/1000; //kbps
        }
    }

    // 关闭输入媒体文件
    avformat_close_input(&fmt_ctx);

    return bitRate;
}



#endif