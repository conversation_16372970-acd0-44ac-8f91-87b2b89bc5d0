#ifndef MUSICFILE_H_
#define MUSICFILE_H_

#include "const.h"

/*************************************************
 * 	系统扫描音频文件部分  - 文件操作宏定义
 */
#define _DEBUG_MUSIC_        0		//打印信息
#define MUSICFILENAMELEN     128	//歌曲文件长度
#define MUSICFILEDIRLEN      128	//歌曲目录长度
#define MUSICUSBDIRMAX       50		//歌曲最大目录数
#define MUSICUSBFILEMAX      1000    //最大USB歌曲数
#define MUSICNETDIRMAX       1000   //最大网络音乐目录（列表）数
#define MUSICNETLISTSONGMAX  1000	//最大列表歌曲数



/*************************************************
 * 	系统扫描音频文件部分  - 音乐结构体定义
 */
typedef struct
{
    char cName[MUSICFILENAMELEN];   //歌曲名称
    char duration[10];			//歌曲总时间
    char size[20];				//歌曲大小
    char SongUrl[256];			//歌曲地址
    char IsDownload;				//下载标志(寻呼台下用作集中模式歌曲播放结果(0x02-不支持此格式  0x03歌曲不存在))
    char UserName[32+1];	        //列表所属的用户名称 
}FILENAME;

typedef struct
{
	char id[64];					//目录id
    int FileStartIndex;				//歌曲起始id(从1开始，兼容1048)
    char cDirName[MUSICFILEDIRLEN];	//目录路径
    FILENAME *musicfile;            //对应目录下的歌曲文件相关信息
    int nFileNum;                   //对应文件夹下的歌曲数
    unsigned char DateTime[32];		//单个目录更新日期	(与xml每个列表的更新时间进行对比,目录下全部歌曲下载完成后保存)
    unsigned char DateTime_TEMP[32];//单个目录更新日期(临时保存)
    char UserName[32+1];	        //列表所属的用户名称
}MUSICDIR;

typedef struct
{
    int DirNum;		   				//usb目录个数
    unsigned char DateTime[32];		//更新日期
    MUSICDIR SongDir[MUSICNETDIRMAX];          	//存贮SD对应目录下的相关信息
    int update_status;				//0代表没有进行同步，1代表正在同步
    int download_num;				//正在下载的歌曲数
}MUSICFILELIST;

extern MUSICFILELIST serverMusicList;


/*************************************************
 * 	系统扫描音频文件部分  - 外部调用函数
 */
int Init_Scan_SD_OR_USB_Info(MUSICFILELIST *musicList,char *s_source);
void Reset_UdiskMusiclist(MUSICFILELIST *musicList);

#endif /* MUSICFILE_H_ */
