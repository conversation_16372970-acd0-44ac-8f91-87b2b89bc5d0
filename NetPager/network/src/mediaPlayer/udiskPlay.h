#ifndef UDISK_PLAY_H_
#define UDISK_PLAY_H_

#if defined(USE_SSD212) || defined(USE_SSD202)

#include "MusicFile.h"

typedef struct {
	unsigned char IsPlug;
	unsigned char playStatus;
	unsigned char playMode;
    int current_playFileId;
	MUSICFILELIST musicList;
}st_udisk_info;
extern st_udisk_info udiskInfo;

void udisk_set_plug_status(int IsPlug);
void udisk_play(int IsSelfcall,int dirId,int songId);
void udisk_stop();

#endif

#endif