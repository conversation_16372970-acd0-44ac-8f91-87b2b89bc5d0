#if defined(USE_SSD212) || defined(USE_SSD202)

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include "sysconf.h"

#include "ffplayer.h"
#include "checkUSB.h"
#include "udiskPlay.h"
#include "win.h"

st_udisk_info udiskInfo;

AudioState* is;

extern bool g_bDecodeDone;

void udisk_set_plug_status(int IsPlug)
{
    if(udiskInfo.IsPlug == IsPlug)
        return;
    udiskInfo.IsPlug = IsPlug;
    if(IsPlug)  //插入时扫描目录
    {
        Init_Scan_SD_OR_USB_Info(&udiskInfo.musicList,UDISK_MOUNT_DIR);
        refresh_usbPlay(0);
    }
    else        //拔出时需要清理相关工作
    {
        //停止播放并刷新页面
        stop_udiskPlay(0);
        Reset_UdiskMusiclist(&udiskInfo.musicList);
        //开始清理，并重置udiskInfo相关变量
        udiskInfo.current_playFileId=0;
    }
}


void udisk_play(int IsSelfcall,int dirId,int songId)
{
    //组合成播放路径
    char path[256]={0};
    sprintf(path,"%s/%s",udiskInfo.musicList.SongDir[dirId].cDirName,udiskInfo.musicList.SongDir[dirId].musicfile[songId].cName);
    printf("udisk_play:%s\n",path);

    if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}

    udisk_stop();

    /* clear audio outbuf */
    //MI_AO_ClearChnBuf(0, 0);

    is = (AudioState*) av_mallocz(sizeof(AudioState));
    int ret=-1;
    static int ffplay_succeed_cnt=0;
    if( (ret=init_ffplayer(is, path)) != 0)
    {
        ffplay_succeed_cnt++;
        printf("init_ffplayer error");
        #if 1    //20231102遇到异常歌曲时，暂不进行下一曲，避免列表内全是异常歌曲时，循环下一曲，系统资源被重复占用
        // 20240705 遇到异常歌曲，需要进行下一曲
        if(udiskInfo.playMode != BP1048_UDISK_PLAY_MODE_SINGLE_LOOP)
        {
            if(ffplay_succeed_cnt<=5)   //连续五首歌错误，则不再继续播放
            {
                g_bDecodeDone=true; //设置标志，以便播放下一曲
            }
        }
        #endif
    }
    else
    {
        ffplay_succeed_cnt=0;
        udiskInfo.playStatus = BP1048_MUSIC_STATUS_PLAY;
    }
    
    udiskInfo.current_playFileId = udiskInfo.musicList.SongDir[dirId].FileStartIndex+songId;
    printf("udiskInfo.current_playFileId=%d\n",udiskInfo.current_playFileId);

    refresh_usbPlay(1);

    if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}

void udisk_stop()
{
    if(is!=NULL)
    {
        deinit_ffplayer(is);
        av_free(is);
        is=NULL;
        printf("udisk_stop succeed!\n");
    }

    udiskInfo.playStatus = BP1048_MUSIC_STATUS_STOP;
}


void udisk_pauseOrResume()
{
    if(udiskInfo.playStatus == BP1048_MUSIC_STATUS_PLAY)
    {
        udiskInfo.playStatus = BP1048_MUSIC_STATUS_PAUSE;
    }
    else if(udiskInfo.playStatus == BP1048_MUSIC_STATUS_PAUSE)
    {
        udiskInfo.playStatus = BP1048_MUSIC_STATUS_PLAY;
    }
}


/*********************************************************************
 * @fn      pthread_udisk_play
 *
 * @brief  	udisk播放处理进程
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void *pthread_udisk_play(void *p_arg)
{
    while(1)
    {
        if(!udiskInfo.IsPlug)
        {
            usleep(100000);
            continue;
        }
        //todo 计时：当处于播放状态，且没有寻呼，没有本地监听时，连续超过30分钟，应停止播放，节省资源
        if(g_bDecodeDone)
        {
            int curPlayFileId=udiskInfo.current_playFileId;
            int dirId=udisk_get_DirId_pos(curPlayFileId);
            int fileId=udisk_get_fileId_pos(curPlayFileId);
            if(dirId == -1 || fileId == -1)
            {
                dirId=0;
                fileId=0;
            }
            else
            {
                //判断播放模式
                if(udiskInfo.playMode == BP1048_UDISK_PLAY_MODE_SINGLE_LOOP)
                {
                    //无需改变，重新开始播放即可
                }
                else if(udiskInfo.playMode == BP1048_UDISK_PLAY_MODE_LIST_LOOP)
                {
                    curPlayFileId++;
                    if(curPlayFileId>=udiskInfo.musicList.SongDir[dirId].FileStartIndex+udiskInfo.musicList.SongDir[dirId].nFileNum)
                    {
                        curPlayFileId = udiskInfo.musicList.SongDir[dirId].FileStartIndex;
                    }
                }
                fileId=udisk_get_fileId_pos(curPlayFileId);
                if(fileId==-1)
                {
                    dirId=0;
                    fileId=0;
                }
            }

            printf("Udisk Play:dirId=%d,fileId=%d\n",dirId,fileId);
            udisk_play(0,dirId,fileId);
            udisk_current_playId_change(0,curPlayFileId);
        }

        usleep(100000);
    }
}

void Create_udisk_play_task(void)
{
    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)pthread_udisk_play, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}

#endif
