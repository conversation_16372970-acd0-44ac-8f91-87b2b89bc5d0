#if defined(USE_SSD212) || defined(USE_SSD202)

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include "sysconf.h"

#include "I2S.h"
#include "sourceControl.h"
#include "uart_adc.h"

#if SUPPORT_CODEC_G722
#include "g722/g722_encoder.h"
#include "g722/g722_decoder.h"
#endif

#include "mi_audio.h"


static int paging_stream_send_cnt=0;
static int send_bell_type=0;
static int send_alarm_type=0;
static unsigned long paging_bell_data_read=0;
static unsigned long paging_alarm_data_read=0;

static const unsigned char *paging_bell_pcm=NULL;
static int paging_bell_length=0;
static const unsigned char *paging_alarm_pcm=NULL;
static int paging_alarm_length=0;
static unsigned long zero_signal_bytes=0;

static char bell_alarm_stream_buf[2048]={0};
static char paging_send_buf[2048]={0};

static char paging_samples_buf[1024]={0};
static char paging_remain_buf[1024]={0};
static int  paging_remain_bytes=0;


int G711_encode(char *a_psrc, char *a_pdst, int in_data_len, unsigned char type);

int encode_paging_stream(char *a_psrc,char *a_pdst,int src_dataLen)
{
    int encode_bytes=0;

    #if SUPPORT_CODEC_G722
	static G722_ENC_CTX *g722_ectx =NULL;
	static int srate = ~ G722_SAMPLE_RATE_8000;

    if(g722_ectx==NULL)
    {
        g722_ectx = g722_encoder_new(64000, srate);
        if (g722_ectx == NULL) {
            fprintf(stderr, "g722_encoder_new() failed\n");
            MyExit (1);
        }
    }
	#endif

    int valid_buf_len=0;
    //确保每次发送的数据量均可以整除/4，否则G.722发送会产生杂音。
    //目前做法：每次发送960bytes，不足部分将缓存下来等待下次发送。
    //当然，所有编码格式采用上述固定960bytes发送也没有问题；暂限定G.722时使用
    int standBytes=MI_AI_SAMPLE_PER_FRAME_PAGING*2;
    if(g_send_decode_pcm_type == DECODE_G722)
    {
        if(src_dataLen+paging_remain_bytes<standBytes)
        {
            memcpy(paging_remain_buf+paging_remain_bytes,a_psrc,src_dataLen);
            paging_remain_bytes=src_dataLen+paging_remain_bytes;

            valid_buf_len=0;
        }
        else
        {
            memcpy(paging_samples_buf,paging_remain_buf,paging_remain_bytes);
            memcpy(paging_samples_buf+paging_remain_bytes,a_psrc,standBytes-paging_remain_bytes);
            int old_remain_bytes=paging_remain_bytes;
            paging_remain_bytes = src_dataLen-(standBytes-paging_remain_bytes);
            memcpy(paging_remain_buf,a_psrc+(standBytes-old_remain_bytes),paging_remain_bytes);
            valid_buf_len=standBytes;
        }
        //printf("src_dataLen=%d,remain_bytes=%d\n",src_dataLen,paging_remain_bytes);
    }
    else
    {
        memcpy(paging_samples_buf,a_psrc,src_dataLen);
        valid_buf_len = src_dataLen;
    }
    if(!valid_buf_len)
    {
        //printf("valid_buf_len == 0!\n");
        return 0;
    }

    if(g_send_decode_pcm_type == DECODE_G722)
    {
        encode_bytes=g722_encode(g722_ectx, paging_samples_buf, valid_buf_len/2, a_pdst);
        //printf("G722,srcLen=%d,dstLen=%d\n",src_dataLen,encode_bytes);
    }
    else if(g_send_decode_pcm_type == DECODE_G711)
    {
        encode_bytes=G711_encode(paging_samples_buf, a_pdst, valid_buf_len, 0);
    }
    else if(g_send_decode_pcm_type == DECODE_STANDARD_PCM)
    {
        memcpy(a_pdst,paging_samples_buf,valid_buf_len);
        encode_bytes = valid_buf_len;
    }
    return encode_bytes;
}

void send_paging_stream(unsigned char* data,int len)
{
    if(adc_allIn_vol!=100)
    {
        int t_adcVol=mSysVol[adc_allIn_vol];
        int samples=len/2;
        int16_t *dataShortInt=(int16_t*) data;
        for(int k=0; k<samples; k++)
        {
            //发送出去的数据还需要根据电位器总音量值设定
            dataShortInt[k] = limit_value_16bit( ((((int32_t)dataShortInt[k]) * t_adcVol + 2048) >> 12) );
        }
    }

    int sendBuf_len=0;
    //第一个寻呼包，判断是否需要
    if(!paging_stream_send_cnt)
    {
        if(!paging_alarm_press_flag)		//消防警报紧急按键没有按下的时候才去播放钟声
        {
            send_bell_type=paging_bell_selected;
            send_alarm_type=0;
            paging_bell_data_read=0;
            paging_alarm_data_read=0;
        }
        else    //消防警报键按下后
        {
            send_bell_type=0;
            send_alarm_type=1;
            paging_bell_data_read=0;
            paging_alarm_data_read=0;
        }

        if(RATE == 32000)
        {
            paging_bell_pcm = paging_bell_32k;
            paging_bell_length = sizeof(paging_bell_32k);
            #if (APP_TYPE == APP_C5A1)
            paging_alarm_pcm = weisheng_customer_alarm;
            paging_alarm_length = sizeof(weisheng_customer_alarm);
            #else
            paging_alarm_pcm = alarm_R_32K_20s;
            paging_alarm_length = sizeof(alarm_R_32K_20s);
            #endif
        }
        else
        {
            paging_bell_length=0;
            paging_alarm_length=0;
        }

        zero_signal_bytes=0;
    }
    else
    {
        //可能发起寻呼后再按下的报警键，此时需要处理
        if(!send_alarm_type && paging_alarm_press_flag)
		{
            send_alarm_type=1;
			paging_alarm_data_read=0;
		}
        else if(send_alarm_type && !paging_alarm_press_flag)
        {
            send_alarm_type=0;
        }
    }

    if(send_bell_type)
    {
		if(paging_bell_data_read<paging_bell_length)
        {
            //先传250ms空信号
            memset(bell_alarm_stream_buf,0,sizeof(bell_alarm_stream_buf));
            if(zero_signal_bytes<16000) //32K采样率1秒数据量为64000
            {
                zero_signal_bytes+=len;
                //printf("zero_signal_bytes=%d\n",zero_signal_bytes);
            }
            else
            {
                int readBytes=paging_bell_length-paging_bell_data_read>=len?len:paging_bell_length-paging_bell_data_read;
                memcpy(bell_alarm_stream_buf,paging_bell_pcm+paging_bell_data_read,readBytes);
                PcmAdjustmentVolume((short*)bell_alarm_stream_buf,sizeof(bell_alarm_stream_buf)/2,0.1);
                paging_bell_data_read+=readBytes;
                //printf("zero_signal_bytes=%d\n",paging_bell_data_read);
            }

            if(paging_bell_data_read>=paging_bell_length)
            {
                send_bell_type=0;
            }

            //SendToZone_PCM_Data_Cmd(bell_alarm_stream_buf,len);
            sendBuf_len = encode_paging_stream(bell_alarm_stream_buf,paging_send_buf,len);
        }
    }
    else if(send_alarm_type)
    {
        int readBytes=paging_alarm_length-paging_alarm_data_read>=len?len:paging_alarm_length-paging_alarm_data_read;
        memcpy(bell_alarm_stream_buf,paging_alarm_pcm+paging_alarm_data_read,readBytes);
        PcmAdjustmentVolume((short*)bell_alarm_stream_buf,sizeof(bell_alarm_stream_buf)/2,0.3);
        paging_alarm_data_read+=readBytes;
        //printf("zero_signal_bytes=%d\n",paging_alarm_data_read);
        if(paging_alarm_data_read>=paging_alarm_length)
        {
            paging_alarm_data_read=0;
        }
        if(g_paging_type == PAGING_TYPE_MIC)
        {
            //合成人声和警报声(播放USB音乐时不合成)
            PcmMixed((short*)data,(short*)bell_alarm_stream_buf,readBytes/2,1,0.6);
            sendBuf_len = encode_paging_stream(data,paging_send_buf,len);
        }
        else
        {
            sendBuf_len = encode_paging_stream((char *)bell_alarm_stream_buf,paging_send_buf,readBytes);
        }
    }
    else
    {
        sendBuf_len = encode_paging_stream(data,paging_send_buf,len);
    }

    if(sendBuf_len>0)
        SendToZone_PCM_Data_Cmd(paging_send_buf,sendBuf_len);
    //printf("sendBuf_len=%d\n",sendBuf_len);

    paging_stream_send_cnt++;
}



/*********************************************************************
 * @fn      pthread_sourceControl
 *
 * @brief  	节目源变化控制
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void *pthread_sourceControl(void *p_arg)
{
    int temp_source=SOURCE_NULL;
    unsigned int sysRunTime=0;
    int cnt_50ms=0;
    int signal_timeOut_count=0;
    while(1)
    {
        int sysSource = get_system_source();

        if( (++cnt_50ms)%20 == 0	)
        {
            sysRunTime++;
            if(sysRunTime == 3)	//3秒后允许检测ADC,因为之前不稳定
			{
				adc_signal_can_detect=1;
                printf("adc_signal_can_detect!!!\n");
			}

            //如果正在MIC寻呼中，检测信号是否超时超时
            if(sysSource == SOURCE_NET_PAGING && g_paging_type == PAGING_TYPE_MIC)
            {
                //20240417 龙之音NEW限制寻呼时间最长30分钟
                #if (IS_LZY_NEW_TRANSMITTER_OR_PAGER && (IS_LZY_LIMIT_PAGING_DURATION_30MIN || IS_LZY_LIMIT_PAGING_DURATION_120MIN))
                signal_timeOut_count++;
                #if IS_LZY_LIMIT_PAGING_DURATION_30MIN
                if(signal_timeOut_count>=1800)
                #elif IS_LZY_LIMIT_PAGING_DURATION_120MIN
                if(signal_timeOut_count>=7200)
                #endif
                {
                    Paging_status = PAGING_STOP;
                    printf("Paging limit timeout!!!\n");
                }
                #else
                if(!adc_signal_valid)   //如果ADC没有有效数据
                {
                    signal_timeOut_count++;
                    if(signal_timeOut_count>=g_Signal_Timeout_array[g_Signal_Timeout_level-1])
                    {
                        Paging_status = PAGING_STOP;
                        printf("ADC signal_timeOut!!!\n");
                    }
                }
                else
                {
                    signal_timeOut_count=0;
                }
                #endif
                //printf("signal_timeOut_count:%d\n",signal_timeOut_count);
            }
            else
            {
                signal_timeOut_count=0;
            }
        }

        sysSource = get_system_source();
        if(temp_source == sysSource)
        {
            usleep(50000);
            continue;
        }
        if(sysSource == SOURCE_NET_PAGING || sysSource == SOURCE_CALL)  //寻呼、对讲
        {
            //如果有钟声，可以等寻呼钟声过后再打开继电器，便于录音。
            //暂不特殊处理
            RelayCtrl(1);
            //点亮寻呼图标和MIC指示灯
            paing_status_show(1);
            //手动寻呼才点亮MIC灯+显示MIC开关图标
            MIC_Led_Show(1);
            if(sysSource == SOURCE_NET_PAGING)
            {
                UI_mic_switch_show(1);
            }
        }
        else if(sysSource == SOURCE_NULL)
        {
            send_bell_type=0;
            send_alarm_type=0;
            paging_stream_send_cnt=0;
            paging_remain_bytes=0;
            RelayCtrl(0);
            paing_status_show(0);
            MIC_Led_Show(0);
            UI_mic_switch_show(0);

            if(temp_source == SOURCE_NET_PAGING)
            {
                /*************通知分区停止***************/
                if(g_network_mode == NETWORK_MODE_LAN)
                {
                    SendToZone_Paging_Ready_Cmd_select_multicast(PCM_SEND_END,1);
                }
                //如果存在使用TCP模式的分区，发送停止
                SendToZone_Paging_Ready_Cmd_select(PCM_SEND_END,0);
            }
            else if(temp_source == SOURCE_CALL)
            {
                Amp_Switch(0);
                //重新打开AO、AI
                mi_audio_out_init(32000, 16, 1);
	            mi_audio_in_init(AI_MODE_PAGING_32K,32000, 16, 1);
            }
            send_online_info();			//状态变化通知主机
        }

        temp_source = sysSource;

        usleep(50000);
    }
}

void Create_sourceControl_task(void)
{
    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)pthread_sourceControl, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}


#endif