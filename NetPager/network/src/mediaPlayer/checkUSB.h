#ifndef CHECKUSB_H_
#define CHECKUSB_H_
#include<sys/stat.h>

/***********************************************************
 * 	usb手动挂载线程检测模块	-	宏定义部分
*/
#define _DEBUG_USB_  	    1	//打印权限

#define UDISK_MOUNT_DIR     "/mnt/udisk"

enum
{
    UDISK_STATUS_NOT_FOUND,
    UDISK_STATUS_HOTPLUG_UNPLUG,
    UDISK_STATUS_HOTPLUG_INSERT,
    UDISK_STATUS_MOUNT_FAILED,
    UDISK_STATUS_MOUNT_OK,
    UDISK_STATUS_SCAN_OK
};

extern int g_udisk_mount_status;

/***********************************************************
 * 	usb手动挂载线程检测模块	-	静态函数调用部分
*/
static int first_check_usb_status();
static void check_usb_status_thread();
static int fn(const char * file, const struct stat  *sb, int flag);
static int CheckUsb_Mount(char *specialfile,char *dir,char *filesystemtype,long flags,const void *string_flags,int times);
int udisk_mount();
int udisk_umount();

/***********************************************************
 * 	usb手动挂载线程检测模块	-	外部函数调用部分
*/
void Init_Usb_Check_Status_M();
int Get_Udisk_Mount_Status();
void Set_Udisk_Mount_Status(int u_UMount_Status);
#endif /* CHECKUSB_H_ */
