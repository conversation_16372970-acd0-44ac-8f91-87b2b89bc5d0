#ifndef _FFPLAYER_H_
#define _FFPLAYER_H_

#if defined(USE_SSD212) || defined(USE_SSD202)

#include <libavutil/avutil.h>
#include <libavutil/attributes.h>
#include <libavutil/opt.h>
#include <libavutil/mathematics.h>
#include <libavutil/imgutils.h>
#include <libavutil/samplefmt.h>
#include <libavutil/timestamp.h>
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
#include <libavutil/mathematics.h>
#include <libswresample/swresample.h>
#include <libavutil/channel_layout.h>
#include <libavutil/common.h>
#include <libavformat/avio.h>
#include <libavutil/file.h>
#include <libswresample/swresample.h>

#include "mi_audio.h"

#define AVCODEC_MAX_AUDIO_FRAME_SIZE 192000     // 1 second of 48khz 32bit audio
#define WAV_SAMPLERATE			    32000
#define MI_AUDIO_SAMPLE_PER_FRAME 	MI_AI_SAMPLE_PER_FRAME_PAGING

typedef struct {
    int videoindex;
    int sndindex;
    AVFormatContext* pFormatCtx;
    AVCodecContext* sndCodecCtx;
    AVCodec* sndCodec;
    SwrContext *swr_ctx;
    DECLARE_ALIGNED(16,uint8_t,audio_buf) [MI_AUDIO_SAMPLE_PER_FRAME * 4];
}AudioState;

 //下面这四个结构体是为了分析wav头的
typedef struct {
    u_int magic;      /* 'RIFF' */
    u_int length;     /* filelen */
    u_int type;       /* 'WAVE' */
} WaveHeader;

typedef struct {
    u_short format;       /* see WAV_FMT_* */
    u_short channels;
    u_int sample_fq;      /* frequence of sample */
    u_int byte_p_sec;
    u_short byte_p_spl;   /* samplesize; 1 or 2 bytes */
    u_short bit_p_spl;    /* 8, 12 or 16 bit */
} WaveFmtBody;

typedef struct {
    u_int type;        /* 'data' */
    u_int length;      /* samplecount */
} WaveChunkHeader;


int init_ffplayer(AudioState* is, char* filepath);
void deinit_ffplayer(AudioState* is);
int ffmpeg_get_media_song_bitRate(char *filename);

#endif

#endif