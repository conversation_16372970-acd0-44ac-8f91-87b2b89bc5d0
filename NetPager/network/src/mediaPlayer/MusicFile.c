/*
*   Copyright (c) 2013 ，广州深宝音响
*   All rights reserved.
*
*   文件名称： MusicFile.c
*   摘要：
*
*   当前版本： 0.0.1
*   作者： ysl ，修改日期： 2013 年 10 月 24 日
*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/statfs.h>
#include <dirent.h>
#include <pthread.h>

#include "MusicFile.h"
#include "udiskPlay.h"
#include "sysconf.h"


pthread_mutex_t Mp3File_mutex=PTHREAD_MUTEX_INITIALIZER;

MUSICFILELIST serverMusicList;

#if defined(USE_SSD212) || defined(USE_SSD202)

/****************************************************
 * @fn      Mp3File_FindDIR
 *
 * @brief   索引目录文件名信息
 *
 * @param   char *source表示索引目录类型主要包括SD/USB
 *
 * @return  成功返回文件夹个数   失败返回-1
 */
int Mp3File_FindDIR(MUSICFILELIST *musicList,char *source)
{
    struct dirent *entry;
    struct dirent *entry2;
    struct stat statbuf;
    DIR *dpDir;
    DIR *dpfile;
    int nDirNum=0;
    int i;
    char tmpDIRbuf[MUSICUSBDIRMAX][MUSICFILEDIRLEN];   //歌曲路径

    /*传递错误判断*/
    #if _DEBUG_MUSIC_
    printf("Mp3File_FindDIR localDir=%s\n", source);
    #endif
    if(source == NULL)
    {
        printf("ERROR:Mp3File_FindDIR no find localDir!!!\n");
        return ERROR;
    }
    /*跟文件目录  判断是否越界*/
    if(strlen(source) >= MUSICFILEDIRLEN)
    	return ERROR;

    dpDir =opendir(source);
    if(dpDir == NULL)
    {
        printf("ERROR:Mp3File_FindDIR opendir dpDir error!!!\n");
        return -1;
    }

    if(strcmp(source,UDISK_MOUNT_DIR) == 0)
    {
		while((entry = readdir(dpDir))!= NULL)
		{
		    if(Get_Udisk_Mount_Status() != UDISK_STATUS_MOUNT_OK)
			{
				return ERROR;
			}
            if(nDirNum >= MUSICUSBDIRMAX)
            {
                nDirNum = MUSICUSBDIRMAX;
                break;
            }
			lstat(entry->d_name,&statbuf);   //由文件描述词取得文件状态
			//if(S_ISDIR(statbuf.st_mode))     //文件的类型和存取的权限    ??有时不能识别目录
				if(strcmp("..",entry->d_name)==0 || strlen(entry->d_name)>=MUSICFILEDIRLEN-strlen(source))   //比较字符串
					continue;

				/*记录文件夹数目*/
				#if _DEBUG_MUSIC_
				//printf("Mp3File_FindDIR... entry->d_type=%d entry->d_name=%s\n", entry->d_type, entry->d_name);
				#endif
				if((entry->d_type == 4) && (strlen(entry->d_name) != 0))
				{
                    if(strcmp(".",entry->d_name)==0)
                    {
                        strcpy(tmpDIRbuf[nDirNum], source);
                    }
                    else
                    {
                        strcpy(tmpDIRbuf[nDirNum], source);
                        strcat(tmpDIRbuf[nDirNum], "/");
                        strcat(tmpDIRbuf[nDirNum], entry->d_name);
                    }

                    /*****Add 判断目录下面是否有歌曲,有歌曲才加入*/
                    DIR *dpfile=opendir(tmpDIRbuf[nDirNum]);
                    while((entry = readdir(dpfile)) != NULL)
                    {
                        lstat(entry->d_name,&statbuf);   //由文件描述词取得文件状态
                        //if(S_ISDIR(statbuf.st_mode))     //文件的类型和存取的权限    ??有时不能识别歌曲
                        {
                            int nlen=strlen(entry->d_name);
                            if(nlen !=0 )
                            {
                                /*格式识别*/
                                if( strncasecmp(&entry->d_name[nlen-4],".mp3",4) &&
                                    strncasecmp(&entry->d_name[nlen-4],".wav",4)
                                )
                                continue;

                                /*歌曲长度超过128舍弃*/
                                if(strlen(entry->d_name) >= (MUSICFILENAMELEN-1))
                                {
                                    continue;
                                }
                                else
                                {
                                    nDirNum++;
                                    break;
                                }
                            }
                        }
                    }

                    /*************End*****************/

					#if _DEBUG_MUSIC_
					//printf("Mp3File_FindDIR000...nDirNum=%d entry->d_name=%s\n", nDirNum,entry->d_name);
					#endif
					//nDirNum++;
				}
		}
		closedir(dpDir);   //关闭目录
    }
    
    pthread_mutex_lock(&Mp3File_mutex);   //加锁

    /*拷贝到结构体*/
    for(i = 0; i < nDirNum; i++)
    {
        if(strlen(tmpDIRbuf[i]) >= MUSICFILEDIRLEN || (strlen(tmpDIRbuf[i]) == 0))
            continue;

        /*记录USB目录名称 */
        if(strcmp(tmpDIRbuf[i], UDISK_MOUNT_DIR) == 0)
        {
            strcpy(musicList->SongDir[i].cDirName, tmpDIRbuf[i]);
        }
        else
        {
            strcpy(musicList->SongDir[i].cDirName, tmpDIRbuf[i]);
            //strcat(musicList->SongDir[i].cDirName, "/");
        }
        #if _DEBUG_MUSIC_
        printf("Mp3File_FindDIR:SongDir[%d].cDirName=%s\n", i,musicList->SongDir[i].cDirName);
        #endif
    }
    pthread_mutex_unlock(&Mp3File_mutex);   //解锁
    musicList->DirNum = nDirNum;
    

    return nDirNum;
}


/****************************************************
 * @fn      Mp3File_FindFile
 *
 * @brief   索引对应路径下的音乐文件数目
 *
 * @param   char *musicpath传递检索路径
 *
 * @return  成功返回音乐个数   失败返回-1
 */
int Mp3File_FindFile(MUSICFILELIST *musicList,int DirID, char *musicpath)
{
    struct dirent *entry;
    struct stat statbuf;
    DIR *dpfile;
    char tmpMusicbuf[MUSICUSBFILEMAX][MUSICFILENAMELEN];   //歌曲名
    int nMusicNum=0;
    int nlen, i;

    #if _DEBUG_MUSIC_
    printf("Mp3File_FindFile DirID=%d musicpath=%s\n", DirID, musicpath);
    #endif
    if(strlen(musicpath) == 0)
    {
        printf("ERROR:Mp3File_FindFile strlen(musicpath)=0!!!\n");
        return ERROR;
    }

    /*判断传递的歌曲目录是否越界 */
    if(strlen(musicpath) >= MUSICFILEDIRLEN)
    	return ERROR;

    dpfile =opendir(musicpath);
    if(dpfile == NULL)
    {
        printf("ERROR:Mp3File_FindFile error!!!\n");
        return ERROR;
    }
    while((entry = readdir(dpfile)) != NULL)
    {
        lstat(entry->d_name,&statbuf);   //由文件描述词取得文件状态
        //if(S_ISDIR(statbuf.st_mode))     //文件的类型和存取的权限    ??有时不能识别歌曲
        {
            nlen=strlen(entry->d_name);
            if(nlen !=0 )
            {
                /*格式识别*/
                if( strncasecmp(&entry->d_name[nlen-4],".mp3",4) &&
                    strncasecmp(&entry->d_name[nlen-4],".wav",4)
                    #if 0
                    &&
                    strncasecmp(&entry->d_name[nlen-4],".wma",4) &&
                    strncasecmp(&entry->d_name[nlen-4],".aac",4) &&
                    strncasecmp(&entry->d_name[nlen-4],".ogg",4) &&
                    strncasecmp(&entry->d_name[nlen-4],"flac",4)
                    #endif
                )
                    continue;

                /*歌曲长度超过63舍弃*/
                if(strlen(entry->d_name) >= (MUSICFILENAMELEN-1))
                {
                	continue;
                }
                else
                {
					strcpy(tmpMusicbuf[nMusicNum], entry->d_name);
					#if _DEBUG_MUSIC_
					//printf("Mp3File_FindFile000... nMusicNum=%d entry->d_name=%s\n", nMusicNum, entry->d_name);
					#endif
					nMusicNum++;
                    if(nMusicNum >= MUSICUSBFILEMAX)
                    {
                        break;
                    }
                }
            }
        }
    }
    closedir(dpfile);   //关闭目录

    if(nMusicNum == 0)  //没有歌曲
    {
    	musicList->SongDir[DirID].nFileNum=0;
    	return ERROR;
    }

    /*分配SD卡文件夹下FILENAME保存歌曲空间*/
    musicList->SongDir[DirID].musicfile =(FILENAME *)malloc(nMusicNum *sizeof(FILENAME));
    if(musicList->SongDir[DirID].musicfile == NULL)
    {
        printf("ERROR:malloc musicList->internal_dir[%d].musicfile error!!!\n", DirID);
        return ERROR;
    }
    memset(musicList->SongDir[DirID].musicfile, 0, nMusicNum*sizeof(FILENAME));

    pthread_mutex_lock(&Mp3File_mutex);   //加锁
    /*拷贝歌曲到结构体*/
    for(i = 0; i < nMusicNum; i++)
    {
        if(strlen(tmpMusicbuf[i]) >= MUSICFILENAMELEN-1)
            continue;

        strcpy(musicList->SongDir[DirID].musicfile[i].cName, tmpMusicbuf[i]);
        #if _DEBUG_MUSIC_
        printf("Mp3File_FindFile:SongDir[%d].musicfile[%d].cName=%s\n", DirID,i,musicList->SongDir[DirID].musicfile[i].cName);
        #endif
    }
    pthread_mutex_unlock(&Mp3File_mutex);   //解锁
    musicList->SongDir[DirID].nFileNum = 0;
    musicList->SongDir[DirID].nFileNum = nMusicNum;
    

    return nMusicNum;
}


/*********************************************************************
 * @fn      Init_Scan_SD_OR_USB_Info
 *
 * @brief   扫描本地sd/可移动usb音乐文件信息
 *
 * @param   char *s_source：音源信息usb、sd
 *
 * @return  成功返回0 失败返回-1
 */
int Init_Scan_SD_OR_USB_Info(MUSICFILELIST *musicList,char *s_source)
{
    int i, n_dirNum;
    char buf[100]={0};
    /*判断传递参数*/
    if(s_source == NULL)
    {
        printf("ERROR:Init_Scan_SD_OR_USB_Info s_source error!!!\n");
        return ERROR;
    }

    n_dirNum = Mp3File_FindDIR(musicList,s_source);
    printf("Init_Scan_USB_Info:DirNum=%d\n",n_dirNum);
    int fileNum=0;
    for(i=0; i< n_dirNum; i++)
    {
        musicList->SongDir[i].FileStartIndex = fileNum+1;
        Mp3File_FindFile(musicList,i, musicList->SongDir[i].cDirName);
        fileNum += musicList->SongDir[i].nFileNum;
        printf("Init_Scan_USB_Info:Dir[%d].FileNum=%d\n",i,musicList->SongDir[i].nFileNum);
    }
    printf("Init_Scan_USB_Info:totalFileNum=%d\n",fileNum);

    return SUCCEED;
}


//重置udisk MusicList结构体
void Reset_UdiskMusiclist(MUSICFILELIST *musicList)
{
    //如果处于udisk页面，需要先退出

    //然后清除内存
    pthread_mutex_lock(&MusicListMutex);
    int i=0;
	for(i=0;i<musicList->DirNum;i++)
	{
		free(musicList->SongDir[i].musicfile);
		musicList->SongDir[i].musicfile=NULL;
	}
    memset(musicList,0,sizeof(MUSICFILELIST));
    pthread_mutex_unlock(&MusicListMutex);
}


#endif