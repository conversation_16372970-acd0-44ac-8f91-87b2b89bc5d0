/*
 * uart.c
 *
 *  Created on: 2020/10/26
 *      Author: Administrator
 */



#include<unistd.h>
#include<string.h>
#include<errno.h>
#include<stdio.h>
#include<fcntl.h>
#include<stdlib.h>
#include<sys/types.h>
#include<sys/stat.h>
#include<linux/types.h>
#include<termios.h>
#include<pthread.h>

#include "uart.h"

/*********************************************************************
 * MACROS
 */

/*********************************************************************
 * CONSTANTS
 */

/*********************************************************************
 * TYPEDEFS
 */

 /********************************************************************
 * EXTERNAL VARIABLES
 */


int g_Terminal_Uartfd=-1;	//串口文件描述符


/*********************************************************************
 * @fn      Checksum
 *
 * @brief   计算数据累加和校验和
 *
 * @param   Data - 校验数据
 *			Length - 校验数据长度
 *
 * @return  Xordata - 校验和
 */
unsigned char Checksum(unsigned char cmd,int len,unsigned char *buf)
{
	int i = 0;
	int sum = 0;
	sum = cmd + (len>>8)+(len);
	for (i = 0; i < len; i++) {
		sum += buf[i];
	}
	return (unsigned char)sum;
}



/*********************************************************************
 * @fn      Open_UartDev
 *
 * @brief   打开串口设备
 *
 * @param   dev - 设备名称
 *
 * @return  ERROR - 打开失败
 *			fd - 打开成功，返回文件描述符
 */
static int Open_UartDev(char *dev)
{
	int fd;

	fd = open(dev, O_RDWR | O_NOCTTY);
	if(fd == -1)
	{
		perror("Can't Open Serial Port:");
		return -1;
	}
	else
	{
		return fd;
	}
}

/*********************************************************************
 * @fn      Close_UartDev
 *
 * @brief   关闭串口设备
 *
 * @param   fd - 文件描述符
 *
 * @return  none
 */
static int Close_UartDev(int fd)
{
	close(fd);
}

/*********************************************************************
 * @fn      Set_ComPort_Speed
 *
 * @brief   串口波特率设置
 *
 * @param   fd - 设备文件描述符
 *			Speed - 波特率
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
static int Set_ComPort_Speed(int fd, int Speed)
{
	int ret=-1;
	int Baudrate;
	struct termios opt;

	/*获取旧的串口属性*/
	if(tcgetattr(fd,&opt) != 0)
	{
		perror("Set_ComPort_Speed tcgetattr error!!!\n");
		return ret;
	}

	/*波特率选择*/
	switch(Speed)
	{
		case BAUD_9600 :
			Baudrate = B9600;
			break;

		case BAUD_19200 :
			Baudrate = B19200;
			break;

		case BAUD_38400 :
			Baudrate = B38400;
			break;

		case BAUD_115200 :
			Baudrate = B115200;
			break;

		case BAUD_4800:
			Baudrate = B4800;
			break;

		case BAUD_57600:
			Baudrate = B57600;
			break;

		default :
			Baudrate = B9600;
			break;
	}

	tcflush(fd, TCIOFLUSH); //刷新输入输出缓存
	cfsetispeed(&opt, Baudrate); //设置输入波特率
	cfsetospeed(&opt, Baudrate); //设置输出波特率
	ret = tcsetattr(fd, TCSANOW, &opt); //立即使设置属性生效
	tcflush(fd, TCIOFLUSH); //刷新输入输出缓存

	return ret;
}

/*********************************************************************
 * @fn      Set_UartAttribute
 *
 * @brief   串口属性设置
 *
 * @param   fd - 设备文件描述符
 *			Speed - 波特率
 *			NumDataBits - 数据位数
 *			Parity - 校验
 *			NumStopBits - 停止位
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
static int Set_UartAttribute(int fd, int Speed, int NumDataBits, char Parity, int NumStopBits)
{

	int  res;
    struct termios  oldtio, newtio;
    char  ch;
    char buf[256] = {0};
	int Baudrate;

    //get current operation parameter
    tcgetattr(fd, &oldtio);
    memset(&newtio, 0, sizeof(newtio));


	switch(Speed)
	{
		case BAUD_9600 :
			Baudrate = B9600;
			break;

		case BAUD_19200 :
			Baudrate = B19200;
			break;

		case BAUD_38400 :
			Baudrate = B38400;
			break;

		case BAUD_115200 :
			Baudrate = B115200;
			break;

		case BAUD_4800:
			Baudrate = B4800;
			break;

		case BAUD_57600:
			Baudrate = B57600;
			break;

		case BAUD_460800:
			Baudrate = B460800;
			break;

		default :
			Baudrate = B9600;
			break;
	}

		/*校验位*/
	switch (Parity)
	{
		case 'n':
		case 'N':
			newtio.c_cflag &= ~PARENB;
			newtio.c_iflag &= ~INPCK;
			break;
		case 'o':
		case 'O':
			newtio.c_cflag |= (PARODD | PARENB);
			newtio.c_iflag |= INPCK;
			break;
		case 'e':
		case 'E':
			newtio.c_cflag |= PARENB;
			newtio.c_cflag &= ~PARODD;
			newtio.c_iflag |= INPCK;
			break;
		case 'S':
		case 's':
			newtio.c_cflag &= ~PARENB;
			newtio.c_cflag &= ~CSTOPB;
			break;
		default:
			newtio.c_cflag &= ~PARENB;
			newtio.c_iflag &= ~INPCK;
			break;
	}

	/*停止位位数*/
	switch (NumStopBits)
	{
		case 1:
			newtio.c_cflag &= ~CSTOPB;
			break;
		case 2:
			newtio.c_cflag |= CSTOPB;
			break;
		default:
			newtio.c_cflag &= ~CSTOPB;
			break;
	}

	if (Parity != 'n' && Parity != 'N')
	{
		newtio.c_iflag |= INPCK;
	}


    //buad = 460800,data type = 8bit,enable receive mode
    newtio.c_cflag = Baudrate | CS8 | CLOCAL | CREAD;
    
    //fuart enable cts/rts
    //newtio.c_cflag |= CRTSCTS;
    
    //ignore parity sum frame errors
    newtio.c_iflag = IGNPAR;

    //enable output options
    //newtio.c_oflag = OPOST;

    //set normal mode
    //newtio.c_lflag = ICANON;
#if 0
2.VTIME和VMIN 决定了read()函数什么时候返回
1．当VTIME>0，VMIN>0时。read调用将保持阻塞直到读取到第一个字符，读到了第一个字符之后开始计时，此后若时间到了VTIME或者时间未到但已读够了VMIN个字符则会返回；若在时间未到之前又读到了一个字符(但此时读到的总数仍不够VMIN)则计时重新开始。
2. 当VTIME>0，VMIN=0时。read调用读到数据则立即返回，否则将为每个字符最多等待VTIME时间。
3. 当VTIME=0，VMIN>0时。read调用一直阻塞，直到读到VMIN个字符后立即返回。
4. 若在open或fcntl设置了O_NDELALY或O_NONBLOCK标志，read调用不会阻塞而是立即返回，那么VTIME和VMIN就没有意义，效果等同于与把VTIME和VMIN都设为了0。
VTIME 和  VMIN
VTIME  定义要求等待的零到几百毫秒的值(通常是一个8位的unsigned char变量)。
VMIN 定义了要求等待的最小字节数, 这个字节数可能是0。
只有设置为阻塞时这两个参数才有效，仅针对于读操作。
#endif

    //此处很重要，避免BP1048丢包，最少接收到20个字符或者等待10ms
    newtio.c_cc[VTIME] = 10;
	#if defined(USE_SSD212) || defined(USE_SSD202)
    newtio.c_cc[VMIN] = 18;
	#else
	newtio.c_cc[VMIN] = 20;
	#endif
    //flush input and output cache
    tcflush(fd, TCIFLUSH);
    //set new operation parameter
    tcsetattr(fd, TCSANOW, &newtio);

	return 0;
}

/*********************************************************************
 * @fn      Init_Serial_Port
 *
 * @brief   串口设备初始化
 *
 * @param   Dev - 设备名称
 *			Speed - 波特率
 *			NumDataBits - 数据位数
 *			Parity - 校验
 *			NumStopBits - 停止位
 *
 * @return  ERROR - 初始化失败
 *			fd - 文件描述符
 */
int Init_Serial_Port(char *Dev, int Speed, int NumDataBits, char Parity, int NumStopBits)
{
	int fd, ret;

	/*打开串口设备*/
	fd = Open_UartDev(Dev);
	if (fd == -1)
	{
		#if _DEBUG_UART_
		printf("%s : Open The Serial Port Failure!\n", Dev);
		#endif
		return -1;
	}
	else
	{
		#if _DEBUG_UART_
		printf("%s : Open The Serial Port Succeed!\n", Dev);
		#endif
	}

	/*设置串口波特率*/
	ret = Set_ComPort_Speed(fd, Speed);
	if (ret == -1)
	{
		#if _DEBUG_UART_
		printf("%s : Set Baudrate Failure!\n", Dev);
		#endif
		return -1;
	}
	else
	{
		#if _DEBUG_UART_
		printf("%s : Set Baudrate Succeed!\n", Dev);
		#endif
	}

	/*设置串口属性*/
	ret = Set_UartAttribute(fd, Speed, NumDataBits, Parity, NumStopBits);
	if (ret == -1)
	{
		#if _DEBUG_UART_
		printf("%s : Set Uart Attribute Failure!\n", Dev);
		#endif
		return -1;
	}
	else
	{
		#if _DEBUG_UART_
		printf("%s : Set Uart Attribute Succeed!\n", Dev);
		#endif
	}
	printf("Init_Serial_Port:%s succeed!\r\n",Dev);

	return fd;
}



/*********************************************************************
 * @fn      Recv_Uart_Terminal_Pthread
 *
 * @brief   串口数据接收线程(回调函数)
 *
 * @param   void
 *
 * @return  none
 */
void *Recv_Uart_Terminal_Pthread(void)
{
	int Rxlen;
	int ret, i;
	int Index = 0;
	fd_set readfd;
	int max_fd;
	int Pkg_Length=0;
	int Read_Size = 2;

	unsigned char room_address;
	unsigned char Rxbuf[MAX_UART_BUF_SIZE]={0};


	max_fd=g_Terminal_Uartfd;

	FD_ZERO(&readfd);               //清空读文件描述集合
	FD_SET(g_Terminal_Uartfd, &readfd);  //注册套接字文件描述符

	while (1)
	{
		FD_ZERO(&readfd);               //清空读文件描述集合
		FD_SET(g_Terminal_Uartfd, &readfd);  //注册套接字文件描述符
		ret = select(max_fd+1, &readfd, NULL, NULL, NULL);
		switch(ret)
		{
			case -1 : //调用出错
				perror("select");
				break;

			case 0 : //超时
				printf("timeout!\n");
				break;

			default : //判断是否有数据可读

				/*接收对应串口的数据*/

				Rxlen = read(g_Terminal_Uartfd, &Rxbuf[Index], Read_Size);

				/*数据校验*/
				if (Rxlen < 0)
				{
					perror("Rxlen<0\n");
					Index = 0;
					Read_Size = 2;
					memset(Rxbuf,0, MAX_UART_BUF_SIZE);
					continue;
				}
				else
				{

					#if _DEBUG_UART_
					printf("Terminal:The Recv Data Is : \n");
					for (i = 0; i < Rxlen; i++)
					{
						printf("0x%x ", Rxbuf[i+Index]);
					}
					printf("\n");
					#endif

					if(Index == 0)	/*判断包头是否正确*/
					{
						if(Rxbuf[0] == BP1048_UART_FRAME_HEAD1 && Rxbuf[1] == BP1048_UART_FRAME_HEAD2)
						{
							Index = Rxlen; //移位接收包后续数据
							Read_Size = 3; //包剩余包头信息
							continue;
						}
						else
						{
							/*接收的包错误，重新接收下一个数据包*/
							printf("Terminal ERROR : Pkg Head Fault : %x\n", Rxbuf[0]);
							Index = 0;
							Read_Size = 2;
							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
							continue;
						}
					}
					else if(Index == 2)
					{
						Index = Rxlen+2; //移位接收包后续数据
						Read_Size = (Rxbuf[4]<<8)+Rxbuf[3]+1; //包剩余数据个数
						Pkg_Length = 5+Read_Size;
						if(Pkg_Length >MAX_UART_BUF_SIZE)	//包长超出最大长度
						{
							Index = 0;
							Read_Size = 2;
							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
						}
						continue;
					}
					else
					{
						Index = Index + Rxlen;
						if (Index < Pkg_Length)
						{
							/*还没接收完包数据，继续接收*/
							printf("Terminal:Package Length Too Short,Continue......\n");
							Read_Size = Pkg_Length - Index;
							continue;
						}
						else
						{
							Index = 0;
							Read_Size = 2;
							if(Pkg_Length < 6)
							{
								printf("Terminal:ERROR : Package Length<6!\n");
								memset(Rxbuf,0, MAX_UART_BUF_SIZE);
								continue;
							}
							int cmd=Rxbuf[2];
							int data_len=Pkg_Length-6;
							if ( Checksum(cmd, data_len,Rxbuf+5) != Rxbuf[Pkg_Length-1] ) //校验数据
							{
								printf("Terminal:ERROR : Package Check Fail\n");
								memset(Rxbuf,0, MAX_UART_BUF_SIZE);
								continue;
							}

						 	printf("UART Checksum succeed,CMD=0x%02x\n",cmd);
							Bp1048_Command_Proc(cmd,data_len,Rxbuf+5);

							memset(Rxbuf,0, MAX_UART_BUF_SIZE);
						}
					}
				}
				break;
		}
	}
	pthread_exit(NULL);
}




/*********************************************************************
 * @fn      Init_Uart
 *
 * @brief   初始化串口通讯，注意所使用的串口设备是否正确
 *          UARTDEV0   "/dev/ttyS0"
 *          UARTDEV1   "/dev/ttyS1"
 *          UARTDEV2   "/dev/ttyS2"
 *          UARTDEV3   "/dev/ttyS3"
 *          UARTDEV5   "/dev/ttyS5"
 *
 * @param   void
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
int Init_Uart(void)
{
	int ret=-1;
	pthread_t t_uart_bp1048_Pthread;
	pthread_attr_t Pthread_TERMINAL_Attr;
	pthread_attr_init(&Pthread_TERMINAL_Attr);
	pthread_attr_setdetachstate(&Pthread_TERMINAL_Attr, PTHREAD_CREATE_DETACHED);

	g_Terminal_Uartfd = Init_Serial_Port(UARTDEV6, BAUD_460800, 8, 'N', 1);

	/*创建一个线程单独接收终端串口数据*/
	ret = pthread_create(&t_uart_bp1048_Pthread, &Pthread_TERMINAL_Attr, (void *)Recv_Uart_Terminal_Pthread, NULL);

	if (ret == -1)
	{
		printf("Uart_Pthread : Creat Failure!\n");
	}
	else
	{
		printf("Uart_Pthread : Creat Seccess!\n");
	}

	return ret;
}

