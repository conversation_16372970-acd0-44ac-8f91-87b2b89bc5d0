/***************************************************
 *   Copyright (c) 2016 ，广州深宝音响
 *   All rights reserved.
 *
 *   文件名称： xml.c
 *   摘要：
 *
 *   当前版本： 0.0.1
 *   作者：蒋明书 ，修改日期： 2016年 2月 19日
 *
 */


#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include "pthread.h"
#include "xml.h"
#include "sysconf.h"
#include "MusicFile.h"
#include "SaveZoneInfo.h"
#include <dirent.h>
#include <sys/statfs.h>
#include <sys/stat.h>
#include "Paging_win.h"
#include "upnp.h"
#include "ixml.h"
#include "SaveUserInfo.h"
#include "NetPager/win/win.h"

//test
char *xmlUrl = "http://192.168.5.15:8888/Update/Playlist.xml";
char *xmlUrl_group = "http://192.168.5.15:8888/Update/Group.xml";
char *xmlUrl_zoneInfo = "http://192.168.200.23:8888/Update/Section.xml";

char g_xml_groupInfo_url[256];
char g_xml_Playlist_url[256];
char g_xml_zoneInfo_url[256];
char g_xml_audiocollectorInfo_url[256];
char g_xml_userInfo_url[256];
#if ENABLE_CALL_FUNCTION
char g_xml_pagerInfo_url[256];
#endif

int g_xml_groupInfo_download_flag=0;
int g_xml_playlist_download_flag=0;
int g_xml_zoneInfo_download_flag=0;
int g_xml_audioCollectorInfo_download_flag=0;
int g_xml_userInfo_download_flag=0;
#if ENABLE_CALL_FUNCTION
int g_xml_pagerInfo_download_flag=0;
#endif

char g_xml_download_ip[32]={0};
int g_xml_download_port=8888;

pthread_mutex_t XMLDownloadMutex=PTHREAD_MUTEX_INITIALIZER;
pthread_mutex_t MusicListMutex=PTHREAD_MUTEX_INITIALIZER;
pthread_mutex_t GroupListMutex=PTHREAD_MUTEX_INITIALIZER;
pthread_mutex_t ZoneInfoMutex=PTHREAD_MUTEX_INITIALIZER;
pthread_mutex_t UserInfoMutex=PTHREAD_MUTEX_INITIALIZER;
#if ENABLE_CALL_FUNCTION
pthread_mutex_t PagerInfoMutex=PTHREAD_MUTEX_INITIALIZER;
#endif

#define FALSE       0
#define TRUE        1


char g_Audio_Collector_Info_dateTime[32];	//音频采集器信息更新日期(主机下发)

extern int group_enter_index;

#if 1
void* p_xml_zoneInfo()
{
		int ret;
		g_xml_zoneInfo_download_flag=1;
		IXML_Document *DescDoc = NULL;

		ret = UpnpDownloadXmlDoc(g_xml_zoneInfo_url, &DescDoc); // 下载设备描述xml文档
		if (ret != UPNP_E_SUCCESS)
		{
			printf("Error obtaining device description from %s -- error = %d\n", g_xml_zoneInfo_url, ret);
			if(DescDoc)
				ixmlDocument_free(DescDoc); // 释放资源
			g_xml_zoneInfo_download_flag=0;

			send_host_xml_update_status(XML_FILE_ZONEINFO,XML_FILE_FAIL);
			return 0;
		}
		else
		{

			printf("\np_xml_zoneInfo DownloadXmlDoc OK\n");
		}



		//整个歌曲列表信息
		Nodeptr nodeTattr=DescDoc->n.firstChild->firstAttr;
		char *SectionCount=NULL;
		char *Section_dateTime=NULL;
		while(nodeTattr)
		{
			char *attrName = nodeTattr->nodeName;
			char *attrValue = nodeTattr->nodeValue;

			if( strncasecmp(attrName,"SectionCount",strlen("SectionCount")) == 0 )					//ID
			{
				SectionCount=attrValue;
				//printf("\nZonelist_count=%s\n",SectionCount);
			}
			else if( strncasecmp(attrName,"DateTime",strlen("DateTime")) == 0 )					//Name
			{
				Section_dateTime=attrValue;
				//printf("\nZonelist_dateTime=%s\n",Section_dateTime);
			}
			nodeTattr=nodeTattr->nextSibling;
		}

#if 0
		memset(&PagingZoneInfo,0,sizeof(PagingZoneInfo));		//清除寻呼分区信息
		Paging_status=PAGING_STOP;
#endif

		pthread_mutex_lock(&ZoneInfoMutex);

		if(Section_dateTime == NULL)
		{
			//发送错误信息至主机
			send_host_xml_update_status(XML_FILE_ZONEINFO,XML_FILE_FAIL);
			pthread_mutex_unlock(&ZoneInfoMutex);
			ixmlDocument_free(DescDoc); // 释放资源
			g_xml_zoneInfo_download_flag=0;
			pthread_exit(NULL);
		}

		static char cur_xml_zoneInfo_url[256]={0};
		bool needUpdate=true;	//需要更新
		if(strcmp(cur_xml_zoneInfo_url,g_xml_zoneInfo_url) == 0)	//XML地址跟当前一样，那么只需要判断日期
		{
			if(	strcmp(Section_dateTime,m_stZone_Info.DateTime)==0 )		//日期相等，不更新
			{
				needUpdate=false;
			}
		}
		else	//XML地址跟当前不同，那么保存地址
		{
			sprintf(cur_xml_zoneInfo_url,"%s",g_xml_zoneInfo_url);
		}
		if(!needUpdate)
		{
			//发送错误信息至主机
			send_host_xml_update_status(XML_FILE_ZONEINFO,XML_FILE_NEWEST);
			pthread_mutex_unlock(&ZoneInfoMutex);
			ixmlDocument_free(DescDoc); // 释放资源
			g_xml_zoneInfo_download_flag=0;
			pthread_exit(NULL);
		}
				
		//todo清空之前先保存隐藏分区
		memset(&m_stZone_Info,0,sizeof(m_stZone_Info));

		sprintf(m_stZone_Info.DateTime,"%s",Section_dateTime);

		ZONEINFO_DETAIL zoneInfo[MAX_ZONE_NUM]={0};
		int zone_index=0;

		//解析
		IXML_NodeList *nodeList = ixmlDocument_getElementsByTagName(DescDoc,"Sections");
		IXML_NodeList *nodeListHead=nodeList;
		while(nodeList)
		{
			//   <RadioInfo type="HTTPS_TEST" count="3" belongs="0">
			char *kk=nodeList->nodeItem->nodeName;		//得到所有节点名,记录节点下子节点个数

			//得到子节点firstChild
			Nodeptr nodeT=nodeList->nodeItem->firstChild;
			Nodeptr nodeTattr=NULL;
			if(nodeT)
			{
				while(nodeT)
				{
					nodeTattr=nodeT->firstAttr;
					int index=0;
					while(nodeTattr)	//nodeTattr
					{
						char *attrName = nodeTattr->nodeName;

						if( strcmp(attrName,"SecID") == 0 )		//SecID
						{
							zoneInfo[zone_index].g_zone_id=zone_index;
						}
						else if( strcmp(attrName,"Model") == 0 )		//Model
						{
							zoneInfo[zone_index].g_zone_DeviceType=atoi(nodeTattr->nodeValue);
						}
						else if( strcmp(attrName,"Mac") == 0 )		//Mac
						{
							//分解MAC为6个字节数据
							unsigned char *p=NULL;
							p=strtok(nodeTattr->nodeValue,":");
							int k=0;
							if(p)
							{
								zoneInfo[zone_index].g_zone_mac[k++]=strtol(p,NULL,16);	//atoi不能转换16进制数据,要用strtol
								while( (p=strtok(NULL,":")) )
								{
									zoneInfo[zone_index].g_zone_mac[k++]=strtol(p,NULL,16);
								}
							}
						}
						else if( strcmp(attrName,"IP") == 0 )		//IP
						{
							zoneInfo[zone_index].g_zone_ip=IPcharToInt(nodeTattr->nodeValue);
						}
						else if( strcmp(attrName,"Name") == 0 )		//Name
						{
							sprintf(zoneInfo[zone_index].g_zone_name,nodeTattr->nodeValue);
						}

						nodeTattr=nodeTattr->nextSibling;
						index++;
					}

					nodeT=nodeT->nextSibling;
					zone_index++;
					if(zone_index>=MAX_ZONE_NUM)
						break;
				}
			}
			nodeList=nodeList->next;
		}


	#if 0
		ixmlFreeDOMString(docString);
	#endif
		//释放结点资源
		if (nodeListHead)
			ixmlNodeList_free(nodeListHead);
		ixmlDocument_free(DescDoc); // 释放资源

		//改变
		memcpy(m_stZone_Info.zoneInfo,zoneInfo,zone_index*sizeof(ZONEINFO_DETAIL));
		m_stZone_Info.ExistTotalZone=zone_index;


		Save_Zone_Info();
		pthread_mutex_unlock(&ZoneInfoMutex);

		Account_Zone_Update();
		Account_Group_Update();

		g_xml_zoneInfo_download_flag=0;
		send_host_xml_update_status(XML_FILE_ZONEINFO,XML_FILE_SUCCEED);

		printf("Zone Xml update...\n");
		//刷新界面
		control_win_update(0,0);
		//马上搜索设备
		SendToZone_Search_Cmd();
}




int XML_ZoneInfo_Thread()
{
	if(g_xml_zoneInfo_download_flag)
	{
		return ERROR;
	}
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)p_xml_zoneInfo, NULL);
	if (ret == -1)
	{
		if(Paging_status != PAGING_START) printf("XML_ZoneInfo_Thread Failure!\n");
		return ERROR;
	}
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}








void* p_xml_audioCollector_Info()
{
		int ret;
		g_xml_audioCollectorInfo_download_flag=1;
		IXML_Document *DescDoc = NULL;

		ret = UpnpDownloadXmlDoc(g_xml_audiocollectorInfo_url, &DescDoc); // 下载设备描述xml文档
		if (ret != UPNP_E_SUCCESS)
		{
			printf("Error obtaining device description from %s -- error = %d\n", g_xml_audiocollectorInfo_url, ret);
			if(DescDoc)
				ixmlDocument_free(DescDoc); // 释放资源
			g_xml_audioCollectorInfo_download_flag=0;

			send_host_xml_update_status(XML_FILE_AUDIO_COLLECTOR,XML_FILE_FAIL);
			return 0;
		}
		else
		{

			printf("\np_xml_audioCollector_Info DownloadXmlDoc OK\n");
		}


		Nodeptr nodeTattr=DescDoc->n.firstChild->firstAttr;
		char *CollectorCount=NULL;
		char *Collector_dateTime=NULL;
		while(nodeTattr)
		{
			char *attrName = nodeTattr->nodeName;
			char *attrValue = nodeTattr->nodeValue;

			if( strncasecmp(attrName,"CollectorCount",strlen("CollectorCount")) == 0 )					//ID
			{
				CollectorCount=attrValue;
				//printf("\nCollectorCount=%s\n",CollectorCount);
			}
			else if( strncasecmp(attrName,"DateTime",strlen("DateTime")) == 0 )					//Name
			{
				Collector_dateTime=attrValue;
				//printf("\nCollector_dateTime=%s\n",Collector_dateTime);
				sprintf(g_Audio_Collector_Info_dateTime,Collector_dateTime);
			}
			nodeTattr=nodeTattr->nextSibling;
		}

		//清除原有的采集器信息列表
		if(Audio_collector_list.list!=NULL)
		{
			free(Audio_collector_list.list);
			Audio_collector_list.list=NULL;
		}

		Audio_collector_list.totalNum=atoi(CollectorCount);

		if(Audio_collector_list.totalNum>0)
		{
			Audio_collector_list.list=calloc(Audio_collector_list.totalNum,sizeof(struct s_audio_collector_info));
		}


		int audio_collector_index=0;

		//解析
		IXML_NodeList *nodeList = ixmlDocument_getElementsByTagName(DescDoc,"AudioCollectors");
		IXML_NodeList *nodeListHead=nodeList;
		while(nodeList)
		{
			//得到子节点firstChild
			Nodeptr nodeT=nodeList->nodeItem->firstChild;
			Nodeptr nodeTattr=NULL;
			char *srcID=NULL;
			char *srcName=NULL;
			if(nodeT)
			{
				while(nodeT)
				{
					nodeTattr=nodeT->firstAttr;
					while(nodeTattr)	//nodeTattr
					{
						char *attrName = nodeTattr->nodeName;
						char *attrValue = nodeTattr->nodeValue;

						if( strncasecmp(attrName,"SrcID",strlen("SrcID")) == 0 )					//ID
						{
							srcID=attrValue;
							Audio_collector_list.list[audio_collector_index].id=atoi(srcID);
						}
						else if( strncasecmp(attrName,"SrcName",strlen("SrcName")) == 0 )					//SrcName
						{
							srcName=attrValue;
							sprintf(Audio_collector_list.list[audio_collector_index].name,nodeTattr->nodeValue);
						}
						nodeTattr=nodeTattr->nextSibling;
					}
					nodeT=nodeT->nextSibling;
					audio_collector_index++;
					//printf("audio_collector_index=%d\n",audio_collector_index);
				}
			}
			nodeList=nodeList->next;
		}


	#if 0
		ixmlFreeDOMString(docString);
	#endif
		//释放结点资源
		if (nodeListHead)
			ixmlNodeList_free(nodeListHead);
		ixmlDocument_free(DescDoc); // 释放资源


		g_xml_audioCollectorInfo_download_flag=0;
		send_host_xml_update_status(XML_FILE_AUDIO_COLLECTOR,XML_FILE_SUCCEED);

		//刷新界面

}




int XML_AudioCollector_Thread()
{
	if(g_xml_audioCollectorInfo_download_flag)
	{
		return ERROR;
	}
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)p_xml_audioCollector_Info, NULL);
	if (ret == -1)
	{
		if(Paging_status != PAGING_START) printf("XML_AudioCollector_Thread Failure!\n");
		return ERROR;
	}
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}





/*********************************************************************
 * @fn      p_xml_group
 *
 * @brief  	xml获取分组信息
 *
 * @param
 *
 * @return ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
void* p_xml_group()
{

		int ret;
		g_xml_groupInfo_download_flag=1;
		IXML_Document *DescDoc = NULL;
		pthread_mutex_lock(&XMLDownloadMutex);
		ret = UpnpDownloadXmlDoc(g_xml_groupInfo_url, &DescDoc); // 下载设备描述xml文档
		if (ret != UPNP_E_SUCCESS)
		{
			printf("Error obtaining device description from %s -- error = %d\n", g_xml_groupInfo_url, ret);
			if(DescDoc)
				ixmlDocument_free(DescDoc); // 释放资源
			g_xml_groupInfo_download_flag=0;
			pthread_mutex_unlock(&XMLDownloadMutex);
			send_host_xml_update_status(XML_FILE_GROUP,XML_FILE_FAIL);
			return 0;
		}
		else
		{
			pthread_mutex_unlock(&XMLDownloadMutex);
			printf("\np_xml_group DownloadXmlDoc OK\n");
		}
		//The caller is required to free the \b DOMString
		 //* returned from this function using \b ixmlFreeDOMString when it
		 //* is no longer required.
	#if 0
		char *docString=ixmlDocumenttoString(DescDoc);
		printf("\nxml=%s\n",docString);
	#endif


		pthread_mutex_lock(&GroupListMutex);

		char tmp_current_groupId[40] ={0};
		if(group_enter_index!=-1)
		{
			sprintf(tmp_current_groupId,"%s",m_stGroup_Info.groupInfo[group_enter_index].g_group_realId);
		}

		//判断总节点名字
		//printf("\nXML NAME=%s\n",DescDoc->n.firstChild->nodeName);
		if(strncasecmp(DescDoc->n.firstChild->nodeName,"SectoinGroup",strlen("SectoinGroup"))!=0)
		{
			//发送错误信息至主机
			send_host_xml_update_status(XML_FILE_GROUP,XML_FILE_FAIL);
			pthread_mutex_unlock(&GroupListMutex);
			ixmlDocument_free(DescDoc); // 释放资源
			g_xml_groupInfo_download_flag=0;
			pthread_exit(NULL);
		}

		//整个歌曲列表信息
		Nodeptr nodeTattr=DescDoc->n.firstChild->firstAttr;
		char *grouplist_count=NULL;
		char *grouplist_dateTime=NULL;
		while(nodeTattr)
		{
			char *attrName = nodeTattr->nodeName;
			char *attrValue = nodeTattr->nodeValue;

			if( strncasecmp(attrName,"GroupCount",strlen("GroupCount")) == 0 )					//ID
			{
				grouplist_count=attrValue;
				//printf("\ngrouplist_count=%s\n",grouplist_count);
			}
			else if( strncasecmp(attrName,"DateTime",strlen("DateTime")) == 0 )					//Name
			{
				grouplist_dateTime=attrValue;
				//printf("\ngrouplist_dateTime=%s\n",grouplist_dateTime);
			}
			nodeTattr=nodeTattr->nextSibling;
		}

		if(strcmp(grouplist_dateTime,serverMusicList.DateTime)==0)		//id存在且更新日期等于现有日期
		{
			//发送错误信息至主机
			send_host_xml_update_status(XML_FILE_GROUP,XML_FILE_NEWEST);
			pthread_mutex_unlock(&GroupListMutex);
			ixmlDocument_free(DescDoc); // 释放资源
			g_xml_groupInfo_download_flag=0;
			pthread_exit(NULL);
		}


		m_stGroup_Info.TotalGroup=atoi(grouplist_count);
		strcpy(m_stGroup_Info.DateTime,grouplist_dateTime);
		if(m_stGroup_Info.groupInfo!=NULL)
		{
			free(m_stGroup_Info.groupInfo);
		}
		 m_stGroup_Info.groupInfo = calloc(m_stGroup_Info.TotalGroup,sizeof(GROUP_DETAIL));

		//解析
		IXML_NodeList *nodeList = ixmlDocument_getElementsByTagName(DescDoc,"Group");
		IXML_NodeList *nodeListHead=nodeList;
		int list_index=0;
		while(nodeList)
		{
			Nodeptr nodeTattr=nodeList->nodeItem->firstAttr;
			char *Group_readId=NULL;
			char *Group_Name=NULL;
			char *Zone_Count=NULL;
			char *GroupUserName=NULL;
			while(nodeTattr)
			{
				char *attrName = nodeTattr->nodeName;
				char *attrValue = nodeTattr->nodeValue;
				//ID="20B4F3C5-2837-4f9e-86FC-47AF86C6BA96" Name="My List" SongCount="2" DateTime="2016-02-20 11:02:20" UpdateType="1"
				if( strncasecmp(attrName,"ID",strlen("ID")) == 0 )		//realId
				{
					Group_readId=attrValue;
				}
				if( strncasecmp(attrName,"Name",strlen("Name")) == 0 )		//ID
				{
					Group_Name=attrValue;
				}
				else if( strncasecmp(attrName,"SectionCount",strlen("SectionCount")) == 0 )		//ID
				{
					Zone_Count=attrValue;
				}
				else if( strncasecmp(attrName,"UserName",strlen("UserName")) == 0 )		//ID
				{
					GroupUserName=attrValue;
				}
				nodeTattr=nodeTattr->nextSibling;
			}

			//printf("\nGroup_Name=%s,Zone_Count=%s\n",Group_Name,Zone_Count);

			//首先判断ID
			if( Group_Name == NULL)
			{
				//发送错误信息至主机
				pthread_mutex_unlock(&GroupListMutex);
				ixmlDocument_free(DescDoc); // 释放资源
				g_xml_groupInfo_download_flag=0;
				pthread_exit(NULL);
			}

			//得到子节点firstChild
			Nodeptr nodeT=nodeList->nodeItem->firstChild;
			nodeTattr=NULL;
			unsigned char mac[6*MAX_ZONE_NUM]={0};
			if(nodeT)
			{
				int group_index=0;

				while(nodeT)
				{
					nodeTattr=nodeT->firstAttr;

					//<Song Duration="240" Size="4110703" PathName="/Program/Common/Solong.mp3"/>

					while(nodeTattr)	//nodeTattr
					{
						char *attrName = nodeTattr->nodeName;
						char *attrValue = nodeTattr->nodeValue;

						if( strncasecmp(attrName,"Mac",strlen("Mac")) == 0 )		//Song Duration
						{

							//分解MAC为6个字节数据
							unsigned char *p=NULL;
							p=strtok(attrValue,":");
							int k=0;
							if(p)
							{
								mac[group_index*6+k++]=strtol(p,NULL,16);
								while( (p=strtok(NULL,":")) )
								{
									mac[group_index*6+k++]=strtol(p,NULL,16);
								}
							}
#if 0
							printf("\nMAC=");
							for(k=0;k<6;k++)
							{
								printf("%x:",mac[group_index*6+k]);
							}
							printf("\n");
#endif
						}
						nodeTattr=nodeTattr->nextSibling;
					}
					nodeT=nodeT->nextSibling;
					group_index++;
				}
			}



			/*
			 * 	unsigned char g_group_id;					 //分组ID
				unsigned char ZoneNum;						 //包含的分区数量
				unsigned char g_group_name[32];	       		 //分组名字
				unsigned char g_zone_contain[MAX_ZONE_NUM];	     	 //包含的分区号
				unsigned char g_zone_mac[6*MAX_ZONE_NUM];	   		 //包含的分区号
				unsigned char g_group_isSelect;		   		 //是否选中
			 */
			m_stGroup_Info.groupInfo[list_index].ZoneNum=atoi(Zone_Count);
			m_stGroup_Info.groupInfo[list_index].g_group_id=list_index;
			strcpy(m_stGroup_Info.groupInfo[list_index].g_group_realId,Group_readId);
			strcpy(m_stGroup_Info.groupInfo[list_index].g_group_name,Group_Name);
			memcpy(m_stGroup_Info.groupInfo[list_index].g_zone_mac,mac,sizeof(mac));
			if(GroupUserName != NULL)
			{
				strcpy(m_stGroup_Info.groupInfo[list_index].g_group_UserName,GroupUserName);
			}

			nodeList=nodeList->next;
			list_index++;
		}


		if(group_enter_index!=-1)
		{
			int curGroupIndex=Get_group_ori_index_by_realId(tmp_current_groupId);
			if(group_enter_index!=curGroupIndex)
			{
				UI_Group_ExitExtraMode();
			}
		}


		Save_Group_Info();
		pthread_mutex_unlock(&GroupListMutex);
		Account_Group_Update();


	#if 0
		ixmlFreeDOMString(docString);
	#endif
		//释放结点资源
		if (nodeListHead)
			ixmlNodeList_free(nodeListHead);
		ixmlDocument_free(DescDoc); // 释放资源

		g_xml_groupInfo_download_flag=0;

		send_host_xml_update_status(XML_FILE_GROUP,XML_FILE_SUCCEED);

		printf("Group Xml update...\n");
		//刷新界面
		control_win_update(1,0);

		#if SUPPORT_AUTO_TRIGGER
		refresh_trigger_settings_win(0,1);
		#endif
}



int XML_Group_Thread()
{
	if(g_xml_groupInfo_download_flag)
	{
		return ERROR;
	}
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)p_xml_group, NULL);
	if (ret == -1)
	{
		if(Paging_status != PAGING_START) printf("XML_Group_Thread Failure!\n");
		return ERROR;
	}
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}








/*********************************************************************
 * @fn      p_xml_playlist
 *
 * @brief  	xml获取歌曲列表信息
 *
 * @param
 *
 * @return ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
void* p_xml_playlist()
{
		int ret;
		if(g_xml_playlist_download_flag)		//如果正在下载，退出
		{
			//发送同步失败信息到主机
			printf("\ng_xml_playlist_download_flag=1,BUSY\n");
			send_host_xml_update_status(XML_FILE_MUSICLIST,XML_FILE_BUSY);
			return 0;
		}

		g_xml_playlist_download_flag=1;


		IXML_Document *DescDoc = NULL;
		pthread_mutex_lock(&XMLDownloadMutex);
		ret = UpnpDownloadXmlDoc(g_xml_Playlist_url, &DescDoc); // 下载设备描述xml文档
		if (ret != UPNP_E_SUCCESS)
		{
			printf("Error obtaining device description from %s -- error = %d\n", g_xml_Playlist_url, ret);
			if(DescDoc)
				ixmlDocument_free(DescDoc); // 释放资源
			g_xml_playlist_download_flag=0;
			pthread_mutex_unlock(&XMLDownloadMutex);
			send_host_xml_update_status(XML_FILE_MUSICLIST,XML_FILE_FAIL);
			return 0;
		}
		else
		{
			pthread_mutex_unlock(&XMLDownloadMutex);
			printf("\np_xml_playlist DownloadXmlDoc OK\n");
		}
		//The caller is required to free the \b DOMString
		 //* returned from this function using \b ixmlFreeDOMString when it
		 //* is no longer required.
	#if 0
		char *docString=ixmlDocumenttoString(DescDoc);
		printf("\nxml=%s\n",docString);
	#endif


		pthread_mutex_lock(&MusicListMutex);

		//判断总节点名字
		//printf("\nXML NAME=%s\n",DescDoc->n.firstChild->nodeName);
		if(strncasecmp(DescDoc->n.firstChild->nodeName,"PlayList",strlen("PlayList"))!=0)
		{
			//发送错误信息至主机
			pthread_mutex_unlock(&MusicListMutex);
			ixmlDocument_free(DescDoc); // 释放资源
			g_xml_playlist_download_flag=0;
			pthread_exit(NULL);
		}

		//整个歌曲列表信息
		Nodeptr nodeTattr=DescDoc->n.firstChild->firstAttr;
		char *musiclist_count=NULL;
		char *musiclist_dateTime=NULL;
		while(nodeTattr)
		{
			char *attrName = nodeTattr->nodeName;
			char *attrValue = nodeTattr->nodeValue;
			//ID="20B4F3C5-2837-4f9e-86FC-47AF86C6BA96" Name="My List" SongCount="2" DateTime="2016-02-20 11:02:20" UpdateType="1"

			if( strncasecmp(attrName,"ListCount",strlen("ListCount")) == 0 )					//ID
			{
				musiclist_count=attrValue;
				//printf("\nmusiclist_count=%s\n",musiclist_count);
			}
			else if( strncasecmp(attrName,"DateTime",strlen("DateTime")) == 0 )					//Name
			{
				musiclist_dateTime=attrValue;
				//printf("\nmusiclist_dateTime=%s\n",musiclist_dateTime);
			}
			nodeTattr=nodeTattr->nextSibling;
		}


		if(musiclist_dateTime == NULL)
		{
			//发送错误信息至主机
			send_host_xml_update_status(XML_FILE_MUSICLIST,XML_FILE_FAIL);
			pthread_mutex_unlock(&MusicListMutex);
			ixmlDocument_free(DescDoc); // 释放资源
			g_xml_playlist_download_flag=0;
			pthread_exit(NULL);
		}

		static char cur_xml_playlist_url[256]={0};
		bool needUpdate=true;	//需要更新
		if(strcmp(cur_xml_playlist_url,g_xml_Playlist_url) == 0)	//XML地址跟当前一样，那么只需要判断日期
		{
			if(	strcmp(musiclist_dateTime,serverMusicList.DateTime)==0 )		//日期相等，不更新
			{
				needUpdate=false;
			}
		}
		else	//XML地址跟当前不同，那么保存地址
		{
			sprintf(cur_xml_playlist_url,"%s",g_xml_Playlist_url);
		}
		if(!needUpdate)
		{
			//发送错误信息至主机
			send_host_xml_update_status(XML_FILE_MUSICLIST,XML_FILE_NEWEST);
			pthread_mutex_unlock(&MusicListMutex);
			ixmlDocument_free(DescDoc); // 释放资源
			g_xml_playlist_download_flag=0;
			pthread_exit(NULL);
		}

		//如果是龙之音V1版本，那么先不退出音乐列表，而是等待结束后刷新一次列表
		#if (!IS_LZY_NEW_TRANSMITTER_OR_PAGER)
		if(GetCurrentWin()==WIN_MUSICLIST)
		{
			musiclist_back_to_control_win(0);
		}
		#endif

		memset(serverMusicList.DateTime,0,sizeof(serverMusicList.DateTime));
		strcpy(serverMusicList.DateTime,musiclist_dateTime);

	
		serverMusicList.DirNum=atoi(musiclist_count);
		if(serverMusicList.DirNum>MUSICNETDIRMAX)
		{
			//发送错误信息至主机
			send_host_xml_update_status(XML_FILE_MUSICLIST,XML_FILE_FAIL);
			pthread_mutex_unlock(&MusicListMutex);
			ixmlDocument_free(DescDoc); // 释放资源
			g_xml_playlist_download_flag=0;
			pthread_exit(NULL);
		}


#if 1
	    int t;
	    for(t=0;t<MUSICNETDIRMAX;t++)
	    {
	    	if(serverMusicList.SongDir[t].musicfile!=NULL)
	    	{
	    		//清空现有歌曲结构体
	    		free(serverMusicList.SongDir[t].musicfile);
	    		serverMusicList.SongDir[t].musicfile=NULL;
	    	}
	    	memset(&serverMusicList.SongDir[t],0,sizeof(&serverMusicList.SongDir[t]));
	    }
#endif

		//解析
		IXML_NodeList *nodeList = ixmlDocument_getElementsByTagName(DescDoc,"List");
		IXML_NodeList *nodeListHead=nodeList;
		int list_index=0;
		while(nodeList)
		{
			//   <RadioInfo type="HTTPS_TEST" count="3" belongs="0">
			//char *kk=nodeList->nodeItem->nodeName;		//得到所有节点名,记录节点下子节点个数
			//printf("\nkk=%s,type=%s,count=%s\n",kk,nodeList->nodeItem->firstAttr->nodeValue,nodeList->nodeItem->firstAttr->nextSibling->nodeValue);

			Nodeptr nodeTattr=nodeList->nodeItem->firstAttr;
			char *List_id=NULL;
			char *List_Name=NULL;
			char *List_SongCount=NULL;
			char *List_DateTime=NULL;
			char *List_UpdateType=NULL;
			char *List_UserName=NULL;
			while(nodeTattr)
			{
				char *attrName = nodeTattr->nodeName;
				char *attrValue = nodeTattr->nodeValue;
				//ID="20B4F3C5-2837-4f9e-86FC-47AF86C6BA96" Name="My List" SongCount="2" DateTime="2016-02-20 11:02:20" UpdateType="1"

				if( strncasecmp(attrName,"ID",strlen("ID")) == 0 )		//ID
				{
					List_id=attrValue;
				}
				else if( strncasecmp(attrName,"Name",strlen("Name")) == 0 )					//Name
				{
					List_Name=attrValue;
				}
				else if( strncasecmp(attrName,"SongCount",strlen("SongCount")) == 0 )		//SongCount
				{
					List_SongCount=attrValue;
				}
				else if( strncasecmp(attrName,"DateTime",strlen("DateTime")) == 0 )			//DateTime
				{
					List_DateTime=attrValue;
				}
				else if( strncasecmp(attrName,"UpdateType",strlen("UpdateType")) == 0 )		//UpdateType
				{
					List_UpdateType=attrValue;
				}
				else if( strncasecmp(attrName,"UserName",strlen("UserName")) == 0 )		//UpdateType
				{
					List_UserName=attrValue;
				}
				nodeTattr=nodeTattr->nextSibling;
			}

			if(atoi(List_SongCount)>MUSICNETLISTSONGMAX)
			{
				//发送错误信息至主机
				printf("\nList_SongCount too large,ERROR\n");
				ixmlDocument_free(DescDoc); // 释放资源
				g_xml_playlist_download_flag=0;
				pthread_mutex_unlock(&XMLDownloadMutex);
				send_host_xml_update_status(XML_FILE_MUSICLIST,XML_FILE_FAIL);
				return 0;
			}

			//首先判断ID
			if( List_id == NULL)
			{
				//发送错误信息至主机
				printf("\nList_id == NULL\n");
				ixmlDocument_free(DescDoc); // 释放资源
				g_xml_playlist_download_flag=0;
				pthread_mutex_unlock(&XMLDownloadMutex);
				send_host_xml_update_status(XML_FILE_MUSICLIST,XML_FILE_BUSY);
				return 0;
			}
			
			serverMusicList.SongDir[list_index].musicfile = calloc( atoi(List_SongCount),sizeof(FILENAME) );

			if(List_UserName == NULL)
			{
				memset(serverMusicList.SongDir[list_index].UserName,0,sizeof(serverMusicList.SongDir[list_index].UserName));
			}
			else
			{
				sprintf(serverMusicList.SongDir[list_index].UserName,"%s",List_UserName);
			}
			

			//得到子节点firstChild
			Nodeptr nodeT=nodeList->nodeItem->firstChild;
			nodeTattr=NULL;

			if(nodeT)
			{
				int file_index=0;
				while(nodeT)
				{
					nodeTattr=nodeT->firstAttr;

					char *Song_Duration=NULL;
					char *Size=NULL;
					char *PathName=NULL;
					char *userName=NULL;

					while(nodeTattr)	//nodeTattr
					{
						char *attrName = nodeTattr->nodeName;
						char *attrValue = nodeTattr->nodeValue;

						if( strncasecmp(attrName,"Duration",strlen("Duration")) == 0 )		//Song Duration
						{
							Song_Duration=attrValue;
						}
						else if( strncasecmp(attrName,"Size",strlen("Size")) == 0 )						//Size
						{
							Size=attrValue;
						}
						else if( strncasecmp(attrName,"PathName",strlen("PathName")) == 0 )				//PathName
						{
							PathName=attrValue;
						}
						else if( strncasecmp(attrName,"UserName",strlen("UserName")) == 0 )				//UserName
						{
							userName=attrValue;
						}
						nodeTattr=nodeTattr->nextSibling;
					}

					//判断文件是否存在

					//得到歌名
					char *SongName=NULL;
					char *p=strrchr(PathName,'/');
					if(p)
					{
						SongName=p+1;
					}
					else
					{
						printf("\np_xml_playlist:SongName error.");
					}

					serverMusicList.SongDir[list_index].musicfile[file_index].IsDownload=0;
					strcpy(serverMusicList.SongDir[list_index].musicfile[file_index].SongUrl,PathName);
					strncpy(serverMusicList.SongDir[list_index].musicfile[file_index].cName,SongName,strlen(SongName)>=MUSICFILENAMELEN?MUSICFILENAMELEN-1:strlen(SongName));
					strcpy(serverMusicList.SongDir[list_index].musicfile[file_index].duration,Song_Duration);
					strcpy(serverMusicList.SongDir[list_index].musicfile[file_index].size,Size);
					if(userName!=NULL)
						strcpy(serverMusicList.SongDir[list_index].musicfile[file_index].UserName,userName);
					else	//为了兼容旧的服务器，可能没有下发userName字段
						strcpy(serverMusicList.SongDir[list_index].musicfile[file_index].UserName,SUPER_USER_NAME);

					nodeT=nodeT->nextSibling;
					file_index++;
				}
			}


			strcpy(serverMusicList.SongDir[list_index].id,List_id);
			serverMusicList.SongDir[list_index].nFileNum=atoi(List_SongCount);
			strcpy(serverMusicList.SongDir[list_index].DateTime,List_DateTime);
			strcpy(serverMusicList.SongDir[list_index].cDirName,List_Name);

			nodeList=nodeList->next;
			list_index++;
		}


		//打印目录信息
#if 0
		int i,k;
		for(i=0;i<serverMusicList.DirNum;i++)
		{
			printf("\nlistName=%s,FileCount=%d\n",serverMusicList.SongDir[i].cDirName,serverMusicList.SongDir[i].nFileNum);
			for(k=0;k<serverMusicList.SongDir[i].nFileNum;k++)
			{
				printf("\nSong_Name=%s,Song_url=%s,download_flag=%d\n",serverMusicList.SongDir[i].musicfile[k].cName,serverMusicList.SongDir[i].musicfile[k].SongUrl,serverMusicList.SongDir[i].musicfile[k].IsDownload);
			}
		}
#endif

		//扫描本机所有目录，并进行对比，如果没有那个目录，则删除目录,再扫描里面所有歌曲，没有则删除

#if 0
		scan_dir();
#endif
		//Save_MusicFile_Info();
		pthread_mutex_unlock(&MusicListMutex);
		g_xml_playlist_download_flag=0;

		send_host_xml_update_status(XML_FILE_MUSICLIST,XML_FILE_SUCCEED);


		//重置集中模式-最后一次播放歌曲信息
		memset(st_Concentrated_Info.Last_play_ListId,0,sizeof(st_Concentrated_Info.Last_play_ListId));
		memset(st_Concentrated_Info.Last_play_MusicName,0,sizeof(st_Concentrated_Info.Last_play_MusicName));

		//终端-开启歌曲下载进程，寻呼机无需开启
#if 0
		MusicFile_Download_thread();
#endif

		//龙之音V1版本播放列表变更后，重新获取存储空间信息
		#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
		musiclist_refresh_list(0);
		Send_host_get_account_storageCapacity(m_stUser_Info.CurrentUserName);
		#endif
		
	#if 0
		ixmlFreeDOMString(docString);
	#endif
		//释放结点资源
		if (nodeListHead)
			ixmlNodeList_free(nodeListHead);
		ixmlDocument_free(DescDoc); // 释放资源
}



int XML_Playlist_Thread()
{
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)p_xml_playlist, NULL);
	if (ret == -1)
	{
		if(Paging_status != PAGING_START) printf("XML_Playlist_Thread Failure!\n");
		return ERROR;
	}
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}




int scan_dir()
{
	int i;
	struct dirent *entry;
	struct dirent *file_entry;
		struct stat statbuf;

		DIR *dpDir;
		DIR *dpfile;
		dpDir =opendir("/mnt/sd");
		if(dpDir == NULL)
		{
	      printf("ERROR:Mp3File_FindDIR opendir dpDir error!!!\n");
	      return 0;
		}

		int dir_p=0;
		while((entry = readdir(dpDir))!= NULL)
		{
			lstat(entry->d_name,&statbuf);   //由文件描述词取得文件状态
			//if(S_ISDIR(statbuf.st_mode))     //文件的类型和存取的权限    ??有时不能识别目录
				if(strcmp(".",entry->d_name)==0 || strcmp("..",entry->d_name)==0)   //比较字符串
					continue;

				/*记录文件夹数目*/
				if((entry->d_type == 4) && (strlen(entry->d_name) != 0))
				{
	#if 1
					if( strcmp(entry->d_name,"lost+found") == 0 )
					{
						continue;
					}
	#endif

					#if _DEBUG_MUSIC_
					printf("Mp3File_FindDIR000...nDirNum=%d entry->d_name=%s\n", dir_p+1,entry->d_name);
					#endif

					int found_index=-1;
					for(i=0;i<serverMusicList.DirNum;i++)
					{
						if( strcmp(serverMusicList.SongDir[i].cDirName,entry->d_name) == 0 )
						{
							found_index=i;
							break;
						}
					}

					if(found_index<0)
					{

						char buf[128]={0};
						sprintf(buf,"rm /mnt/sd/%s -rf",space_change(entry->d_name));
						system(buf);
						continue;
					}

					//找到此目录
					char dirName[128]={0};
					strcpy(dirName,entry->d_name);

					char buf[128]={0};
					sprintf(buf,"/mnt/sd/%s",entry->d_name);
					dpfile=NULL;
					dpfile=opendir(buf);
					if(dpfile == NULL)
					{
				      printf("ERROR:Mp3File_FindDIR opendir dpfile error!!!\n");
				      continue;
					}


					while((file_entry = readdir(dpfile))!= NULL)
					{
						lstat(file_entry->d_name,&statbuf);   //由文件描述词取得文件状态
						//if(S_ISDIR(statbuf.st_mode))     //文件的类型和存取的权限    ??有时不能识别目录
							if(strcmp(".",file_entry->d_name)==0 || strcmp("..",file_entry->d_name)==0)   //比较字符串
								continue;
						int found_song=0;
						for(i=0;i<serverMusicList.SongDir[found_index].nFileNum;i++)
						{
							//如果找到文件但下载标志为0，则删除
							if( strcmp(serverMusicList.SongDir[found_index].musicfile[i].cName,file_entry->d_name) == 0 )
							{
								found_song=1;
								if( !serverMusicList.SongDir[found_index].musicfile[i].IsDownload )
								{
									char buf[128]={0};
									char buf1[128]={0};
									char buf2[128]={0};
									strcpy(buf1,(char *)space_change(dirName));
									strcpy(buf2,(char *)space_change(file_entry->d_name));
									sprintf(buf,"rm /mnt/sd/%s/%s -f",buf1,buf2);
									//sprintf(buf,"rm /mnt/sd/%s/%s -f",space_change(dirName),space_change(file_entry->d_name));	//一个函数不能同时多次调用space_change，否则值相同
									system(buf);
								}
								break;
							}
						}
						if(!found_song)
						{
							char buf[128]={0};
							char buf1[128]={0};
							char buf2[128]={0};
							strcpy(buf1,(char *)space_change(dirName));
							strcpy(buf2,(char *)space_change(file_entry->d_name));
							sprintf(buf,"rm /mnt/sd/%s/%s -f",buf1,buf2);
							system(buf);
						}
					}
					closedir(dpfile);   //关闭目录

					dir_p++;
				}
		}
		closedir(dpDir);   //关闭目录


		 return SUCCEED;
}


#endif














/*********************************************************************
 * @fn      p_xml_user
 *
 * @brief  	xml获取用户信息
 *
 * @param
 *
 * @return ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
void* p_xml_user()
{
		int ret;
		g_xml_userInfo_download_flag=1;
		IXML_Document *DescDoc = NULL;
		pthread_mutex_lock(&XMLDownloadMutex);
		ret = UpnpDownloadXmlDoc(g_xml_userInfo_url, &DescDoc); // 下载设备描述xml文档
		if (ret != UPNP_E_SUCCESS)
		{
			printf("Error obtaining device description from %s -- error = %d\n", g_xml_userInfo_url, ret);
			if(DescDoc)
				ixmlDocument_free(DescDoc); // 释放资源
			g_xml_userInfo_download_flag=0;
			pthread_mutex_unlock(&XMLDownloadMutex);
			send_host_xml_update_status(XML_FILE_USER,XML_FILE_FAIL);
			return 0;
		}
		else
		{
			pthread_mutex_unlock(&XMLDownloadMutex);
			printf("p_xml_userInfo DownloadXmlDoc OK\n");
		}
		//The caller is required to free the \b DOMString
			//* returned from this function using \b ixmlFreeDOMString when it
			//* is no longer required.
	#if 0
		char *docString=ixmlDocumenttoString(DescDoc);
		printf("\nxml=%s\n",docString);
	#endif


		pthread_mutex_lock(&UserInfoMutex);

		

		//判断总节点名字
		//printf("\nXML NAME=%s\n",DescDoc->n.firstChild->nodeName);
		if(strncasecmp(DescDoc->n.firstChild->nodeName,"Users",strlen("Users"))!=0)
		{
			//发送错误信息至主机
			send_host_xml_update_status(XML_FILE_USER,XML_FILE_FAIL);
			
			pthread_mutex_unlock(&UserInfoMutex);
			ixmlDocument_free(DescDoc); // 释放资源
			g_xml_userInfo_download_flag=0;
			pthread_exit(NULL);
		}

		//整个歌曲列表信息
		Nodeptr nodeTattr=DescDoc->n.firstChild->firstAttr;
		char *user_count=NULL;
		char *user_dateTime=NULL;
		while(nodeTattr)
		{
			char *attrName = nodeTattr->nodeName;
			char *attrValue = nodeTattr->nodeValue;

			if( strncasecmp(attrName,"UserCount",strlen("UserCount")) == 0 )					//ID
			{
				user_count=attrValue;
				printf("user_count=%s\n",user_count);
			}
			else if( strncasecmp(attrName,"DateTime",strlen("DateTime")) == 0 )					//Name
			{
				user_dateTime=attrValue;
				//printf("user_dateTime=%s\n",user_dateTime);
			}
			nodeTattr=nodeTattr->nextSibling;
		}

		if(strlen(user_dateTime)>0 && strcmp(user_dateTime,m_stUser_Info.DateTime)==0)		//id存在且更新日期等于现有日期
		{
			//发送错误信息至主机
			printf("p_xml_user:DataTime equal...\n");
			send_host_xml_update_status(XML_FILE_USER,XML_FILE_NEWEST);
			g_xml_userInfo_download_flag=0;

			pthread_mutex_unlock(&UserInfoMutex);
			ixmlDocument_free(DescDoc); // 释放资源
			pthread_exit(NULL);
		}


		//保存原来的用户结构体
		USERINFO_DETAIL PreUserInfo[MAX_USER_NUM]={0};
		memcpy(&PreUserInfo,&m_stUser_Info.userInfo,sizeof(PreUserInfo));
		int preTotalUser = m_stUser_Info.TotalUser;
		int t=0;
		for(t=0;t<preTotalUser;t++)
		{
			//将用户结构体中的分区数据保存下来
			//printf("ttt00,zoneCount=%d\n",PreUserInfo[t].ZoneCount);
			PreUserInfo[t].ZoneMac = malloc(PreUserInfo[t].ZoneCount*6);
			if(PreUserInfo[t].ZoneMac == NULL || m_stUser_Info.userInfo[t].ZoneMac == NULL)
			{
				if(PreUserInfo[t].ZoneMac == NULL)
				{
					//printf("error01...\n");
					//printf("error001...\n");
				}
				else
				{
					//printf("error02...\n");
					//printf("error002...\n");
				}
			}
			memcpy(PreUserInfo[t].ZoneMac,m_stUser_Info.userInfo[t].ZoneMac,PreUserInfo[t].ZoneCount*6);
			//释放用户结构体
			if(m_stUser_Info.userInfo[t].ZoneMac)
			{
				free(m_stUser_Info.userInfo[t].ZoneMac);
				m_stUser_Info.userInfo[t].ZoneMac=NULL;
			}
		}
		memset(&m_stUser_Info.userInfo,0,sizeof(m_stUser_Info.userInfo));

		m_stUser_Info.TotalUser=atoi(user_count)>MAX_USER_NUM?MAX_USER_NUM:atoi(user_count);
		strcpy(m_stUser_Info.DateTime,user_dateTime);

		//解析
		IXML_NodeList *nodeList = ixmlDocument_getElementsByTagName(DescDoc,"User");
		IXML_NodeList *nodeListHead=nodeList;
		int list_index=0;

		int user_change=0;
		int found_current_user=0;
		while(nodeList)
		{
			Nodeptr nodeTattr=nodeList->nodeItem->firstAttr;
			char *User_Name=NULL;
			char *User_Password=NULL;
			char *User_Authorization=NULL;
			char *User_UUID=NULL;
			char *User_PlayMode=NULL;
			char *User_zone_Count=NULL;
			char *User_LimitCode=NULL;

			while(nodeTattr)
			{
				char *attrName = nodeTattr->nodeName;
				char *attrValue = nodeTattr->nodeValue;
				//ID="20B4F3C5-2837-4f9e-86FC-47AF86C6BA96" Name="My List" SongCount="2" DateTime="2016-02-20 11:02:20" UpdateType="1"

				if( strncasecmp(attrName,"Name",strlen("Name")) == 0 )								//NAME
				{
					User_Name=attrValue;
				}
				else if( strncasecmp(attrName,"Password",strlen("Password")) == 0 )					//password
				{
					User_Password=attrValue;
				}
				else if( strncasecmp(attrName,"Authorization",strlen("Authorization")) == 0 )		//Authorization
				{
					User_Authorization=attrValue;
				}
				else if( strncasecmp(attrName,"UUID",strlen("UUID")) == 0 )		//UUID
				{
					User_UUID=attrValue;
				}
				else if( strncasecmp(attrName,"PlayMode",strlen("PlayMode")) == 0 )		//Authorization
				{
					User_PlayMode=attrValue;
				}
				else if( strncasecmp(attrName,"SectionCount",strlen("SectionCount")) == 0 )			//section count
				{
					User_zone_Count=attrValue;
				}
				else if( strncasecmp(attrName,"LimitCode",strlen("LimitCode")) == 0 )			//section count
				{
					User_LimitCode=attrValue;
				}
				nodeTattr=nodeTattr->nextSibling;
			}


			//首先判断ID
			if( User_Name == NULL)
			{
				//发送错误信息至主机
				send_host_xml_update_status(XML_FILE_USER,XML_FILE_FAIL);
				for(t=0;t<preTotalUser;t++)
				{
					if(PreUserInfo[t].ZoneMac)
					{
						free(PreUserInfo[t].ZoneMac);
					}
				}
				pthread_mutex_unlock(&UserInfoMutex);
				ixmlDocument_free(DescDoc); // 释放资源
				g_xml_userInfo_download_flag=0;
				pthread_exit(NULL);
			}

			m_stUser_Info.userInfo[list_index].ZoneMac = calloc(atoi(User_zone_Count),6);

			//得到子节点firstChild
			Nodeptr nodeT=nodeList->nodeItem->firstChild;
			nodeTattr=NULL;
			unsigned char *mac=calloc(atoi(User_zone_Count),6);
			if(nodeT)
			{
				int zone_index=0;

				while(nodeT)
				{
					nodeTattr=nodeT->firstAttr;

					while(nodeTattr)	//nodeTattr
					{
						char *attrName = nodeTattr->nodeName;
						char *attrValue = nodeTattr->nodeValue;

						if( strncasecmp(attrName,"Mac",strlen("Mac")) == 0 )		//Song Duration
						{
							//分解MAC为6个字节数据
							unsigned char *p=NULL;
							p=strtok(attrValue,":");
							int k=0;
							if(p)
							{
								mac[zone_index*6+k++]=strtol(p,NULL,16);
								while( (p=strtok(NULL,":")) )
								{
									mac[zone_index*6+k++]=strtol(p,NULL,16);
								}
							}
						}
						nodeTattr=nodeTattr->nextSibling;
					}
					nodeT=nodeT->nextSibling;
					zone_index++;
				}
			}

			if(	strcmp(m_stUser_Info.CurrentUserName,User_Name) == 0 )
			{
				found_current_user=1;

				//当前账户密码或者分区变了，需要退出到登录界面
				if(	strcmp(PreUserInfo[m_stUser_Info.CurrentUserIndex].password,User_Password) || \
					( strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) && ( PreUserInfo[m_stUser_Info.CurrentUserIndex].ZoneCount != atoi(User_zone_Count) || \
					memcmp(PreUserInfo[m_stUser_Info.CurrentUserIndex].ZoneMac,mac,atoi(User_zone_Count)*6) ) ) )
				{
					user_change=1;
				}
				//更新至新的索引
				m_stUser_Info.CurrentUserIndex = list_index;
			}

			int k=0;
			int singleUser_found=0;
			for(k=0;k<preTotalUser;k++)
			{
				if(strcmp(PreUserInfo[k].name,User_Name) == 0)
				{
					singleUser_found=1;
					break;
				}
			}
			if(singleUser_found)	//找到了
			{
				//账户密码变了，取消之前的保存状态
				if(	strcmp( PreUserInfo[k].password,User_Password ) )
				{
					m_stUser_Info.userInfo[list_index].passwordRemember=0;
					memset(m_stUser_Info.userInfo[list_index].password2,0,sizeof(m_stUser_Info.userInfo[list_index].password2));
				}
				else
				{
					m_stUser_Info.userInfo[list_index].passwordRemember=PreUserInfo[k].passwordRemember;
					sprintf(m_stUser_Info.userInfo[list_index].password2,"%s",PreUserInfo[k].password2);
				}
			}
			else	//没找到
			{
				m_stUser_Info.userInfo[list_index].passwordRemember=0;
				memset(m_stUser_Info.userInfo[list_index].password2,0,sizeof(m_stUser_Info.userInfo[list_index].password2));
			}
			

			m_stUser_Info.userInfo[list_index].id=list_index;
			sprintf(m_stUser_Info.userInfo[list_index].name,"%s",User_Name);
			sprintf(m_stUser_Info.userInfo[list_index].password,"%s",User_Password);
			#if 1
			m_stUser_Info.userInfo[list_index].authority=atoi(User_Authorization);
			#endif
			m_stUser_Info.userInfo[list_index].ZoneCount=atoi(User_zone_Count);
			memcpy(m_stUser_Info.userInfo[list_index].ZoneMac,mac,atoi(User_zone_Count)*6);
			if(User_LimitCode!=NULL)
				m_stUser_Info.userInfo[list_index].LimitCode=atoi(User_LimitCode);

			free(mac);

			if(	User_UUID!=NULL	)
			{
				sprintf(m_stUser_Info.userInfo[list_index].uuid,"%s",User_UUID);
			}
			if(User_PlayMode!=NULL)
			{
				m_stUser_Info.userInfo[list_index].playmode = atoi(User_PlayMode);
			}

			//printf("Id:%d,name=%s,ZoneCount=%d\n",list_index,m_stUser_Info.userInfo[list_index].name,m_stUser_Info.userInfo[list_index].ZoneCount);
			nodeList=nodeList->next;
			list_index++;
			if(list_index == m_stUser_Info.TotalUser)
			{
				break;
			}
		}
		
		
		//如果没有找到当前用户
		if(!found_current_user)
		{
			user_change=1;
			m_stUser_Info.CurrentUserIndex=GetUserIndexByUserName(SUPER_USER_NAME);
#if (!IS_APP_HIDE_LOGIN_ACCOUNT)
			sprintf(m_stUser_Info.CurrentUserName,"%s",SUPER_USER_NAME);
#endif
		}
		
		for(t=0;t<preTotalUser;t++)
		{
			if(PreUserInfo[t].ZoneMac)
			{
				free(PreUserInfo[t].ZoneMac);
			}
		}
		pthread_mutex_unlock(&UserInfoMutex);


	#if 0
		ixmlFreeDOMString(docString);
	#endif
		//释放结点资源
		if (nodeListHead)
			ixmlNodeList_free(nodeListHead);
		ixmlDocument_free(DescDoc); // 释放资源

		g_xml_userInfo_download_flag=0;

		Save_User_Info();

		send_host_xml_update_status(XML_FILE_USER,XML_FILE_SUCCEED);

		
		if(GetCurrentWin()==WIN_LOGIN)
		{
			#if (!IS_APP_HIDE_LOGIN_ACCOUNT)
			reFresh_user_list(0);
			#endif
		}
		else if( user_change && IsValidWin(WIN_CONTROL) )
		{
			printf("Back to Login window...\n");
			Back_to_Login_win();
		}

		//更新播放模式
		if(IsValidWin(WIN_CONTROL) && !user_change)
		{
			if( m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].playmode!=0 )
			{
				if(	g_PlayMode != m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].playmode )
				{
					g_PlayMode=m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].playmode;
					save_sysconf("MusicPlay","PlayMode");
					MusicList_Win_PlayMode_change(g_PlayMode,0);
				}
			}
		}
}



int XML_User_Thread()
{
	if(g_xml_userInfo_download_flag)
	{
		return ERROR;
	}
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)p_xml_user, NULL);
	if (ret == -1)
	{
		if(Paging_status != PAGING_START) printf("XML_USER_Thread Failure!\n");
		return ERROR;
	}
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}







#if ENABLE_CALL_FUNCTION
/*********************************************************************
 * @fn      p_xml_pagerInfo
 *
 * @brief  	xml获取寻呼台信息
 *
 * @param
 *
 * @return ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
void* p_xml_pagerInfo()
{
		int ret;
		g_xml_pagerInfo_download_flag=1;
		IXML_Document *DescDoc = NULL;
		pthread_mutex_lock(&XMLDownloadMutex);
		ret = UpnpDownloadXmlDoc(g_xml_pagerInfo_url, &DescDoc); // 下载设备描述xml文档
		if (ret != UPNP_E_SUCCESS)
		{
			printf("Error obtaining device description from %s -- error = %d\n", g_xml_pagerInfo_url, ret);
			if(DescDoc)
				ixmlDocument_free(DescDoc); // 释放资源
			g_xml_pagerInfo_download_flag=0;
			pthread_mutex_unlock(&XMLDownloadMutex);
			send_host_xml_update_status(XML_FILE_PAGER,XML_FILE_FAIL);
			return 0;
		}
		else
		{
			pthread_mutex_unlock(&XMLDownloadMutex);
			printf("p_xml_pagerInfo DownloadXmlDoc OK\n");
		}
		//The caller is required to free the \b DOMString
			//* returned from this function using \b ixmlFreeDOMString when it
			//* is no longer required.
	#if 0
		char *docString=ixmlDocumenttoString(DescDoc);
		printf("\nxml=%s\n",docString);
	#endif


		pthread_mutex_lock(&PagerInfoMutex);

		

		//判断总节点名字
		//printf("\nXML NAME=%s\n",DescDoc->n.firstChild->nodeName);
		if(strncasecmp(DescDoc->n.firstChild->nodeName,"Pagers",strlen("Pagers"))!=0)
		{
			//发送错误信息至主机
			send_host_xml_update_status(XML_FILE_PAGER,XML_FILE_FAIL);
			
			pthread_mutex_unlock(&PagerInfoMutex);
			ixmlDocument_free(DescDoc); // 释放资源
			g_xml_pagerInfo_download_flag=0;
			pthread_exit(NULL);
		}

		//整个歌曲列表信息
		Nodeptr nodeTattr=DescDoc->n.firstChild->firstAttr;
		char *pager_count=NULL;
		char *pager_dateTime=NULL;
		while(nodeTattr)
		{
			char *attrName = nodeTattr->nodeName;
			char *attrValue = nodeTattr->nodeValue;

			if( strncasecmp(attrName,"PagerCount",strlen("PagerCount")) == 0 )					//ID
			{
				pager_count=attrValue;
				printf("pager_count=%s\n",pager_count);
			}
			else if( strncasecmp(attrName,"DateTime",strlen("DateTime")) == 0 )					//Name
			{
				pager_dateTime=attrValue;
				printf("pager_dateTime=%s\n",pager_dateTime);
			}
			nodeTattr=nodeTattr->nextSibling;
		}

		if(strcmp(pager_dateTime,m_stPager_Info.DateTime)==0)		//id存在且更新日期等于现有日期
		{
			//发送错误信息至主机
			printf("p_xml_pagerInfo:DataTime equal...\n");
			send_host_xml_update_status(XML_FILE_PAGER,XML_FILE_NEWEST);
			g_xml_pagerInfo_download_flag=0;

			pthread_mutex_unlock(&PagerInfoMutex);
			ixmlDocument_free(DescDoc); // 释放资源
			pthread_exit(NULL);
		}

		sprintf(m_stPager_Info.DateTime,"%s",pager_dateTime);

		//解析
		IXML_NodeList *nodeList = ixmlDocument_getElementsByTagName(DescDoc,"Pager");
		IXML_NodeList *nodeListHead=nodeList;
		int list_index=0;

		while(nodeList)
		{
			Nodeptr nodeTattr=nodeList->nodeItem->firstAttr;
			char *Pager_Mac=NULL;
			char *Pager_IP=NULL;
			char *Pager_Name=NULL;

			unsigned char int_Pager_Mac[6]={0};

			while(nodeTattr)
			{
				char *attrName = nodeTattr->nodeName;
				char *attrValue = nodeTattr->nodeValue;

				if( strncasecmp(attrName,"Mac",strlen("Mac")) == 0 )					//mac
				{
					Pager_Mac=attrValue;
					//分解MAC为6个字节数据
					unsigned char *p=NULL;
					p=strtok(nodeTattr->nodeValue,":");
					int k=0;
					if(p)
					{
						int_Pager_Mac[k++]=strtol(p,NULL,16);	//atoi不能转换16进制数据,要用strtol
						while( (p=strtok(NULL,":")) )
						{
							int_Pager_Mac[k++]=strtol(p,NULL,16);
						}
					}
				}
				else if( strncasecmp(attrName,"IP",strlen("IP")) == 0 )						//IP
				{
					Pager_IP=attrValue;
				}
				if( strncasecmp(attrName,"Name",strlen("Name")) == 0 )						//NAME
				{
					Pager_Name=attrValue;
				}
				
				nodeTattr=nodeTattr->nextSibling;
			}

			//如果MAC是本机，不处理
			if(memcmp(int_Pager_Mac,MAC_ADDR,6) == 0)
			{
				printf("Local Pager,continue!!!\n");
				nodeList=nodeList->next;
				continue;
			}

			int t=0;
			int found_pager_index=-1;
			for(t=0;t<m_stPager_Info.TotalPager;t++)
			{
				if( memcmp(int_Pager_Mac,m_stPager_Info.pagerInfo[t].mac,6) == 0 )
				{
					found_pager_index=t;
					break;
				}
			}
			if(found_pager_index !=-1 )
			{
				if(strcmp(m_stPager_Info.pagerInfo[found_pager_index].name,Pager_Name))
					sprintf(m_stPager_Info.pagerInfo[found_pager_index].name,"%s",Pager_Name);
				m_stPager_Info.pagerInfo[found_pager_index].ip=IPcharToInt(Pager_IP);
			}
			else
			{
				//新增
				if(m_stPager_Info.TotalPager<MAX_PAGER_NUM)
				{
					int current_pager_num=m_stPager_Info.TotalPager;
					m_stPager_Info.pagerInfo[current_pager_num].id = current_pager_num;
					memcpy(m_stPager_Info.pagerInfo[current_pager_num].mac,int_Pager_Mac,6);
					sprintf(m_stPager_Info.pagerInfo[current_pager_num].name,"%s",Pager_Name);
					m_stPager_Info.pagerInfo[current_pager_num].ip=IPcharToInt(Pager_IP);

					m_stPager_Info.pagerInfo[current_pager_num].source = SOURCE_OFFLINE;
					m_stPager_Info.pagerInfo[current_pager_num].conection = 0;
					m_stPager_Info.pagerInfo[current_pager_num].isSelect = 0;
					m_stPager_Info.pagerInfo[current_pager_num].call_status = 0;

					m_stPager_Info.TotalPager++;
				}
			}

			nodeList=nodeList->next;
		}
		
		
		pthread_mutex_unlock(&PagerInfoMutex);


	#if 0
		ixmlFreeDOMString(docString);
	#endif
		//释放结点资源
		if (nodeListHead)
			ixmlNodeList_free(nodeListHead);
		ixmlDocument_free(DescDoc); // 释放资源

		g_xml_pagerInfo_download_flag=0;

		send_host_xml_update_status(XML_FILE_PAGER,XML_FILE_SUCCEED);

		//查询所有寻呼台设备信息
		QueryAllPagerDeviceInfo();
		printf("pager Xml update...\n");
		//刷新界面
		control_win_update(2,0);
}



int XML_PagerInfo_Thread()
{
	if(g_xml_pagerInfo_download_flag)
	{
		return ERROR;
	}
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)p_xml_pagerInfo, NULL);
	if (ret == -1)
	{
		if(Paging_status != PAGING_START) printf("XML_PagerInfo_Thread Failure!\n");
		return ERROR;
	}
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}

#endif