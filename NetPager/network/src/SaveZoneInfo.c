/***************************************************
*   Copyright (c) 2015 ，广州深宝音响
*   All rights reserved.
*
*   文件名称： SaveZoneInfo.c
*   摘要：
*
*/

#include <stdio.h>
#include <stdlib.h>
#include <malloc.h>
#include <sys/types.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <unistd.h>
#include <string.h>

#include "SaveZoneInfo.h"
#include "sysconf.h"
#include "Paging_win.h"



extern pthread_mutex_t ZoneInfoMutex;


/*********************************************************************
 * @fn      Scan_Zone_Info
 *
 * @brief   浏览分区信息
 *
 * @param
 *
 * @return ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
int Scan_Zone_Info()
{
	FILE *fps;
	int i;
    fps = fopen(ZONEINFOFILE, "r");
    if(fps == NULL)
    {

    	perror("Scan_Save_Zone_Info open error,Create!!!\n");

    	Save_Zone_Info();

    	fps = fopen(ZONEINFOFILE, "r");
    	if(fps == NULL)
    	{
    		return ERROR;
    	}
    }

	//先读出更新日期
    if(!fread(m_stZone_Info.DateTime, sizeof(m_stZone_Info.DateTime), 1, fps))
	{
		perror("Scan_Zone_Info fread DateTime error,Create!!!\n");
		fclose(fps);

		memset(&m_stZone_Info,0,sizeof(m_stZone_Info));
		Save_Zone_Info();
		return SUCCEED;
	}

    if(!fread(m_stZone_Info.zoneInfo, sizeof(ZONEINFO_DETAIL), MAX_ZONE_NUM, fps))
    {
    	perror("Scan_Zone_Info fread error!!!\n");
    	fclose(fps);

		memset(&m_stZone_Info,0,sizeof(m_stZone_Info));
		Save_Zone_Info();
    	return SUCCEED;
    }
    fclose(fps);

    for(i=0;i<MAX_ZONE_NUM;i++)
	{
    	if( m_stZone_Info.zoneInfo[i].g_zone_ip!=0 )
    	{
    		m_stZone_Info.ExistTotalZone++;
    	}
	}
	printf("ExistTotalZone=%d,DateTime=%s\n",m_stZone_Info.ExistTotalZone,m_stZone_Info.DateTime);
    for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
	{
        //将分区音源全部置为离线状态
    	m_stZone_Info.zoneInfo[i].g_zone_source = SOURCE_OFFLINE;
		m_stZone_Info.zoneInfo[i].g_zone_conection=0;
		m_stZone_Info.zoneInfo[i].g_zone_offline_count=0;
		m_stZone_Info.zoneInfo[i].g_zone_isSelect=0;	//非选中
		m_stZone_Info.zoneInfo[i].g_zone_ip=0;
	}




    return SUCCEED;
}



/*********************************************************************
 * @fn      Save_Zone_Info
 *
 * @brief   保存分区信息
 *
 * @param
 *
 * @return ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
int Save_Zone_Info()
{
	FILE *fpd;
	fpd = fopen(ZONEINFOFILE, "w+");
    if(fpd == NULL)
    {
    	perror("Save_Zone_Info open error!!!\n");
        return FILE_ERROE;
    }
	int wfd = fileno(fpd);
	//先写更新日期
    if(!fwrite(m_stZone_Info.DateTime, sizeof(m_stZone_Info.DateTime), 1, fpd))
	{
		perror("Save_Zone_Info fwrite DateTime error!!!\n");
		fflush(fpd);
		fsync(wfd);
		fclose(fpd);
		return ERROR;
	}

    if(!fwrite(m_stZone_Info.zoneInfo, sizeof(ZONEINFO_DETAIL), MAX_ZONE_NUM, fpd))
    {
        perror("Save_Zone_Info fwrite error!!!\n");
		fflush(fpd);
		fsync(wfd);
        fclose(fpd);
        return ERROR;
    }

	printf("Save_Zone_Info succeed:%s\n",m_stZone_Info.DateTime);

	fflush(fpd);
	fsync(wfd);
    fclose(fpd);
    return SUCCEED;
}




/*********************************************************************
 * @fn      OS_Init_Zone_M
 *
 * @brief   初始化用户保存信息模块
 *
 * @param
 *
 * @return  成功返回 - 	0
 * 			失败返回 -	-1
 */
int OS_Init_Zone_M()
{
	int ret;
	ret=Scan_Zone_Info();
	if(ret < 0)
	{
		if(Paging_status != PAGING_START) printf("OS_Init_Zone_M error!!!\n");
		return ERROR;
	}

    //重置分区上线信息
    int i=0;
    for(i=0;i<MAX_ZONE_NUM;i++)
    {
    	m_stZone_Info.zoneInfo[i].g_zone_conection=0;
    	m_stZone_Info.zoneInfo[i].g_zone_isSelect=0;
		//重置设备特性
		m_stZone_Info.zoneInfo[i].g_zone_device_feature = 0;
    }

	int temp_number=0;
	for (i = 0; i <= MAX_ZONE_NUM; i++)
	{
		if(m_stZone_Info.zoneInfo[i].g_zone_ip!=0)		//如果IP存在则更新所有分区数
		{
			temp_number++;
		}
	}
	m_stZone_Info.ExistTotalZone=temp_number;

	return SUCCEED;
}






/*********************************************************************
 * @fn      Scan_Group_Info
 *
 * @brief   浏览分组信息
 *
 * @param
 *
 * @return ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
int Scan_Group_Info()
{
	FILE *fps;
	int i;
    fps = fopen(GROUPINFOFILE, "r");
    if(fps == NULL)
    {
    	perror("Scan_Save_Group_Info open error,Create!!!\n");
    	Save_Group_Info();

    	fps = fopen(GROUPINFOFILE, "r");
    	if(fps == NULL)
    	{
    		return ERROR;
    	}
    }

    //先读出group总数
    if(!fread(&m_stGroup_Info.TotalGroup, sizeof(m_stGroup_Info.TotalGroup), 1, fps))
    {
    	perror("Scan_Group_Info fread TotalGroup error,Create!!!\n");
		fclose(fps);

		memset(&m_stGroup_Info,0,sizeof(m_stGroup_Info));
		Save_Group_Info();
		return SUCCEED;
    }
    //再读出更新日期
    if(!fread(m_stGroup_Info.DateTime, sizeof(m_stGroup_Info.DateTime), 1, fps))
	{
		perror("Scan_Group_Info fread DateTime error,Create\n");
		fclose(fps);

		memset(&m_stGroup_Info,0,sizeof(m_stGroup_Info));
		Save_Group_Info();
		return SUCCEED;
	}
    //最后读出详细信息
    if(m_stGroup_Info.groupInfo)
    {
    	free(m_stGroup_Info.groupInfo);
    	m_stGroup_Info.groupInfo=NULL;
    }

    m_stGroup_Info.groupInfo = calloc(m_stGroup_Info.TotalGroup,sizeof(GROUP_DETAIL));

    printf("\nTotalGroup=%d\n",m_stGroup_Info.TotalGroup);
    if(m_stGroup_Info.TotalGroup)
    {
		if(!fread(m_stGroup_Info.groupInfo, sizeof(GROUP_DETAIL), m_stGroup_Info.TotalGroup, fps))
		{
			perror("Scan_Group_Info fread groupInfo error!!!\n");
			fclose(fps);
			return ERROR;
		}
    }
    fclose(fps);

    return SUCCEED;
}



/*********************************************************************
 * @fn      Save_Group_Info
 *
 * @brief   保存分组信息
 *
 * @param
 *
 * @return ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
int Save_Group_Info()
{
	FILE *fpd;
	fpd = fopen(GROUPINFOFILE, "w+");
    if(fpd == NULL)
    {
    	perror("Save_Group_Info open error!!!\n");
        return FILE_ERROE;
    }

	int wfd = fileno(fpd);
    //先写group总数
    if(!fwrite(&m_stGroup_Info.TotalGroup, sizeof(m_stGroup_Info.TotalGroup), 1, fpd))
    {
    	perror("Scan_Group_Info fwrite TotalGroup error!!!\n");
		fflush(fpd);
		fsync(wfd);
		fclose(fpd);
		return ERROR;
    }

    //再写更新日期
    if(!fwrite(m_stGroup_Info.DateTime, sizeof(m_stGroup_Info.DateTime), 1, fpd))
	{
		perror("Scan_Group_Info fwrite DateTime error!!!\n");
		fflush(fpd);
		fsync(wfd);
		fclose(fpd);
		return ERROR;
	}


    if(!fwrite(m_stGroup_Info.groupInfo, sizeof(GROUP_DETAIL), m_stGroup_Info.TotalGroup, fpd))
    {
        perror("Save_Group_Info fwrite error!!!\n");
		fflush(fpd);
		fsync(wfd);
        fclose(fpd);
        return ERROR;
    }

	fflush(fpd);
	fsync(wfd);
    fclose(fpd);
    return SUCCEED;
}




/*********************************************************************
 * @fn      OS_Init_Group_M
 *
 * @brief   初始化分组保存信息模块
 *
 * @param
 *
 * @return  成功返回 - 	0
 * 			失败返回 -	-1
 */
int OS_Init_Group_M()
{
	int ret;
	ret=Scan_Group_Info();
	if(ret < 0)
	{
		if(Paging_status != PAGING_START) printf("OS_Init_Group_M error!!!\n");
		return ERROR;
	}
    //重置分组上线信息
    int i=0;
    for(i=0;i<m_stGroup_Info.TotalGroup;i++)
    {
    	m_stGroup_Info.groupInfo[i].g_group_isSelect=0;
    }

	return SUCCEED;
}





int Set_Group_Info(unsigned char g_group_id,unsigned char zoneNum,unsigned char *g_group_name,unsigned char *g_zone_mac)
{
	int i=0,k=0,ret;
	if(g_group_id>=m_stGroup_Info.TotalGroup)
		return ERROR;

	//组ID
	//组名
	m_stGroup_Info.groupInfo[g_group_id].g_group_id=g_group_id;
	m_stGroup_Info.groupInfo[g_group_id].ZoneNum=zoneNum;
	memset(m_stGroup_Info.groupInfo[g_group_id].g_group_name,0,sizeof(m_stGroup_Info.groupInfo[g_group_id].g_group_name));
	strcpy(m_stGroup_Info.groupInfo[g_group_id].g_group_name,g_group_name);
	memset(m_stGroup_Info.groupInfo[g_group_id].g_zone_contain,0,sizeof(m_stGroup_Info.groupInfo[g_group_id].g_zone_contain));
	memset(m_stGroup_Info.groupInfo[g_group_id].g_zone_mac,0,sizeof(m_stGroup_Info.groupInfo[g_group_id].g_zone_mac));
	//包含的分区号
	for(i=0;i<zoneNum;i++)
	{
		//m_stGroup_Info.groupInfo[g_group_id].g_zone_contain[i]=g_zone_contain[i];
		for(k=0;k<6;k++)
		{
			m_stGroup_Info.groupInfo[g_group_id].g_zone_mac[6*i+k]=g_zone_mac[6*i+k];
		}
	}

	ret=Save_Group_Info();
   	if(ret < 0)
	{
		if(Paging_status != PAGING_START) printf("Save_Group_Info error!!!\n");
		return ERROR;
	}
	else
		if(Paging_status != PAGING_START) printf("Save_Group_Info succed!\n");
   	return SUCCEED;
}


int Get_group_ori_index_by_realId(char *id)
{
	int i;
	int group_index=-1;
	for(i=0;i<m_stGroup_Info.TotalGroup;i++)
	{
		if( strcmp(m_stGroup_Info.groupInfo[i].g_group_realId,id) == 0 )
		{
			group_index = i;
			break;
		}
	}
	return group_index;
}

int Get_group_real_index_by_realId(char *id)
{
	int i;
	int group_real_index=-1;
	int ori_index=Get_group_ori_index_by_realId(id);
	if(ori_index>=0)
	{
		for(i=0;i<m_stGroup_Info.TotalGroup;i++)
		{
			if(!m_stGroup_Info.groupInfo[i].g_group_isHide)
			{
				group_real_index++;
			}
			if(i == ori_index)
			{
				break;
			}
		}
	}
	return group_real_index;
}

int Get_Group_ori_index_by_realIndex(int realIndex)
{
	int i;
	int group_index=-1;
	for(i=0;i<m_stGroup_Info.TotalGroup;i++)
	{
		if(!m_stGroup_Info.groupInfo[i].g_group_isHide)
			group_index++;
		if(group_index == realIndex)
		{
			return i;
		}
	}
	return -1;
}


int Set_Zone_Info(unsigned char g_zone_id,unsigned char *g_zone_name, long int g_zone_ip,unsigned char g_zone_mac[6], unsigned char g_zone_source, unsigned char *g_zone_media_name, unsigned char g_zone_vol,
		unsigned char g_zone_conection)
{
	int ret;
	m_stZone_Info.zoneInfo[g_zone_id].g_zone_conection=g_zone_conection;
	m_stZone_Info.zoneInfo[g_zone_id].g_zone_id=g_zone_id;

	m_stZone_Info.zoneInfo[g_zone_id].g_zone_ip=g_zone_ip;
	int i=0;
	for(i=0;i<6;i++)
		m_stZone_Info.zoneInfo[g_zone_id].g_zone_mac[i]=g_zone_mac[i];

	memset(m_stZone_Info.zoneInfo[g_zone_id].g_zone_media_name,0,sizeof(m_stZone_Info.zoneInfo[g_zone_id].g_zone_media_name));
	strcpy(m_stZone_Info.zoneInfo[g_zone_id].g_zone_media_name,g_zone_media_name);
	memset(m_stZone_Info.zoneInfo[g_zone_id].g_zone_name,0,sizeof(m_stZone_Info.zoneInfo[g_zone_id].g_zone_name));
	strcpy(m_stZone_Info.zoneInfo[g_zone_id].g_zone_name,g_zone_name);
	m_stZone_Info.zoneInfo[g_zone_id].g_zone_source=g_zone_source;
	m_stZone_Info.zoneInfo[g_zone_id].g_zone_vol=g_zone_vol;

	ret=Save_Zone_Info();
   	if(ret < 0)
	{
		if(Paging_status != PAGING_START) printf("Save_Zone_Info error!!!\n");
		return ERROR;
	}
	else
		if(Paging_status != PAGING_START) printf("Save_Zone_Info succed!\n");
   	return SUCCEED;
}




/********************************************************
 * @fn      Get_module_number
 *
 * @brief   获取在线分区的数量
 *
 * @param	none
 *
 * @return  int 返回在线分区数量
 *
 */


int Get_online_zone_number()
{
	memset(m_stOnlineZone_Info.onlineZone,0,sizeof(m_stOnlineZone_Info.onlineZone));
	int onlineNum=0;
	int i=0;
	for(i=0;i<MAX_ZONE_NUM;i++)
	{
		if( g_network_mode == NETWORK_MODE_LAN && m_stZone_Info.zoneInfo[i].g_zone_offline_count>=8 )		//注意，当有分区上线时要把对应的m_stZone_Info.g_zone_offline_count[i]置0,g_zone_isSelect置0
		{
			m_stZone_Info.zoneInfo[i].g_zone_conection=0;
			m_stZone_Info.zoneInfo[i].g_zone_offline_count=0;
			m_stZone_Info.zoneInfo[i].g_zone_source=SOURCE_OFFLINE;
			if(m_stZone_Info.zoneInfo[i].g_zone_isSelect)
			{
				 m_stZone_Info.zoneInfo[i].g_zone_isSelect=0;	//非选中
				 if(g_system_work_mode == WORK_MODE_CONCENTRATED || g_network_mode == NETWORK_MODE_WAN)
				 {
					 Update_Selected_Zone();
				 }
			}
		}
		else
		if( m_stZone_Info.zoneInfo[i].g_zone_conection )	//在线
		{
			onlineNum++;
			m_stOnlineZone_Info.onlineZone[onlineNum-1]=i;
		}
	}
	return onlineNum;
}



/********************************************************
 * @fn      Update_Online_zone_moudle
 *
 * @brief   实时更新分区在线数量(5S)
 *
 * @param	none
 *
 * @return  none
 *
 */
/********************************************************/

void *Update_OnlineZone_Check() {
	int i;
	int onlineNum;

	while (1) {
		//在线分区数量数量改变，则初始化全部音源和音量
		pthread_mutex_lock(&ZoneInfoMutex);
		if ( ( onlineNum=Get_online_zone_number() ) != m_stZone_Info.OnlineTotalZone) {
			//更新相关界面,针对离线分区,新上线分区在Network_client中已处理更新
			if(m_stZone_Info.OnlineTotalZone > onlineNum)	//原分区数量比新的还多,说明是分区下线
			{
				m_stZone_Info.OnlineTotalZone = onlineNum;
				control_win_update(0xFF,0);
			}
			else
			{
				m_stZone_Info.OnlineTotalZone = onlineNum;
				//更新信息栏
				//control_win_labelInfo_update(0);
				control_win_update(0xFF,0);
			}
			if(Paging_status != PAGING_START) printf("\nm_stZone_Info.ExistTotalZone=%d\n",m_stZone_Info.ExistTotalZone);
		}
		pthread_mutex_unlock(&ZoneInfoMutex);

		/***********************更新分区状态更新播放窗口*************************************/

		if(g_network_mode == NETWORK_MODE_LAN && g_system_work_mode == WORK_MODE_DISTRIBUTIONAL)
		{
			for (i = 0; i < MAX_ZONE_NUM; i++) {
				m_stZone_Info.zoneInfo[i].g_zone_offline_count++;
			}
		}
		sleep(3);		//30s没有收到上线信息则判定掉线	//while循环体接sleep结尾时后面不能再执行语句，否则段错误
	}
}


//在线分区检测线程
void OnlineZone_Check_thread() {
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr,(void *) Update_OnlineZone_Check, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}



int g_ZoneStatus_refreshTime;
int g_ZoneStatus_save_flag;

/********************************************************
 * @fn      Update_ZoneStatus
 *
 * @brief   定时更新分区状态
 *
 * @param	none
 *
 * @return  none
 *
 */
/********************************************************/
void *Update_ZoneStatus() {
	int i;
	while (1) {
		pthread_mutex_lock(&ZoneInfoMutex);
		if(g_ZoneStatus_refreshTime != 0)
		{
			g_ZoneStatus_refreshTime++;
			if( g_ZoneStatus_refreshTime >= 5 )		//500ms刷新
			{
				g_ZoneStatus_refreshTime=0;
				printf("Update_ZoneStatus.\n");
				control_win_update(0,0);
				if(g_ZoneStatus_save_flag)
				{
					Save_Zone_Info();
				}
				g_ZoneStatus_save_flag=0;
			}
		}
		
		pthread_mutex_unlock(&ZoneInfoMutex);

		usleep(150000);
	}
}


//在线分区检测线程
void Zone_UpdateStatus_thread() {
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr,(void *) Update_ZoneStatus, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}


//调用此函数时，注意加ZoneInfoMutex锁
void set_zoneStatus_refreshTime(int IsSave)
{
	g_ZoneStatus_refreshTime=1;
	if(IsSave)
		g_ZoneStatus_save_flag=1;
}




//设置分区上线/下线状态
void Set_Zone_Online_status(long int ip,int status)
{
	int i=0;
	for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
	{
		if(m_stZone_Info.zoneInfo[i].g_zone_ip == ip)
		{
			m_stZone_Info.zoneInfo[i].g_zone_conection = status;
			m_stZone_Info.zoneInfo[i].g_zone_isSelect=0;	//非选中
			m_stZone_Info.zoneInfo[i].g_zone_offline_count=0;
			break;
		}
	}
}




/****************************************************
 * @fn      Get_ZoneIndex_By_Ip
 *
 * @brief   //根据IP获取分区索引
 *
 * @param
 *
 * @return	分区索引 index 返回-1代表没有此分区
 */
int Get_ZoneIndex_By_Ip(long int ip)
{
	int index=-1;
	int i=0;
	for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
	{
		if(m_stZone_Info.zoneInfo[i].g_zone_ip == ip)
		{
			index=i;
		}
	}
	return index;
}



/****************************************************
 * @fn      Get_ZoneIndex_By_MAC
 *
 * @brief   //根据MAC获取分区索引
 *
 * @param
 *
 * @return	分区索引 index 返回-1代表没有此分区
 */
int Get_ZoneIndex_By_MAC(char *mac)
{
	int i=0;
	int lock_val=pthread_mutex_trylock(&ZoneInfoMutex);
	for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
	{
		if( memcmp(mac,m_stZone_Info.zoneInfo[i].g_zone_mac,6) == 0 )
		{
			if(!lock_val)	//成功加锁
			{
				pthread_mutex_unlock(&ZoneInfoMutex);
			}
			return i;
		}
	}

	if(!lock_val)	//成功加锁
	{
		pthread_mutex_unlock(&ZoneInfoMutex);
	}

	return -1;
}




/****************************************************
 * @fn      Get_ZoneIndex_By_Ip
 *
 * @brief   //根据音源id获取音源名
 *
 * @param
 *
 * @return	分区索引 index 返回-1代表没有此分区
 */
char* Get_Source_Name_ById(unsigned char id)
{
	static char sourceName[32]={0};

	switch(id)
	{
		case SOURCE_FIRE_ALARM:
			strcpy(sourceName,language_playsource_alarm_text);
			break;
		case SOURCE_NET_PAGING:
			strcpy(sourceName,language_playsource_paging_text);
			break;
		case SOURCE_AUX:
			strcpy(sourceName,language_playsource_localPlay_text);
			break;
		case SOURCE_LOCAL_PLAY:
			strcpy(sourceName,language_playsource_netPlay_text);
			break;
		case SOURCE_CALL:
			strcpy(sourceName,language_playsource_intercom_text);
			break;
		case SOURCE_TIMING:
			strcpy(sourceName,language_playsource_timing_text);
			break;
		case SOURCE_NULL:
			strcpy(sourceName,language_playsource_idle_text);
			break;
		case SOURCE_100V:
			strcpy(sourceName,language_playsource_100V_text);
			break;
		case SOURCE_OFFLINE:
			strcpy(sourceName,language_playsource_offline_text);
			break;
		default:
			if(id >= SOURCE_AUDIO_COLLECTOR_MIN && id <= SOURCE_AUDIO_COLLECTOR_MAX )	//信号采集
			{
				//查找音频采集器设备列表
				int i=0;
				for(i=0;i<Audio_collector_list.totalNum;i++)
				{
					if( id >= Audio_collector_list.list[i].id && id<=Audio_collector_list.list[i].id+3 )
						break;
				}
				if(i == Audio_collector_list.totalNum)
				{
					strcpy(sourceName,language_playsource_audioCollector_text);
				}
				else
				{
					strcpy(sourceName,Audio_collector_list.list[i].name);
					//判断属于哪个音频通道
					char channel_num = (id-SOURCE_AUDIO_COLLECTOR_MIN)%4 +1;
					sprintf(sourceName,"%s(CH%d)",sourceName,channel_num);
				}
			}
			break;
	}

	return sourceName;
}


unsigned char IsSupportCallDevice(unsigned char deviceFeature)
{
    return (deviceFeature&DF_CALL);
}

unsigned char IsSupportVideoDevice(unsigned char deviceFeature)
{
    return (deviceFeature&DF_VIDEO);
}