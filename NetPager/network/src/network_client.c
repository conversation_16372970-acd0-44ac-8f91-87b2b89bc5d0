/*********************************************************************
 * Filename: 		network_client.c
 *
 * Description:		网络通讯客户端
 *
 * Author: 			jiangmingshu
 *
 * Date: 			2020.9.20
 */
#include<stdio.h>
#include<stdlib.h>
#include<unistd.h>
#include<string.h>
#include<sys/socket.h>
#include<arpa/inet.h>
#include<netinet/in.h>
#include<sys/types.h>
#include<netdb.h>
#include<sys/ioctl.h>
#include<net/if.h>
#include <ifaddrs.h>
#include<pthread.h>
#include <fcntl.h>
#include <limits.h>

#include "network_client.h"
#include "Auxdio_protocol.h"
#include "Paging_win.h"
#include "http_client.h"
#include "xml.h"
#include "MusicFile.h"
#include "TimerP.h"
#include "I2S.h"
#include "tcp_host.h"
#include "sysconf.h"
#include "md5.h"
#include "win.h"

//寻呼台创建5个socket：

//socket1:组播，加入端口：52074 组播地址：************** 负责接收终端的上线指令。	//只接收不发送
//socket2:组播，加入端口：52052 组播地址：************** 负责发送PCM码流到终端		//只发送不接收
//socket3:组播，加入端口：52094 组播地址：************** 向所有终端发送通知组播组	// 发送/接收 	只接收主机的组播信息
//socket4：单播 48888	负责发送与接收主机单播指令
//socket5：单播 46666	负责发送与接收终端单播指令

#define MULTI_SOCKET_SEND_PCM_PORT 		52052
#define MULTI_SOCKET_SEND_PCM_ADDR 		"**************"
#define MULTI_SOCKET_SEND_COMMAND_PORT  52094
#define MULTI_SOCKET_SEND_COMMAND_ADDR  "**************"
#define MULTI_SOCKET_RECV_COMMAND_PORT  52074
#define MULTI_SOCKET_RECV_COMMAND_ADDR  "**************"
#define MULTI_SOCKET_NETTOOLS_SEND_COMMAND_PORT  52075
#define MULTI_SOCKET_NETTOOLS_SEND_COMMAND_ADDR  "**************"

#define UNICAST_TERMINAL_SOCKET_PORT	46666
#define UNICAST_HOST_SOCKET_PORT		48888
#define UNICAST_NETTOOLS_SOCKET_PORT	48889
#define UNICAST_UDP_RESET_PORT			47777
#define UNICAST_VILLAGE_SOCKET_PORT		60010	//村村响

int multi_socket_send_pcm;
int multi_socket_send_command;
int multi_socket_recv_command;
int unicast_socket_pager;
int unicast_socket_host;
int netTools_socket_host;

struct sockaddr_in multi_socket_send_pcm_peerAddr;
struct sockaddr_in multi_socket_send_command_peerAddr;
struct sockaddr_in multi_socket_recv_command_peerAddr;
struct sockaddr_in unicast_socket_netTools_peerAddr;
struct sockaddr_in unicast_socket_pager_peerAddr;
struct sockaddr_in unicast_socket_host_peerAddr;
struct sockaddr_in unicast_socket_village_peerAddr;

long int g_host_ip;
long int g_networktools_ip;

extern int g_xml_download_port;

extern unsigned char MAC_ADDR[6];

int zoneInfo_update_thread_flag=0xff;




extern pthread_mutex_t TimeMutex;

extern int init_succeed;

extern int gps_count;


int Paging_Fail_Check_Thread_flag=0;

extern int g_send_decode_pcm_type;	//发送的编码算法类型



extern char upgrade_file_md5_str[MD5_STR_LEN+1];
extern int g_update_type;	//0-app 1:bp1048


struct ifreq nif_eth0,nif_4g;

void nif_init()
{
    memset(&nif_eth0, 0, sizeof(nif_eth0));
    memset(&nif_4g, 0, sizeof(nif_4g));
    strcpy(nif_eth0.ifr_name,"eth0");
    strcpy(nif_4g.ifr_name,"eth1");
}

//多网卡时，网络接口变化后（特定条件：开机时是4G，后面插入了有线），一定要将当前网络接口加入组播，否则无法接收。
void socket_join_multicast_membership()
{
	struct ip_mreq mreq;
	mreq.imr_multiaddr.s_addr = inet_addr(MULTI_SOCKET_SEND_COMMAND_ADDR);
	mreq.imr_interface.s_addr = htonl(INADDR_ANY);
	/* 把本机加入组播地址，即本机网卡作为组播成员，只有加入组才能收到组播消息 */
	if (setsockopt(multi_socket_send_command, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq,sizeof (struct ip_mreq)) == -1)
	{
		perror("set multi_socket_send_command IP_ADD_MEMBERSHIP\n");
	}

	mreq.imr_multiaddr.s_addr = inet_addr(MULTI_SOCKET_RECV_COMMAND_ADDR);
	mreq.imr_interface.s_addr = htonl(INADDR_ANY);
	/* 把本机加入组播地址，即本机网卡作为组播成员，只有加入组才能收到组播消息 */
	if (setsockopt(multi_socket_recv_command, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq,sizeof (struct ip_mreq)) == -1)
	{
		perror("set multi_socket_recv_command IP_ADD_MEMBERSHIP\n");
	}
}

void socketInit()
{
	 struct sockaddr_in ia;
	 int sockfd;
	 char recmsg[CLIENT_BUF_SIZE + 1];
	 unsigned int socklen, n;
	 struct ip_mreq mreq;     /* 创建 socket 用于UDP通讯 */

	 multi_socket_send_pcm = socket (AF_INET, SOCK_DGRAM, 0);
	 multi_socket_send_command = socket (AF_INET, SOCK_DGRAM, 0);
	 multi_socket_recv_command = socket (AF_INET, SOCK_DGRAM, 0);
	 unicast_socket_pager = socket(AF_INET, SOCK_DGRAM, 0);
	 unicast_socket_host = socket(AF_INET, SOCK_DGRAM, 0);
	 netTools_socket_host = socket(AF_INET, SOCK_DGRAM, 0);

	#ifndef USE_PC_SIMULATOR
	//发送组播的socket需要先绑定网卡（如果存在4G网卡，那么此处必须）
	if (setsockopt(multi_socket_send_pcm, SOL_SOCKET,SO_BINDTODEVICE, (char *)&nif_eth0, sizeof(nif_eth0)) < 0)
	{
		close(multi_socket_send_pcm);
		perror("multi_socket_send_pcm bind interface fail");
		MyExit (0);
	}
	//发送组播的socket需要先绑定网卡（如果存在4G网卡，那么此处必须）
	if (setsockopt(multi_socket_send_command, SOL_SOCKET,SO_BINDTODEVICE, (char *)&nif_eth0, sizeof(nif_eth0)) < 0)
	{
		close(multi_socket_send_command);
		perror("multi_socket_send_command bind interface fail");
		MyExit (0);
	}
	//发送组播的socket需要先绑定网卡（如果存在4G网卡，那么此处必须）
	if (setsockopt(multi_socket_recv_command, SOL_SOCKET,SO_BINDTODEVICE, (char *)&nif_eth0, sizeof(nif_eth0)) < 0)
	{
		close(multi_socket_recv_command);
		perror("multi_socket_recv_command bind interface fail");
		MyExit (0);
	}
	#endif

#if 0
	 fcntl(multi_socket_send_pcm,F_SETFL,O_NONBLOCK);

	 int send_len=512*1024;
	 setsockopt(multi_socket_send_pcm,SOL_SOCKET,SO_SNDBUF,&send_len,sizeof(int));
#endif
	 //SEND PCM SOCKET
	 sockfd = multi_socket_send_pcm;
	 if (sockfd < 0)
	 {
		if(Paging_status != PAGING_START) printf ("SEND PCM SOCKET creating err in udptalk\n");
		MyExit (1);
	 }

	socklen = sizeof (struct sockaddr_in);
	memset (&multi_socket_send_pcm_peerAddr, 0, socklen);
	multi_socket_send_pcm_peerAddr.sin_family = AF_INET;
	multi_socket_send_pcm_peerAddr.sin_port = htons (MULTI_SOCKET_SEND_PCM_PORT);
	inet_pton(AF_INET, MULTI_SOCKET_SEND_PCM_ADDR, &multi_socket_send_pcm_peerAddr.sin_addr);
#if 0
	/* 绑定自己的端口和IP信息到socket上 */
	if (bind(sockfd, (struct sockaddr *) &multi_socket_send_pcm_peerAddr,sizeof (struct sockaddr_in)) == -1)
	{
		if(Paging_status != PAGING_START) printf ("Bind SEND PCM SOCKET error\n");
		MyExit (0);
	}
#endif

	//SEND COMMAND SOCKET
	 sockfd = multi_socket_send_command;
	 if (sockfd < 0)
	 {
		if(Paging_status != PAGING_START) printf ("SEND COMMAND SOCKET creating err in udptalk\n");
		MyExit (1);
	 }
	 /* 设置要加入组播的地址 */
	 bzero(&mreq, sizeof (struct ip_mreq));
	 inet_pton(AF_INET,MULTI_SOCKET_SEND_COMMAND_ADDR,&ia.sin_addr);
	 /* 设置组地址 */
	 bcopy (&ia.sin_addr.s_addr, &mreq.imr_multiaddr.s_addr, sizeof (struct in_addr));
	 /* 设置发送组播消息的源主机的地址信息 */
	 mreq.imr_interface.s_addr = htonl (INADDR_ANY);
	 /* 把本机加入组播地址，即本机网卡作为组播成员，只有加入组才能收到组播消息 */
	 if (setsockopt(sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq,sizeof (struct ip_mreq)) == -1)
	 {
		perror ("SEND COMMAND SOCKET:");
		MyExit (-1);
	 }
	socklen = sizeof (struct sockaddr_in);
	memset (&multi_socket_send_command_peerAddr, 0, socklen);
	multi_socket_send_command_peerAddr.sin_family = AF_INET;
	multi_socket_send_command_peerAddr.sin_port = htons (MULTI_SOCKET_SEND_COMMAND_PORT);
	inet_pton(AF_INET, MULTI_SOCKET_SEND_COMMAND_ADDR, &multi_socket_send_command_peerAddr.sin_addr);
	/* 绑定自己的端口和IP信息到socket上 */
	if (bind(sockfd, (struct sockaddr *) &multi_socket_send_command_peerAddr,sizeof (struct sockaddr_in)) == -1)
	{
		perror ("Bind SEND COMMAND SOCKET error\n");
		MyExit (0);
	}



	//RECV COMMAND SOCKET
	 sockfd = multi_socket_recv_command;
	 if (sockfd < 0)
	 {
		if(Paging_status != PAGING_START) printf ("RECV COMMAND SOCKET creating err in udptalk\n");
		MyExit (1);
	 }
	 /* 设置要加入组播的地址 */
	 bzero(&mreq, sizeof (struct ip_mreq));
	 inet_pton(AF_INET,MULTI_SOCKET_RECV_COMMAND_ADDR,&ia.sin_addr);
	 /* 设置组地址 */
	 bcopy (&ia.sin_addr.s_addr, &mreq.imr_multiaddr.s_addr, sizeof (struct in_addr));
	 /* 设置发送组播消息的源主机的地址信息 */
	 mreq.imr_interface.s_addr = htonl (INADDR_ANY);
	 /* 把本机加入组播地址，即本机网卡作为组播成员，只有加入组才能收到组播消息 */
	 if (setsockopt(sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq,sizeof (struct ip_mreq)) == -1)
	 {
		perror ("RECV COMMAND SOCKET:");
		MyExit (-1);
	 }
		socklen = sizeof (struct sockaddr_in);
		memset (&multi_socket_recv_command_peerAddr, 0, socklen);
		multi_socket_recv_command_peerAddr.sin_family = AF_INET;
		multi_socket_recv_command_peerAddr.sin_port = htons (MULTI_SOCKET_RECV_COMMAND_PORT);
		inet_pton(AF_INET, MULTI_SOCKET_RECV_COMMAND_ADDR, &multi_socket_recv_command_peerAddr.sin_addr);
		/* 绑定自己的端口和IP信息到socket上 */
		if (bind(sockfd, (struct sockaddr *) &multi_socket_recv_command_peerAddr,sizeof (struct sockaddr_in)) == -1)
		{
			if(Paging_status != PAGING_START) printf ("Bind RECV COMMAND SOCKET error\n");
			MyExit (0);
		}

		int nRecvBuf=256*1024;//设置为256KB
		setsockopt(sockfd,SOL_SOCKET,SO_RCVBUF,(const char*)&nRecvBuf,sizeof(int));


		//unicat
		sockfd=unicast_socket_pager;
		memset(&unicast_socket_pager_peerAddr, 0, sizeof(struct sockaddr_in));	//清空地址信息
		unicast_socket_pager_peerAddr.sin_family = AF_INET; 		//选用TCP/IP协议
		unicast_socket_pager_peerAddr.sin_port = htons(UNICAST_TERMINAL_SOCKET_PORT);//注册端口
		unicast_socket_pager_peerAddr.sin_addr.s_addr = htonl(INADDR_ANY); //允许任何IP地址访问服务器

		if (bind(sockfd, (struct sockaddr *) &unicast_socket_pager_peerAddr,sizeof (struct sockaddr_in)) == -1)
		{
			if(Paging_status != PAGING_START) printf ("Bind unicast_socket_pager SOCKET error\n");
			MyExit (0);
		}

		int nSndBuf=128*1024;//设置为128KB
		setsockopt(sockfd,SOL_SOCKET,SO_SNDBUF,(const char*)&nSndBuf,sizeof(int));

		sockfd=unicast_socket_host;
		memset(&unicast_socket_host_peerAddr, 0, sizeof(struct sockaddr_in));	//清空地址信息
		unicast_socket_host_peerAddr.sin_family = AF_INET; 		//选用TCP/IP协议
		unicast_socket_host_peerAddr.sin_port = htons(UNICAST_HOST_SOCKET_PORT);//注册端口
		unicast_socket_host_peerAddr.sin_addr.s_addr = htonl(INADDR_ANY); //允许任何IP地址访问服务器

		if (bind(sockfd, (struct sockaddr *) &unicast_socket_host_peerAddr,sizeof (struct sockaddr_in)) == -1)
		{
			if(Paging_status != PAGING_START) printf ("Bind unicast_socket_host SOCKET error\n");
			MyExit (0);
		}

		nRecvBuf=128*1024;//设置为128KB
		setsockopt(sockfd,SOL_SOCKET,SO_RCVBUF,(const char*)&nRecvBuf,sizeof(int));


		sockfd=netTools_socket_host;
		memset(&unicast_socket_netTools_peerAddr, 0, sizeof(struct sockaddr_in));	//清空地址信息
		unicast_socket_netTools_peerAddr.sin_family = AF_INET; 		//选用TCP/IP协议
		unicast_socket_netTools_peerAddr.sin_port = htons(UNICAST_NETTOOLS_SOCKET_PORT);//注册端口
		unicast_socket_netTools_peerAddr.sin_addr.s_addr = htonl(INADDR_ANY); //允许任何IP地址访问服务器

		if (bind(sockfd, (struct sockaddr *) &unicast_socket_netTools_peerAddr,sizeof (struct sockaddr_in)) == -1)
		{
			if(Paging_status != PAGING_START) printf ("Bind netTools_socket_host SOCKET error\n");
			MyExit (0);
		}

		nRecvBuf=128*1024;//设置为128KB
		setsockopt(sockfd,SOL_SOCKET,SO_RCVBUF,(const char*)&nRecvBuf,sizeof(int));


		if(Paging_status != PAGING_START) printf("\nsocketInit success!\n");

}





/****************************************************
 * @fn      Udp_Client
 *
 * @brief   socket接收处理共两个socket
 *
 * @param
 *
 * @return
 */
void *Udp_Client()
{
	int Rxlen;
	int Index = 0;
	int i,j, ret;
	int so_broadcast = 1;
	struct ifreq *ifr;
	struct ifconf ifc;
	int Pkg_Length=0;
	int Read_Size = CLIENT_BUF_SIZE;
	unsigned char id_buf[8]={0};
	unsigned char RxBuf[CLIENT_BUF_SIZE];
	struct timeval timeout;
	fd_set readfd; 		//读文件描述符集合
	int fdmax=0;
	int socklen = sizeof (struct sockaddr_in);
	struct sockaddr_in socketAddr;

	while(1)
	{
		FD_ZERO(&readfd); //清空读文件描述集合
		FD_SET(multi_socket_recv_command, &readfd); //注册套接字文件描述符
		FD_SET(multi_socket_send_command, &readfd); //注册套接字文件描述符
		FD_SET(unicast_socket_pager, &readfd); 		//注册套接字文件描述符
		FD_SET(unicast_socket_host, &readfd); //注册套接字文件描述符
		FD_SET(netTools_socket_host, &readfd); //注册套接字文件描述符
		int max_socket1;
		if(multi_socket_recv_command > multi_socket_send_command)
		{
			max_socket1 = multi_socket_recv_command;
		}
		else
		{
			max_socket1 = multi_socket_send_command;
		}
		int max_socket2;
		if(max_socket1 > unicast_socket_pager)
		{
			max_socket2 = max_socket1;
		}
		else
		{
			max_socket2 = unicast_socket_pager;
		}

		int max_socket3;
		if(max_socket2 > unicast_socket_host)
		{
			max_socket3 = max_socket2;
		}
		else
		{
			max_socket3 = unicast_socket_host;
		}

		int max_socket;
		if(max_socket3 > netTools_socket_host)
		{
			max_socket = max_socket3;
		}
		else
		{
			max_socket = netTools_socket_host;
		}

		fdmax=max_socket;

		//if(Paging_status != PAGING_START) printf("\nWait Socket............\n");
		ret = select(fdmax+1, &readfd, NULL, NULL, NULL);
		switch(ret)
		{
			case -1 : //调用出错
				perror("select error!!!\n");
				break;

			case 0 : //超时
				if(Paging_status != PAGING_START) printf("timeout!\n");
				break;

			default : //有数据可读
				//if(Paging_status != PAGING_START) printf("\nSocket recv Data:\n");
				if (FD_ISSET(multi_socket_recv_command, &readfd))	//组播数据 终端发过来的
				{
					/*读取数据*/
	                memset(RxBuf, 0, CLIENT_BUF_SIZE);
	                Pkg_Length = recvfrom(multi_socket_recv_command,
							&RxBuf[Index],
							CLIENT_BUF_SIZE,
							0,
							(struct sockaddr *)&socketAddr,
							&socklen);
	                if(	strcmp( (char *)inet_ntoa(socketAddr.sin_addr),"127.0.0.1") ==0 || strcmp( (char *)inet_ntoa(socketAddr.sin_addr),ipAddress) == 0 )
					{
						//本机自己的组播命令不处理
						continue;
					}
					if (Pkg_Length < 0)
					{
						#if 0
						close(multi_socket_recv_command);
						close(multi_socket_send_command);
						close(unicast_socket_pager);
						close(unicast_socket_host);
						perror("Multicast terminal Socket recvfrom error!!!\n");
						pthread_exit(NULL);
						#endif
						continue;
					}
					else
					{
							#if _DEBUG_CLIENT_
							if(Paging_status != PAGING_START) printf("Multicast terminal:The Package Length is :%d\n", Pkg_Length);
							if(Paging_status != PAGING_START) printf("Multicast terminal:recv server IP=%s, Port=%d\n",inet_ntoa(socketAddr.sin_addr),
													htons(socketAddr.sin_port));

							if(Paging_status != PAGING_START) printf("Multicast terminal:Socket recv data:");
							for (i = 0; i < Pkg_Length; i++) //打印数据
							{
								if(Paging_status != PAGING_START) printf("0x%02x ", RxBuf[i]);
							}
							if(Paging_status != PAGING_START) printf("\n");
							#endif

							if (Pkg_Length < 9)
							{
								if(Paging_status != PAGING_START) printf("\nMulticast terminal:Pkg_length<9...\n");
							}
							else
							{
								if(g_network_mode==NETWORK_MODE_WAN)	//如果为WAN模式，不接收设备组播上线信息
								{
									break;
								}

								/*p2p数据  - 接收处理*/
								if( Calculate_XorDat(&RxBuf[8],Pkg_Length-9) == RxBuf[Pkg_Length-1] )
								{
									Process_Multi_Terminal_Pkg(RxBuf, Pkg_Length,socketAddr);
								}
								else
								{
									if(Paging_status != PAGING_START) printf("\nMulticast terminal:verify error\n");
								}
							}
					}
				}
				else if (FD_ISSET(multi_socket_send_command, &readfd))			//组播数据 主机发过来的
				{
					/*读取数据*/
					memset(RxBuf, 0, CLIENT_BUF_SIZE);
					Pkg_Length = recvfrom(multi_socket_send_command,
							&RxBuf[Index],
							CLIENT_BUF_SIZE,
							0,
							(struct sockaddr *)&socketAddr,
							&socklen);

					if(	strcmp( (char *)inet_ntoa(socketAddr.sin_addr),"127.0.0.1") ==0 || strcmp( (char *)inet_ntoa(socketAddr.sin_addr),ipAddress) == 0 )
					{
						//本机自己的组播命令不处理
						continue;
					}

					if (Pkg_Length < 0)
					{
						perror("Multicast HOST Socket recvfrom error!!!\n");
						usleep(100000);
						pthread_exit(NULL);
					}
					else
					{
							#if _DEBUG_CLIENT_
							if(Paging_status != PAGING_START) printf("Multicast HOST:The Package Length is :%d\n", Pkg_Length);
							if(Paging_status != PAGING_START) printf("Multicast HOST:recv server IP=%s, Port=%d\n",inet_ntoa(socketAddr.sin_addr),
													htons(socketAddr.sin_port));

							if(Paging_status != PAGING_START) printf("Multicast HOST:Socket recv data:");
							for (i = 0; i < Pkg_Length; i++) //打印数据
							{
								if(Paging_status != PAGING_START) printf("0x%02x ", RxBuf[i]);
							}
							if(Paging_status != PAGING_START) printf("\n");
							#endif

							if (Pkg_Length < 9)
							{
								if(Paging_status != PAGING_START) printf("\nMulticast HOST:Pkg_length<9...\n");
							}
							else
							{

								/*p2p数据  - 接收处理*/
								if( Calculate_XorDat(&RxBuf[8],Pkg_Length-9) == RxBuf[Pkg_Length-1] )
								{
									//当前网络模式是UDP或者设备类型为网络配置工具
									int device_model = RxBuf[4];
									if( g_network_mode == NETWORK_MODE_LAN || device_model == DEVICE_MODEL_NETWORK_TOOLS || device_model == DEVICE_MODEL_HOST)
									{
										Process_Multi_Host_Pkg(RxBuf, Pkg_Length,socketAddr);
									}
								}
								else
								{
									if(Paging_status != PAGING_START) printf("\nMulticast HOST:verify error\n");
								}
							}
					}
				}
				else	// 点对点数据可读
				{
					/*读取数据*/
					memset(RxBuf, 0, CLIENT_BUF_SIZE);
					if(FD_ISSET(unicast_socket_pager, &readfd))
					{
						Pkg_Length = recvfrom(unicast_socket_pager,
								&RxBuf[Index],
								CLIENT_BUF_SIZE,
								0,
								(struct sockaddr *)&socketAddr,
								&socklen);
					}
					else if(FD_ISSET(unicast_socket_host, &readfd))
					{
						Pkg_Length = recvfrom(unicast_socket_host,
								&RxBuf[Index],
								CLIENT_BUF_SIZE,
								0,
								(struct sockaddr *)&socketAddr,
								&socklen);
					}
					else if(FD_ISSET(netTools_socket_host, &readfd))
					{
						Pkg_Length = recvfrom(netTools_socket_host,
								&RxBuf[Index],
								CLIENT_BUF_SIZE,
								0,
								(struct sockaddr *)&socketAddr,
								&socklen);
					}
					if (Pkg_Length < 0)
					{
						#if 0
						close(multi_socket_recv_command);
						close(multi_socket_send_command);
						close(unicast_socket_pager);
						close(unicast_socket_host);
						perror("Unicast Socket recvfrom error!!!\n");
						pthread_exit(NULL);
						#endif
						continue;
					}
					#if _DEBUG_CLIENT_
                    if(Paging_status != PAGING_START) printf("Unicast:The Package Length is :%d\n", Pkg_Length);
                    if(Paging_status != PAGING_START) printf("Unicast: recv server IP=%s, Port=%d\n",inet_ntoa(socketAddr.sin_addr),
                    						htons(socketAddr.sin_port));

					if(Paging_status != PAGING_START) printf("Unicast Socket recv data:");
					for (i = 0; i < Pkg_Length; i++) //打印数据
					{
						if(Paging_status != PAGING_START) printf("0x%02x ", RxBuf[i]);
					}
					if(Paging_status != PAGING_START) printf("\n");

					#endif
					if (Pkg_Length < 9)
					{
						if(Paging_status != PAGING_START) printf("\nUnicast:Pkg_length<9...\n");
					}
					else
					{
						/*p2p数据  - 接收处理*/
						if( Calculate_XorDat(&RxBuf[8],Pkg_Length-9) == RxBuf[Pkg_Length-1] )
						{
							//当前网络模式是UDP或者设备类型为网络配置工具
							int device_model = RxBuf[4];
							if( g_network_mode == NETWORK_MODE_LAN || device_model == DEVICE_MODEL_NETWORK_TOOLS || device_model == DEVICE_MODEL_HOST)
								Process_Unicast_Pkg(RxBuf, Pkg_Length,socketAddr);
						}
						else
						{
							if(Paging_status != PAGING_START) printf("\nUnicast:verify error\n");
						}
					}
				}
				break;
			}
	}
	Socket_close();
	pthread_exit(NULL);
}












/*********************************************************************
 * @fn      UDP_SendData
 *
 * @brief   UDP数据包发送
 *
 * @param   int socketType,char *SendBuf,int Tx_Len,int ip(单播时有效)
 *
 * @return  none
 */
int UDP_SendData(int socketType,char *SendBuf,int Tx_Len,long int unicastIp)
{
	int sendlen=0;
	struct sockaddr_in UnicastsocketAddr;
	int cmd_word=(SendBuf[0]<<8)+SendBuf[1];
	int must_send_udp_host=0;

	switch(socketType)
	{
		case Send_MULTI_SOCKET_SEND_PCM:
			if(g_network_mode == NETWORK_MODE_LAN || cmd_word == CMD_SEND_PCM_DATA)
				sendlen=sendto(multi_socket_send_pcm, SendBuf, Tx_Len, 0,(struct sockaddr*)&multi_socket_send_pcm_peerAddr, sizeof(multi_socket_send_pcm_peerAddr));
			break;
		case Send_MULTI_SOCKET_SEND_COMMAND:
			if(g_network_mode == NETWORK_MODE_LAN || cmd_word == CMD_SEND_MULTICAST_CMD)
				sendlen=sendto(multi_socket_send_command, SendBuf, Tx_Len, 0,(struct sockaddr*)&multi_socket_send_command_peerAddr, sizeof(multi_socket_send_command_peerAddr));
			break;
		case Send_MULTI_SOCKET_SEND_ONLINE:		//组播上线通知到主机
			#if USE_SSD212
			SendBuf[4]=DEVICE_MODEL_PAGING_B;	//重要：SSD212发送给服务器的设备型号为寻呼台B
			#elif USE_SSD202
			SendBuf[4]=DEVICE_MODEL_PAGING_C;	//重要：SSD202发送给服务器的设备型号为寻呼台B
			#endif
			sendlen = sendto(multi_socket_recv_command, SendBuf, Tx_Len, 0,(struct sockaddr*)&multi_socket_recv_command_peerAddr, sizeof(multi_socket_recv_command_peerAddr));
			
			struct sockaddr_in multi_send_netTools_sockAddr;
			memset (&multi_send_netTools_sockAddr, 0, sizeof(multi_send_netTools_sockAddr));
			multi_send_netTools_sockAddr.sin_family = AF_INET;
			multi_send_netTools_sockAddr.sin_port = htons (MULTI_SOCKET_NETTOOLS_SEND_COMMAND_PORT);
			inet_pton(AF_INET, MULTI_SOCKET_NETTOOLS_SEND_COMMAND_ADDR, &multi_send_netTools_sockAddr.sin_addr);

			sendlen = sendto(multi_socket_recv_command, SendBuf, Tx_Len, 0,(struct sockaddr*)&multi_send_netTools_sockAddr, sizeof(multi_send_netTools_sockAddr));
			break;
		case Send_UNICAST:
			if(unicastIp == g_host_ip)
			{
				if( !(cmd_word!=CMD_HOST_QUERY_SET_IP_INFO && cmd_word!=CMD_HOST_QUERY_SET_WORK_MODE && cmd_word!=CMD_FIRWARE_UPDATE &&
					cmd_word!=CMD_CONTROL_REBOOT && cmd_word!=CMD_CONTROL_FORMAT) )
				{
					must_send_udp_host=1;
				}
			}
			if(	g_network_mode == NETWORK_MODE_LAN || must_send_udp_host)
			{
				UnicastsocketAddr.sin_family = AF_INET; 		//选用TCP/IP协议
				//判断是发往主机还是终端，如果是主机则此处为48888,否则46666
				if(	unicastIp == g_host_ip)
				{
					UnicastsocketAddr.sin_port = htons(UNICAST_HOST_SOCKET_PORT);//注册端口
					#if USE_SSD212
					SendBuf[4]=DEVICE_MODEL_PAGING_B;	//重要：SSD212发送给服务器的设备型号为寻呼台B
					#elif USE_SSD202
					SendBuf[4]=DEVICE_MODEL_PAGING_C;	//重要：SSD202发送给服务器的设备型号为寻呼台B
					#endif
				}
				else if(unicastIp == g_networktools_ip)
				{
					#if USE_SSD212
					SendBuf[4]=DEVICE_MODEL_PAGING_B;	//重要：SSD212发送给服务器的设备型号为寻呼台B
					#elif USE_SSD202
					SendBuf[4]=DEVICE_MODEL_PAGING_C;	//重要：SSD202发送给服务器的设备型号为寻呼台B
					#endif
				}
				else
					UnicastsocketAddr.sin_port = htons(UNICAST_TERMINAL_SOCKET_PORT);//注册端口
				inet_pton(AF_INET, IPIntToChar(unicastIp), &UnicastsocketAddr.sin_addr);		//将char*型IP转换为网络地址
				if(	unicastIp != g_host_ip && unicastIp!= g_networktools_ip && cmd_word!=CMD_SEND_PAGING_NOTIFY )	//不是发往主机且命令字不是寻呼通知时才开启重发校验
					Add_NetPkgEcc(SendBuf,Tx_Len,cmd_word,unicastIp,UNICAST_TERMINAL_SOCKET_PORT,0);

				sendlen=sendto(unicast_socket_pager, SendBuf, Tx_Len, 0,(struct sockaddr*)&UnicastsocketAddr, sizeof(UnicastsocketAddr));
			}
			if(	g_network_mode == NETWORK_MODE_WAN)
			{
				#if USE_SSD212
				SendBuf[4]=DEVICE_MODEL_PAGING_B;	//重要：SSD212发送给服务器的设备型号为寻呼台B
				#elif USE_SSD202
				SendBuf[4]=DEVICE_MODEL_PAGING_C;	//重要：SSD202发送给服务器的设备型号为寻呼台B
				#endif
				host_tcp_send_data(SendBuf, Tx_Len);

#if 0
				UnicastsocketAddr.sin_family = AF_INET; 		//选用TCP/IP协议
				UnicastsocketAddr.sin_port = htons(49988);//注册端口
				inet_pton(AF_INET, "************", &UnicastsocketAddr.sin_addr);		//将char*型IP转换为网络地址
				memset(SendBuf,0,4);
				sendlen=sendto(unicast_socket_pager, SendBuf, Tx_Len, 0,(struct sockaddr*)&UnicastsocketAddr, sizeof(UnicastsocketAddr));
				printf("send hearbeat:%d...\n",Tx_Len);
#endif
			}
			

			//判断网络配置工具是否下发过命令(单播),如果是,则同时应答给它
			if(g_networktools_ip)
			{
				#if USE_SSD212
				SendBuf[4]=DEVICE_MODEL_PAGING_B;	//重要：SSD212发送给服务器的设备型号为寻呼台B
				#elif USE_SSD202
				SendBuf[4]=DEVICE_MODEL_PAGING_C;	//重要：SSD202发送给服务器的设备型号为寻呼台B
				#endif
				
				printf("udp unicast send to g_networktool:%s,Tx_Len=%d\n",IPIntToChar(g_networktools_ip),Tx_Len);
				UnicastsocketAddr.sin_family = AF_INET; 		//选用TCP/IP协议		//一定要加这句，否则发不出去
				UnicastsocketAddr.sin_port = htons(UNICAST_HOST_SOCKET_PORT);						//注册端口
				inet_pton(AF_INET, IPIntToChar(g_networktools_ip), &UnicastsocketAddr.sin_addr);		//将char*型IP转换为网络地址
				g_networktools_ip=0;				//发送完一次后不再发送，除非收到网络配置工具的UDP命令
				sendto(unicast_socket_pager, SendBuf, Tx_Len, 0,(struct sockaddr*)&UnicastsocketAddr, sizeof(UnicastsocketAddr));
			
				UnicastsocketAddr.sin_family = AF_INET; 		//选用TCP/IP协议		//一定要加这句，否则发不出去
				UnicastsocketAddr.sin_port = htons(UNICAST_NETTOOLS_SOCKET_PORT);						//注册端口
				sendto(netTools_socket_host, SendBuf, Tx_Len, 0,(struct sockaddr*)&UnicastsocketAddr, sizeof(UnicastsocketAddr));
			}

			break;
		case Send_UDP_RESET:
			if(g_network_mode == NETWORK_MODE_LAN)
			{
				UnicastsocketAddr.sin_family = AF_INET; 		//选用TCP/IP协议
				//判断是发往主机还是终端，如果是主机则此处为48888,否则46666
				UnicastsocketAddr.sin_port = htons(UNICAST_UDP_RESET_PORT);//注册端口
				inet_pton(AF_INET, IPIntToChar(unicastIp), &UnicastsocketAddr.sin_addr);		//将char*型IP转换为网络地址
				sendlen=sendto(unicast_socket_pager, SendBuf, Tx_Len, 0,(struct sockaddr*)&UnicastsocketAddr, sizeof(UnicastsocketAddr));
			}
			break;
	}


	if(sendlen!=-1)
	{
		//if(Paging_status != PAGING_START) printf("\nUDP_SendData:socketType=%d,TXlen=%d,CMD_WORD=0x%04x\n",socketType,Tx_Len,cmd_word);
	}
	else
	{
		//if(Paging_status != PAGING_START) printf("\nUDP_SendData ERROR\n");
		return ERROR;
	}
	return SUCCEED;
}



/*********************************************************************
 * @fn      Socket_close
 *
 * @brief   关闭所有socket
 *
 * @param   none
 *
 * @return  none
 */
void Socket_close()
{
	close(multi_socket_recv_command);
	close(unicast_socket_pager);
	close(multi_socket_send_pcm);
	close(multi_socket_send_command);
}


/*********************************************************************
 * @fn      Process_Multi_Host_Pkg
 *
 * @brief   组播接收处理 只是主机的包
 *
 * @param   Pkg - 指向接收缓存
 *			Pkg_Length - 接收数据长度
 *
 * @return  none
 */
static void Process_Multi_Host_Pkg(unsigned char *Pkg, int Pkg_Length,struct sockaddr_in socketAddr )
{
	int i;
	int Send_Len=0, Txlen;				//局部变量使用前一定要初始化，否则Send_Len的值不定
	unsigned char Respond_Data[CLIENT_BUF_SIZE];
	/*清空缓存*/
	memset(Respond_Data, 0, CLIENT_BUF_SIZE);
	unsigned int command= (Pkg[0]<<8)+Pkg[1];

	char ipaddress[16]={0};
	strcpy(ipaddress,inet_ntoa(socketAddr.sin_addr));
	int port=htons(socketAddr.sin_port);

	int device_model = Pkg[4];
	if(device_model == DEVICE_MODEL_HOST)
	{
		if(g_network_mode == NETWORK_MODE_WAN)
		{
			if( command!=CMD_HOST_QUERY_SET_IP_INFO && command!=CMD_NETWORK_MODE_MULTICAST && command!=CMD_FIRWARE_UPDATE &&
				 command!=CMD_CONTROL_REBOOT && command!=CMD_CONTROL_FORMAT)
			{
				return;
			}
		}
	}

	if(Paging_status != PAGING_START) printf("\nProcess_Multi_Host_Pkg,ip=%s,command=0x%x\n",ipaddress,command);
#if 0	//测试用，只接收相关主机
	if(strcmp(ipaddress,"**************")!=0 && strcmp(ipaddress,"***************")!=0)
	{
		printf("Process_Multi_Host_Pkg,HOST IP!=*************** && HOST IP!=***************,return!\n");
		return;
	}
#endif
	//获得设备型号
	if( device_model!= DEVICE_MODEL_HOST && device_model != DEVICE_MODEL_NETWORK_TOOLS )
	{
		if(Paging_status != PAGING_START) printf("\n ERROR:device_model=0x%x\n",device_model);
		return;
	}

	//保存主机IP
	if(device_model == DEVICE_MODEL_HOST)
	{
		g_host_ip=IPcharToInt(ipaddress);
		g_host_device_TimeOut=0;
	}


	switch ( command )
	{
		case CMD_SEND_SEARCH_ZONE_INFO:			//主机或寻呼台主动发送搜索设备 注：当寻呼台收到此消息时需应答  此处只需要应答给主机即可。另外一上线或者某个终端掉线次数达到一定计数后需发送此命令
			send_online_info();
			break;
		case CMD_TIME_SYNC:						//由主机定时发送	//需应答给主机
			pkg_set_local_time(Pkg);
			break;
		case CMD_VERSION_QUERY:					//单播/组播	响应主机的查询版本信息命令
			pkg_query_firmware_version();
			break;
		case CMD_FIRWARE_UPDATE:				//单播/组播	得到升级文件信息，并响应主机本机升级进度，此处因为不能立即应答，所以需要记录主机的地址
			pkg_get_update_info(Pkg);
			break;
		case CMD_QUERY_FILE:					//主机向终端查询存储在本地的文件信息
			response_send_host_xml_file_info(Pkg,0);
			break;
    	case CMD_QUERY_TIME_INFO:				//主机向设备请求查询设备日期时间
    		HOST_QUERY_TIME_INFO(Pkg);
    		break;
    	case CMD_HOST_QUERY_SET_IP_INFO:		//主机向终端设置IP属性
    		HOST_QUERY_SET_IP_INFO(Pkg);
    		break;
		case CMD_HOST_SET_DSP_FIRMWARE_FEATURE:	//主机或配置工具向终端查询/设置DSP固件功能特性（组播）
			#if defined(USE_SSD212) || defined(USE_SSD202)
			Host_Set_Dsp_Firmware_Feature(Pkg);
			#endif
			break;
		case CMD_HOST_SET_SERIAL_NUMBER:		//主机或配置工具向终端获取设备序列号（组播)
			Host_Set_SerialNumber(Pkg);
			break;

		case CMD_NETWORK_MODE_MULTICAST:
			ProcessHostSetNetWork(Pkg,Pkg_Length,true);

		default :
			break;
	}
}






/*********************************************************************
 * @fn      Process_Multi_Pkg
 *
 * @brief   组播接收处理 只是终端的包
 *
 * @param   Pkg - 指向接收缓存
 *			Pkg_Length - 接收数据长度
 *
 * @return  none
 */
static void Process_Multi_Terminal_Pkg(unsigned char *Pkg, int Pkg_Length,struct sockaddr_in socketAddr )
{
	int i;
	int Send_Len=0, Txlen;				//局部变量使用前一定要初始化，否则Send_Len的值不定
	unsigned char Respond_Data[CLIENT_BUF_SIZE];
	/*清空缓存*/
	memset(Respond_Data, 0, CLIENT_BUF_SIZE);
	unsigned int command= (Pkg[0]<<8)+Pkg[1];
	//if(Paging_status != PAGING_START) printf("Process_Multi_Terminal_Pkg,command=0x%x\n",command);

	char ipaddress[16]={0};
	strcpy(ipaddress,(char *)inet_ntoa(socketAddr.sin_addr));
	int port=htons(socketAddr.sin_port);

	//获得设备型号
	int device_model = Pkg[4];
	if( device_model!= DEVICE_MODEL_ZONE_A  && device_model!= DEVICE_MODEL_ZONE_B && device_model!=DEVICE_MODEL_ZONE_C && device_model!=DEVICE_MODEL_ZONE_D \
		&& device_model!=DEVICE_MODEL_ZONE_E && device_model!=DEVICE_MODEL_ZONE_F)
	{
		//if(Paging_status != PAGING_START) printf("\n ERROR:Process_Multi_Terminal_Pkg:device_model=0x%x\n",device_model);
		return;
	}
	switch ( command )
	{
		case CMD_RECV_ZONE_ONLINE_INFO:			//终端在线应答或终端间隔一定时间主动发送在线信息 只接收此上线信息
#if 0		//集中模式也监听分区状态变化(但不新增分区),避免网络异常时小几率导致的分区状态没有同步更新的问题
			if(g_network_mode == NETWORK_MODE_WAN || g_system_work_mode == WORK_MODE_CONCENTRATED)	//集中模式下分区状态变化由主机发起(0x0042)
				break;
#endif

			if( g_system_work_mode == WORK_MODE_CONCENTRATED && IsValidWin(WIN_CONTROL) && strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) )		//非管理员且已连接服务器的情况下，不处理
			{
				break;
			}
			pthread_mutex_lock(&ZoneInfoMutex);
			Send_Len = HANDLE_CMD_RECV_ZONE_ONLINE_INFO(ipaddress,Pkg);
			pthread_mutex_unlock(&ZoneInfoMutex);
			break;
		case CMD_GET_ZONE_DETAIL_INFO:			//查询终端信息(音量、节目源、节目名称)	此处需要时发送给终端查询信息，然后终端应答
			if(g_system_work_mode == WORK_MODE_CONCENTRATED)	//集中模式下分区状态变化由主机发起(0x0042)
				break;
			pthread_mutex_lock(&ZoneInfoMutex);
			pkg_get_zone_detail_info(Pkg,IPcharToInt(ipaddress));
			pthread_mutex_unlock(&ZoneInfoMutex);
			break;
		default :
			break;
	}
}




/*********************************************************************
 * @fn      Process_Unicast_Pkg
 *
 * @brief   单播接收处理
 *
 * @param   Pkg - 指向接收缓存
 *			Pkg_Length - 接收数据长度
 *
 * @return  none
 */
static void Process_Unicast_Pkg(unsigned char *Pkg, int Pkg_Length,struct sockaddr_in socketAddr)
{
	int i;
	int Send_Len=0, Txlen;
	unsigned char Respond_Data[CLIENT_BUF_SIZE];
	/*清空缓存*/
	memset(Respond_Data, 0, CLIENT_BUF_SIZE);
	int command= (Pkg[0]<<8)+Pkg[1];
	//if(Paging_status != PAGING_START) printf("\nProcess_Unicast_Pkg,command=0x%x\n",command);

	char ipaddress[16]={0};
	strcpy(ipaddress,inet_ntoa(socketAddr.sin_addr));
	int port=htons(socketAddr.sin_port);
	int device_model = Pkg[4];
	if( device_model!= DEVICE_MODEL_HOST && device_model!= DEVICE_MODEL_NETWORK_TOOLS && device_model!= DEVICE_MODEL_ZONE_A && device_model!= DEVICE_MODEL_ZONE_B && device_model!=DEVICE_MODEL_ZONE_C\
		&& device_model!=DEVICE_MODEL_ZONE_D && device_model!=DEVICE_MODEL_ZONE_E && device_model!=DEVICE_MODEL_ZONE_F )
	{
		//if(Paging_status != PAGING_START) printf("\n ERROR:device_model=0x%x\n",device_model);
		return;
	}

	Del_NetPkgEcc(command,IPcharToInt(ipaddress),port);

	int null_response=0;
	if(Pkg_Length == PAYLOAD_START+1)
	{
		null_response=1;
	}
	if( null_response && Paging_status == PAGING_START && ( device_model == DEVICE_MODEL_ZONE_A || device_model== DEVICE_MODEL_ZONE_B || device_model== DEVICE_MODEL_ZONE_C\
		|| device_model== DEVICE_MODEL_ZONE_D || device_model== DEVICE_MODEL_ZONE_E || device_model== DEVICE_MODEL_ZONE_F) )
	{
		if(command == CMD_SEND_PAGING_NOTIFY || command == CMD_SEND_PAGING_NOTIFY_MULTICAST)	//如果是寻呼通知，则记录寻呼开始时间
		{

		}
		return;
	}
	//保存主机IP
	if(device_model == DEVICE_MODEL_HOST)
	{
		g_host_ip=IPcharToInt(ipaddress);
		g_host_device_TimeOut=0;
	}
	if(device_model == DEVICE_MODEL_NETWORK_TOOLS)
	{
		g_networktools_ip=IPcharToInt(ipaddress);
	}
	if(device_model == DEVICE_MODEL_HOST)
	{
		if(g_network_mode == NETWORK_MODE_WAN)
		{
			if( command!=CMD_HOST_QUERY_SET_IP_INFO && command!=CMD_HOST_QUERY_SET_WORK_MODE && command!=CMD_FIRWARE_UPDATE &&
				 command!=CMD_CONTROL_REBOOT && command!=CMD_CONTROL_FORMAT)
			{
				printf("WAN:command=0x%x,return\n",command);
				return;
			}
			else
			{
				printf("WAN:command=0x%x\n",command);
			}
		}
		//printf("control:HOST response command=0x%04x\n",command);
		if( g_system_work_mode == WORK_MODE_CONCENTRATED )
		{
			clear_concentrated_pkg_pos(command);
		}
		if(command == CMD_SET_ZONE_VOL || command == CMD_PAGING_SEND_HOST_PLAY_SOURCE || command == CMD_SET_ZONE_IDLE_STATUS)
			return;
	}

	#if ENABLE_CALL_FUNCTION
	if(command == CMD_ALL_PAGER_STATUS || command == CMD_CALLING_INVITATION || command == CMD_CALLED_RESPONSE || command == CMD_CALLED_STATUS || command == CMD_CALLING_AUDIOSTREAM)
	{
		//如果不支持对讲,不接收这些指令
		if(!g_isSupportCall)
		{
			return;
		}
	}
	#endif


	switch ( command )
	{
		case CMD_VERSION_QUERY:					//单播/组播	响应主机的查询版本信息命令
			pkg_query_firmware_version();
			break;
		case CMD_FIRWARE_UPDATE:				//单播/组播	得到升级文件信息，并响应主机本机升级进度，此处因为不能立即应答，所以需要记录主机的地址
			pkg_get_update_info(Pkg);
			break;
		case CMD_SET_ZONE_VOL:					//有可能是空应答，所以需要为查询的时候才进入
			//音量
			#if 0								//接收组播变化即可
		{
			int vol=Pkg[PAYLOAD_START+1];
			//printf("vol:%d\e\n", vol);
			int index=Get_ZoneIndex_By_Ip(IPcharToInt(ipaddress));
			if(index == -1)
				break;
			if(vol!=m_stZone_Info.zoneInfo[index].g_zone_vol)
			{
				vol=m_stZone_Info.zoneInfo[index].g_zone_vol;

				//Zone_Info_Update_Thread();
				set_zoneStatus_refreshTime();
			}
		}
			#endif
			break;
		case CMD_GET_HOST_FILE_SYNC_INFO:		//主机向终端请求更新文件
			Get_UpdateXML_Info_From_Host(Pkg);
			break;
		case CMD_QUERY_FILE:					//主机向终端查询存储在本地的文件信息
			response_send_host_xml_file_info(Pkg,0);
			break;
		case CMD_SET_ZONE_IDLE_STATUS:			//主机设置终端空闲模式
			response_cmd_set_zone_idle_status(Pkg);
			break;

    	case CMD_CONTROL_REBOOT:		//主机向终端发送重启指令
    		host_control_reboot(Pkg);
    		break;
    	case CMD_CONTROL_FORMAT:		//主机向设备请求清除数据
    		host_control_format(Pkg);
    		break;

    	case CMD_SET_ALIAS :        	// 设置设备别名
    		pkg_set_device_alias(Pkg);
    		break;

    	case CMD_QUERY_FLASH_INFO:		//主机向设备请求查询FLASH信息
    		HOST_QUERY_FLASH_INFO(Pkg);
    		break;

    	case CMD_QUERY_MAC_INFO:		//主机向设备请求查询/设置MAC地址
    		HOST_QUERY_SET_MAC_INFO(Pkg);
    		break;

    	case CMD_QUERY_TIME_INFO:		//主机向设备请求查询设备日期时间
    		HOST_QUERY_TIME_INFO(Pkg);
    		break;

    	case CMD_HOST_QUERY_SET_WORK_MODE:	//主机向终端设置网络模式
    		ProcessHostSetNetWork(Pkg,Pkg_Length,false);
    		break;

    	case CMD_HOST_QUERY_RECORD_LIST:	//主机获取设备记录文件列表
    		TcpSendRecordListToHost(Pkg);
    		break;

    	case CMD_SEND_RECORD_FILE_CONTENT:	//主机获取设备记录文件
    		TcpSendRecordFileContent(Pkg);
    		break;

    	case CMD_HOST_SEND_AUDIO_COLLECTOR_DEVICE_LIST:	//主机向其他控制设备下发音频采集器设备列表
    		HOST_SEND_AUDIO_COLLECTOR_DEVICE_LIST(Pkg);
    		break;

    	case CMD_QUERY_SET_WORK_MODE:	//主机向设备查询/设置工作模式
    		HOST_QUERY_SET_WORK_MODE(Pkg);
    		break;

     	case CMD_PAGING_SEND_HOST_SELECTED_ZONE:	//寻呼台/移动设备/分控设备向主机发送选中的分区（集中模式）
     		Host_Response_ZoneList_Concentrated(Pkg);
     		break;

    	case CMD_HOST_QUERY_SET_IP_INFO:			//主机向终端设置IP属性
    		HOST_QUERY_SET_IP_INFO(Pkg);
    		break;

    		//集中模式
    	case CMD_PAGING_SEND_HOST_PLAY_SOURCE:		//寻呼机/移动设备/分控软件请求主机播放节目源（集中模式）
    		Host_Response_Play_Music_Concentrated(Pkg);
    		break;

    	case CMD_SET_PLAYMODE:						//主机设置终端播放模式
    		//需要判断是主机应答还是寻呼台主动向主机设置
    		if(device_model == DEVICE_MODEL_HOST)
    		{
    			Host_Response_SET_Play_Mode_Concentrated(Pkg);
    		}
    		break;
    	case CMD_SEND_PAGING_NOTIFY_TCP://通知终端开始寻呼(TCP模式)
    		break;

		case CMD_TCP_HOST_SEND_ZONE_DETAIL:	//集中模式下接收主机下发的分区状态命令
			pthread_mutex_lock(&ZoneInfoMutex);
			TCP_HANDLE_CMD_RECV_ZONE_DETAIL_INFO(Pkg);
			pthread_mutex_unlock(&ZoneInfoMutex);
			break;

#if ENABLE_CALL_FUNCTION
		case CMD_ALL_PAGER_STATUS:
			pthread_mutex_lock(&PagerInfoMutex);
			GetAllPagerDeviceInfo(Pkg);
			pthread_mutex_unlock(&PagerInfoMutex);
		break;
		case CMD_CALLING_INVITATION:
			Recv_calling_invation(Pkg);
		break;
		case CMD_CALLED_RESPONSE:
			Recv_called_response(Pkg);
		break;
		case CMD_CALLED_STATUS:
			Recv_callStatus_feedback(Pkg);
		break;
		case CMD_CALLING_AUDIOSTREAM:
			Recv_Call_Audio_Stream(Pkg);
		break;

	#if USE_SSD202
		/*视频对讲*/
		case CMD_CALL_REQUEST_VIDEO:
			Recv_request_video_call(Pkg);
		break;
		case CMD_CALL_VIDEO_PARM:
			Recv_video_call_codecs_parm(Pkg);
		break;
		case CMD_CALL_VIDEO_STATUS:
			Recv_video_call_status(Pkg);
		break;
		case CMD_CALL_VIDEO_STREAM:
			Recv_Call_Video_Stream(Pkg);
		break;
	#endif
#endif

		case CMD_TIME_SYNC:						//主动请求后的应答
			pkg_set_local_time(Pkg);
			break;
#if ENABLE_LISTEN_FUNCTION
		case CMD_LISTEN_RESPONSE:
			Recv_listen_event(Pkg);
			break;
		case CMD_LISTEN_STREAM_UPLOAD:
			Recv_Listen_Audio_Stream(Pkg);
			break;
#endif

#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
		case CMD_SEND_HOST_GET_ACCOUNT_STORAGE_CAPACITY:
			recv_get_account_storageCapacity(Pkg);
			break;
		case CMD_SEND_HOST_REQUEST_UPLOAD_SONG_FILE:
			recv_request_upload_song(Pkg);
			break;
		case CMD_SEND_HOST_NOTIFY_UPLOAD_STATUS:
			recv_upload_song_status(Pkg);
			break;
		case CMD_SEND_HOST_REQUEST_DELETE_SONG:
			recv_request_delete_song(Pkg);
			break;
#endif	
		case CMD_HOST_SET_BROADCAST_PAGING:
			host_set_broadcast_paging(Pkg);
			break;
	
		default :
			break;
	}
}








/*********************************************************************
 * @fn      Calculate_XorDat
 *
 * @brief   计算数据包异或校验和
 *
 * @param   Data - 校验数据
 *			Length - 校验数据长度
 *
 * @return  Xordata - 校验和
 */
unsigned char Calculate_XorDat(unsigned char *Data, int Length)
{
	int i;
	unsigned char Xordata = 0;

	for (i = 0; i < Length; i++)
	{
		Xordata ^= Data[i];
	}

	return Xordata;
}

/****************************************************
 * @fn      Start_Client_RX_Pthread
 *
 * @brief   启动客服端接收线程
 *
 * @param
 *
 * @return	成功 - SUCCEED
 * 			失败 - ERROR
 */
int Start_Client_RX_Pthread()
{
	int ret;
	pthread_t Udp_Pthread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Udp_Pthread, &Pthread_Attr, (void *)Udp_Client, NULL);
	if (ret == -1)
	{
		if(Paging_status != PAGING_START) printf("Pthread Creat Failure!\n");
		return ERROR;
	}

	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}







/*********************************************************************
 * @fn      response_cmd_set_zone_idle_status
 *
 * @brief  应答主机切换至空闲状态
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */
int response_cmd_set_zone_idle_status(char *rxbuf)
{
	
	return SUCCEED;
}








/*********************************************************************
 * @fn      pkg_set_local_time
 *
 * @brief   设置本地时间日期
 *
 * @param   void
 *
 * @return	void
 */
void pkg_set_local_time(unsigned char *rxbuf)
{
	int i;
	int payloadSize = 0;
	int data_len = 0;
	unsigned char time_buf[32];
	unsigned char date_buf[32];
	unsigned char cmd_buf[64];

	// 初始化缓存
	memset(date_buf, 0x00, sizeof(date_buf));
	memset(time_buf, 0x00, sizeof(time_buf));
	memset(cmd_buf, 0x00, sizeof(cmd_buf));

	// 接收日期数据
	if (rxbuf[PAYLOAD_START+payloadSize] < 10)
	{

	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize];
		payloadSize += 1;
		for (i=0; i<data_len; i++)
		{
			date_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
		}
		payloadSize += data_len;
		if(Paging_status != PAGING_START) printf("Date = %s\n", date_buf);
	}

	// 接收时间数据
	if (rxbuf[PAYLOAD_START+payloadSize] < 8)
	{

	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize];
		payloadSize += 1;
		for (i=0; i<data_len; i++)
		{
			time_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
		}
		if(Paging_status != PAGING_START) printf("Time = %s\n", time_buf);
	}

	// 开始设置系统时间
	pthread_mutex_lock(&TimeMutex);
	sprintf(cmd_buf, "date \"%s %s\"", date_buf, time_buf);
	int year=0,mon=0,day=0;
	int hour=0,min=0,sec=0;

	sscanf(date_buf,"%d-%d-%d",&year,&mon,&day);
	sscanf(time_buf,"%d:%d:%d",&hour,&min,&sec);
	int update_time_flag=1;
	printf("year=%d,mon=%d,day=%d,hour=%d,min=%d,sec=%d\n",year,mon,day,hour,min,sec);
	if( CURRENT_TIME.year == year && CURRENT_TIME.mon == mon && CURRENT_TIME.day == day )
	{
		int time_host=hour*3600+min*60+sec;
		int time_current = CURRENT_TIME.hour*3600+CURRENT_TIME.min*60+CURRENT_TIME.sec;
		if( abs(time_host-time_current) <5 )
		{
			update_time_flag=0;
		}
	}
	if(update_time_flag)
	{
		pox_system(cmd_buf);
	}
	else
	{
		printf("time not change\n");
	}
	//pox_system("hwclock -w");

	pthread_mutex_unlock(&TimeMutex);
	if(Paging_status != PAGING_START) printf("Date&time synchronization cmd = %s\n", cmd_buf);
}










/****************************************************
 * @fn      HANDLE_CMD_RECV_ZONE_ONLINE_INFO
 *
 * @brief   处理分区上线信息
 *
 * @param
 *
 * @return	int 应答包长度
 */
int HANDLE_CMD_RECV_ZONE_ONLINE_INFO(unsigned char *ip,unsigned char *Pkg)
{
	int i,k;
	int pos=8;
	unsigned char mac[6]={0};
	int zone_name_len=Pkg[pos++];
	int Firmware_version_length;

	for(i=0;i<6;i++)
	{
		mac[i]=Pkg[zone_name_len+pos+1+i];
	}

	//判断MAC是否存在
	int found=0;
	int index=-1;

	for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
	{
		for(k=0;k<6;k++)
		{
			if(	m_stZone_Info.zoneInfo[i].g_zone_mac[k]	!= mac[k] )
			{
				break;
			}
		}
		if(k == 6)
		{
			found=1;
			index=i;
			break;
		}
	}
#if _DEBUG_CLIENT_
	if(Paging_status != PAGING_START) printf("\nHANDLE_CMD_RECV_ZONE_ONLINE_INFO:MAC:%02x:%02x:%02x:%02x:%02x:%02x\n",mac[0],mac[1],mac[2],mac[3],mac[4],mac[5]);
#endif
	if(!found)		//分区不存在
	{
#if _DEBUG_CLIENT_
		if(Paging_status != PAGING_START) printf("\nzone isn't exsit,new zone online,ip=%s\n",ip);
#endif
		if(g_system_work_mode == WORK_MODE_CONCENTRATED)
		{
			return ERROR;
		}
		//2019.10.25,jms,增加最大分区数量判断，避免溢出
		if(m_stZone_Info.ExistTotalZone>=MAX_ZONE_NUM)
		{
			printf("m_stZone_Info.ExistTotalZone>=%d,return\n",MAX_ZONE_NUM);
			return ERROR;
		}
		index=(++m_stZone_Info.ExistTotalZone)-1;
	}
	else			//分区存在
	{
#if _DEBUG_CLIENT_
		if(Paging_status != PAGING_START) printf("\nzone exsit\n");
#endif
	}

	ZONEINFO_DETAIL zoneInfo;
	if(found)	//如果分区存在，需要保存当前结构体变量，以便于下面的比较
	{
		zoneInfo=m_stZone_Info.zoneInfo[index];

#if 0
		if(zoneInfo.g_zone_conection)
		{

			if(zoneInfo.g_zone_ip != IPcharToInt(ip))	//在线但是IP不同，判定MAC重复
			{
				host_set_terminal_mac(zoneInfo.g_zone_ip);
				host_set_terminal_mac(IPcharToInt(ip));
				return 0;
			}
		}
#endif

	}

	//分区ID
	m_stZone_Info.zoneInfo[index].g_zone_id=index;
	//分区别名
	memset(m_stZone_Info.zoneInfo[index].g_zone_name,0,sizeof(m_stZone_Info.zoneInfo[index].g_zone_name));
	for(i=0;i<zone_name_len && i<32;i++)
	{
		m_stZone_Info.zoneInfo[index].g_zone_name[i]=Pkg[i+pos];
	}
	pos+=zone_name_len;

	//分区ip
	//将分区IP转换为十六进制 4字节形式
	m_stZone_Info.zoneInfo[index].g_zone_ip=IPcharToInt(ip);
	//MAC
	for(i=0;i<6;i++)
	{
		m_stZone_Info.zoneInfo[index].g_zone_mac[i]=mac[i];
	}
	pos+=7;

	//音量
	m_stZone_Info.zoneInfo[index].g_zone_vol=Pkg[pos++];
	//音源
	m_stZone_Info.zoneInfo[index].g_zone_source=Pkg[pos++];

	//节目名长度
	memset(m_stZone_Info.zoneInfo[index].g_zone_media_name,0,sizeof(m_stZone_Info.zoneInfo[index].g_zone_media_name));
	int media_name_len=Pkg[pos++];
	for(i=0;i<media_name_len && i<64;i++)
	{
		m_stZone_Info.zoneInfo[index].g_zone_media_name[i]=Pkg[i+pos];
	}
	pos+=media_name_len;

	//播放状态
	#if 0
	m_stZone_Info.zoneInfo[index].g_zone_playStatus=Pkg[pos++];
	#else
	pos++;
	#endif

	//设置成在线状态
	m_stZone_Info.zoneInfo[index].g_zone_conection=1;
	//在线计数清0
	m_stZone_Info.zoneInfo[index].g_zone_offline_count=0;
	//设置设备类型
	m_stZone_Info.zoneInfo[index].g_zone_DeviceType=Pkg[4];

#if _DEBUG_CLIENT_
	if(Paging_status != PAGING_START) printf("index=%d,name=%s,ip=%s,vol=%d,source=0x%x\n",index,m_stZone_Info.zoneInfo[index].g_zone_name,IPIntToChar(m_stZone_Info.zoneInfo[index].g_zone_ip),
			m_stZone_Info.zoneInfo[index].g_zone_vol,m_stZone_Info.zoneInfo[index].g_zone_source);
#endif

	//如果非管理员，且没有这个分区的管理权限，那么不刷新
	if( IsValidWin(WIN_CONTROL) )
	{
		if(strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME)!=0)
		{
			int valid_zone_flag=0;
			for(k=0;k<m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneCount;k++)
			{
				if(	memcmp(m_stZone_Info.zoneInfo[index].g_zone_mac,&m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneMac[6*k],6 ) == 0 )
				{
					valid_zone_flag=1;
					break;
				}
			}
			if(!valid_zone_flag)
			{
				m_stZone_Info.zoneInfo[index].g_zone_isHide=1;
				return;
			}
		}
	}

	if(found)	//已存在的需要判断部分结构体变量是否改变
	{
#if 0
		//判断终端寻呼是否掉线
		if(Paging_status == PAGING_START)
		{
			//是否处于寻呼列表中
			for(i=0;i<PagingZoneInfo.ZoneNum;i++)
			{
				if( strcmp(PagingZoneInfo.ZoneInfo[i].ZoneMac,m_stZone_Info.zoneInfo[index].g_zone_mac) == 0 && m_stZone_Info.zoneInfo[index].g_zone_conection &&
						m_stZone_Info.zoneInfo[index].g_zone_source != SOURCE_NET_PAGING && m_stZone_Info.zoneInfo[index].g_zone_source != SOURCE_FIRE_ALARM)
				{
					//保存寻呼结束时间
					//sprintf( PagingZoneInfo.ZoneInfo[i].EndTime,"%4d-%02d-%02d %02d:%02d:%02d",CURRENT_TIME.year,CURRENT_TIME.mon,CURRENT_TIME.day,CURRENT_TIME.hour,CURRENT_TIME.min,CURRENT_TIME.sec);
					//发送寻呼掉线信息到主机
					//SendTo_Host_Zone_offline_Info(ip,PagingZoneInfo.ZoneInfo[i].StartTime,PagingZoneInfo.ZoneInfo[i].EndTime);
					//发送再次寻呼通知
					SendToZone_offline_Paging_Again(m_stZone_Info.zoneInfo[index].g_zone_mac);
					//LOG("SendToZone_offline_Paging_Again:id=%d,name=%s,ip=%s",m_stZone_Info.zoneInfo[index].g_zone_id,m_stZone_Info.zoneInfo[index].g_zone_name,IPIntToChar(m_stZone_Info.zoneInfo[index].g_zone_ip));
					
					break;
				}
			}
		}
#endif
		if( zoneInfo.g_zone_vol != m_stZone_Info.zoneInfo[index].g_zone_vol ||
				zoneInfo.g_zone_source != m_stZone_Info.zoneInfo[index].g_zone_source ||
				strcmp(zoneInfo.g_zone_name,m_stZone_Info.zoneInfo[index].g_zone_name)!=0 ||
				zoneInfo.g_zone_conection != m_stZone_Info.zoneInfo[index].g_zone_conection ||
				zoneInfo.g_zone_ip != m_stZone_Info.zoneInfo[index].g_zone_ip ||
				strcmp(zoneInfo.g_zone_media_name,m_stZone_Info.zoneInfo[index].g_zone_media_name)!=0 )
		{
			#if 0
			if(Paging_status != PAGING_START) printf("Tindex=%d,name=%s,ip=%s,vol=%d,source=0x%x\n",index,m_stZone_Info.zoneInfo[index].g_zone_name,IPIntToChar(m_stZone_Info.zoneInfo[index].g_zone_ip),
				m_stZone_Info.zoneInfo[index].g_zone_vol,m_stZone_Info.zoneInfo[index].g_zone_source);
			#endif
			set_zoneStatus_refreshTime(0);
		}
	}
	else		
	{
		set_zoneStatus_refreshTime(1);
	}


	return 0;
}

#if 0
int zone_refresh_count=0;
//此处启动刷新线程(1s后刷新，防止卡顿)
int P_Zone_Info_Update_Thread()
{
	while(++zone_refresh_count<8)
	{
		usleep(50000);
	}
	int lock_value=pthread_mutex_trylock(&ZoneInfoMutex);
	zoneInfo_update_thread_flag=0xff;
	printf("P_Zone_Info_Update_Thread...\n");
	control_win_update(0,0);
	Save_Zone_Info();
	if(!lock_value)
		pthread_mutex_unlock(&ZoneInfoMutex);

}


int Zone_Info_Update_Thread()
{
	zone_refresh_count=0;
	if(zoneInfo_update_thread_flag != 0xff)
	{
		return ERROR;
	}
	zoneInfo_update_thread_flag=0x1;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)P_Zone_Info_Update_Thread, NULL);
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}
#endif




/****************************************************
 * @fn      SendToZone_Search_Cmd
 *
 * @brief   //主动搜索分区命令-发送组播
 *
 * @param
 *
 * @return	int 应答包长度
 */
int SendToZone_Search_Cmd()
{
	//服务器已连接的情况下不主动搜索分区设备
	if(g_system_work_mode == WORK_MODE_CONCENTRATED)
		return ERROR;
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_SEARCH_ZONE_INFO,DEVICE_MODEL_PAGING_A,0,NULL);
	if(UDP_SendData(Send_MULTI_SOCKET_SEND_COMMAND,sendBuf,sendLen,0) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}




/****************************************************
 * @fn      SendToZone_Paging_Ready_Cmd
 *
 * @brief   //寻呼前发送UDP单播通知分区
 *
 * @param
 *
 * @return	int 应答包长度
 */
int SendToZone_Paging_Ready_Cmd(long int ip,int flag)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	unsigned char data[128]={0};
	int pos=0;

	if(flag == PCM_SEND_START)
	{
		data[pos++]=0x08;
		memcpy(data+pos,MAC_ADDR,6);
		pos+=6;
		data[pos++]=strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME)==0?0x10:0x00;		//寻呼台管理员及服务器为0x10,其余为0x00
		if(paging_alarm_press_flag)
			data[pos++]=100;
		else
			data[pos++]=g_paging_vol;
		data[pos++]=RATE>>16;
		data[pos++]=RATE>>8;
		data[pos++]=RATE&0xFF;
		data[pos++]=FMT;
		data[pos++]=CHANNELS;
		data[pos++]=g_send_decode_pcm_type;
		data[pos++]=0x03;	//TCP单播
		data[pos++]=strlen(MULTI_SOCKET_SEND_PCM_ADDR);		//TCP用
		memcpy(data+pos,MULTI_SOCKET_SEND_PCM_ADDR,strlen(MULTI_SOCKET_SEND_PCM_ADDR));
		pos+=strlen(MULTI_SOCKET_SEND_PCM_ADDR);
		data[pos++]=MULTI_SOCKET_SEND_PCM_PORT>>8;
		data[pos++]=MULTI_SOCKET_SEND_PCM_PORT&0xFF;

		//寻呼类型
		data[pos++]=g_paging_type;
		printf("g_paging_type=%d\r\n",g_paging_type);
	}
	else
	{
		data[pos++]=0x80;
		memcpy(data+pos,MAC_ADDR,6);
		pos+=6;
		data[pos++]=strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME)==0?0x10:0x00;		//寻呼台管理员及服务器为0x10,其余为0x00
	}

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_PAGING_NOTIFY_TCP,DEVICE_MODEL_PAGING_A,pos,data);

	if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL) //&& g_network_mode == NETWORK_MODE_LAN)
	{
		//UDP_SendData(Send_UNICAST,sendBuf,sendLen,ip);
		//分布模式不处理
	}
	else
	{
		#if SUPPORT_TCP_DEFAULT
		set_concentrated_pkg_cmd( CMD_SEND_PAGING_NOTIFY_TCP,sendBuf,sendLen);
		#endif
	}
	return SUCCEED;
}






/****************************************************
 * @fn      SendToZone_Paging_Ready_Cmd_select
 *
 * @brief   //寻呼前发送UDP单播通知所有已选分区
 *
 * @param  flag-PCM_SEND_START,PCM_SEND_END  type-0表示只停止选中的，1表示只停止寻呼过的，2表示停止所有
 *
 * @return	int 应答包长度
 */
int SendToZone_Paging_Ready_Cmd_select(int flag,int type)
{
	NetPkgMutex_Lock(1);
	SendToZone_Paging_Ready_Cmd(0,flag);
	NetPkgMutex_Lock(0);

	return SUCCEED;
}









/****************************************************
 * @fn      SendToZone_Paging_Ready_Cmd_select_multicast
 *
 * @brief   //寻呼前发送UDP组播通知所有已选分区
 *
 * @param  flag-PCM_SEND_START,PCM_SEND_END  type-0表示只停止选中的，1表示只停止寻呼过的，2表示停止所有
 *
 * @return	int 应答包长度
 */
int SendToZone_Paging_Ready_Cmd_select_multicast(int flag,int type)
{
	NetPkgMutex_Lock(1);

	int i,k,t,z;
	int zone_num=0;
	int send_times=0;	//需要发送的次数，一个包最多200个分区
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	unsigned char data[CLIENT_BUF_SIZE]={0};
	int pos=0;

	if(flag == PCM_SEND_START)
	{
		if(PagingZoneInfo.ZoneNum%200 > 0)
		{
			send_times=PagingZoneInfo.ZoneNum/200 + 1;
		}
		else
		{
			send_times=PagingZoneInfo.ZoneNum/200;
		}
		for(z=1;z<=send_times;z++)
		{
			zone_num=0;
			memset(data,0,sizeof(data));

			pos=1;
			for(i=200*(z-1);i<PagingZoneInfo.ZoneNum && i<200*z;i++)
			{
				int zoneId=Get_ZoneIndex_By_MAC(PagingZoneInfo.ZoneInfo[i].ZoneMac);
				if(zoneId == -1)
					continue;
				memcpy(data+pos,m_stZone_Info.zoneInfo[zoneId].g_zone_mac,6);
				pos+=6;

				zone_num++;
			}
			data[0]=zone_num;
			data[pos++]=(CMD_SEND_PAGING_NOTIFY>>8);
			data[pos++]=CMD_SEND_PAGING_NOTIFY;

			int cmd_len_index=pos;
			pos+=2;
			data[pos++]=0x08;
			memcpy(data+pos,MAC_ADDR,6);
			pos+=6;
			data[pos++]=strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME)==0?0x10:0x00;		//寻呼台管理员及服务器为0x10,其余为0x00
			if(paging_alarm_press_flag)
				data[pos++]=100;
			else
				data[pos++]=g_paging_vol;
			data[pos++]=RATE>>16;
			data[pos++]=RATE>>8;
			data[pos++]=RATE&0xFF;
			data[pos++]=FMT;
			data[pos++]=CHANNELS;
			data[pos++]=g_send_decode_pcm_type;
			data[pos++]=0;	//UDP组播
			data[pos++]=strlen(MULTI_SOCKET_SEND_PCM_ADDR);
			memcpy(data+pos,MULTI_SOCKET_SEND_PCM_ADDR,strlen(MULTI_SOCKET_SEND_PCM_ADDR));
			pos+=strlen(MULTI_SOCKET_SEND_PCM_ADDR);
			data[pos++]=MULTI_SOCKET_SEND_PCM_PORT>>8;
			data[pos++]=MULTI_SOCKET_SEND_PCM_PORT&0xFF;

			//寻呼类型
			data[pos++]=g_paging_type;

			int cmd_len=pos-cmd_len_index-2;

			data[cmd_len_index]=(cmd_len>>8);
			data[cmd_len_index+1]=(cmd_len);
			cmd_len=5+6*zone_num+cmd_len;

			if(zone_num>0)
			{
				memset(sendBuf,0,sizeof(sendBuf));
				int sendLen = Network_Send_Compose_CMD(sendBuf,CMD_SEND_MULTICAST_CMD,DEVICE_MODEL_PAGING_A,cmd_len,data);

				UDP_SendData(Send_MULTI_SOCKET_SEND_COMMAND, sendBuf, sendLen, 0);
			}
		}
	}
	else if(flag == PCM_SEND_END)
	{
		if(type == 1)	//只停止寻呼过的
		{
			//判断总共需要发送几次
			if(PagingZoneInfo.ZoneNum%200 > 0)
			{
				send_times=PagingZoneInfo.ZoneNum/200 + 1;
			}
			else
			{
				send_times=PagingZoneInfo.ZoneNum/200;
			}
			for(z=1;z<=send_times;z++)
			{
				zone_num=0;
				memset(data,0,sizeof(data));

				pos=1;
				for(i=200*(z-1);i<PagingZoneInfo.ZoneNum && i<200*z;i++)
				{
					int zoneId=Get_ZoneIndex_By_MAC(PagingZoneInfo.ZoneInfo[i].ZoneMac);
					if(zoneId == -1)
						continue;
					memcpy(data+pos,m_stZone_Info.zoneInfo[zoneId].g_zone_mac,6);
					pos+=6;

					zone_num++;
				}

				data[0]=zone_num;
				data[pos++]=(CMD_SEND_PAGING_NOTIFY>>8);
				data[pos++]=CMD_SEND_PAGING_NOTIFY;

				int cmd_len_index=pos;
				pos+=2;
				data[pos++]=0x80;
				memcpy(data+pos,MAC_ADDR,6);
				pos+=6;
				data[pos++]=strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME)==0?0x10:0x00;		//寻呼台管理员及服务器为0x10,其余为0x00
				int cmd_len=pos-cmd_len_index-2;

				data[cmd_len_index]=(cmd_len>>8);
				data[cmd_len_index+1]=(cmd_len);
				cmd_len=5+6*zone_num+cmd_len;
				if(zone_num>0)
				{
					memset(sendBuf,0,sizeof(sendBuf));
					int sendLen = Network_Send_Compose_CMD(sendBuf,CMD_SEND_MULTICAST_CMD,DEVICE_MODEL_PAGING_A,cmd_len,data);
					UDP_SendData(Send_MULTI_SOCKET_SEND_COMMAND, sendBuf, sendLen, 0);

				}
			}
		}
		#if 0
		else if(type == 0)	//只停止选中的
		{
			int select_zone_num=0;
			for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
			{
				if(m_stZone_Info.zoneInfo[i].g_zone_isSelect)
				{
					select_zone_num++;
				}
			}

			//判断总共需要发送几次

			if(select_zone_num%200 > 0)
			{
				send_times=select_zone_num/200 + 1;
			}
			else
			{
				send_times=select_zone_num/200;
			}

			for(z=1;z<=send_times;z++)
			{
				zone_num=0;
				memset(data,0,sizeof(data));

				pos=1;
				for(i=200*(z-1);i<m_stZone_Info.ExistTotalZone && zone_num<200*z;i++)
				{
					if(m_stZone_Info.zoneInfo[i].g_zone_isSelect)
					{
						memcpy(data+pos,m_stZone_Info.zoneInfo[i].g_zone_mac,6);
						pos+=6;

						zone_num++;
					}
				}

				data[0]=zone_num;
				data[pos++]=(CMD_SEND_PAGING_NOTIFY>>8);
				data[pos++]=CMD_SEND_PAGING_NOTIFY;

				int cmd_len_index=pos;
				pos+=2;
				data[pos++]=0x80;
				memcpy(data+pos,MAC_ADDR,6);
				pos+=6;
				data[pos++]=m_stUser_Info.CurrentUserIndex==0?0x10:0x00;		//寻呼台管理员及服务器为0x10,其余为0x00
				int cmd_len=pos-cmd_len_index-2;
				data[cmd_len_index]=(cmd_len>>8);
				data[cmd_len_index+1]=(cmd_len);
				cmd_len=5+6*zone_num+cmd_len;
				if(zone_num>0)
				{
					memset(sendBuf,0,sizeof(sendBuf));
					int sendLen = Network_Send_Compose_CMD(sendBuf,CMD_SEND_MULTICAST_CMD,DEVICE_MODEL_PAGING_A,cmd_len,data);
					UDP_SendData(Send_MULTI_SOCKET_SEND_COMMAND, sendBuf, sendLen, 0);
				}

			}

		}
		else if(type == 2)	//停止所有，这里为了方便起见，直接停掉所有分区
		{
			//判断总共需要发送几次
			if(m_stZone_Info.ExistTotalZone%200 > 0)
			{
				send_times=m_stZone_Info.ExistTotalZone/200 + 1;
			}
			else
			{
				send_times=m_stZone_Info.ExistTotalZone/200;
			}

			for(z=1;z<=send_times;z++)
			{
				zone_num=0;
				memset(data,0,sizeof(data));

				pos=1;
				for(i=200*(z-1);i<m_stZone_Info.ExistTotalZone && i<200*z;i++)
				{
					if(m_stZone_Info.zoneInfo[i].g_zone_isHide == 0)	//非隐藏的才停止
					{

						memcpy(data+pos,m_stZone_Info.zoneInfo[i].g_zone_mac,6);
						pos+=6;

						zone_num++;
					}
				}

				data[0]=zone_num;
				data[pos++]=(CMD_SEND_PAGING_NOTIFY>>8);
				data[pos++]=CMD_SEND_PAGING_NOTIFY;

				int cmd_len_index=pos;
				pos+=2;
				data[pos++]=0x80;
				memcpy(data+pos,MAC_ADDR,6);
				pos+=6;
				data[pos++]=m_stUser_Info.CurrentUserIndex==0?0x10:0x00;		//寻呼台管理员及服务器为0x10,其余为0x00
				int cmd_len=pos-cmd_len_index-2;
				data[cmd_len_index]=(cmd_len>>8);
				data[cmd_len_index+1]=(cmd_len);
				cmd_len=5+6*zone_num+cmd_len;
				if(zone_num>0)
				{
					memset(sendBuf,0,sizeof(sendBuf));
					int sendLen = Network_Send_Compose_CMD(sendBuf,CMD_SEND_MULTICAST_CMD,DEVICE_MODEL_PAGING_A,cmd_len,data);
					UDP_SendData(Send_MULTI_SOCKET_SEND_COMMAND, sendBuf, sendLen, 0);
				}
			}
		}
		#endif
	}

	NetPkgMutex_Lock(0);

	return SUCCEED;
}






/****************************************************
 * @fn      SendToZone_offline_Paging_Again
 *
 * @brief   主机向终端发送再次寻呼指令
 *
 * @param
 *
 * @return	int 应答包长度
 */
int SendToZone_offline_Paging_Again(char *ZoneMac)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	unsigned char data[128]={0};
	int pos=0;


	data[pos++]=0x08;
	memcpy(data+pos,MAC_ADDR,6);
	pos+=6;
	data[pos++]=strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME)==0?0x10:0x00;		//寻呼台管理员及服务器为0x10,其余为0x00
	if(paging_alarm_press_flag)
		data[pos++]=100;
	else
		data[pos++]=g_paging_vol;
	data[pos++]=RATE>>16;
	data[pos++]=RATE>>8;
	data[pos++]=RATE&0xFF;
	data[pos++]=FMT;
	data[pos++]=CHANNELS;
	data[pos++]=g_send_decode_pcm_type;
	data[pos++]=0x03;	//TCP单播
	data[pos++]=strlen(MULTI_SOCKET_SEND_PCM_ADDR);		//TCP用
	memcpy(data+pos,MULTI_SOCKET_SEND_PCM_ADDR,strlen(MULTI_SOCKET_SEND_PCM_ADDR));
	pos+=strlen(MULTI_SOCKET_SEND_PCM_ADDR);
	data[pos++]=MULTI_SOCKET_SEND_PCM_PORT>>8;
	data[pos++]=MULTI_SOCKET_SEND_PCM_PORT&0xFF;

	//寻呼类型
	data[pos++]=g_paging_type;
	printf("g_paging_type=%d\r\n",g_paging_type);


	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_OFFLINE_PAGING_AGAIN_TCP,DEVICE_MODEL_PAGING_A,pos,data);

	if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL) //&& g_network_mode == NETWORK_MODE_LAN)
	{
		//UDP_SendData(Send_UNICAST,sendBuf,sendLen,ip);
		//分布模式不处理
	}
	else
	{
		#if SUPPORT_TCP_DEFAULT
		set_concentrated_pkg_cmd( CMD_SEND_OFFLINE_PAGING_AGAIN_TCP,sendBuf,sendLen);
		#endif
	}
	return SUCCEED;

}





/****************************************************
 * @fn      SendToZone_offline_info
 *
 * @brief   寻呼台向主机发送终端寻呼中途掉线信息
 *
 * @param
 *
 * @return	int 应答包长度
 */
int SendTo_Host_Zone_offline_Info(char *ip,char *startTime,char *endTime)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=1+strlen(ip)+1+strlen(startTime)+1+strlen(endTime);

	int pos=0;
	data[pos++]=strlen(ip);
	for(i=0;i<strlen(ip);i++)
	{
		data[pos++]=ip[i];
	}
	data[pos++]=strlen(startTime);
	for(i=0;i<strlen(startTime);i++)
	{
		data[pos++]=startTime[i];
	}
	data[pos++]=strlen(endTime);
	for(i=0;i<strlen(endTime);i++)
	{
		data[pos++]=endTime[i];
	}
	printf("\nSendTo_Host_Zone_offline_Info:");
	printf("IP=%s,startTime=%s,endTime=%s\n",ip,startTime,endTime);
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_HOST_ZONE_PAGING_OFFLINE,DEVICE_MODEL_PAGING_A,data_len,data);

	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}




/****************************************************
 * @fn      SendToZone_PCM_Data_Cmd
 *
 * @brief   //发送PCM码流
 *
 * @param
 *
 * @return	int 应答包长度
 */
int SendToZone_PCM_Data_Cmd( unsigned char* data,int len )
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int Pkg_count=0;
	int once_sendByte=RSIZE/2;

	  if(len%once_sendByte > 0)
		  Pkg_count=len/once_sendByte + 1;
	  else
		  Pkg_count=len/once_sendByte;
	if(Paging_status != PAGING_START) printf("\nSendToZone_PCM_Data_Cmd,len=%d，Pkg_count=%d\n",len,Pkg_count);
	int sendLen=0;
	int Len_Sended=0;

	int i;
	for(i=0;i<Pkg_count;i++)		//最大
	{
		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_PCM_DATA , DEVICE_MODEL_PAGING_A,(len>=once_sendByte)?once_sendByte:len,data+Len_Sended);
		sendBuf[3] = g_paging_type;
		//if(g_network_mode == NETWORK_MODE_LAN)
		{
			UDP_SendData( Send_MULTI_SOCKET_SEND_PCM, sendBuf, sendLen,0 );
		}
		#if SUPPORT_TCP_DEFAULT
		//同时将流发往服务器，由服务器决定转发至处于TCP模式下的终端(同时，该条指令主机不需要应答)
		sendBuf[0]=CMD_SEND_PCM_DATA_TCP>>8;		//命令字高位
		sendBuf[1]=CMD_SEND_PCM_DATA_TCP;			//命令字低位
		sendBuf[5] = 0x01;							//需要转发

		UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
		#endif
		
		len-=once_sendByte;
		Len_Sended+=once_sendByte;
	}

	return SUCCEED;
}




/****************************************************
 * @fn      SendToZone_SongUrl
 *
 * @brief   //发送歌曲地址
 *
 * @param
 *
 * @return	int 应答包长度
 */

int SendToZone_SongUrl( long int ip,int type )
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	unsigned char song_data[1024]={0};	//歌曲地址
	int song_len=0;
	char *url;
	if(type == 0)	//钟声
	{
		url=TEST_RING_URL;
	}
	else			//歌曲
	{
		url=TEST_SONG_URL;
	}
	song_len=strlen(url);
	song_data[0]=song_len>>8;
	song_data[1]=song_len;

	int i;
	for(i=0;i<song_len;i++)
	{
		song_data[2+i]=url[i];
	}

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_SONG_URL,DEVICE_MODEL_PAGING_A,song_len+2,song_data);
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}




/****************************************************
 * @fn      SendToZone_Set_MuteStatus
 *
 * @brief   //控制分区静音状态
 *
 * @param
 *
 * @return	int 应答包长度
 */

int SendToZone_Set_MuteStatus( long int ip,int status )
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	unsigned char data[2]={0};
	int data_len=2;
	data[0]=CMD_SET;
	data[1]=status;
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SET_ZONE_MUTE_STATUS,DEVICE_MODEL_PAGING_A,data_len,data);
	//加入集中模式/TCP重发结构体
	set_concentrated_pkg_cmd(CMD_SET_ZONE_MUTE_STATUS,sendBuf,sendLen);
	for(i=0;i<SEND_REPEAT_COUNT;i++)
	{
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,ip) != SUCCEED)
		{
			return ERROR;
		}
	}
		return SUCCEED;
}





/****************************************************
 * @fn      SendToZone_SetQueryVol
 *
 * @brief   //调节终端音量
 *
 * @param
 *
 * @return	int 应答包长度
 */

int SendToZone_SetQueryVol(unsigned char type,long int ip,unsigned char vol )
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	int data_len=0;
	unsigned char data[2]={0};
	if(type == CMD_SET)
	{
		data_len=2;
		data[0]=CMD_SET;
		data[1]=vol;
	}
	else
	{
		data_len=1;
		data[0]=CMD_QUERY;
	}
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SET_ZONE_VOL,DEVICE_MODEL_PAGING_A,data_len,data);

	//加入集中模式/TCP重发结构体
	set_concentrated_pkg_cmd(CMD_SET_ZONE_VOL, sendBuf, sendLen);

	//if(st_Concentrated_Info.Zone_Identify)	//只有分区标识一致才发送
	if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL && g_network_mode == NETWORK_MODE_LAN)
	{
		UDP_SendData(Send_UNICAST,sendBuf,sendLen,ip);
	}


	return SUCCEED;
}



/****************************************************
 * @fn      SendToZone_SetQueryVol
 *
 * @brief   //设置终端为空闲模式
 *
 * @param
 *
 * @return	int 应答包长度
 */

int SendToZone_Idle(long int ip)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	int data_len=7;
	unsigned char data[7]={0};
	data[0]=strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME)==0?0x10:0x00;		//寻呼台管理员及服务器为0x10,其余为0x00
	memcpy(&data[1], MAC_ADDR, 6);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SET_ZONE_IDLE_STATUS,DEVICE_MODEL_PAGING_A,data_len,data);

	if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL && g_network_mode == NETWORK_MODE_LAN)
	{
		UDP_SendData(Send_UNICAST,sendBuf,sendLen,ip);
	}
	else
	{
		//加入集中模式/TCP重发结构体
		set_concentrated_pkg_cmd(CMD_SET_ZONE_IDLE_STATUS, sendBuf, sendLen);
	}

	return SUCCEED;
}







/****************************************************
 * @fn      SendToZone_UpdateInfo
 *
 * @brief   //模拟主机发送升级信息给终端
 *
 * @param
 *
 * @return	int 应答包长度
 */

int SendToZone_UpdateInfo( long int ip )
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=0;
	char *file_version="V0.1.0";
	char *ip_url="************";
	int port=8888;
	char *file_url="/Update/Net_Box_V0.1.0.tar.gz";

	int file_version_len=strlen(file_version);
	int ip_url_len=strlen(ip_url);
	int file_url_len=strlen(file_url);
#if 0
	目标终端设备类型	参见设备型号定义
	升级固件版本号长度
	升级固件版本号
	Web服务器IP地址长度
	Web服务器IP地址
	Web服务器端口	固定为2个字节
	升级文件的保存路径的长度	固定为2个字节
	升级文件的保存路径	不包含域名
#endif

	data[data_len]=DEVICE_MODEL_ZONE_A;
	data_len++;
	data[data_len]=file_version_len;
	data_len++;
	for(i=0;i<file_version_len;i++)
	{
		data[data_len+i]=file_version[i];
	}
	data_len+=file_version_len;

	data[data_len]=ip_url_len;
	data_len++;
	for(i=0;i<ip_url_len;i++)
	{
		data[data_len+i]=ip_url[i];
	}
	data_len+=ip_url_len;

	data[data_len]=port/256;
	data_len++;
	data[data_len]=port%256;
	data_len++;

	data[data_len]=file_url_len/256;
	data_len++;
	data[data_len]=file_url_len%256;
	data_len++;
	for(i=0;i<file_url_len;i++)
	{
		data[data_len+i]=file_url[i];
	}
	data_len+=file_url_len;


	if(Paging_status != PAGING_START) printf("\nSendToZone_UpdateInfo:data=\n");
	for(i=0;i<data_len;i++)
	{
		if(Paging_status != PAGING_START) printf("0x%x ",data[i]);
		if(i == data_len-1)
		{
			if(Paging_status != PAGING_START) printf("\n");
		}
	}
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_UPDATE_INFO,DEVICE_MODEL_PAGING_A,data_len,data);
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,ip) != SUCCEED)
		{
			return ERROR;
		}
	}
	return SUCCEED;
}




/****************************************************
 * @fn      Network_Send_Compose_CMD
 *
 * @brief   //发送命令组合
 *
 * @param  unsigned char *sendBuf -发送包指针,unsigned int cmd - 命令字,unsigned char deviceType - 设备类型,unsigned char Datalen-数据负载长度,unsigned char *Data 数据包
 *
 * @return	int 应答包长度
 */

int Network_Send_Compose_CMD(unsigned char *sendBuf,unsigned int cmd,unsigned char deviceType,int Datalen,unsigned char *Data)
{
	int i;
	int SendLen;
	memset(sendBuf,0,sizeof(sendBuf));
	sendBuf[0]=cmd>>8;		//命令字高位
	sendBuf[1]=cmd;			//命令字低位
	sendBuf[2]=0;			//包序号

	if(cmd == CMD_RECV_ZONE_ONLINE_INFO || cmd == CMD_SEND_SEARCH_ZONE_INFO)
	{
		sendBuf[3]=g_system_work_mode<<2;						//工作模式
		sendBuf[3] = sendBuf[3] | g_network_mode << 4;
		sendBuf[3] = sendBuf[3] | 0x01 << 6;
	}
	else
	{
		sendBuf[3]=st_Concentrated_Info.Zone_Identify;			//选中分区ID
	}

	sendBuf[4]=deviceType;	//设备型号

	int need_relay=0;
	if(g_network_mode == NETWORK_MODE_WAN || g_system_work_mode == WORK_MODE_CONCENTRATED)
	{
		if(cmd == CMD_SET_ZONE_VOL || cmd == CMD_SET_ZONE_IDLE_STATUS || cmd == CMD_SET_ZONE_MUTE_STATUS || cmd == CMD_PAGING_SEND_HOST_PLAY_SOURCE || cmd == CMD_SET_ZONE_PLAYSTATUS  ||
			 cmd == CMD_SEND_PAGING_NOTIFY_TCP || cmd == CMD_SEND_PCM_DATA_TCP || cmd == CMD_SEND_OFFLINE_PAGING_AGAIN_TCP)
		{
			need_relay=1;
		}
	}
	if(need_relay)
	{
		sendBuf[5] = 0x01;		//需要转发
	}
	else
	{
		sendBuf[5]=0;			//包属性4bit&编码格式4bit
	}

	int official_data_pos=PAYLOAD_START;
	if(cmd == CMD_SEND_PCM_DATA || cmd == CMD_SEND_PCM_DATA_TCP)
	{
		//加入本机MAC
		memcpy( sendBuf+official_data_pos,MAC_ADDR,6);
		official_data_pos+=6;
		Datalen+=6;
	}

	sendBuf[6]=Datalen>>8;
	sendBuf[7]=Datalen;

	for(i=0;i<Datalen;i++)
	{
		sendBuf[official_data_pos+i]=Data[i];
	}
	sendBuf[PAYLOAD_START+Datalen]=Calculate_XorDat(sendBuf+PAYLOAD_START,Datalen);
	SendLen=PAYLOAD_START+1+Datalen;
#if 0
	if(Paging_status != PAGING_START) printf("\nNetwork_Send_Compose_CMD,SendLen=%d,Data:\n",SendLen);
	if(SendLen<100)
	{
		for(i=0;i<SendLen;i++)
		{
			if(Paging_status != PAGING_START) printf("0x%x ",sendBuf[i]);
		}
	}
#endif
	return SendLen;
}




/*********************************************************************
 * @fn      respond_pkg_update_status_code
 *
 * @brief   应答主机升级状态
 *
 * @param   code - 状态码
 *
 * @return	void
 */
int respond_pkg_update_status_code(unsigned char code)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[0]=code;
	data_len=1;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_FIRWARE_UPDATE,DEVICE_MODEL_PAGING_A,data_len,data);
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
		{
			return ERROR;
		}
	}
	return SUCCEED;
}



/*********************************************************************
 * @fn      send_download_rate_of_progress
 *
 * @brief   向主机发送下载进度
 *
 * @param   progress - 进度（%0~100%）
 *
 * @return	void
 */
int send_download_rate_of_progress(unsigned char progress)
{

	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[0]=progress;
	data_len=1;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_FIRWARE_UPDATE_PROGRESS,DEVICE_MODEL_PAGING_A,data_len,data);
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
		{
			return ERROR;
		}
	}
	return SUCCEED;
}



/*********************************************************************
 * @fn      pkg_get_update_info
 *
 * @brief   获取主机推送的升级信息
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_get_update_info(unsigned char *rxbuf)
{
	int i;
	int data_len = 0;
	int payloadSize = 0;
	unsigned char data_buf[256];
	unsigned char verison[32]={0};

	unsigned char device_type=rxbuf[PAYLOAD_START+payloadSize];
	payloadSize++;
	if(device_type!=DEVICE_MODEL_PAGING_A && device_type!=DEVICE_MODEL_PAGING_B && device_type!=DEVICE_MODEL_PAGING_C)
	{
		if(Paging_status != PAGING_START) printf("\ndevice_type error,value=%d\n",device_type);
		return;
	}

	if( g_update_flag )
	{
	  printf("pkg_get_update_info:Upgrading...\r\n");
	  respond_pkg_update_status_code(START_DOWNLOAD);
	  return;
	}

	// 获取主机推送的固件版本
	memset(data_buf, 0x00, sizeof(data_buf));
	if (rxbuf[PAYLOAD_START+payloadSize] <= 0)
	{
		respond_pkg_update_status_code(UPDATE_FAIL);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize];
	}
	payloadSize += 1;
	for (i=0; i<data_len; i++)
	{
		data_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
	}
	payloadSize += data_len;
#if UDP_DEBUG
	if(g_paging_status != PAGING_START) printf("Host push firmware version is : %s\n", data_buf);
#endif

	strcpy(verison,data_buf);



	// 获取web服务器的域名或IP
	memset(data_buf, 0x00, sizeof(data_buf));
	if (rxbuf[PAYLOAD_START+payloadSize] <= 0)
	{
		respond_pkg_update_status_code(UPDATE_FAIL);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize];
	}
	payloadSize += 1;
	for (i=0; i<data_len; i++)
	{
		data_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
	}
	payloadSize += data_len;
#if UDP_DEBUG
	if(g_paging_status != PAGING_START) printf("Host web server ip or domain is : %s\n", data_buf);
#endif
	convert_domain_to_ip(data_buf); // 将域名转换为IP地址

	// 获取主机web服务器端口
	g_web_server_port = rxbuf[PAYLOAD_START+payloadSize]*256 + rxbuf[PAYLOAD_START+payloadSize+1];
	payloadSize += 2;

	// 获取主机升级文件存放的绝对路径
	memset(g_url_buf, 0x00, sizeof(g_url_buf));
	if ((rxbuf[PAYLOAD_START+payloadSize]*256+rxbuf[PAYLOAD_START+payloadSize+1]) <= 0)
	{
		respond_pkg_update_status_code(UPDATE_FAIL);
		return;
	}
	else
	{
		data_len = rxbuf[PAYLOAD_START+payloadSize]*256 + rxbuf[PAYLOAD_START+payloadSize+1];
	}
	payloadSize += 2;
	for (i=0; i<data_len; i++)
	{
		g_url_buf[i] = rxbuf[PAYLOAD_START+payloadSize+i];
	}
	payloadSize += data_len;
#if UDP_DEBUG
	if(g_paging_status != PAGING_START) printf("Host update file path is : %s\n", g_url_buf);
#endif


	int payload_len=(rxbuf[6]<<8)+rxbuf[7];
    if( payload_len == payloadSize)	//如果没有附带MD5信息
	{
		printf("No md5 info.\r\n");
	}
	else
	{
		int md5Len=rxbuf[PAYLOAD_START+payloadSize];
		payloadSize += 1;
		memset(upgrade_file_md5_str,0,sizeof(upgrade_file_md5_str));
		strncpy(upgrade_file_md5_str,&rxbuf[PAYLOAD_START+payloadSize],md5Len);
		printf("md5len=%d,val=%s\r\n",md5Len,upgrade_file_md5_str);
		payloadSize+=md5Len;
	}
	
	//如果升级文件路径包括DSP，则判断DSP固件的版本号
	if(	strstr(g_url_buf,"DSP") )
	{
		#if defined(USE_SSD212) || defined(USE_SSD202)
		respond_pkg_update_status_code(FIRMWARE_NEWEST);
		return;
		#endif
	  	//去掉前面的‘V’
	  	char version_noV[32]={0};
		strncpy(version_noV,verison+1,strlen(verison)-1);
		if(strcasecmp(version_noV,g_bp1048_info.version) <= 0)
		{
			printf("The dsp firmware is newest,no need to update!\r\n");
			respond_pkg_update_status_code(FIRMWARE_NEWEST);
			return;
		}
		g_update_type=1;
	}
	else if(strcasecmp(verison, VERSION) <= 0)
	{
		printf("The local firmware is newest,not to update!\r\n");
		respond_pkg_update_status_code(FIRMWARE_NEWEST);
		return;
	}
	else
	{
		g_update_type=0;
	}
	

	if(g_network_mode == NETWORK_MODE_WAN)
	{
		sprintf(g_web_server_ip,"%s",g_host_tcp_prase_ipAddress);
	}

	printf("g_url_buf=%s,g_update_type=%d\n",g_url_buf,g_update_type);

	memset(g_download_path, 0x00, sizeof(g_download_path));
	sprintf(g_download_path, "%s", UPDATE_SAVE_PATH);
	start_download_file_pthread(); // 启动文件下载线程开始下载升级文件
}


/*********************************************************************
 * @fn      pkg_query_firmware_version
 *
 * @brief   应答主机查询本机当前固件版本
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
int pkg_query_firmware_version()
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[0]=strlen(VERSION);
	for(i=0;i<data[0];i++)
	{
		data[1+i]=VERSION[i];
	}
	data_len=1+strlen(VERSION);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_VERSION_QUERY,DEVICE_MODEL_PAGING_A,data_len,data);
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
		{
			return ERROR;
		}
	}
	return SUCCEED;
}



/*********************************************************************
 * @fn      pkg_get_zone_detail_info
 *
 * @brief	获取详细分区info
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
static void pkg_get_zone_detail_info(unsigned char *rxbuf,long int ip)
{
	int i,k;
	int index=Get_ZoneIndex_By_Ip(ip);
	if( index<0 )
	{
		printf("Error:pkg_get_zone_detail_info,index<0\n");
		return;
	}
	int pos=PAYLOAD_START;
	int vol=rxbuf[pos++];			//音量
	int zone_source=rxbuf[pos++];	//节目源

	int ui_change_flag=0;
	if(vol!=m_stZone_Info.zoneInfo[index].g_zone_vol)
	{
		m_stZone_Info.zoneInfo[index].g_zone_vol=vol;
		ui_change_flag=1;
	}
	if(zone_source!=m_stZone_Info.zoneInfo[index].g_zone_source)
	{
		m_stZone_Info.zoneInfo[index].g_zone_source=zone_source;
		ui_change_flag=1;
	}
	//播放状态1个字节
	int playStatus=rxbuf[pos++];
	#if 0
	if(	playStatus!=m_stZone_Info.zoneInfo[index].g_zone_playStatus)
	{
		m_stZone_Info.zoneInfo[index].g_zone_playStatus=playStatus;
		ui_change_flag=1;
	}
	#endif

	int mediaNameLen=rxbuf[pos++];
	if(strncmp(m_stZone_Info.zoneInfo[index].g_zone_media_name,rxbuf+pos,mediaNameLen)!=0)
	{
		ui_change_flag=1;
		memset(m_stZone_Info.zoneInfo[index].g_zone_media_name,0,sizeof(m_stZone_Info.zoneInfo[index].g_zone_media_name));
		for(i=0;i<mediaNameLen;i++)		//节目名称长度
		{
			m_stZone_Info.zoneInfo[index].g_zone_media_name[i]=rxbuf[pos++];
		}
#if 0
		if(i>0 &&i<64)
			m_stZone_Info.zoneInfo[index].g_zone_media_name[i]='\0';
#endif
	}
	//if(Paging_status != PAGING_START) printf("g_zone_source=%d,ui_change_flag=%d",m_stZone_Info.zoneInfo[index].g_zone_source,ui_change_flag);
	//printf("\npkg_get_zone_detail_info:zone_index=%d,source=%d\n",index,zone_source);


	//如果非管理员，且没有这个分区的管理权限，那么不刷新
	if( IsValidWin(WIN_CONTROL) )
	{
		if(strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME)!=0)
		{
			int valid_zone_flag=0;
			for(k=0;k<m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneCount;k++)
			{
				if(	memcmp(m_stZone_Info.zoneInfo[index].g_zone_mac,&m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneMac[6*k],6 ) == 0 )
				{
					valid_zone_flag=1;
					break;
				}
			}
			if(!valid_zone_flag)
			{
				return;
			}
		}
	}

	if(ui_change_flag)
	{
		//printf("ui_change_flag...\n");
		//Zone_Info_Update_Thread();
		set_zoneStatus_refreshTime(0);
	}
}



/****************************************************
 * @fn      IPcharToInt
 *
 * @brief   将char型IP转换为整型数据
 *
 * @param
 *
 * @return	long int
 */
long int IPcharToInt(char *ip)
{
	long int result;
	if(strlen(ip)>16)
	{
		if(Paging_status != PAGING_START) printf("\nERROR:IPcharToInt:IP_Len >16\n");
	}

	char ipbuf[16]={0};
	strcpy(ipbuf,ip);
	char *p=NULL;
	char buf[20]={0};
	p=strtok(ipbuf,".");
	if(p)
	{
		int bit=24;
		sprintf(buf,"%s",p);
		result=(atoi(buf)<<bit);

		while( (p=strtok(NULL,".")) )
		{
			bit-=8;
			sprintf(buf,"%s",p);
			result+=(atoi(buf)<<bit);
		}
	}
	return result;
}



/****************************************************
 * @fn      IPIntToChar
 *
 * @brief   将int型IP转换为字符串
 *
 * @param
 *
 * @return	unsigned char*
 */
char* IPIntToChar(long int ip)
{
	static char ipBuf[20]={0};
	memset(ipBuf,0,sizeof(ipBuf));
	sprintf(ipBuf,"%d.%d.%d.%d",(ip>>24)&0xff,(ip>>16)&0xff,(ip>>8)&0xff,(ip)&0xff);
	//if(Paging_status != PAGING_START) printf("\nIPIntToChar:ip=%s",ipBuf);
	return ipBuf;
}


// char数组转成uint类型
unsigned int uCharsToInt(const unsigned char *pData)
{
    // 一定要先转成unsigned char，因为char装不下FF
    unsigned int  data = 	((unsigned int)(pData[0]))*256*256*256 +
			   				((unsigned int)(pData[1]))*256*256 +
			   				((unsigned int)(pData[2]))*256 +
               				pData[3];

    return data;
}

// char数组转成ushort类型
unsigned short uCharsToShort(const unsigned char *pData)
{
    // 一定要先转成unsigned char，因为char装不下FF
    unsigned short  data = 	((unsigned short)(pData[0]))*256 +
               				pData[1];

    return data;
}

// char数组转成unsign long long类型
unsigned long long uCharsToLongLong(const unsigned char *pData)
{
    // 一定要先转成unsigned char，因为char装不下FF
    unsigned long long  data = 	((unsigned long long)(pData[0]))*256*256*256*256*256*256*256 +
              					((unsigned long long)(pData[1]))*256*256*256*256*256*256 +
              					((unsigned long long)(pData[2]))*256*256*256*256*256 +
			   					((unsigned long long)(pData[3]))*256*256*256*256 +
			   					((unsigned long long)(pData[4]))*256*256*256 +
			   					((unsigned long long)(pData[5]))*256*256 +
			   					((unsigned long long)(pData[6]))*256 +
               					pData[7];
    return data;
}





/*********************************************************************
 * @fn      GetLocalIp
 *
 * @brief   获取本地网卡IP地址，"eth0/1"需根据实际网卡号进行填写
 *
 * @param   sockfd - 套接字文件描述符
 *			Localip - 获取的IP地址
 *
 * @return  1 - 获取成功
 *			-1 - 获取失败
 */
int GetLocalIp(char *localIP)
{
    char ifName[64]={0};
    char ifName2[64]={0};
    struct ifaddrs * ifAddrStruct=NULL;
    void * tmpAddrPtr=NULL;
    getifaddrs(&ifAddrStruct);
    struct ifaddrs * ifAddrStruct_first=ifAddrStruct;

    #ifdef USE_PC_SIMULATOR
    sprintf(ifName,"%s","ens32");
    sprintf(ifName2,"%s","wifi0");
    #else
    sprintf(ifName,"%s","eth0");
    if(!eth_link_status)
    {
		#if defined(USE_SSD212) || defined(USE_SSD202)
        if(g_module_4G_status <= MODULE_4G_OFF)
		#else
		if(1)
		#endif
        {
            memset(localIP,0,sizeof(localIP));
        }
        else
        {
            sprintf(ifName,"%s","eth1");
        }
    }

    #endif


    while (ifAddrStruct!=NULL)
    {
        if ( strcmp(ifAddrStruct->ifa_name,ifName) == 0 && ifAddrStruct->ifa_addr->sa_family==AF_INET)
        {   // check it is IP4 is a valid IP4 Address
            tmpAddrPtr = &((struct sockaddr_in *)ifAddrStruct->ifa_addr)->sin_addr;
            char addressBuffer[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
            //printf("%s IPV4 Address %s\n", ifAddrStruct->ifa_name, addressBuffer);
            if(strcmp(addressBuffer,"127.0.0.1") != 0)
            {
                printf("GetLocalIp:%s\n",addressBuffer);
                memset(localIP,0,sizeof(localIP));
                strcpy(localIP, addressBuffer);

                if(ifAddrStruct_first!=NULL)
                    freeifaddrs(ifAddrStruct_first);
                return 0;
            }
        }
        #ifdef USE_PC_SIMULATOR
        if ( strcmp(ifAddrStruct->ifa_name,ifName2) == 0 && ifAddrStruct->ifa_addr->sa_family==AF_INET)
        {   // check it is IP4 is a valid IP4 Address
            tmpAddrPtr = &((struct sockaddr_in *)ifAddrStruct->ifa_addr)->sin_addr;
            char addressBuffer[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, tmpAddrPtr, addressBuffer, INET_ADDRSTRLEN);
            //printf("%s IPV4 Address %s\n", ifAddrStruct->ifa_name, addressBuffer);
            if(strcmp(addressBuffer,"127.0.0.1") != 0)
            {
                printf("GetLocalIp:%s\n",addressBuffer);
                memset(localIP,0,sizeof(localIP));
                strcpy(localIP, addressBuffer);

                if(ifAddrStruct_first!=NULL)
                    freeifaddrs(ifAddrStruct_first);
                return 0;
            }
        }
        #endif

        ifAddrStruct = ifAddrStruct->ifa_next;
    }

    if(ifAddrStruct_first!=NULL)
        freeifaddrs(ifAddrStruct_first);
    return -1;
}



/*********************************************************************
 * @fn      GetLocalIp
 *
 * @brief   获取本地网卡MAC地址，"eth0/1"需根据实际网卡号进行填写
 *
 * @param   sockfd - 套接字文件描述符
 *			Localip - 获取的IP地址
 *
 * @return  1 - 获取成功
 *			-1 - 获取失败
 */
int GetLocalMAC(unsigned char *mac_addr,bool bSet)
{
#ifdef USE_PC_SIMULATOR
	struct ifreq ifr_ip;
	char temp_buf[10]={0};
    char temp_buf2[10]={0};
	strcpy(temp_buf,"ens32");
    strcpy(temp_buf2,"wifi0");
    memset(&ifr_ip, 0, sizeof(ifr_ip));
	strncpy(ifr_ip.ifr_name, temp_buf, sizeof(ifr_ip.ifr_name)-1);

    int sockfd;
    if ((sockfd = socket(AF_INET, SOCK_DGRAM, 0)) == -1)
        return;
	if (ioctl(sockfd, SIOCGIFHWADDR, &ifr_ip) < 0)
	{
        memset(&ifr_ip, 0, sizeof(ifr_ip));
        strncpy(ifr_ip.ifr_name, temp_buf2, sizeof(ifr_ip.ifr_name)-1);
		if (ioctl(sockfd, SIOCGIFHWADDR, &ifr_ip) < 0)
            return;
	}
	close(sockfd);
	int i;
	for(i=0;i<6;i++)
		mac_addr[i]=  ifr_ip.ifr_hwaddr.sa_data[i];
    #if 1
    int appCnt=0;
    char readline[32]={0};
    FILE* fp = popen("ps -ef | grep demo | wc -l", "r" );
    if ( NULL == fp )
    {
        printf("popen ps error!");
    }
    else
    {
        memset( readline, 0, sizeof( readline ) );
        fgets( readline,sizeof( readline ),fp );
        pclose(fp);

        appCnt = atoi(readline);
        printf("appCnt=%d\n",appCnt);
    }

    mac_addr[4] = appCnt>>8;
    mac_addr[5] = appCnt;
    #endif
	printf("GetLocalMAC=%02x:%02x:%02x:%02x:%02x:%02x\n",mac_addr[0],mac_addr[1],mac_addr[2],mac_addr[3],mac_addr[4],mac_addr[5]);

#else

	Exec_System_CMD("ifconfig eth0 up");
	struct ifreq ifr_ip;
	char temp_buf[10]={0};
	strcpy(temp_buf,"eth0");
    memset(&ifr_ip, 0, sizeof(ifr_ip));
	strncpy(ifr_ip.ifr_name, temp_buf, sizeof(ifr_ip.ifr_name)-1);

    int sockfd;
    if ((sockfd = socket(AF_INET, SOCK_DGRAM, 0)) == -1)
        return;
	if (ioctl(sockfd, SIOCGIFHWADDR, &ifr_ip) < 0)
        return;
	close(sockfd);
	int i;
	for(i=0;i<6;i++)
		mac_addr[i]=  ifr_ip.ifr_hwaddr.sa_data[i];

	printf("GetLocalMAC=%02x:%02x:%02x:%02x:%02x:%02x\n",mac_addr[0],mac_addr[1],mac_addr[2],mac_addr[3],mac_addr[4],mac_addr[5]);

	unsigned char temp_mac_addr[6]={0};
	#if USE_ASM9260
		while( strcmp( g_bp1048_info.version,"" ) == 0 )
		{
			//如果没有获取到BP1048的版本号，循环获取
			Bp1048_Send_Get_Info(BP1048_CMD_CHIP_INFO);
			usleep(500000);
		}
		temp_mac_addr[0]=0x2C;
		temp_mac_addr[1]=0x21;
		temp_mac_addr[2]=g_bp1048_info.uuid[1]+g_bp1048_info.uuid[2];
		temp_mac_addr[3]=g_bp1048_info.uuid[3]+g_bp1048_info.uuid[4];
		temp_mac_addr[4]=g_bp1048_info.uuid[5]+g_bp1048_info.uuid[6];
		temp_mac_addr[5]=g_bp1048_info.uuid[7];

	#elif defined(USE_SSD212) || defined(USE_SSD202)
		//由uuid取得MAC,然后设置MAC
		unsigned long long  u64Uuid=sstar_get_chip_uuid();
		if(u64Uuid>0)
		{
			temp_mac_addr[0]=0x2C;
			temp_mac_addr[1]=((u64Uuid)>>40) & 0xFF;
			temp_mac_addr[2]=((u64Uuid)>>32) & 0xFF;
			temp_mac_addr[3]=((u64Uuid)>>24) & 0xFF;
			temp_mac_addr[4]=((u64Uuid)>>16) & 0xFF;
			temp_mac_addr[5]=((u64Uuid)>>8) & 0xFF;
		}
	#endif
	
	if(memcmp(temp_mac_addr,mac_addr,6) == 0)
	{
		printf("Mac not changed!\n");
		//测试时才打开
		#if 1
		//MAC没有变化，说明程序已经启动了一遍，这个时候需要重新设置网关，否则加入组播会报错
		if(g_IP_Assign == IP_ASSIGN_DHCP)
		{
			//要先杀掉dhcpc进程，否则端口可能被占用。
			#if defined(USE_SSD212) || defined(USE_SSD202)
			pox_system("pidof zcip | xargs kill");
			pox_system("pidof udhcpc | xargs kill");
			char cmd[64]={0};
			usleep(500000);
			sprintf(cmd,"route add default gw %s","***********");
			pox_system(cmd);
			#endif
		}
		#endif
	}
	else
	{
		memcpy(mac_addr,temp_mac_addr,6);
		printf("NewMAC=%02x:%02x:%02x:%02x:%02x:%02x\n",mac_addr[0],mac_addr[1],mac_addr[2],mac_addr[3],mac_addr[4],mac_addr[5]);
		if(bSet)
			set_mac_address(mac_addr);
	}
#endif
}


/*********************************************************************
 * @fn      convert_domain_to_ip
 *
 * @brief   将域名转换为IP地址，如果输入为IP地址转出来也为IP地址
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */
static void convert_domain_to_ip(unsigned char *domain)
{
	struct hostent *host;
	struct in_addr **addr;

	// 通过域名获取IP地址
	if ((host = gethostbyname(domain)) == NULL)
	{
		perror("gethostbyname");
	}
	else
	{
		memset(g_web_server_ip, 0x00, sizeof(g_web_server_ip));
		addr = (struct in_addr**)host->h_addr_list;
		sprintf(g_web_server_ip, "%s", inet_ntoa(*addr[0]));
		if(Paging_status != PAGING_START) printf("g_web_server_ip : %s\n", g_web_server_ip);
	}
}










/**
 *  File Type	文件类型，参见3.8	1 Byte
	Update DateTime Length	文件更新时间长度	1 Byte
	Update DateTime	文件更新时间，
	如“2016-02-22 10:02:50”	19Bytes
	WEB server IP length	Web服务器IP地址长度	1 Byte
	WEB server IP	Web服务器IP地址	长度不定
	WEB server port	Web服务器端口	2Bytes
	Absolute path length	文件的保存路径的长度	2Bytes
	Absolute path	文件的保存路径	长度不定，不包含域名和端口
 *
 */

/*********************************************************************
 * @fn      Get_UpdateXML_Info_From_Host
 *
 * @brief   获取主机XML文件
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */

int Get_UpdateXML_Info_From_Host(unsigned char *rxbuf)
{
	int i;
	int pos=PAYLOAD_START;
	int file_type = rxbuf[pos++];
	int Update_DateTime_length = rxbuf[pos++];
	char Update_DateTime[64]={0};
	for(i=0;i<Update_DateTime_length;i++)
	{
		Update_DateTime[i] = rxbuf[pos++];
	}
	int web_server_ip_length = rxbuf[pos++];
	char web_server_ip[64]={0};
	for(i=0;i<web_server_ip_length;i++)
	{
		web_server_ip[i] = rxbuf[pos++];
	}

	int rx1=rxbuf[pos++];
	int rx2=rxbuf[pos++];
	g_xml_download_port = (rx1 <<8)+(rx2);
	rx1=rxbuf[pos++];
	rx2=rxbuf[pos++];
	int Absolute_path_length = (rx1 <<8)+(rx2);

	char absolute_path[128]={0};
	for(i=0;i<Absolute_path_length;i++)
	{
		absolute_path[i] = rxbuf[pos++];
	}

	//20240323 加多一个字段，用于判断龙之音版本寻呼台之间对讲
	unsigned char isLZYSupportIntercom=0;
	int nLen=(rxbuf[6]<<8)+rxbuf[7];
	if(pos-PAYLOAD_START<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
    {
        isLZYSupportIntercom = rxbuf[pos++];
		//20250327 加多一个字段，用于判断话筒任意用户可以看到管理员创建的歌曲列表（适用于非龙之音云版本）
		if(pos-PAYLOAD_START<nLen)    //如果当前pos比负载数据长度小，证明存在此字段
			g_isShowPlayListDirAndSongAdmin = rxbuf[pos++];
    }

	if(g_network_mode == NETWORK_MODE_WAN)
	{
		sprintf(web_server_ip,"%s",g_host_tcp_prase_ipAddress);
	}

	printf("Get_UpdateXML_Info_From_Host,file_type=%d\n",file_type);
	switch(file_type)
	{
		case XML_FILE_GROUP:
			memset(g_xml_groupInfo_url,0,sizeof(g_xml_groupInfo_url));
			strcat(g_xml_groupInfo_url,"http://");
			strcat(g_xml_groupInfo_url,web_server_ip);
			strcat(g_xml_groupInfo_url,":");
			char port_buf[10]={0};
			sprintf(port_buf,"%d",g_xml_download_port);
			strcat(g_xml_groupInfo_url,port_buf);
			strcat(g_xml_groupInfo_url,absolute_path);
			printf("g_xml_groupInfo_url=%s\n",g_xml_groupInfo_url);
#if 0
			if(Paging_status == PAGING_START)
			{
				send_host_xml_update_status(XML_FILE_GROUP,XML_FILE_BUSY);
			}
			else
#endif
			if(strncmp(serverMusicList.DateTime,Update_DateTime,strlen(Update_DateTime))==0)		//id存在且更新日期等于现有日期
			{
				//应答主机
				send_host_xml_update_status(XML_FILE_GROUP,XML_FILE_NEWEST);
			}
			else
			{
				XML_Group_Thread();
			}
			break;
		case XML_FILE_ZONEINFO:
			memset(g_xml_zoneInfo_url,0,sizeof(g_xml_zoneInfo_url));
			strcat(g_xml_zoneInfo_url,"http://");
			strcat(g_xml_zoneInfo_url,web_server_ip);
			strcat(g_xml_zoneInfo_url,":");
			char port_buf3[10]={0};
			sprintf(port_buf3,"%d",g_xml_download_port);
			strcat(g_xml_zoneInfo_url,port_buf3);
			strcat(g_xml_zoneInfo_url,absolute_path);
			printf("g_xml_zoneInfo_url=%s\n",g_xml_zoneInfo_url);

#if 0
			if(Paging_status == PAGING_START)
			{
				send_host_xml_update_status(XML_FILE_ZONEINFO,XML_FILE_BUSY);
			}
			else
#endif
			{
				XML_ZoneInfo_Thread();
			}
			break;
		case XML_FILE_MUSICLIST:
			memset(g_xml_Playlist_url,0,sizeof(g_xml_Playlist_url));
			strcat(g_xml_Playlist_url,"http://");
			strcat(g_xml_Playlist_url,web_server_ip);
			strcat(g_xml_Playlist_url,":");
			char port_buf2[10]={0};
			sprintf(port_buf2,"%d",g_xml_download_port);
			strcat(g_xml_Playlist_url,port_buf2);
			strcat(g_xml_Playlist_url,absolute_path);
			printf("g_xml_Playlist_url=%s\n",g_xml_Playlist_url);
#if 0
			if(Paging_status == PAGING_START)
			{
				send_host_xml_update_status(XML_FILE_MUSICLIST,XML_FILE_BUSY);
			}
			else
#endif
			if(strncmp(serverMusicList.DateTime,Update_DateTime,strlen(Update_DateTime))==0)		//id存在且更新日期等于现有日期
			{
				//应答主机
				send_host_xml_update_status(XML_FILE_MUSICLIST,XML_FILE_NEWEST);
			}
			else
			{
				XML_Playlist_Thread();
			}
			break;
		case XML_FILE_TIMER:
			send_host_xml_update_status(XML_FILE_TIMER,XML_FILE_NEWEST);
			break;
		case XML_FILE_AUDIO_COLLECTOR:
			memset(g_xml_audiocollectorInfo_url,0,sizeof(g_xml_audiocollectorInfo_url));
			strcat(g_xml_audiocollectorInfo_url,"http://");
			strcat(g_xml_audiocollectorInfo_url,web_server_ip);
			strcat(g_xml_audiocollectorInfo_url,":");
			char port_buf4[10]={0};
			sprintf(port_buf4,"%d",g_xml_download_port);
			strcat(g_xml_audiocollectorInfo_url,port_buf4);
			strcat(g_xml_audiocollectorInfo_url,absolute_path);
			printf("g_xml_audiocollectorInfo_url=%s\n",g_xml_audiocollectorInfo_url);
#if 0
			if(Paging_status == PAGING_START)
			{
				send_host_xml_update_status(XML_FILE_AUDIO_COLLECTOR,XML_FILE_BUSY);
			}
			else
#endif
			{
				XML_AudioCollector_Thread();
			}
			break;

		case XML_FILE_USER:
			memset(g_xml_userInfo_url,0,sizeof(g_xml_userInfo_url));
			strcat(g_xml_userInfo_url,"http://");
			strcat(g_xml_userInfo_url,web_server_ip);
			strcat(g_xml_userInfo_url,":");
			char port_buf9[10]={0};
			sprintf(port_buf9,"%d",g_xml_download_port);
			strcat(g_xml_userInfo_url,port_buf9);
			strcat(g_xml_userInfo_url,absolute_path);
			printf("g_xml_userInfo_url=%s\n",g_xml_userInfo_url);
			XML_User_Thread();
			break;
#if ENABLE_CALL_FUNCTION
		case XML_FILE_PAGER:
			memset(g_xml_pagerInfo_url,0,sizeof(g_xml_pagerInfo_url));
			strcat(g_xml_pagerInfo_url,"http://");
			strcat(g_xml_pagerInfo_url,web_server_ip);
			strcat(g_xml_pagerInfo_url,":");
			char port_buf11[10]={0};
			sprintf(port_buf11,"%d",g_xml_download_port);
			strcat(g_xml_pagerInfo_url,port_buf11);
			strcat(g_xml_pagerInfo_url,absolute_path);
			printf("g_xml_pagerInfo_url=%s\n",g_xml_pagerInfo_url);
			XML_PagerInfo_Thread();

			//20240323,收到该指令，代表支持寻呼台之间对讲（目前仅龙之音V1版本不支持）
			if(IS_APP_LZY_VERSION)
			{
				if(isLZYSupportIntercom)
				{
					g_isSupportPagerCall=1;
				}
			}
			else
			{
				g_isSupportPagerCall=1;
			}
			
			break;
#endif
	}

	return SUCCEED;
}




/*
 *
 * Downloading Status	下载状态：
0x01：正在同步
0x02：由于某种原因同步终止	1 Byte
All Songs Count	一共需要同步的歌曲数目	2 Bytes
Finished Songs Count	已同步完成的歌曲数目	2 Bytes
 *
 *
 *
 */
/*********************************************************************
 * @fn      send_host_musicList_sync_progress
 *
 * @brief   歌曲文件同步进度上传
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */
int send_host_musicList_sync_progress(int sync_status)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[0]=sync_status;
	data[1]=serverMusicList.DirNum>>8;
	data[2]=serverMusicList.DirNum;

	int num=0;
	for(i=0;i<serverMusicList.DirNum;i++)
	{
		for(k=0;k<serverMusicList.SongDir[i].nFileNum;k++)
		{
			if(serverMusicList.SongDir[i].musicfile[k].IsDownload)
			{
				num++;
			}
		}
	}

	data[3]=num>>8;
	data[4]=num;
	data_len=5;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_HOST_MUSICLIST_SYNC_PROGRESS,DEVICE_MODEL_PAGING_A,data_len,data);
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
		{
			return ERROR;
		}
	}
	return SUCCEED;
}



/*********************************************************************
 * @fn      Send_Unonline_Info
 *
 * @brief   发送离线通知
 *
 * @param   void
 *
 * @return	void
 *********************************************************************/
void Send_Unonline_Info(void)
{
	int i = 0;
	int pos = 0;
	unsigned char data_buf[CLIENT_BUF_SIZE];
	unsigned char send_buf[128];
	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存
	memset(data_buf, 0x00, sizeof(data_buf));
	// 添加命令字
	send_buf[pos++] = (unsigned char)(CMD_OFFLINE>>8);
	send_buf[pos++] = (unsigned char)CMD_OFFLINE;
	// 添加包序号
	send_buf[pos++] = 0;
	// 保留位

	send_buf[pos++] = 0;

	// 产品型号
	send_buf[pos++] = DEVICE_MODEL_PAGING_A;
	// 包属性及编码格式
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x00;
	send_buf[pos++] = 0x06;
	for (i=0; i<6; i++)
	{
		send_buf[pos++] = MAC_ADDR[i];
	}
	send_buf[pos++] = Calculate_XorDat(&send_buf[PAYLOAD_START], 6);

	UDP_SendData(Send_MULTI_SOCKET_SEND_ONLINE,send_buf,pos,0);
	if(g_network_mode == NETWORK_MODE_WAN)
	{
		//如果是WAN模式下，发送TCP单播上线通知
		UDP_SendData(Send_UNICAST,send_buf,pos,g_host_ip);
	}
}



/*********************************************************************
 * @fn      send_online_info
 *
 * @brief   发送在线通知
 *
 * @param   void
 *
 * @return	void
 */
int send_online_info()
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;
	int pos=0;

	// 添加设备别名
	data[pos++]=strlen(g_device_alias);
	for(i=0;i<strlen(g_device_alias);i++)
	{
		data[i+pos]=g_device_alias[i];
	}
	pos+=strlen(g_device_alias);

	//添加终端ID(mac)
	data[pos++]=6;
	for(i=0;i<6;i++)
	{
		data[i+pos]=MAC_ADDR[i];
	}


	pos+=6;
	data[pos++] = 50;		//音量
	if(Paging_status == PAGING_START)
		data[pos++] = SOURCE_NET_PAGING; //节目源
	else if(Paging_status == CALLING_START)
		data[pos++] = SOURCE_CALL; 		//节目源
	else
		data[pos++] = SOURCE_NULL;		 //节目源

	data[pos++] = 0x00;		//节目源名称长度
	data[pos++] = 0x00;		//播放状态
	data[pos++] = 0x00;		//终端控制模式

	char versionBuf[64]={0};
	#if defined(USE_SSD212) || defined(USE_SSD202)
	#if LZY_COMMERCIAL_VERISON
	sprintf(versionBuf, "%sL_C%d%s", VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION);
	#else
	sprintf(versionBuf, "%sC%d%s", VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION);
	#endif
	#if DEBUG_CORE_MODE
	strcat(versionBuf,"(Debug)");
	#endif
	#else
	if(g_bp1048_info.firmware_type == BP1048_FW_UDISK_TYPE)
	{
		sprintf(versionBuf, "%sC%d%s/%sU", VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION,g_bp1048_info.version);
	}
	else
	{
		sprintf(versionBuf, "%sC%d%s/%s", VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION,g_bp1048_info.version);
	}
	#endif

	#if NETWORK_VPN_INTERNET
	strcat(versionBuf,"(VPN)");
	#endif

	#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
	#if IS_LZY_LIMIT_PAGING_DURATION_30MIN
	strcat(versionBuf,"(LPT30)");
	#elif IS_LZY_LIMIT_PAGING_DURATION_120MIN
	strcat(versionBuf,"(LPT120)");
	#endif
	#endif
	

	data[pos++] = strlen(versionBuf);		//固件版本信息长度
	for(i=0;i<strlen(versionBuf);i++)
	{
		data[i+pos]=versionBuf[i];
	}
	pos+= strlen(versionBuf);
	//加入扩展字段
	#if ENABLE_CALL_FUNCTION
	if(g_isSupportCall)
	{
		#if USE_SSD202
		data[pos++] = 0x0C;	//对讲+可视
		#else
		data[pos++] = 0x04;	//对讲
		#endif
	}
	else
	{
		data[pos++] = 0x00;	//无
	}
	#else
	data[pos++] = 0x00; // 无
	#endif

	//混音状态
	data[pos++] = 0;

#if defined(USE_SSD212) || defined(USE_SSD202)
	//4G模块信号质量
	data[pos++] = eth_link_status?0:stModule4GInfo.csq_rssi;		//0或者99都代表未知，使用的是非4G模块或者是4G模块但使用了有线网卡

	//4G模块卡号长度
	int iccid_length=strlen(stModule4GInfo.iccid);
	data[pos++] = iccid_length;		//0或者99都代表未知，使用非4G模块或者，或者4G模块使用了有线通讯
	//4G模块卡号
	memcpy(data+pos,stModule4GInfo.iccid,iccid_length);
	pos+=iccid_length;
#else
	data[pos++] = 0;	//信号强度
	data[pos++] = 0;	//4G卡号长度
#endif

	//用户名
	int account_len=0;
	if( IsValidWin(WIN_CONTROL) )
	{
		account_len = strlen(m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].name);
	}
	data[pos++] = account_len;
	memcpy(data+pos,m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].name,account_len);
	pos+=account_len;

	data_len=pos;

	int sendLen = Network_Send_Compose_CMD(sendBuf, CMD_RECV_ZONE_ONLINE_INFO, DEVICE_MODEL_PAGING_A, data_len,data);

	// 发送数据
	#if 0
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		UDP_SendData(Send_MULTI_SOCKET_SEND_ONLINE,sendBuf,sendLen,0);
	}
	else
	{
		UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
	}
	#else
	//不论是LAN还是WAN，均发送组播上线通知
	UDP_SendData(Send_MULTI_SOCKET_SEND_ONLINE,sendBuf,sendLen,0);
	if(g_network_mode == NETWORK_MODE_WAN)
	{
		//如果是WAN模式下，发送TCP单播上线通知
		UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
	}

	#endif

}





/*********************************************************************
 * @fn      send_request_time_sync
 *
 * @brief   上线后主动请求时间同步
 *
 * @param   void
 *
 * @return	void
 */
int send_request_time_sync(void)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;
	int pos=0;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_TIME_SYN_FIRST,DEVICE_MODEL_PAGING_A,data_len,data);
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_MULTI_SOCKET_SEND_ONLINE,sendBuf,sendLen,0) != SUCCEED)
		{
			return ERROR;
		}
	}

	printf("send_request_time_sync...\n");
	return SUCCEED;
}








/*********************************************************************
 * @fn      response_send_host_xml_file_info
 *
 * @brief   应答主机向终端查询存储在本地的文件信息
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */
int response_send_host_xml_file_info(char *rxbuf,int file_type)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	if(rxbuf == NULL)
	{
		data[0]=file_type;
		if(file_type == XML_FILE_MUSICLIST)
			sprintf(serverMusicList.DateTime,"%s","1970-01-01 01:01:01");
		if(file_type == XML_FILE_GROUP)
			sprintf(m_stGroup_Info.DateTime,"%s","1970-01-01 01:01:01");
		if(file_type == XML_FILE_ZONEINFO)
			sprintf(m_stZone_Info.DateTime,"%s","1970-01-01 01:01:01");
		if(file_type == XML_FILE_AUDIO_COLLECTOR)
			sprintf(g_Audio_Collector_Info_dateTime,"%s","1970-01-01 01:01:01");
		if(file_type == XML_FILE_USER)
			sprintf(m_stUser_Info.DateTime,"%s","1970-01-01 01:01:01");
		#if ENABLE_CALL_FUNCTION
		if(file_type == XML_FILE_PAGER)
			sprintf(m_stPager_Info.DateTime,"%s","1970-01-01 01:01:01");
		#endif
	}
	else
	{
		data[0]=rxbuf[PAYLOAD_START];
	}

	if(data[0] == XML_FILE_MUSICLIST)
	{
		data[1]=strlen(serverMusicList.DateTime);
		for(i=0;i<strlen(serverMusicList.DateTime);i++)
		{
			data[2+i]=serverMusicList.DateTime[i];
		}
		data_len=2+strlen(serverMusicList.DateTime);
	}
	else if(data[0] == XML_FILE_GROUP)
	{
		data[1]=strlen(m_stGroup_Info.DateTime);
		for(i=0;i<strlen(m_stGroup_Info.DateTime);i++)
		{
			data[2+i]=m_stGroup_Info.DateTime[i];
		}
		data_len=2+strlen(m_stGroup_Info.DateTime);
	}
	else if(data[0] == XML_FILE_ZONEINFO)
	{
		data[1]=strlen(m_stZone_Info.DateTime);
		for(i=0;i<strlen(m_stZone_Info.DateTime);i++)
		{
			data[2+i]=m_stZone_Info.DateTime[i];
		}
		data_len=2+strlen(m_stZone_Info.DateTime);
	}
	else if(data[0] == XML_FILE_AUDIO_COLLECTOR)
	{
		data[1]=strlen(g_Audio_Collector_Info_dateTime);
		for(i=0;i<strlen(g_Audio_Collector_Info_dateTime);i++)
		{
			data[2+i]=g_Audio_Collector_Info_dateTime[i];
		}
		data_len=2+strlen(g_Audio_Collector_Info_dateTime);
	}
	else if(data[0] == XML_FILE_USER)
	{
		data[1]=strlen(m_stUser_Info.DateTime);
		for(i=0;i<strlen(m_stUser_Info.DateTime);i++)
		{
			data[2+i]=m_stUser_Info.DateTime[i];
		}
		data_len=2+strlen(m_stUser_Info.DateTime);
	}
	#if ENABLE_CALL_FUNCTION
	else if(data[0] == XML_FILE_PAGER)
	{
		data[1]=strlen(m_stPager_Info.DateTime);
		for(i=0;i<strlen(m_stPager_Info.DateTime);i++)
		{
			data[2+i]=m_stPager_Info.DateTime[i];
		}
		data_len=2+strlen(m_stPager_Info.DateTime);
	}
	#endif
	else
	{
		return SUCCEED;
	}
	printf("\nresponse_send_host_xml_file_info:type=%d\n",data[0]);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_QUERY_FILE,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
		{
			return ERROR;
		}
	}
	return SUCCEED;
}







/*********************************************************************
 * @fn      send_host_xml_sync_status
 *
 * @brief   XML文件更新状态
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */
int send_host_xml_update_status(int file_type,int update_status)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[0]=file_type;
	data[1]=update_status;
	data_len=2;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_GET_HOST_FILE_SYNC_INFO,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
		{
			return ERROR;
		}
	}
	return SUCCEED;
}





/*********************************************************************
 * @fn      host_set_terminal_mac
 *
 * @brief   主机请求重新设置终端MAC
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */
int host_set_terminal_mac(long int ip)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SET_ZONE_MAC,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,ip) != SUCCEED)
		{
			return ERROR;
		}
	}
	return SUCCEED;
}



/*********************************************************************
 * @fn      response_send_host_xml_file_info
 *
 * @brief   主机向终端请求播放本地歌曲
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */
int host_play_local_music(char *ListId,char *MusicName,long int ip)
{
	//先发送播放模式
	host_change_playMode(ip);

	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	unsigned char data[1024]={0};
	int data_len=0;
	data[data_len++]=strlen(ListId);
	int i;
	for(i=0;i<strlen(ListId);i++)
	{
		data[i+data_len]=ListId[i];
	}
	data_len+=strlen(ListId);
	data[data_len++]=strlen(MusicName);
	for(i=0;i<strlen(MusicName);i++)
	{
		data[i+data_len]=MusicName[i];
	}
	data_len+=strlen(MusicName);
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_PLAY_LOCAL_MUSIC,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,ip) != SUCCEED)
		{
			return ERROR;
		}
	}
	return SUCCEED;
}






/*********************************************************************
 * @fn      host_change_playMode
 *
 * @brief   主机向终端请求设置播放模式
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */
int host_change_playMode(long int ip)
{
	printf("\npager change_playMode:%d to ip=%s\n",g_PlayMode,IPIntToChar(ip));
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	unsigned char data[1024]={0};
	int data_len=0;
	data[data_len++]=CMD_SET;
	data[data_len++]=g_PlayMode;
	int i;
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SET_PLAYMODE,DEVICE_MODEL_PAGING_A,data_len,data);
	//加入集中模式/TCP重发结构体
	#if 0
	set_concentrated_pkg_cmd(CMD_SET_PLAYMODE,sendBuf,sendLen);
	#endif
	// 发送数据
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,ip) != SUCCEED)
		{
			return ERROR;
		}
	}
	return SUCCEED;
}



/*********************************************************************
 * @fn      host_play_ring
 *
 * @brief   主机向终端请求播放钟声
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */
int host_play_ring(long int ip)
{
	int i;
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	unsigned char data[1024]={0};
	int data_len=0;
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_PLAY_RING,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,ip) != SUCCEED)
		{
			return ERROR;
		}
	}
	return SUCCEED;
}





/*********************************************************************
 * @fn      send_terminal_set_playstatus
 *
 * @brief   主机控制终端播放状态
 *
 * @param   domain - 主机域名
 *
 * @return	void
 */
int send_terminal_set_playstatus(char playstatus,long int ip)
{
	int i;
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	unsigned char data[1024]={0};
	int data_len=1;
	data[0]=playstatus;
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SET_ZONE_PLAYSTATUS,DEVICE_MODEL_PAGING_A,data_len,data);

	if(ip == g_host_ip)	//需要转发
	{
		set_concentrated_pkg_cmd(CMD_SET_ZONE_PLAYSTATUS,sendBuf,sendLen);
		if(!st_Concentrated_Info.Host_RX_Zone_Flag)	//分区表示不匹配
		{
			printf("\nPAGING_SEND_HOST_PLAY_SOURCE:Zone_Identify not match...\n");
			Paging_Send_ZoneList_To_Host_Concentrated();
		}
	}
	else
	{
		// 发送数据
		for(i=0;i<1;i++)
		{
			if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,ip) != SUCCEED)
			{
				return ERROR;
			}
		}
	}
	return SUCCEED;
}



/*********************************************************************
 * @fn      host_control_reboot
 *
 * @brief   主机控制终端重启
 *
 * @param   char *rxbuf接收包
 *
 * @return	void
 */
int host_control_reboot(char *rxbuf)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	unsigned char data[1024]={0};
	int data_len=0;
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CONTROL_REBOOT,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
	printf("\nhost_control_reboot...\n");
	System_Reboot();
	return SUCCEED;
}




/*********************************************************************
 * @fn      host_control_format
 *
 * @brief   主机向设备请求清除数据
 *
 * @param   char *rxbuf接收包
 *
 * @return	void
 */
int host_control_format(char *rxbuf)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	unsigned char data[1024]={0};
	int data_len=1;
	int i;
	int type=rxbuf[PAYLOAD_START];
	data[0]=type;
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CONTROL_FORMAT,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);

	if(Paging_status == PAGING_START)
	{
		//寻呼中,退出
		return -1;
	}

	printf("host_control_format，type=%d\n",type);
	char cmd[128]={0};
	switch(type)
	{
		case 0xff:		//恢复出厂设置，清除全部
		#if 0
			//清除设备别名
			memset(g_device_alias,0,sizeof(g_device_alias));
			save_device_alias();
			send_online_info();
			
			pthread_mutex_lock(&ZoneInfoMutex);
			memset(&m_stZone_Info,0,sizeof(m_stZone_Info));
			memset(m_stZone_Info.DateTime,0,sizeof(m_stZone_Info.DateTime));
			Save_Zone_Info();
			pthread_mutex_unlock(&ZoneInfoMutex);
			memset(&m_stGroup_Info,0,sizeof(m_stGroup_Info));
			Save_Group_Info();
			
			//清除账户信息
			Delete_AllUser();
			System_Reboot();
		#endif
			sprintf(cmd,"rm %s",BASIC_INI_FILE);
			pox_system(cmd);
			sprintf(cmd,"rm %s",FILE_DEVICE_ALIAS);
			pox_system(cmd);
			sprintf(cmd,"rm %s",MUSICLISTFILE);
			pox_system(cmd);
			sprintf(cmd,"rm %s",USERINFOFILE);
			pox_system(cmd);
			sprintf(cmd,"rm %s",ZONEINFOFILE);
			pox_system(cmd);
			sprintf(cmd,"rm %s",GROUPINFOFILE);
			pox_system(cmd);

			System_Reboot();

			break;
		case 0x01:		//分区数据
			pthread_mutex_lock(&ZoneInfoMutex);
			memset(&m_stZone_Info,0,sizeof(m_stZone_Info));
			memset(m_stZone_Info.DateTime,0,sizeof(m_stZone_Info.DateTime));
			Save_Zone_Info();
			//如果处于集中模式，发送到主机以便获取分区信息
			if(g_system_work_mode == WORK_MODE_CONCENTRATED || g_network_mode == NETWORK_MODE_WAN)
			{
				response_send_host_xml_file_info(NULL,XML_FILE_ZONEINFO);
			}
			pthread_mutex_unlock(&ZoneInfoMutex);

			break;
		case 0x02:		//分组数据
			memset(&m_stGroup_Info,0,sizeof(m_stGroup_Info));
			Save_Group_Info();

			break;
		case 0x03:		//播放列表（包括已同步文件）
			response_cmd_set_zone_idle_status(NULL);


			break;
		case 0x04:		//定时数据
			break;
	}

	return SUCCEED;
}





/*********************************************************************
 * @fn      pkg_set_device_alias
 *
 * @brief   设置本设备别名
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void pkg_set_device_alias(unsigned char *rxbuf)
{
	// 应答

	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	unsigned char data[1024]={0};
	int data_len=0;
	int i;
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SET_ALIAS,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);


	int alias_len = 0;

	// 判断别名长度是否大于0
	if (rxbuf[PAYLOAD_START] == 0)
	{

	}
	else
	{
		alias_len = rxbuf[PAYLOAD_START]; // 获取别名长度
	}

	// 获取别名并保存
	memset(g_device_alias, 0x00, sizeof(g_device_alias));
	for (i=0; i<alias_len; i++)
	{
		g_device_alias[i] = rxbuf[PAYLOAD_START+1+i];
	}
	save_device_alias(); // 保存别名至系统中

	send_online_info();

	printf("pkg_set_device_alias:%s\n", g_device_alias);
}







/*********************************************************************
 * @fn      send_device_reset
 *
 * @brief   超时命令复位
 *
 * @param   void
 *
 * @return	void
 */
int send_device_reset(long int ip)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CONTROL_REBOOT,DEVICE_MODEL_PAGING_A,data_len,data);
	for(i=0;i<1;i++)
	{
		if(UDP_SendData(Send_UDP_RESET,sendBuf,sendLen,ip) != SUCCEED)
		{
			return ERROR;
		}
	}
	return SUCCEED;
}







/*********************************************************************
 * @fn      HOST_QUERY_FLASH_INFO
 *
 * @brief   主机向设备请求查询/设置FLASH信息
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void HOST_QUERY_FLASH_INFO(unsigned char *pkg_data)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=0;
	char flash_info[128]={0};
	Get_FlashInfo(flash_info);
	char space_info[64]={0};

	char flash_info_tmp[128]={0};
	strcpy(flash_info_tmp,flash_info);
	strlwr(flash_info_tmp);		//先转换为小写

	int space=Get_SD_Disk_Space();
	if(strstr(flash_info_tmp,"samsung")!=0 || strstr(flash_info_tmp,"Toshiba")!=0 || strstr(flash_info_tmp,"micron")!=0)
	{
		if(space>=1000)
		{
			float space2=((float)(space))/1000;
			sprintf(space_info,"%.1fGB/4GB",space2);
		}
		else
		{
			sprintf(space_info,"%dMB/4GB",space);
		}
	}
	else if(strstr(flash_info_tmp,"toshiba")!=0)
	{
		sprintf(space_info,"%dMB/256MB",space);
	}
	printf("\nflash_info=%s,space_info=%s\n",flash_info,space_info);

	data[0]=strlen(flash_info);
	for(i=0;i<strlen(flash_info);i++)
	{
		data[1+i]=flash_info[i];
	}
	data[1+strlen(flash_info)]=strlen(space_info);
	for(i=0;i<strlen(flash_info);i++)
	{
		data[2+strlen(flash_info)+i]=space_info[i];
	}

	data_len=2+strlen(flash_info)+strlen(space_info);
	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_QUERY_FLASH_INFO,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
}



/*********************************************************************
 * @fn      HOST_QUERY_MAC_INFO
 *
 * @brief   主机向设备请求查询/设置MAC地址
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void HOST_QUERY_SET_MAC_INFO(unsigned char *pkg_data)
{
	switch (pkg_data[PAYLOAD_START])
	{
		case CMD_QUERY : // 查询
		{
			unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
			int i=0;
			unsigned char data[1024]={0};
			int data_len=0;
			data[0]=6;
			for(i=0;i<6;i++)
			{
				data[1+i]=MAC_ADDR[i];
			}
			data_len=7;
			int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_QUERY_MAC_INFO,DEVICE_MODEL_PAGING_A,data_len,data);
			// 发送数据
			UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
		}
			break;
		case CMD_SET : 	//设置
		{
			unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
			int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_QUERY_MAC_INFO,DEVICE_MODEL_PAGING_A,0,NULL);
			UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);

			int mac_len=pkg_data[9];
			int i;
			//第一个字节固定00
			for(i=1;i<mac_len;i++)
			{
				MAC_ADDR[i]=pkg_data[10+i];
			}
			printf("\nMAC=%x:%x:%x:%x:%x:%x\n",MAC_ADDR[0],MAC_ADDR[1],MAC_ADDR[2],MAC_ADDR[3],MAC_ADDR[4],MAC_ADDR[5]);
			FILE *fp_macaddr;

			set_mac_address(); // 设置MAC地址

			 /*重启以便重新获取IP*/
			System_Reboot();
		}
			break;
		default :
			break;
	}
}







/*********************************************************************
 * @fn      HOST_QUERY_TIME_INFO
 *
 * @brief   主机向设备请求查询设备日期时间
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void HOST_QUERY_TIME_INFO(unsigned char *pkg_data)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=20+1;
	char date_buf[20]={0};
	sprintf(date_buf,"%04d-%02d-%02d",CURRENT_TIME.year,CURRENT_TIME.mon,CURRENT_TIME.day);
	char time_buf[20]={0};
	sprintf(time_buf,"%02d:%02d:%02d",CURRENT_TIME.hour,CURRENT_TIME.min,CURRENT_TIME.sec);
	data[0]=10;
	for(i=0;i<10;i++)
	{
		data[1+i]=date_buf[i];
	}
	data[11]=8;
	for(i=0;i<8;i++)
	{
		data[12+i]=time_buf[i];
	}
	data[20]=0;

	printf("\nHOST_QUERY_TIME_INFO:date_buf=%s,time_buf=%s\n",date_buf,time_buf);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_QUERY_TIME_INFO,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
}









//此处启动寻呼失败提示线程(1s后刷新，防止卡顿)
void *P_Paging_Fail_Check_Thread()
{
	Paging_Fail_Check_Thread_flag=1;
	sleep(1);
	if(Paging_status == PAGING_START)
	{
		//发消息给主线程，弹出窗口提示分区寻呼失败

	}
	Paging_Fail_Check_Thread_flag=0;
	pthread_exit(NULL);
}


int Paging_Fail_Check_Thread()
{
	if(Paging_Fail_Check_Thread_flag)
		return ERROR;

	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)P_Paging_Fail_Check_Thread, NULL);
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}







/*********************************************************************
 * @fn      respond_null_payload_pkg
 *
 * @brief   空负载应答包
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void respond_null_payload_pkg(unsigned char *rxbuf,long int ip)
{
	int payloadSize = 0;
	unsigned char send_buf[1500];
	unsigned char client;
	memset(send_buf, 0x00, sizeof(send_buf)); // 清空缓存

	client=rxbuf[4];
	// 添加命令字
	send_buf[0] = rxbuf[0];
	send_buf[1] = rxbuf[1];
	// 添加包序号
	send_buf[2] = rxbuf[2];
	// 保留位
	send_buf[3] = 0;
	// 产品型号
	send_buf[4] = DEVICE_MODEL_PAGING_A;
	// 包属性及编码格式
	send_buf[5] = 0;

	// 添加负载数据长度
	send_buf[6] = payloadSize/256;
	send_buf[7] = payloadSize%256;

	// 计算校验和
	send_buf[payloadSize+PAYLOAD_START] = Calculate_XorDat(&send_buf[PAYLOAD_START], payloadSize);

	if(client == DEVICE_MODEL_HOST || client == DEVICE_MODEL_NETWORK_TOOLS)
	{
		// 发送数据
		UDP_SendData(Send_UNICAST,send_buf,payloadSize+9,g_host_ip);
	}
}








/*********************************************************************
 * @fn      HOST_SEND_AUDIO_COLLECTOR_DEVICE_LIST
 *
 * @brief   主机向其他控制设备下发音频采集器设备列表
 *
 * @param   rxbuf - 接收缓存
 *
 * @return	void
 */
void HOST_SEND_AUDIO_COLLECTOR_DEVICE_LIST(unsigned char *rxbuf)
{
	int i,k;
	respond_null_payload_pkg(rxbuf,0);

	int pos=PAYLOAD_START;
	//获取音频采集器总数
	Audio_collector_list.totalNum= rxbuf[pos++];
	printf("\nHOST_SEND_AUDIO_COLLECTOR_DEVICE_LIST1,totalNum=%d\n",Audio_collector_list.totalNum);
	//分配内存
	if(Audio_collector_list.list!=NULL)
	{
		free(Audio_collector_list.list);
		Audio_collector_list.list=NULL;
	}
	if(Audio_collector_list.totalNum>0)
	{
		Audio_collector_list.list=calloc(Audio_collector_list.totalNum,sizeof(struct s_audio_collector_info));
	}
	for(i=0;i<Audio_collector_list.totalNum;i++)
	{
		Audio_collector_list.list[i].id=rxbuf[pos++];
		int NameLen=rxbuf[pos++];
		memcpy(Audio_collector_list.list[i].name,&rxbuf[pos++],NameLen);
		pos+=NameLen;
		printf("\nAudio_collector_list[%d].name=%s\n",i,Audio_collector_list.list[i].name);
	}
}





void Get_Ip_Info(char *buf, int *buf_temp)
{
	char *p = NULL;
	p = (char*)strtok(buf, ".");
	int k = 0;
	if(p != NULL)
	{
		buf_temp[k++] = atoi(p);
		while((p = strtok(NULL, ".")) != NULL)
		{
			buf_temp[k++] = atoi(p);
		}
	}
}

/*********************************************************************
 * @fn      HOST_QUERY_SET_IP_INFO
 *
 * @brief   //主机向终端设置IP属性
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void HOST_QUERY_SET_IP_INFO(unsigned char *pkg_data)
{
	//respond_null_payload_pkg(pkg_data);
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=0;
	int sendLen=0;
	int pos=PAYLOAD_START+6;
	// 发送数据
	unsigned char client=pkg_data[4];

	int payload_len=(pkg_data[6]<<8)+pkg_data[7];
#if 0
	printf("IpInfo pkg:");
	for(i=0;i<payload_len;i++)
	{
		printf("0x%x ",pkg_data[PAYLOAD_START+i]);
	}
	printf("\n");
#endif
	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(i=0;i<6;i++)
	{
		if( pkg_data[PAYLOAD_START+i] != MAC_ADDR[i] )
		{
			mac_match=0;
			printf("\nHOST_QUERY_SET_IP_INFO:MAC not match,return.");
			break;
		}
	}
	if(!mac_match)
		return;

	switch (pkg_data[PAYLOAD_START+6])
	{
		case CMD_QUERY : // 查询
		{
			//IP分配方式
			for(i=0;i<6;i++)
			{
				data[i]=MAC_ADDR[i];
			}
			data[6]=g_IP_Assign;
			if(g_IP_Assign == IP_ASSIGN_DHCP)
			{
				data_len=7;
			}
			else
			{
				//静态IP地址
				data_len=7;
				data[data_len++] = strlen(g_Static_ip_address);
				memcpy(&data[data_len],g_Static_ip_address,strlen(g_Static_ip_address));
				data_len+=strlen(g_Static_ip_address);
				//子网掩码
				data[data_len++] = strlen(g_Subnet_Mask);
				memcpy(&data[data_len],g_Subnet_Mask,strlen(g_Subnet_Mask));
				data_len+=strlen(g_Subnet_Mask);
				//网关
				data[data_len++] = strlen(g_GateWay);
				memcpy(&data[data_len],g_GateWay,strlen(g_GateWay));
				data_len+=strlen(g_GateWay);
				//主DNS服务器
				data[data_len++] = strlen(g_Primary_DNS);
				memcpy(&data[data_len],g_Primary_DNS,strlen(g_Primary_DNS));
				data_len+=strlen(g_Primary_DNS);
				//备用DNS
				data[data_len++] = strlen(g_Alternative_DNS);
				memcpy(&data[data_len],g_Alternative_DNS,strlen(g_Alternative_DNS));
				data_len+=strlen(g_Alternative_DNS);
			}

			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_SET_IP_INFO,DEVICE_MODEL_PAGING_A,data_len,data);
			host_udp_multicast_send_cmd_data(sendBuf, sendLen);

			// 再通过单播发送给服务器
			UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);

		}
		break;

		case CMD_SET : 	// 设置
		{
			pos++;
			int temp_len=0;
			//IP分配方式
			int IP_Assign_temp=pkg_data[pos++];
			printf("\nIP_Assign_temp=%d\n",IP_Assign_temp);
			//发送应答

			if(IP_Assign_temp == g_IP_Assign && g_IP_Assign == IP_ASSIGN_DHCP)	//都为DHCP
			{
				printf("\nIP_Assign_temp=g_IP_Assign=IP_ASSIGN_DHCP.\n");
				break;
			}

			char TempStatic_ip_address[32]={0};
			char TempSubnet_Mask[32]={0};
			char TempGateWay[32]={0};
			char TempPrimary_DNS[32]={0};
			char TempAlternative_DNS[32]={0};

			if(IP_Assign_temp == IP_ASSIGN_STATIC)
			{
				//静态IP地址
				temp_len=pkg_data[pos++];
				memset(TempStatic_ip_address,0,sizeof(TempStatic_ip_address));
				memcpy(TempStatic_ip_address,&pkg_data[pos],temp_len);
				printf("g_Static_ip_address=%s,temp_len=%d\n",TempStatic_ip_address,temp_len);
				pos+=temp_len;
				//子网掩码
				temp_len=pkg_data[pos++];
				memset(TempSubnet_Mask,0,sizeof(TempSubnet_Mask));
				memcpy(TempSubnet_Mask,&pkg_data[pos],temp_len);
				printf("g_Subnet_Mask=%s,temp_len=%d\n",TempSubnet_Mask,temp_len);
				pos+=temp_len;
				//网关
				temp_len=pkg_data[pos++];
				memset(TempGateWay,0,sizeof(TempGateWay));
				memcpy(TempGateWay,&pkg_data[pos],temp_len);
				printf("g_GateWay=%s,temp_len=%d\n",TempGateWay,temp_len);
				pos+=temp_len;
				//主DNS服务器
				temp_len=pkg_data[pos++];
				memset(TempPrimary_DNS,0,sizeof(TempPrimary_DNS));
				memcpy(TempPrimary_DNS,&pkg_data[pos],temp_len);
				printf("g_Primary_DNS=%s,temp_len=%d\n",TempPrimary_DNS,temp_len);
				pos+=temp_len;
				//备用DNS
				temp_len=pkg_data[pos++];
				memset(TempAlternative_DNS,0,sizeof(TempAlternative_DNS));
				memcpy(TempAlternative_DNS,&pkg_data[pos],temp_len);
				printf("g_Alternative_DNS=%s,temp_len=%d\n",TempAlternative_DNS,temp_len);
				pos+=temp_len;

				//检测IP、掩码、网关是否输入错误
				if(!NET_INFO_ERROR(TempStatic_ip_address, TempSubnet_Mask, TempGateWay) && \
					!isValidIP_GatewayByNetmask(TempStatic_ip_address,TempSubnet_Mask,TempGateWay))
				{
					printf("Net parameter check ok!\n");
				}
				else
				{
					//长度或内容错误,返回错误
					return;
				}

				if(strcmp(g_Static_ip_address, TempStatic_ip_address) == 0 && strcmp(g_Subnet_Mask, TempSubnet_Mask) == 0 && strcmp(g_GateWay, TempGateWay) == 0 &&\
					(g_IP_Assign == IP_Assign_temp) &&\
					strcmp(g_Primary_DNS, TempPrimary_DNS) == 0 && strcmp(g_Alternative_DNS, TempAlternative_DNS) == 0)
				{
					printf("HOST_QUERY_SET_IP_INFO:No parameter changed.%d\n", g_IP_Assign);
					return;
				}

				memcpy(g_Static_ip_address, TempStatic_ip_address, 32);
				memcpy(g_Subnet_Mask, TempSubnet_Mask, 32);
				memcpy(g_GateWay,TempGateWay,32);

				//DNS
				if(if_a_string_is_a_valid_ipv4_address(TempPrimary_DNS))
				{
					memcpy(g_Primary_DNS,TempPrimary_DNS,32);
				}
				else
				{
					memset(g_Primary_DNS,0,sizeof(g_Primary_DNS));
				}
				if(if_a_string_is_a_valid_ipv4_address(TempAlternative_DNS))
				{
					memcpy(g_Alternative_DNS,TempAlternative_DNS,32);
				}
				else
				{
					memset(g_Alternative_DNS,0,sizeof(g_Alternative_DNS));
				}

			}

			g_IP_Assign = IP_Assign_temp;
			if(g_network_mode == NETWORK_MODE_LAN)
			{
				Send_Unonline_Info();

				//设置成功，发送给主机
				memcpy(data, MAC_ADDR, 6);
				data[6]=g_IP_Assign;
				if(g_IP_Assign == IP_ASSIGN_DHCP)
				{
					data_len=7;
				}
				else
				{
					//静态IP地址
					data_len=7;
					data[data_len++] = strlen(g_Static_ip_address);
					memcpy(&data[data_len],g_Static_ip_address,strlen(g_Static_ip_address));
					data_len+=strlen(g_Static_ip_address);
					//子网掩码
					data[data_len++] = strlen(g_Subnet_Mask);
					memcpy(&data[data_len],g_Subnet_Mask,strlen(g_Subnet_Mask));
					data_len+=strlen(g_Subnet_Mask);
					//网关
					data[data_len++] = strlen(g_GateWay);
					memcpy(&data[data_len],g_GateWay,strlen(g_GateWay));
					data_len+=strlen(g_GateWay);
					//主DNS服务器
					data[data_len++] = strlen(g_Primary_DNS);
					memcpy(&data[data_len],g_Primary_DNS,strlen(g_Primary_DNS));
					data_len+=strlen(g_Primary_DNS);
					//备用DNS
					data[data_len++] = strlen(g_Alternative_DNS);
					memcpy(&data[data_len],g_Alternative_DNS,strlen(g_Alternative_DNS));
					data_len+=strlen(g_Alternative_DNS);
				}

				sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_SET_IP_INFO,DEVICE_MODEL_PAGING_A,data_len,data);
				host_udp_multicast_send_cmd_data(sendBuf, sendLen);

				// 再通过单播发送给服务器
				UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
			}

			save_sysconf("Network",NULL);

			//重启生效
			System_Reboot();
		}
		break;
	}
}


/*********************************************************************
 * @fn      Host_Set_Dsp_Firmware_Feature
 *
 * @brief   主机或配置工具向终端查询/设置DSP固件功能特性（组播）
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void Host_Set_Dsp_Firmware_Feature(unsigned char *pkg_data)
{
#if defined(USE_SSD212) || defined(USE_SSD202)
	
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int pos=PAYLOAD_START+6;
	// 发送数据
	unsigned char client=pkg_data[4];

	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(i=0;i<6;i++)
	{
		if( pkg_data[PAYLOAD_START+i] != MAC_ADDR[i] )
		{
			mac_match=0;
			//Dbg("Host_Set_Dsp_Firmware_Feature:MAC not match,return.\r\n");
			break;
		}
	}

	if(!mac_match)
		return;

	switch (pkg_data[PAYLOAD_START+6])
	{
		case CMD_QUERY : // 查询
		{
			printf("Query dsp feature!\n");
			memcpy(data,MAC_ADDR,6);
			data_len+=6;
			for(i=0;i<DSP_AUDIO_MODULE_MAX;i++)
			{
				data[data_len++] = dsp_firmware_feature.module_switch[i];
				data[data_len++] = dsp_firmware_feature.module_gain[i]>>8;
				data[data_len++] = dsp_firmware_feature.module_gain[i];
			}
			
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_DSP_FIRMWARE_FEATURE,DEVICE_MODEL_PAGING_A,data_len,data);
			host_udp_multicast_send_cmd_data(sendBuf, sendLen);
		}
		break;

		case CMD_SET : // 设置
		{
			printf("Set dsp feature!\n");
		  	pos=PAYLOAD_START+7;
		  	for(i=0;i<DSP_AUDIO_MODULE_MAX;i++)
			{
				dsp_firmware_feature.module_switch[i] = pkg_data[pos];
				dsp_firmware_feature.module_gain[i] = (pkg_data[pos+1]<<8) + (pkg_data[pos+2]);
#if 0
				if(dsp_firmware_feature.module_gain[i]>0x3FFF)
				  dsp_firmware_feature.module_gain[i]=0x3FFF;
				else if(dsp_firmware_feature.module_gain[i]<0x3FF)
				  dsp_firmware_feature.module_gain[i]=0x3FF;
#endif
				pos+=3;
			}
			//dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L] = 1;
			dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_L] = 1;
			dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_R] = 1;
		  
			for(i = 0;i< DSP_AUDIO_MODULE_MAX ;i++)
			{
				printf("Module[%d]:switch=%d,gain=%d\n",i,dsp_firmware_feature.module_switch[i],dsp_firmware_feature.module_gain[i]);
			}
	
		  	memcpy(data,MAC_ADDR,6);
			data_len+=6;
			for(i=0;i<DSP_AUDIO_MODULE_MAX;i++)
			{
				data[data_len++] = dsp_firmware_feature.module_switch[i];
				data[data_len++] = dsp_firmware_feature.module_gain[i]>>8;
				data[data_len++] = dsp_firmware_feature.module_gain[i];
			}
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_DSP_FIRMWARE_FEATURE,DEVICE_MODEL_PAGING_A,data_len,data);
			host_udp_multicast_send_cmd_data(sendBuf, sendLen);
		  
			//保存配置信息
			save_sysconf(INI_SETCION_DSP_Firmware,NULL);
			
			#if defined(USE_SSD212) || defined(USE_SSD202)
			Get_Screen_Rotations_Value();
			Get_Screen_LCD_type_Value(true);
			#endif

			//重启生效
			System_Reboot();
		}
		break;
	}

#endif
}

/*********************************************************************
 * @fn      Host_Set_SerialNumber
 *
 * @brief   //主机或配置工具向终端获取设备序列号（组播)
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void Host_Set_SerialNumber(unsigned char *pkg_data)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	unsigned char data[128]={0};
	int data_len=0;
	int sendLen=0;
	int pos=PAYLOAD_START+6;
	// 发送数据
	unsigned char client=pkg_data[4];

	//判断是否为分区的MAC，否则退出
	int mac_match=1;
	for(i=0;i<6;i++)
	{
		if( pkg_data[PAYLOAD_START+i] != MAC_ADDR[i] )
		{
			mac_match=0;
			break;
		}
	}

	if(!mac_match)
		return;

	switch (pkg_data[PAYLOAD_START+6])
	{
		case CMD_QUERY : // 查询
		{
			printf("Query SerialNumber!\r\n");
			memcpy(data,MAC_ADDR,6);
			data_len+=6;
			data[data_len++]=pkg_data[PAYLOAD_START+6];
			int sn_len=strlen(g_device_serialNum);
			data[data_len++]=sn_len;
			memcpy(data+data_len,g_device_serialNum,sn_len);
			data_len+=sn_len;
			
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_SERIAL_NUMBER,DEVICE_MODEL_PAGING_A,data_len,data);
			host_udp_multicast_send_cmd_data(sendBuf, sendLen);
		}
		break;

		case CMD_SET : // 设置
		{
			pos=PAYLOAD_START+7;
			int sn_len=pkg_data[pos++];
			if(sn_len>16)
			{
				break;
			}
			memset(g_device_serialNum,0,sizeof(g_device_serialNum));
			memcpy(g_device_serialNum,pkg_data+pos,sn_len);
			pos+=sn_len;
			
			printf("Set SerialNumber=%s\r\n",g_device_serialNum);
			//应答
			memcpy(data,MAC_ADDR,6);
			data_len+=6;
			data[data_len++]=pkg_data[PAYLOAD_START+6];
			data[data_len++]=sn_len;
			memcpy(data+data_len,g_device_serialNum,sn_len);
			data_len+=sn_len;
			
			sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_SERIAL_NUMBER,DEVICE_MODEL_PAGING_A,data_len,data);
			host_udp_multicast_send_cmd_data(sendBuf, sendLen);

			save_sysconf("Manufacturer","SN");
			//刷新设备信息页SN码
			refresh_deviceInfo();
		}
		break;
	}
}







/*********************************************************************
 * @fn      HOST_QUERY_SET_WORK_MODE
 *
 * @brief   主机向设备查询/设置工作模式
 *
 * @param   pkg_data - 包数据
 *
 * @return  none
 */
void HOST_QUERY_SET_WORK_MODE(unsigned char *pkg_data)
{
	switch (pkg_data[PAYLOAD_START])
	{
		case CMD_QUERY : // 查询
		{
			printf("\nQUERY work_mode=%d\n",g_system_work_mode);
			unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
			int i=0;
			unsigned char data[1024]={0};
			int data_len=1;
			data[0]=g_system_work_mode;
			int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_QUERY_SET_WORK_MODE,DEVICE_MODEL_PAGING_A,data_len,data);
			// 发送数据
			unsigned char client=pkg_data[4];
			UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
		}
			break;
		case CMD_SET : 	//设置
		{
			respond_null_payload_pkg(pkg_data,g_host_ip);

			int work_mode=pkg_data[PAYLOAD_START+1];
			printf("\nSET work_mode=%d\n",work_mode);
			if(g_system_work_mode == work_mode)
			{
				printf("g_system_work_mode=work_mode,return\n");
				break;
			}
			g_system_work_mode=work_mode;
			//LOG("host set work mode=%d",g_system_work_mode);
			//清除现有所有分区
			pthread_mutex_lock(&ZoneInfoMutex);
			memset(&st_Concentrated_Info,0,sizeof(st_Concentrated_Info));
			memset(&m_stZone_Info,0,sizeof(m_stZone_Info));
			memset(m_stZone_Info.DateTime,0,sizeof(m_stZone_Info.DateTime));
			Save_Zone_Info();
			//如果处于集中模式，发送到主机以便获取分区信息
			if(g_system_work_mode == WORK_MODE_CONCENTRATED || g_network_mode == NETWORK_MODE_WAN)
			{
				//连接上服务器,刷新设置-设备信息
				refresh_deviceInfo();
				//发送账户信息
				SendTo_Host_Account_Info();

				response_send_host_xml_file_info(NULL,XML_FILE_ZONEINFO);
				response_send_host_xml_file_info(NULL,XML_FILE_GROUP);
				response_send_host_xml_file_info(NULL,XML_FILE_MUSICLIST);
				response_send_host_xml_file_info(NULL,XML_FILE_AUDIO_COLLECTOR);
				response_send_host_xml_file_info(NULL,XML_FILE_USER);
				#if ENABLE_CALL_FUNCTION
				response_send_host_xml_file_info(NULL,XML_FILE_PAGER);
				#endif
			}
			pthread_mutex_unlock(&ZoneInfoMutex);

			send_online_info();
		}
			break;
		default :
			break;
	}
}





/*********************************************************************
 * @fn      Paging_Send_ZoneList_To_Host_Concentrated
 *
 * @brief   寻呼台/移动设备/分控设备向主机发送选中的分区（集中模式）
 *
 * @param   none
 *
 * @return  none
 */
void Paging_Send_ZoneList_To_Host_Concentrated()
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i,z=0;
	unsigned char data[CLIENT_BUF_SIZE]={0};
	int data_len=0;

	//判断总共需要发送几次
	int send_times,zone_num=0;
	if(st_Concentrated_Info.Zone_Num%200 > 0)
	{
		send_times=st_Concentrated_Info.Zone_Num/200 + 1;
	}
	else
	{
		send_times=st_Concentrated_Info.Zone_Num/200;
	}

	for(z=1;z<=send_times;z++)
	{
		zone_num=0;
		memset(data,0,sizeof(data));
		data[0]=send_times;							//选中分区包总数
		data[1]=z;									//选中分区包ID
		for(i=200*(z-1);i<st_Concentrated_Info.Zone_Num && zone_num<200*z;i++)
		{
			zone_num++;
		}
		memcpy(&data[3],st_Concentrated_Info.Zone_MAC+200*(z-1)*6,6*zone_num);	//每个分区的MAC
		data[2]=zone_num;							//此包所包含的分区总数

		data_len=3+6*zone_num;

		int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_PAGING_SEND_HOST_SELECTED_ZONE,DEVICE_MODEL_PAGING_A,data_len,data);
		// 发送数据
		UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
		printf("\nPaging_Send_ZoneList_To_Host_Concentrated:Zone_Num=%d,identify=%d\n",st_Concentrated_Info.Zone_Num,st_Concentrated_Info.Zone_Identify);
	}
}





/*********************************************************************
 * @fn      PAGING_SEND_HOST_PLAY_SOURCE
 *
 * @brief   寻呼机/移动设备/分控软件请求主机播放节目源（集中模式）
 *
 * @param   int type,type=1:点播歌曲 type=2:播放钟声
 *
 * @return  none
 */
void PAGING_SEND_HOST_PLAY_SOURCE(int type,char *ListId,char *MusicName)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[0]=type;
	data_len = 1;
	if(type == 1)	//播放音乐
	{
		data[data_len++]=strlen(ListId);
		for(i=0;i<strlen(ListId);i++)
		{
			data[i+data_len]=ListId[i];
		}
		data_len+=strlen(ListId);
		data[data_len++]=strlen(MusicName);
		for(i=0;i<strlen(MusicName);i++)
		{
			data[i+data_len]=MusicName[i];
		}
		data_len+=strlen(MusicName);
	}

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_PAGING_SEND_HOST_PLAY_SOURCE,DEVICE_MODEL_PAGING_A,data_len,data);

	//保存集中模式-最后一次播放歌曲信息
	sprintf(st_Concentrated_Info.Last_play_ListId,ListId);
	sprintf(st_Concentrated_Info.Last_play_MusicName,MusicName);

	//加入集中模式/TCP重发结构体
	set_concentrated_pkg_cmd(CMD_PAGING_SEND_HOST_PLAY_SOURCE,sendBuf,sendLen);

}




/*********************************************************************
 * @fn      Host_Response_ZoneList_Concentrated
 *
 * @brief   主机应答设置分区信息命令(集中模式)
 *
 * @param   none
 *
 * @return  none
 */
void Host_Response_ZoneList_Concentrated(unsigned char *pkg_data)
{
	int Zone_Identify=pkg_data[3];	//保留字段
	int pkg_id=pkg_data[8];
	if(pkg_id<1 || pkg_id>3)
	{
		printf("Host_Response_ZoneList_Concentrated:pkg_id<1 || pkg_id>3,error!\n");
		return;
	}

	int i;
	printf("\nHost_Response_ZoneList_Concentrated:RX_Zone_Identify,Real=%d\n",Zone_Identify,st_Concentrated_Info.Zone_Identify);
	if(Zone_Identify == st_Concentrated_Info.Zone_Identify)
	{
		st_Concentrated_Info.Host_respone_PkgId[pkg_id-1] = 1;
		//判断总共有几个包
		int total_pkgs=0;
		if(st_Concentrated_Info.Zone_Num%200 > 0)
		{
			total_pkgs=st_Concentrated_Info.Zone_Num/200 + 1;
		}
		else
		{
			total_pkgs=st_Concentrated_Info.Zone_Num/200;
		}
		int match_id=0;
		switch(total_pkgs)
		{
			case 1:
				if(st_Concentrated_Info.Host_respone_PkgId[0])
					match_id=1;
			break;
			case 2:
				if(st_Concentrated_Info.Host_respone_PkgId[0] && st_Concentrated_Info.Host_respone_PkgId[1])
					match_id=1;
			break;
			case 3:
				if(st_Concentrated_Info.Host_respone_PkgId[0] && st_Concentrated_Info.Host_respone_PkgId[1] && st_Concentrated_Info.Host_respone_PkgId[2] )
					match_id=1;
			break;
		}

		if(match_id)
		{
			printf("\nHost_Response_ZoneList_Concentrated,match...\n");
			st_Concentrated_Info.Host_RX_Zone_Flag=1;
			//匹配，发送命令字
			for(i=0;i<MAX_CONCENTRATED_CMD;i++)
			{
				if( st_Concentrated_Info.Last_CMD[i] )	//存在命令字
				{
					UDP_SendData(Send_UNICAST,st_Concentrated_Info.Last_CMD_Pkg_Data[i],st_Concentrated_Info.Last_CMD_Pkg_len[i],g_host_ip);
				}
			}
		}
	}
	else
	{
		printf("\nHost_Response_ZoneList_Concentrated,not match...\n");
		st_Concentrated_Info.Host_RX_Zone_Flag=0;
		memset(st_Concentrated_Info.Host_respone_PkgId,0,sizeof(st_Concentrated_Info.Host_respone_PkgId));
		Paging_Send_ZoneList_To_Host_Concentrated();
	}
}



/*********************************************************************
 * @fn      Host_Response_Play_Music_Concentrated
 *
 * @brief   主机应答播放音乐(集中模式)
 *
 * @param   none
 *
 * @return  none
 */
void Host_Response_Play_Music_Concentrated(unsigned char *pkg_data)
{
	printf("\nHost_Response_Play_Music_Concentrated\n");
	int Zone_Identify=pkg_data[3];	//保留字段
	int cmd_identify=(pkg_data[0]<<8)+pkg_data[1];
	if(Zone_Identify == st_Concentrated_Info.Zone_Identify )
	{
#if 0
		pthread_mutex_lock(&MusicListMutex);
		//保存是否播放成功
		//根据最后一次播放列表找出歌曲所在列表索引
		int i,k;
		int found_song=0;
		for(i=0;i<serverMusicList.DirNum;i++)
		{
			if( strcmp(serverMusicList.SongDir[i].id,st_Concentrated_Info.Last_play_ListId) == 0 )
			{
				for(k=0;k<serverMusicList.SongDir[i].nFileNum;k++)
				{
					if( strcmp(serverMusicList.SongDir[i].musicfile[k].cName,st_Concentrated_Info.Last_play_MusicName) == 0 )
					{
						found_song=1;
						break;
					}
				}
				if(found_song)
					break;
			}
		}
		if(found_song)
		{
			serverMusicList.SongDir[i].musicfile[k].IsDownload=pkg_data[PAYLOAD_START];
			if(serverMusicList.SongDir[i].musicfile[k].IsDownload != 1)
			{

			}
		}
		pthread_mutex_unlock(&MusicListMutex);
#endif
		printf("\nHost_Response_Play_Music_Concentrated,OK\n");
	}
}



/*********************************************************************
 * @fn      Host_Response_SET_Play_Mode_Concentrated
 *
 * @brief   主机应答/设置播放模式(集中模式)
 *
 * @param   none
 *
 * @return  none
 */
void Host_Response_SET_Play_Mode_Concentrated(unsigned char *pkg_data)
{
	//需要判断是主机应答还是主动设置
	int payload=(pkg_data[6]<<8)+pkg_data[7];
	printf("\nHost_Response_SET_Play_Mode_Concentrated:payload=%d\n",payload);
	if(payload == 0)//应答
	{
		 //st_Concentrated_Info.Host_RX_PLAYMODE_Flag=1;
	}
	else if(payload == 2)
	{
		if( pkg_data[9]>0 && pkg_data[9]<6 )
		{
			//账户存在播放模式，那么不接受全局的播放模式(管理员除外，为了快速更新，也接收)
			pthread_mutex_lock(&UserInfoMutex);
			if( strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME)!=0 && m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].playmode!=0 )
			{

			}
			else
			{
				if( g_PlayMode != pkg_data[9] )
				{
					g_PlayMode=pkg_data[9];
					save_sysconf("MusicPlay","PlayMode");
					MusicList_Win_PlayMode_change(g_PlayMode,0);
					printf("\nHost_Response_SET_Play_Mode_Concentrated:Set g_PlayMode=%d\n",g_PlayMode);
				}
			}
			pthread_mutex_unlock(&UserInfoMutex);
		}
		respond_null_payload_pkg(pkg_data,g_host_ip);
	}
}



/****************************************************
 * @fn      SendTo_Host_Account_Info
 *
 * @brief   //控制设备主机发送账户信息
 *
 * @param
 *
 * @return	int 应答包长度
 */

int SendTo_Host_Account_Info()
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0;
	if( !IsValidWin(WIN_CONTROL) )
	{
		return ERROR;
	}
	pthread_mutex_lock(&UserInfoMutex);

	if( m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].playmode!=0 )
	{
		if(	g_PlayMode != m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].playmode)
		{
			g_PlayMode=m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].playmode;
		}
	}

	int account_len=strlen(m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].name);
	int data_len=1+account_len;
	unsigned char data[128]={0};
	data[0]=account_len;
	memcpy(data+1,m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].name,account_len);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_HOST_ACCOUNT,DEVICE_MODEL_PAGING_A,data_len,data);

	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		pthread_mutex_unlock(&UserInfoMutex);
		return ERROR;
	}
	
	pthread_mutex_unlock(&UserInfoMutex);
	return SUCCEED;
}







/*********************************************************************
 * @fn      host_udp_multicast_send_cmd_data
 *
 * @brief   向UDP客户端发送组播命令字数据
 *
 * @param   udp_buf - 发送缓存
 *			send_len - 发送数据长度
 *
 * @return  none
 */
void host_udp_multicast_send_cmd_data(unsigned char *udp_buf, int send_len)
{
	int i;
	int txLen;

	/*向客户端发送应答数据*/
	if (send_len < 9)
	{
		return;
	}
	else
	{
		struct sockaddr_in multicast_cmd_socketAddr;
		multicast_cmd_socketAddr.sin_family = AF_INET; 		//选用TCP/IP协议
		multicast_cmd_socketAddr.sin_port = htons(MULTI_SOCKET_RECV_COMMAND_PORT);//注册端口
		inet_pton(AF_INET, MULTI_SOCKET_RECV_COMMAND_ADDR, &multicast_cmd_socketAddr.sin_addr);
		//UnicastsocketAddr.sin_addr.s_addr = inet_addr(MULTI_SOCKET_SEND_COMMAND_ADDR);

		txLen = sendto(multi_socket_send_command, udp_buf, send_len, 0, (struct sockaddr *)&multicast_cmd_socketAddr, sizeof(struct sockaddr_in));
		
		//新增发往新的配置工具
		multicast_cmd_socketAddr.sin_port = htons(MULTI_SOCKET_NETTOOLS_SEND_COMMAND_PORT);//注册端口
		inet_pton(AF_INET, MULTI_SOCKET_NETTOOLS_SEND_COMMAND_ADDR, &multicast_cmd_socketAddr.sin_addr);
		txLen = sendto(multi_socket_send_command, udp_buf, send_len, 0, (struct sockaddr *)&multicast_cmd_socketAddr, sizeof(struct sockaddr_in));
		
		
		if (txLen < 0)
		{
				perror("host_udp_multicast_send_cmd_data");
		}
		else
		{
			 printf("host_udp_multicast_send_cmd_data!\n");
#if UDP_DEBUG
			for(i=0; i<send_len; i++)
			{
				if(g_paging_status != PAGING_START) printf("0x%02x ", udp_buf[i]);
			}
			if(g_paging_status != PAGING_START) printf("\n\n");
#endif
		}
	}
}








/****************************************************
 * @fn      TCP_HANDLE_CMD_RECV_ZONE_DETAIL_INFO
 *
 * @brief	TCP或集中模式下接收主机下发的分区状态命令 0x0042
 *
 * @param
 *
 * @return	int 应答包长度
 */
int TCP_HANDLE_CMD_RECV_ZONE_DETAIL_INFO(unsigned char *Pkg)
{
	int i,k,t;
	int pos=8;	//Pkg读指针
	int zone_num=0;				//分区数
	unsigned char zone_mac[6*MAX_ZONE_NUM]={0};	//分区mac数组
	int zone_volume=0;			//分区音量
	int zone_source=0;			//分区音源
	int zone_playStatus=0;			//分区播放状态
	int zone_media_name_length=0;	//分区节目名称长度
	char zone_media_name[64]={0};	//分区节目名称

	int zone_Timer_Valid=0;			//分区定时点是否有效(寻呼台不需要处理)
	unsigned char deviceFeature=0;	//设备特性
	int zone_Sync_Status=0;			//分区同步状态（寻呼台不需要处理）
	ZONEINFO_DETAIL zoneInfo;		//分区信息

	zone_num=Pkg[pos++];	//分区总数
	printf("ZONE_DETAIL_INFO:count=%d\n",zone_num);

	//UDP模式下需要应答
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		respond_null_payload_pkg(Pkg,g_host_ip);
		if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL)
			return -1;
	}

	for(i=0;i<zone_num;i++)
	{
		memcpy(zone_mac+6*i,&Pkg[pos],6);	//分区MAC
		pos+=6;

		//判断MAC是否存在
		int found=0;
		int index=-1;

		for(t=0;t<m_stZone_Info.ExistTotalZone;t++)
		{
			for(k=0;k<6;k++)
			{
				if(	m_stZone_Info.zoneInfo[t].g_zone_mac[k]	!= zone_mac[k+6*i] )
				{
					break;
				}
			}
			if(k == 6)
			{
				found=1;
				index=t;
				break;
			}
		}

		zone_volume=Pkg[pos++];
		zone_source=Pkg[pos++];
		zone_playStatus=Pkg[pos++];
		zone_media_name_length=Pkg[pos++];
		memset(zone_media_name,0,sizeof(zone_media_name));
		memcpy(zone_media_name,&Pkg[pos],zone_media_name_length>=63?63:zone_media_name_length);
		pos+=zone_media_name_length;
		zone_Timer_Valid=Pkg[pos++];
		deviceFeature=Pkg[pos++];
		zone_Sync_Status=Pkg[pos++];
#if 0		//暂不处理
		int payload_len=(Pkg[6]<<8)+Pkg[7];
		if(pos == payload_len+8)		//如果此处没有多余的负载数据，证明没有网络模式字段下发（以前的主机版本）
		{
			printf("No NetworkMode info.\n");
		}
		else
		{
			int networkMode=Pkg[pos++];
			pos++;
		}
#endif	
		if(!found)	//不存在此分区时不处理
		{
			continue;
		}
		//如果分区存在，需要保存当前结构体变量，以便于下面的比较
		zoneInfo=m_stZone_Info.zoneInfo[index];
		if(zone_source == SOURCE_OFFLINE )	//离线
		{
			if( zoneInfo.g_zone_source != SOURCE_OFFLINE )
			{
				m_stZone_Info.zoneInfo[index].g_zone_source = SOURCE_OFFLINE;
				m_stZone_Info.zoneInfo[index].g_zone_conection=0;
				m_stZone_Info.zoneInfo[index].g_zone_offline_count=0;
				if(m_stZone_Info.zoneInfo[index].g_zone_isSelect)
				{
					m_stZone_Info.zoneInfo[index].g_zone_isSelect=0;	//非选中
				}
				//Zone_Info_Update_Thread();
				set_zoneStatus_refreshTime(0);
			}
			continue;
		}
		//设置成在线状态
		m_stZone_Info.zoneInfo[index].g_zone_conection=1;
		//在线计数清0
		m_stZone_Info.zoneInfo[index].g_zone_offline_count=0;
		//音量
		m_stZone_Info.zoneInfo[index].g_zone_vol=zone_volume;
		//音源
		m_stZone_Info.zoneInfo[index].g_zone_source=zone_source;
		//printf("\nm_stZone_Info.zoneInfo[%d].g_zone_source=0x%x\n",index,zone_source);
		//播放状态
		m_stZone_Info.zoneInfo[index].g_zone_playStatus=zone_playStatus;
		//节目名称
		memset(m_stZone_Info.zoneInfo[index].g_zone_media_name,0,sizeof(m_stZone_Info.zoneInfo[index].g_zone_media_name));
		strcpy(m_stZone_Info.zoneInfo[index].g_zone_media_name,zone_media_name);	//分区MAC

		//设备特性
		m_stZone_Info.zoneInfo[index].g_zone_device_feature = deviceFeature;

		#if 1
		//判断终端寻呼是否掉线
		if(Paging_status == PAGING_START)
		{
			//是否处于寻呼列表中
			for(t=0;t<PagingZoneInfo.ZoneNum;t++)
			{
				if( memcmp(PagingZoneInfo.ZoneInfo[t].ZoneMac,m_stZone_Info.zoneInfo[index].g_zone_mac,6) == 0 && m_stZone_Info.zoneInfo[index].g_zone_conection &&
						m_stZone_Info.zoneInfo[index].g_zone_source != SOURCE_NET_PAGING && m_stZone_Info.zoneInfo[index].g_zone_source != SOURCE_FIRE_ALARM)
				{
					//保存寻呼结束时间
					//sprintf( PagingZoneInfo.ZoneInfo[i].EndTime,"%4d-%02d-%02d %02d:%02d:%02d",CURRENT_TIME.year,CURRENT_TIME.mon,CURRENT_TIME.day,CURRENT_TIME.hour,CURRENT_TIME.min,CURRENT_TIME.sec);
					//发送寻呼掉线信息到主机
					//SendTo_Host_Zone_offline_Info(ip,PagingZoneInfo.ZoneInfo[i].StartTime,PagingZoneInfo.ZoneInfo[i].EndTime);
					//发送再次寻呼通知
					char ZoneMacStr[32]={0};
					sprintf(ZoneMacStr,"%02x:%02x:%02x:%02x:%02x:%02x",m_stZone_Info.zoneInfo[index].g_zone_mac[0],m_stZone_Info.zoneInfo[index].g_zone_mac[1],m_stZone_Info.zoneInfo[index].g_zone_mac[2],m_stZone_Info.zoneInfo[index].g_zone_mac[3],m_stZone_Info.zoneInfo[index].g_zone_mac[4],m_stZone_Info.zoneInfo[index].g_zone_mac[5]);
					printf("paging again:%s\n",ZoneMacStr);
					SendToZone_offline_Paging_Again(m_stZone_Info.zoneInfo[index].g_zone_mac);
					//LOG("SendToZone_offline_Paging_Again:id=%d,name=%s,ip=%s",m_stZone_Info.zoneInfo[index].g_zone_id,m_stZone_Info.zoneInfo[index].g_zone_name,IPIntToChar(m_stZone_Info.zoneInfo[index].g_zone_ip));
					
					break;
				}
			}
		}
		#endif

		if( zoneInfo.g_zone_vol != m_stZone_Info.zoneInfo[index].g_zone_vol ||
				zoneInfo.g_zone_source != m_stZone_Info.zoneInfo[index].g_zone_source ||
				strcmp(zoneInfo.g_zone_name,m_stZone_Info.zoneInfo[index].g_zone_name)!=0 ||
				zoneInfo.g_zone_conection != m_stZone_Info.zoneInfo[index].g_zone_conection ||
				zoneInfo.g_zone_ip != m_stZone_Info.zoneInfo[index].g_zone_ip ||
				(m_stZone_Info.zoneInfo[index].g_zone_source == SOURCE_LOCAL_PLAY &&  zoneInfo.g_zone_playStatus != m_stZone_Info.zoneInfo[index].g_zone_playStatus) ||
				strcmp(zoneInfo.g_zone_media_name,m_stZone_Info.zoneInfo[index].g_zone_media_name)!=0 )
		{
			//Zone_Info_Update_Thread();
			#if 0
			if(Paging_status != PAGING_START) printf("Tindex=%d,name=%s,ip=%s,vol=%d,source=0x%x\n",index,m_stZone_Info.zoneInfo[index].g_zone_name,IPIntToChar(m_stZone_Info.zoneInfo[index].g_zone_ip),
				m_stZone_Info.zoneInfo[index].g_zone_vol,m_stZone_Info.zoneInfo[index].g_zone_source);
			#endif
			
			//如果非管理员，且没有这个分区的管理权限，那么不刷新
			if( IsValidWin(WIN_CONTROL) )
			{
				if(strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME)!=0)
				{
					int valid_zone_flag=0;
					for(k=0;k<m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneCount;k++)
					{
						if(	memcmp(m_stZone_Info.zoneInfo[index].g_zone_mac,&m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneMac[6*k],6 ) == 0 )
						{
							valid_zone_flag=1;
							break;
						}
					}
					if(valid_zone_flag)
					{
						set_zoneStatus_refreshTime(0);
					}
					else
					{
						printf("User not belong!\n");
					}
				}
				else
				{
					set_zoneStatus_refreshTime(0);
				}
			}
			else
			{
				printf("User not login!\n");
			}
		}

	}

	return 0;
}





#if ENABLE_CALL_FUNCTION
/****************************************************
 * @fn      GetAllPagerDeviceInfo
 *
 * @brief	获取所有寻呼台设备的状态 
 *
 * @param
 *
 * @return	int 应答包长度
 */
int GetAllPagerDeviceInfo(unsigned char *Pkg)
{
	int i,k,t;
	int pos=PAYLOAD_START;	//Pkg读指针
	int pager_num=0;				//分区数
	unsigned char device_mac[6*128]={0};	//分区mac数组

	int ipLen=0;
	char cip[32]={0};
	int deviceName_len=0;
	char deviceName[32]={0};
	int source=SOURCE_NULL;
	int supportCall=0;
	int supportVideo=0;
	int reserve=0;

	PAGER_DETAIL *pagerInfo;		//寻呼台信息

	pager_num=Pkg[pos++];			//本次发送的寻呼台设备数

	printf("GetAllPagerDeviceInfo::::::pager_num=%d\n",pager_num);

	//UDP模式下需要应答
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		respond_null_payload_pkg(Pkg,g_host_ip);
		if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL)
			return -1;
	}

	for(i=0;i<pager_num;i++)
	{
		memcpy(device_mac+6*i,&Pkg[pos],6);	//分区MAC
		pos+=6;

		//判断MAC是否存在
		int found=0;
		int index=-1;

		for(t=0;t<m_stPager_Info.TotalPager;t++)
		{
			for(k=0;k<6;k++)
			{
				if(	m_stPager_Info.pagerInfo[t].mac[k] != device_mac[k+6*i] )
				{
					break;
				}
			}
			if(k == 6)
			{
				found=1;
				index=t;
				break;
			}
		}

		source=Pkg[pos++];
		supportCall=Pkg[pos++];
		supportVideo=Pkg[pos++];
		reserve=Pkg[pos++];


		if(!found)	//不存在此寻呼台设备时,不处理
		{
			#if 0
			//如果是自己寻呼台的MAC，跳过
			if(memcmp(&device_mac[6*i],MAC_ADDR,6) == 0)
			{
				printf("pager:myself.\n");
				continue;
			}
			pagerInfo=&m_stPager_Info.pagerInfo[m_stPager_Info.TotalPager];
			pagerInfo->id = m_stPager_Info.TotalPager;
			m_stPager_Info.TotalPager++;
			#endif
			continue;
		}
		else
		{
			pagerInfo=&m_stPager_Info.pagerInfo[index];
		}

		pagerInfo->source = source;
		pagerInfo->isSupportCall = supportCall;
		if(pagerInfo->source!= SOURCE_OFFLINE)
		{
			pagerInfo->conection = 1;
		}
		else
		{
			pagerInfo->conection = 0;
			pagerInfo->isSelect = 0;
		}
		pagerInfo->isSupportVideo=supportVideo;
	}

	int onlinePagerNum=0;
	for(i=0;i<m_stPager_Info.TotalPager;i++)
	{
		if(m_stPager_Info.pagerInfo[i].conection)
			onlinePagerNum++;
	}
	if(onlinePagerNum!=m_stPager_Info.OnlinePager)
	{
		m_stPager_Info.OnlinePager=onlinePagerNum;
	}

	//刷新
	control_win_update(2,0);
	printf("GetAllPagerDeviceInfo:Pager cnt=%d,Online cnt=%d\n",m_stPager_Info.TotalPager,m_stPager_Info.OnlinePager);

	return 0;
}


/****************************************************
 * @fn      QueryAllPagerDeviceInfo
 *
 * @brief	查询所有寻呼台设备的状态 
 *
 * @param
 *
 * @return	int 应答包长度
 */
int QueryAllPagerDeviceInfo()
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=1;

	data[0]=0x00;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_ALL_PAGER_STATUS,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}




/****************************************************
 * @fn      calling_invation
 *
 * @brief	主叫方邀请对讲 
 *
 * @param	unsigned char *calledMac 被叫方MAC
 *
 * @return	int 应答包长度
 */
int Send_calling_invation(unsigned char *calledMac)
{
	printf("Send_calling_invation...\n");
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=20;

	memcpy(data,MAC_ADDR,6);	//主叫方MAC
	memcpy(data+6,calledMac,6);	//被叫方MAC

	unsigned char audioCoding=g_send_decode_pcm_type;          //语音编码
	data[12]=audioCoding;	//音频编码
	data[13]=1;	//NAT
	data[14]=0;	//port high
	data[15]=0;	//port low
	data[16]=DEVICE_MODEL_PAGING_A;	//主叫方设备型号
	data[17]=FRAME_PAGER_CALL>>8;	//媒体时间戳单元 high
	data[18]=FRAME_PAGER_CALL&0xFF;	//媒体时间戳单元 low
	
	#if USE_SSD202
	data[19]=1;	//支持视频
	#else
	data[19]=0;
	#endif

	m_stPager_Info.self_isCallingParty = 1;	//主叫方
	m_stPager_Info.self_audioCoding = audioCoding;	//PCM编码
	m_stPager_Info.selt_enableNat =   1;	//开启NAT
	m_stPager_Info.self_audioPort =   0;	//端口0（开启NAT时此项无效）
	memcpy(m_stPager_Info.self_callMac,calledMac,6);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CALLING_INVITATION,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}



/****************************************************
 * @fn      Recv_calling_invation
 *
 * @brief	收到主叫方的对讲邀请 
 *
 * @param	
 *
 * @return	int 应答包长度
 */
int Recv_calling_invation(unsigned char *Pkg)
{
	printf("Recv_calling_invation1...\n");
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=20;

	//如果当前处于寻呼状态，拒绝
	if(Paging_status == PAGING_START || Paging_status == CALLING_START || m_stPager_Info.self_callStatus != CALL_STATUS_FREE)
	{
		//应答状态
		Send_callStatus_feedback(CALL_STATUS_BUSY);
		return ERROR;
	}

	int pos=PAYLOAD_START;

	unsigned char callingMac[6]={0};
	unsigned char calledMac[6]={0};
	memcpy(callingMac,Pkg+pos,6);	//保存主叫方MAC
	memcpy(calledMac,Pkg+pos+6,6);	//保存被叫方MAC
	pos+=12;

	//如果被叫方MAC与本机不符，不响应
	if(	memcmp(calledMac,MAC_ADDR,6) )
	{
		Send_callStatus_feedback(CALL_STATUS_REJECT);
		return ERROR;
	}

	//如果当前不是控制页面，拒绝
	if(	GetCurrentWin() == WIN_LOGIN )
	{
		Send_callStatus_feedback(CALL_STATUS_REJECT);
		return ERROR;
	}
#if 0
	//如果寻呼列表里面没有主叫方,拒绝
	int called_index=-1;
	for(i=0;i<m_stPager_Info.TotalPager;i++)
	{
		if( memcmp(callingMac,m_stPager_Info.pagerInfo[i].mac,6) == 0 )
		{
			called_index=i;
			break;
		}
	}
	if(called_index == -1)
	{
		Send_callStatus_feedback(CALL_STATUS_REJECT);
		return ERROR;
	}
#endif

    unsigned char  audioCoding=Pkg[pos++];          //语音编码
    unsigned char  enableNat=Pkg[pos++];            //开启NAT后由主机转发双方音频流
    unsigned short audioPort=(Pkg[pos]<<8)+(Pkg[pos+1]);      //关闭NAT后有效
	pos+=2;
	//主叫方设备型号
	unsigned char callingModel = Pkg[pos++];
	//媒体时间戳单元
	unsigned short mediaStampUnit = (Pkg[pos]<<8)+(Pkg[pos+1]);
	pos+=2;

	//add 20231220,是否支持对讲
	int payload_len=(Pkg[6]<<8)+Pkg[7];
	if(pos == payload_len+8)		//如果此处没有多余的负载数据，证明没有网络模式字段下发（以前的主机版本）
	{
		printf("Recv_calling_invation1:No more info,isSupportVideo=0!\n");
		m_stPager_Info.other_isSupportVideo=false;
	}
	else
	{
		int isSupportVideo=Pkg[pos++];
		m_stPager_Info.other_isSupportVideo=isSupportVideo;
		printf("Recv_called_response:isSupportVideo=%d!\n",isSupportVideo);
	}

	
	//if(m_stPager_Info.self_audioCoding != DECODE_STANDARD_PCM && m_stPager_Info.self_audioCoding != DECODE_G711 && \
		m_stPager_Info.self_audioCoding != DECODE_G722 && m_stPager_Info.self_audioCoding != DECODE_G722_1)
	if(audioCoding != DECODE_STANDARD_PCM && audioCoding != DECODE_G711 && audioCoding != DECODE_G722)
	{
		Send_callStatus_feedback(CALL_STATUS_CODECES_NOT_SUPPORT);
		return ERROR;
	}
	m_stPager_Info.self_audioCoding = audioCoding;
	m_stPager_Info.selt_enableNat =   enableNat;
	m_stPager_Info.self_audioPort = audioPort;
	m_stPager_Info.self_isCallingParty = 0;	//被叫方

	m_stPager_Info.other_deviceModel = callingModel;

	memcpy(m_stPager_Info.self_callMac,callingMac,6);

	printf("Recv_calling_invation2,callingMac:\n");
	
	for(i=0;i<6;i++)
	{
		printf("%x: ",m_stPager_Info.self_callMac[i]);
	}
	printf("\n");


	memcpy(data,callingMac,6);	//主叫方MAC
	memcpy(data+6,calledMac,6);	//被叫方MAC
	data[12]=audioCoding;	//音频编码
	data[13]=enableNat;		//NAT
	data[14]=audioPort>>8;	//port high
	data[15]=audioPort;	//port low
	data[16]=DEVICE_MODEL_PAGING_A;	//主叫方设备型号
	data[17]=FRAME_PAGER_CALL>>8;	//媒体时间戳单元 high
	data[18]=FRAME_PAGER_CALL&0xFF;		//媒体时间戳单元 low

	#if USE_SSD202
	data[19]=1;	//支持视频
	#else
	data[19]=0;
	#endif

	if(data_len)
	{
		int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CALLED_RESPONSE,DEVICE_MODEL_PAGING_A,data_len,data);
		// 发送数据
		if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
		{
			return ERROR;
		}
		
		m_stPager_Info.self_callStatus = CALL_STATUS_WAIT_CALLED_ANSWER;
		m_stPager_Info.other_callStatus = CALL_STATUS_WAIT_CALLED_ANSWER;	//对方是主叫，等待本方应答
		Send_callStatus_feedback(CALL_STATUS_WAIT_CALLED_ANSWER);
		create_call_cont(0);
		Start_Call_Ring_Play_Thread();
	}
	return SUCCEED;
}




/****************************************************
 * @fn      Recv_called_response
 *
 * @brief	收到被叫方的对讲应答 
 *
 * @param	
 *
 * @return	int 应答包长度
 */
int Recv_called_response(unsigned char *Pkg)
{
	printf("Recv_called_response1...\n");
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	int pos=PAYLOAD_START;

	unsigned char calledMac[6]={0};
	memcpy(calledMac,Pkg+pos+6,6);	//被叫方MAC
	pos+=12;

    if( memcmp(calledMac,m_stPager_Info.self_callMac,6) )		//跟发起时的被叫MAC不符，退出
	{
		Send_callStatus_feedback(CALL_STATUS_BUSY);
		return false;
	}

	unsigned char  audioCoding=Pkg[pos++];          //语音编码
    unsigned char  enableNat=Pkg[pos++];            //开启NAT后由主机转发双方音频流
    unsigned short audioPort=(Pkg[pos]<<8)+(Pkg[pos+1]);      //关闭NAT后有效
	pos+=2;
	//主叫方设备型号
	unsigned char calledModel = Pkg[pos++];
	//媒体时间戳单元
	unsigned short mediaStampUnit = (Pkg[pos]<<8)+(Pkg[pos+1]);
	pos+=2;

	int payload_len=(Pkg[6]<<8)+Pkg[7];
	if(pos == payload_len+PAYLOAD_START)		//如果此处没有多余的负载数据，证明没有网络模式字段下发（以前的主机版本）
	{
		printf("Recv_called_response:No more info,isSupportVideo=0!\n");
		m_stPager_Info.other_isSupportVideo=0;
	}
	else
	{
		int isSupportVideo=Pkg[pos++];
		m_stPager_Info.other_isSupportVideo=isSupportVideo;
		printf("Recv_called_response:isSupportVideo=%d!\n",isSupportVideo);
	}
	
	if(audioCoding != DECODE_STANDARD_PCM && audioCoding != DECODE_G711 && audioCoding != DECODE_G722)
	{
		Send_callStatus_feedback(CALL_STATUS_CODECES_NOT_SUPPORT);
		return false;
	}

	printf("Recv_called_response ok...\n");
	m_stPager_Info.self_audioCoding = audioCoding;
	m_stPager_Info.selt_enableNat =   enableNat;
	m_stPager_Info.self_audioPort = audioPort;
	m_stPager_Info.self_isCallingParty = 1;	//主叫方

	m_stPager_Info.other_deviceModel = calledModel;

	//
	//m_stPager_Info.self_callStatus = CALL_STATUS_WAIT_CALLED_ANSWER;
	//Send_callStatus_feedback(CALL_STATUS_WAIT_CALLED_ANSWER);

	return SUCCEED;
}





/****************************************************
 * @fn      Send_callStatus_feedback
 *
 * @brief	对讲设备主动发送对讲状态 
 *
 * @param	
 *
 * @return	int 应答包长度
 */
int Send_callStatus_feedback(int status)
{
	printf("Send_callStatus_feedback,status=%d...\n",status);
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=7;

	memcpy(data,MAC_ADDR,6);	//本机MAC
	data[6]=status;	//音频编码

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CALLED_STATUS,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}


/****************************************************
 * @fn      Recv_callStatus_feedback
 *
 * @brief	收到对讲方的状态应答 
 *
 * @param	
 *
 * @return	int 应答包长度
 */
int Recv_callStatus_feedback(unsigned char *Pkg)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	int pos=PAYLOAD_START;

	unsigned char calledMac[6]={0};
	memcpy(calledMac,Pkg+pos,6);			//MAC
	pos+=6;
	int callStatus=Pkg[pos++];

	printf("Recv_callStatus_feedback1:%d\n",callStatus);
	
	if( memcmp(calledMac,m_stPager_Info.self_callMac,6) != 0 )
	{
		return ERROR;
	}
	m_stPager_Info.other_callStatus = callStatus;

	//printf("Recv_callStatus_feedback2:%d\n",callStatus);

	if(m_stPager_Info.self_callStatus != CALL_STATUS_FREE && ( callStatus == CALL_STATUS_FREE || callStatus == CALL_STATUS_HANGUP ) )
	{
		set_call_status_show(CALL_STATUS_HANGUP,0);
		//如果是挂断，需要立即响应，避免线程轮询后接收多个状态后覆盖
		m_stPager_Info.self_callStatus = CALL_STATUS_FREE;
		close_calling_win_extern(0,1,1000);
	}
	else if(m_stPager_Info.self_callStatus == CALL_STATUS_WAIT_CALLED_ANSWER && callStatus == CALL_STATUS_CONNECT)
	{
		m_stPager_Info.self_callStatus = CALL_STATUS_CONNECT;
		//20230223需要告知被叫，主叫（自己）也进入了通话状态，否则被叫在call_process中会认为主叫一直处于等待接听状态，超过40秒后会停止对讲
		Send_callStatus_feedback(CALL_STATUS_CONNECT);
		set_call_status_show(CALL_STATUS_CONNECT,0);
		//打开音频接口
		#if defined(USE_SSD212) || defined(USE_SSD202)
		Paging_status = CALLING_START;
		//此处需要再重新打开一次ao，否则之前里面有铃声缓存导致延时很大
		mi_audio_out_init(16000, 16, 1);
		mi_audio_in_init(AI_MODE_CALL_16K,16000, 16, 1);
		#else
		i2s_call_thread();
		#endif
	}
	

	return SUCCEED;
}




/****************************************************
 * @fn      Send_Call_Audio_Stream
 *
 * @brief   //发送对讲音频流
 *
 * @param
 *
 * @return	int 应答包长度
 */
int Send_Call_Audio_Stream( unsigned char* data,int len )
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int Pkg_count=0;

	  if(len%512 > 0)
		  Pkg_count=len/512 + 1;
	  else
		  Pkg_count=len/512;
	//if(Paging_status != PAGING_START) printf("\nSendToZone_PCM_Data_Cmd,len=%d，Pkg_count=%d\n",len,Pkg_count);
	int sendLen=0;
	int Len_Sended=0;

	int i;
	for(i=0;i<Pkg_count;i++)		//最大
	{
		
		unsigned char dataBuf[CLIENT_BUF_SIZE]={0};
		int pos=0;

		//本机mac
		memcpy(dataBuf+pos,MAC_ADDR,6);			//MAC
		pos+=6;

		int streamLen=(len>=512)?512:len;
		//数据流长度
		dataBuf[pos++]=streamLen>>8;				//stream_len High
		dataBuf[pos++]=streamLen;					//stream_len low
		
		//数据流偏移量
		dataBuf[pos++]=6+2+1+4;					//mac+streamLen+streamOffset+timeStamp
		
		//媒体时间戳
		dataBuf[pos++]=m_stPager_Info.self_mediaTimeStamp>>24;
		dataBuf[pos++]=m_stPager_Info.self_mediaTimeStamp>>16;
		dataBuf[pos++]=m_stPager_Info.self_mediaTimeStamp>>8;
		dataBuf[pos++]=m_stPager_Info.self_mediaTimeStamp;

		memcpy(dataBuf+pos,data+Len_Sended,streamLen);
		pos+=streamLen;

		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CALLING_AUDIOSTREAM , DEVICE_MODEL_PAGING_A,pos,dataBuf);
		UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
		len-=512;
		Len_Sended+=512;
	}
	//每发送一次整包，时间戳自增
	m_stPager_Info.self_mediaTimeStamp += FRAME_PAGER_CALL;

	return SUCCEED;
}

/****************************************************
 * @fn      Recv_Call_Audio_Stream
 *
 * @brief   //接收对讲音频流
 *
 * @param
 *
 * @return	int 应答包长度
 */
int Recv_Call_Audio_Stream(unsigned char *Pkg)
{
	if( m_stPager_Info.self_callStatus != CALL_STATUS_CONNECT )
		return ERROR;

	int i=0;

	int pos=PAYLOAD_START;

	//MAC
	unsigned char calledMac[6]={0};
	memcpy(calledMac,Pkg+pos,6);			//MAC
	pos+=6;
	//数据流长度
	int streamLen = (Pkg[pos]<<8)+Pkg[pos+1];
	pos+=2;
	//数据流偏移量
	unsigned char streamOffset=Pkg[pos++];
	//媒体时间戳
	unsigned int mediaTimeStamp = (Pkg[pos]<<24) + (Pkg[pos+1]<<16) + (Pkg[pos+2]<<8) + Pkg[pos+3] ;
	pos+=4;

	
	if( memcmp(calledMac,m_stPager_Info.self_callMac,6) )
	{
		return ERROR;
	}

	pthread_mutex_lock(&call_data_mutex);
	memcpy(rx_call_stream.rx_call_data[rx_call_stream.rx_call_read_pos],Pkg+PAYLOAD_START+streamOffset,streamLen);
	rx_call_stream.rx_call_len[rx_call_stream.rx_call_read_pos]=streamLen;
	rx_call_stream.rx_call_valid[rx_call_stream.rx_call_read_pos]=1;
	//printf("rx_call_stream.rx_call_read_pos=%d,len=%d\n",rx_call_stream.rx_call_read_pos,streamLen);
	rx_call_stream.rx_call_read_pos++;
	if(rx_call_stream.rx_call_read_pos>=RX_CALL_BUF_PKG_MAX)
		rx_call_stream.rx_call_read_pos=0;
	pthread_mutex_unlock(&call_data_mutex);
	return SUCCEED;
}



#if USE_SSD202
/****************************************************
 * @fn      calling_invation
 *
 * @brief	发送请求视频对讲 
 *
 * @param	none
 *
 * @return	int 应答包长度
 */
int Send_request_video_call(int eventType,int acceptResult)
{
	printf("Send_request_video_call...\n");
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	memcpy(data,MAC_ADDR,6);	//本机MAC
	data_len+=6;

	data[6]=eventType;	//1：发起通知，0：应答
	data_len++;
	if(eventType == 0)
	{
		data[7] = acceptResult;	//1：接受 2：拒绝（不支持视频对讲）3：拒绝（用户拒绝）

		data_len++;
	}


	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CALL_REQUEST_VIDEO,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}


/****************************************************
 * @fn      Recv_request_video_call
 *
 * @brief	接收请求视频对讲 
 *
 * @param	none
 *
 * @return	int 应答包长度
 */
int Recv_request_video_call(unsigned char *Pkg)
{
	printf("Recv_request_video_call...\n");

	int pos=PAYLOAD_START;
	//MAC
	unsigned char calledMac[6]={0};
	memcpy(calledMac,Pkg+pos,6);			//MAC
	pos+=6;

	if( memcmp(calledMac,m_stPager_Info.self_callMac,6) )
	{
		return ERROR;
	}

	int type=Pkg[pos++];
	if(type == 1)	//对方发起的通知
	{
		//接受并应答给对方
		Send_request_video_call(0,1);
		m_stPager_Info.self_video_callStatus=VIDEO_CALL_STATUS_TRY_OPEN_RTSP;
		Send_video_call_status_feedback(m_stPager_Info.self_video_callStatus);
		//开启RTSP，获取相关参数，由独立的视频对讲线程去处理
		startRTCPClient_thread(0);
	}
	else if(type == 0)	//对方的应答
	{
		//判断那是不是接受
		int acceptResult=Pkg[pos++];
		if(acceptResult == 1)	//接受
		{
			m_stPager_Info.self_video_callStatus = VIDEO_CALL_STATUS_TRY_OPEN_RTSP;
			Send_video_call_status_feedback(m_stPager_Info.self_video_callStatus);
			//开启RTSP，获取相关参数，由独立的视频对讲线程去处理
			startRTCPClient_thread(0);
		}
		else{		//拒绝，应该退出视频对讲

		}
	}
}

/****************************************************
 * @fn      Send_video_call_codecs_parm
 *
 * @brief	发送对讲设备视频流参数 
 *
 * @param	none
 *
 * @return	int 应答包长度
 */
int Send_video_call_codecs_parm(int codecs)
{
	printf("Send_video_call_codecs_parm...\n");
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	memcpy(data,MAC_ADDR,6);	//本机MAC
	data_len+=6;

	data[6]=codecs;
	data_len++;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CALL_VIDEO_PARM,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}


/****************************************************
 * @fn      Recv_video_call_codecs_parm
 *
 * @brief	接收对讲设备视频流参数 
 *
 * @param	none
 *
 * @return	int 应答包长度
 */
int Recv_video_call_codecs_parm(unsigned char *Pkg)
{
	printf("Recv_video_call_codecs_parm...\n");
	
	if( m_stPager_Info.self_callStatus < VIDEO_CALL_STATUS_TRY_OPEN_RTSP )
		return ERROR;

	int pos=PAYLOAD_START;
	//MAC
	unsigned char calledMac[6]={0};
	memcpy(calledMac,Pkg+pos,6);			//MAC
	pos+=6;

	if( memcmp(calledMac,m_stPager_Info.self_callMac,6) )
	{
		return ERROR;
	}

	int videoCodecs=Pkg[pos++];

	if(videoCodecs!=VIDEO_CODECS_H264 && videoCodecs!=VIDEO_CODECS_H265)
	{
		//todo 参数不对，退出视频
	}
	//组建SDP文件并打开数据接收
	//这个时候自己的RTSP不一定打开了
	start_ffplayer_video_by_videoCodes(videoCodecs);
}


/****************************************************
 * @fn      Send_video_call_status_feedback
 *
 * @brief	发送视频对讲状态 
 *
 * @param	none
 *
 * @return	int 应答包长度
 */
int Send_video_call_status_feedback(int status)
{
	printf("Send_video_call_status_feedback:self_status=%d...\n",status);
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	memcpy(data,MAC_ADDR,6);	//本机MAC
	data_len+=6;

	data[6]=status;
	data_len++;

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CALL_VIDEO_STATUS,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}


/****************************************************
 * @fn      Recv_video_call_status
 *
 * @brief	接收对讲设备视频状态
 *
 * @param	none
 *
 * @return	int 应答包长度
 */
int Recv_video_call_status(unsigned char *Pkg)
{
	printf("Recv_video_call_status111\n");
	int pos=PAYLOAD_START;
	//MAC
	unsigned char calledMac[6]={0};
	memcpy(calledMac,Pkg+pos,6);			//MAC
	pos+=6;

	if( memcmp(calledMac,m_stPager_Info.self_callMac,6) )
	{
		return ERROR;
	}

	//如果本机是空闲，不接收对方视频状态
	if(m_stPager_Info.self_video_callStatus == VIDEO_CALL_STATUS_FREE)
	{
		return ERROR;
	}

	int status=Pkg[pos++];
	printf("Recv_video_call_status:status=%d\n",status);
	m_stPager_Info.other_video_callStatus = status;

	if(m_stPager_Info.other_video_callStatus == VIDEO_CALL_STATUS_ALL_READY)
	{
		rtspStartPlay();
	}
}



/****************************************************
 * @fn      Send_Call_Video_Stream
 *
 * @brief   //发送对讲视频流
 *
 * @param
 *
 * @return	int 应答包长度
 */
int Send_Call_Video_Stream( unsigned char* stream,int streamlen,unsigned int pkgId )
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int Pkg_count=0;

	int max_pkg_data=768;

	  if(streamlen%max_pkg_data > 0)
		  Pkg_count=streamlen/max_pkg_data + 1;
	  else
		  Pkg_count=streamlen/max_pkg_data;
	//if(Paging_status != PAGING_START) printf("\nSendToZone_PCM_Data_Cmd,len=%d，Pkg_count=%d\n",len,Pkg_count);
	int sendLen=0;
	int Len_Sended=0;

	int i;
	bool needSubPkg=streamlen>max_pkg_data?true:false;
	for(i=0;i<Pkg_count;i++)		//最大
	{
		unsigned char dataBuf[CLIENT_BUF_SIZE]={0};
		int pos=0;

		//本机mac
		memcpy(dataBuf+pos,MAC_ADDR,6);			//MAC
		pos+=6;

		int streamLen=(streamlen>=max_pkg_data)?max_pkg_data:streamlen;
		//数据流长度
		dataBuf[pos++]=streamLen>>8;				//stream_len High
		dataBuf[pos++]=streamLen;					//stream_len low
		
		//数据流偏移量
		dataBuf[pos++]=6+2+1+4+1+1;					//mac+streamLen+streamOffset+Pkg Id+Sub pkgId+Pkg Type

		//包id，同一分包，包id应该一致
		dataBuf[pos++]=pkgId>>24;
		dataBuf[pos++]=pkgId>>16;
		dataBuf[pos++]=pkgId>>8;
		dataBuf[pos++]=pkgId;

		//分包id
		if(needSubPkg)
		{
			dataBuf[pos++]=i+1;	//分包id
		}
		else
		{
			dataBuf[pos++]=0;	//无需分包
		}

		dataBuf[pos++]=1;	//包数据类型：1表示RTP，2表示RTCP,暂未用到

		memcpy(dataBuf+pos,stream+Len_Sended,streamLen);
		pos+=streamLen;

		sendLen=Network_Send_Compose_CMD(sendBuf,CMD_CALL_VIDEO_STREAM , DEVICE_MODEL_PAGING_A,pos,dataBuf);
		UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
		streamlen-=max_pkg_data;
		Len_Sended+=max_pkg_data;
	}

	return SUCCEED;
}


/****************************************************
 * @fn      Recv_Call_Video_Stream
 *
 * @brief   //接收对讲视频流
 *
 * @param
 *
 * @return	int 应答包长度
 */
int Recv_Call_Video_Stream(unsigned char *Pkg)
{
	if( m_stPager_Info.self_video_callStatus != VIDEO_CALL_STATUS_ALL_READY )
		return ERROR;

	int pos=PAYLOAD_START;

	//MAC
	unsigned char calledMac[6]={0};
	memcpy(calledMac,Pkg+pos,6);			//MAC
	pos+=6;
	
	if( memcmp(calledMac,m_stPager_Info.self_callMac,6) )
	{
		return ERROR;
	}

	//数据流长度
	int streamLen = (Pkg[pos]<<8)+Pkg[pos+1];
	pos+=2;
	//数据流偏移量
	unsigned char streamOffset=Pkg[pos++];
	//包id
	unsigned int pkgId = (Pkg[pos]<<24) + (Pkg[pos+1]<<16) + (Pkg[pos+2]<<8) + Pkg[pos+3] ;
	pos+=4;
	//分包id
	unsigned char subPkgId = Pkg[pos++];
	//包数据类型
	unsigned char pkgType = Pkg[pos++];

	//printf("Recv:pkgId=%d,subPkgId=%d,Len=%d,Offset=%d\n",pkgId,subPkgId,streamLen,streamOffset);

	m_stPager_Info.stVideoStream.pkgId[m_stPager_Info.stVideoStream.nextPkgIndex] = pkgId;
	m_stPager_Info.stVideoStream.subPkgId[m_stPager_Info.stVideoStream.nextPkgIndex] = subPkgId;
	memcpy(m_stPager_Info.stVideoStream.streamBuf[m_stPager_Info.stVideoStream.nextPkgIndex],Pkg+streamOffset+PAYLOAD_START,streamLen);
	m_stPager_Info.stVideoStream.streamLen[m_stPager_Info.stVideoStream.nextPkgIndex] = streamLen;

	//检测期待的包id是否完整
	int nFoundType=0;
	int i=0,j=0;

	unsigned char realStreamBuf[1500]={0};
	unsigned int realStreamLen=0;

	while(1)
	{
		realStreamLen=0;
		nFoundType=0;
		//轮询
		for(i=0;i<MAX_VIDEO_PACKETS;i++)
		{
			if(m_stPager_Info.stVideoStream.streamLen[i] == 0)
			{
				continue;
			}
			if(m_stPager_Info.stVideoStream.pkgId[i] == m_stPager_Info.stVideoStream.expectNextPkgId)
			{
				int subPkgId=m_stPager_Info.stVideoStream.subPkgId[i];
				if(subPkgId == 0)
				{
					nFoundType=1;
				}
				else
				{
					for(j=0;j<MAX_VIDEO_PACKETS;j++)
					{
						if(j == i || m_stPager_Info.stVideoStream.streamLen[j] == 0)
							continue;
						if(m_stPager_Info.stVideoStream.pkgId[j] == m_stPager_Info.stVideoStream.expectNextPkgId )
						{
							if(subPkgId == 1)
							{
								if(m_stPager_Info.stVideoStream.subPkgId[j] == 2)
								{
									nFoundType=2;
								}
							}
							else if(subPkgId == 2)
							{
								if(m_stPager_Info.stVideoStream.subPkgId[j] == 1)
								{
									nFoundType=2;
								}
							}
						}
						if(nFoundType)
						{
							break;
						}
					}
				}
				if(nFoundType == 1)	//完整包
				{
					//保存实际待发送数据
					memcpy(realStreamBuf,m_stPager_Info.stVideoStream.streamBuf[i],m_stPager_Info.stVideoStream.streamLen[i]);
					realStreamLen+=m_stPager_Info.stVideoStream.streamLen[i];
					//清理缓存
					m_stPager_Info.stVideoStream.streamLen[i]=0;
				}
				else if(nFoundType ==2)	//分包
				{
					//组合并保存实际待发送数据
					if(m_stPager_Info.stVideoStream.subPkgId[i]<m_stPager_Info.stVideoStream.subPkgId[j])
					{
						memcpy(realStreamBuf,m_stPager_Info.stVideoStream.streamBuf[i],m_stPager_Info.stVideoStream.streamLen[i]);
						realStreamLen+=m_stPager_Info.stVideoStream.streamLen[i];
						memcpy(realStreamBuf+realStreamLen,m_stPager_Info.stVideoStream.streamBuf[j],m_stPager_Info.stVideoStream.streamLen[j]);
						realStreamLen+=m_stPager_Info.stVideoStream.streamLen[j];
					}
					else if(m_stPager_Info.stVideoStream.subPkgId[i]>m_stPager_Info.stVideoStream.subPkgId[j])
					{
						memcpy(realStreamBuf,m_stPager_Info.stVideoStream.streamBuf[j],m_stPager_Info.stVideoStream.streamLen[j]);
						realStreamLen+=m_stPager_Info.stVideoStream.streamLen[j];
						memcpy(realStreamBuf+realStreamLen,m_stPager_Info.stVideoStream.streamBuf[i],m_stPager_Info.stVideoStream.streamLen[i]);
						realStreamLen+=m_stPager_Info.stVideoStream.streamLen[i];
					}

					//清理缓存
					m_stPager_Info.stVideoStream.streamLen[i]=0;
					m_stPager_Info.stVideoStream.streamLen[j]=0;
				}

				if(nFoundType)
					break;
			}
		}
		if(!nFoundType)
		{
			//没找到，如果已经满了，删除pkg_id最小的一个,再将期望pkg_id指向里面最小的那个pkg_id
			int used_num=0;
			for(i=0;i<MAX_VIDEO_PACKETS;i++)
			{
				if(m_stPager_Info.stVideoStream.streamLen[i]==0)
				{
					continue;
				}
				used_num++;
			}
			unsigned int min_pkg_id=UINT_MAX;
			if(used_num >= MAX_VIDEO_PACKETS)
			{
				for(i=0;i<MAX_VIDEO_PACKETS;i++)
				{
					if(m_stPager_Info.stVideoStream.streamLen[i]==0)
					{
						continue;
					}
					if (m_stPager_Info.stVideoStream.pkgId[i] < min_pkg_id) {
						min_pkg_id = m_stPager_Info.stVideoStream.pkgId[i];
					}
				}

				//判断min_pkg_id存在与哪些位置，全部清除
				for(i=0;i<MAX_VIDEO_PACKETS;i++)
				{
					if(m_stPager_Info.stVideoStream.streamLen[i]==0)
					{
						continue;
					}
					if (m_stPager_Info.stVideoStream.pkgId[i] == min_pkg_id) {
						m_stPager_Info.stVideoStream.streamLen[i]=0;
					}
				}

				min_pkg_id=UINT_MAX;
				for(i=0;i<MAX_VIDEO_PACKETS;i++)
				{
					if(m_stPager_Info.stVideoStream.streamLen[i]==0)
					{
						continue;
					}
					if (m_stPager_Info.stVideoStream.pkgId[i] < min_pkg_id) {
						min_pkg_id = m_stPager_Info.stVideoStream.pkgId[i];
					}
				}
				m_stPager_Info.stVideoStream.expectNextPkgId = min_pkg_id;
				printf("expectNextPkgId_fail=%d\n",min_pkg_id);
			}
		}
		else	//已找到，直接将期望pkg_id+1即可
		{
			m_stPager_Info.stVideoStream.expectNextPkgId+=1;
			//printf("expectNextPkgId_ok=%d\n",m_stPager_Info.stVideoStream.expectNextPkgId);
		}

		if(realStreamLen>0)
		{
			//printf("sendOK:Len=%d,expectNextPkgId=%d\n",realStreamLen,m_stPager_Info.stVideoStream.expectNextPkgId);
			#if USE_SSD202
			rtspStreamForwardLocalPort(realStreamBuf,realStreamLen);
			#endif
			//printf("sendOK:realStreamLen=%d\n",realStreamLen);
		}
		else
		{
			#if 0
			printf("pkgId error,need ID:%d\n",m_stPager_Info.stVideoStream.expectNextPkgId);
			for(i=0;i<MAX_VIDEO_PACKETS;i++)
			{
				if(m_stPager_Info.stVideoStream.streamLen[i]!=0)
				{
					printf("index=%d,pkgId:%d,subPkgId:%d,stremLen:%d\n",i,m_stPager_Info.stVideoStream.pkgId[i],m_stPager_Info.stVideoStream.subPkgId[i],m_stPager_Info.stVideoStream.streamLen[i]);
				}
			}
			#endif

			break;
		}

	}
#if 1
	//把小于expectNextPkgId的都清除掉
	for(i=0;i<MAX_VIDEO_PACKETS;i++)
	{
		if (m_stPager_Info.stVideoStream.pkgId[i] <m_stPager_Info.stVideoStream.expectNextPkgId) {
			m_stPager_Info.stVideoStream.streamLen[i]=0;
		}
	}
#endif
	//重置下一次保存包的位置,找到第一个空闲的位置保存即可
	for(i=0;i<MAX_VIDEO_PACKETS;i++)
	{
		if(m_stPager_Info.stVideoStream.streamLen[i]==0)
		{
			m_stPager_Info.stVideoStream.nextPkgIndex = i;
			break;
		}
	}


	return SUCCEED;
}

#endif

#endif




#if ENABLE_LISTEN_FUNCTION

/****************************************************
 * @fn      Send_listen_event
 *
 * @brief   //主动发起监听
 *
 * @param
 *
 * @return	int 应答包长度
 */
int Send_listen_event(unsigned char *listenedMac,unsigned char event,unsigned char type)
{
	printf("Send_listen_event:%d,type:%d...\n",event,type);
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=16;

	memcpy(data,MAC_ADDR,6);		//监听端MAC
	memcpy(data+6,listenedMac,6);	//被监听端MAC

	data[12]=event;					//事件（开始/结束）
	data[13]=type;					//type（输出监听/现场监听）

	data[14]=0;	//音频编码
	data[15]=1;	//NAT
	data[16]=0;	//port high
	data[17]=0;	//port low

	if(event == LISTEN_STATUS_START)
	{
		m_stListen_Info.self_isListeningParty = 1;	//监听方
		m_stListen_Info.self_audioCoding = 0;		//PCM编码
		m_stListen_Info.selt_enableNat =   1;		//开启NAT
		m_stListen_Info.self_audioPort =   0;		//端口0（开启NAT时此项无效）

		memcpy(m_stListen_Info.self_listenedMac,listenedMac,6);

		m_stListen_Info.self_listenStatus = LISTEN_STATUS_START;
	}
	else
	{
		m_stListen_Info.self_listenStatus = LISTEN_STATUS_STOP;
	}
	

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_LISTEN_EVENT,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}

/****************************************************
 * @fn      Recv_listen_event
 *
 * @brief   //接收被监听设备应答
 *
 * @param
 *
 * @return	int 应答包长度
 */
int Recv_listen_event(unsigned char *Pkg)
{
	printf("Recv_listen_event...\n");
	int pos=PAYLOAD_START+6;
	int result=Pkg[pos++];
	respond_null_payload_pkg(Pkg,0);
	if(result == 1)	//接受
	{
		if(m_stListen_Info.self_listenStatus == LISTEN_STATUS_STOP)
		{
			return ERROR;
		}
		i2s_listen_thread();
	}
	else
	{
		m_stListen_Info.self_listenStatus = LISTEN_STATUS_STOP;
	}
	
	return SUCCEED;
}

extern int g_fd_dsp;

extern pthread_mutex_t listen_data_mutex;
/****************************************************
 * @fn      Recv_Listen_Audio_Stream
 *
 * @brief   //接收被监听设备上传的音频流
 *
 * @param
 *
 * @return	int 应答包长度
 */
int invalid_stream_cnt=0;
int pos=0;

int listen_write_pos=0;

int Recv_Listen_Audio_Stream(unsigned char *Pkg)
{
	int i;
	int pos=PAYLOAD_START;
	pos+=6;
	int sampleRate=Pkg[pos++];
	int stream_length=(Pkg[6]<<8)+(Pkg[7])-7;
	//printf("sampleRate=%d,length=%d\n",sampleRate,stream_length);

	if(sampleRate == 0)
	{
		pthread_mutex_lock(&listen_data_mutex);
		printf("Recv_Listen_Audio_Stream:sampleRate=%d\n",sampleRate);
		int temp_rate=rx_monitor_stream.rx_monitor_samplerate;
		memset(&rx_monitor_stream,0,sizeof(stRx_monitor_stream));
		rx_monitor_stream.rx_monitor_samplerate=temp_rate;
		invalid_stream_cnt=1;
		listen_write_pos=0;
		Send_listen_status();
		pthread_mutex_unlock(&listen_data_mutex);
	}
	if(invalid_stream_cnt >= 1 && invalid_stream_cnt<=4)		//采样率改变，丢弃4个包
	{
		invalid_stream_cnt++;
		printf("invalid stream...\n");
		return 0;
	}
	else
	{
		rx_monitor_stream.rx_monitor_samplerate=sampleRate_array[sampleRate];
	}
	if(stream_length>0)
	{
		memcpy(rx_monitor_stream.rx_monitor_data[rx_monitor_stream.rx_monitor_read_cnt]+listen_write_pos,Pkg+pos,stream_length);
		listen_write_pos+=stream_length;
		if(listen_write_pos>=512)
		{
			//printf("listen_write_pos=%d\n",listen_write_pos);
			rx_monitor_stream.rx_monitor_len[rx_monitor_stream.rx_monitor_read_cnt]=listen_write_pos;
			rx_monitor_stream.rx_monitor_valid[rx_monitor_stream.rx_monitor_read_cnt]=1;
			//printf("rx_monitor_stream.rx_monitor_read_cnt=%d,len=%d\n",rx_monitor_stream.rx_monitor_read_cnt,stream_length);
			rx_monitor_stream.rx_monitor_read_cnt++;
			rx_monitor_stream.rx_monitor_stream_cnt++;
			if(rx_monitor_stream.rx_monitor_read_cnt>=RX_MONITOR_BUF_PKG_MAX)
				rx_monitor_stream.rx_monitor_read_cnt=0;

			listen_write_pos=0;	
		}
	}
	
	return SUCCEED;
}



/****************************************************
 * @fn      Send_listen_status
 *
 * @brief   //监听设备发送监听状态
 *
 * @param
 *
 * @return	int 应答包长度
 */
int Send_listen_status()
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=13;

	memcpy(data,MAC_ADDR,6);		//监听端MAC
	memcpy(data+6,m_stListen_Info.self_listenedMac,6);	//被监听端MAC

	data[12]=m_stListen_Info.self_listenStatus;					//事件（开始/结束）

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_LISTEN_STATUS,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}

#endif


#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
/****************************************************
 * @fn      Send_host_get_account_storageCapacity
 *
 * @brief   //寻呼台向主机获取账户存储容量
 *
 * @param
 *
 * @return	int 应答包长度
 */
int Send_host_get_account_storageCapacity(const char *userAccount)
{
	//如果未登录，不获取
	if( !IsValidWin(WIN_CONTROL) )
	{
		return ERROR;
	}
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};

	int userAccount_len = strlen(userAccount);	//用户名长度
	int data_len=1+userAccount_len;
 
	data[0] = userAccount_len;					//用户名长度
	memcpy(data+1,userAccount,userAccount_len);	//用户名

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_HOST_GET_ACCOUNT_STORAGE_CAPACITY,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}



/****************************************************
 * @fn      recv_get_account_storageCapacity
 *
 * @brief   //寻呼台接收主机发下来的账户存储容量
 *
 * @param
 *
 * @return	int 应答包长度
 */
int recv_get_account_storageCapacity(unsigned char *Pkg)
{
	printf("recv_get_account_storageCapacity...\n");
	//如果未登录，不发送（因为需要登录用户名）
	if( !IsValidWin(WIN_CONTROL) )
	{
		return ERROR;
	}

	int pos=PAYLOAD_START;
	int account_len=0;
    char account[64]={0};
    unsigned long long storage_capacity=0;
    unsigned long long storage_used=0;
    unsigned long long storage_remaining=0;
	int compress_bitrate=0;

    account_len = Pkg[pos++];
    memcpy(account,Pkg+pos,account_len);
    pos+=account_len;

	if(strcmp(m_stUser_Info.CurrentUserName,account)!=0)
	{
		return ERROR;
	}

	storage_capacity = uCharsToLongLong(Pkg+pos);
	pos+=8;
	storage_used =	uCharsToLongLong(Pkg+pos);
	pos+=8;
	storage_remaining = uCharsToLongLong(Pkg+pos);
	pos+=8;

	compress_bitrate = uCharsToShort(Pkg+pos);
	pos+=2;

	songUploadInfo.storageCapacity=storage_capacity;
	songUploadInfo.storageSpaceUsed=storage_used;
	songUploadInfo.storageSpaceRemaining=storage_remaining;
	songUploadInfo.compress_bitrate=compress_bitrate;
	printf("storageCapacity:storage_capacity=%lld,storage_used=%lld,storage_remaining=%lld,compress_bitrate=%d\n",
			storage_capacity,storage_used,storage_remaining,compress_bitrate);

	//刷新存储标签栏
	musiclist_labelInfo_update(0);

	return SUCCEED;
}



/****************************************************
 * @fn      Send_host_request_upload_song
 *
 * @brief   //寻呼台请求上传歌曲文件
 *
 * @param
 *
 * @return	int 应答包长度
 */
int Send_host_request_upload_song(char *songName,char *listID,int songSize,int songSize_compressed)
{
	//如果未登录，不接收
	if( !IsValidWin(WIN_CONTROL) )
	{
		return ERROR;
	}
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	int songNameLength = strlen(songName);
	data[data_len++] = songNameLength;				//上传歌曲文件名长度
	memcpy(data+data_len,songName,songNameLength);	//上传歌曲文件名	
	data_len+=songNameLength;

	int listID_len = strlen(listID);
	data[data_len++] = listID_len;				//播放列表ID长度
	memcpy(data+data_len,listID,listID_len);	//播放列表ID
	data_len+=listID_len;

	data[data_len++]=((songSize)>>24) & 0xFF;	//歌曲文件大小
	data[data_len++]=((songSize)>>16) & 0xFF;
	data[data_len++]=((songSize)>>8) & 0xFF;
	data[data_len++]=songSize & 0xFF;

	data[data_len++]=((songSize_compressed)>>24) & 0xFF;	//预估压缩后歌曲文件大小
	data[data_len++]=((songSize_compressed)>>16) & 0xFF;
	data[data_len++]=((songSize_compressed)>>8) & 0xFF;
	data[data_len++]=songSize_compressed & 0xFF;


	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_HOST_REQUEST_UPLOAD_SONG_FILE,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}

/****************************************************
 * @fn      recv_request_upload_song
 *
 * @brief   //寻呼台接收请求上传歌曲文件应答
 *
 * @param
 *
 * @return	int 应答包长度
 */
int recv_request_upload_song(unsigned char *Pkg)
{
	printf("recv_request_upload_song...\n");
	//如果未登录，不发送（因为需要登录用户名）
	if( !IsValidWin(WIN_CONTROL) )
	{
		return ERROR;
	}

	int pos=PAYLOAD_START;
	int result=0;
	int uploadUrlLength=0;
    char uploadUrl[256]={0};

    result = Pkg[pos++];

	if(result!=0)
	{
		printf("recv_request_upload_song:error,result=%d\n",result);
	}

	int hostIP_length=Pkg[pos++];
	char hostIP[32]={0};
	memcpy(hostIP,Pkg+pos,hostIP_length);
	pos+=hostIP_length;

	short hostPort=uCharsToShort(Pkg+pos);
	pos+=2;

	uploadUrlLength = Pkg[pos++];
	memcpy(uploadUrl,Pkg+pos,uploadUrlLength);
	pos+=uploadUrlLength;

	if(g_network_mode == NETWORK_MODE_WAN)	//如果为WAN模式，不接收设备组播上线信息
	{
		sprintf(hostIP,"%s",g_host_tcp_prase_ipAddress);
	}
	
	printf("hostIP=%s,hostPort=%d,uploadUrl=%s\n",hostIP,hostPort,uploadUrl);
	char cgiPostUrl[384]={0};
	sprintf(cgiPostUrl,"http://%s:%d/%s",hostIP,hostPort,uploadUrl);
	printf("recv_request_upload_song:uploadUrl=%s\n",cgiPostUrl);

	sprintf(songUploadInfo.uploadUrl,"%s",cgiPostUrl);

	lzy_udisk_request_upload_change(result);

	return SUCCEED;
}


/****************************************************
 * @fn      Send_host_notfify_upload_status
 *
 * @brief   //寻呼台通知服务器上传状态
 *
 * @param
 *
 * @return	int 应答包长度
 */
int Send_host_notfify_upload_status(unsigned char event,char *listID,char *songName)
{
	//如果未登录，不接收
	if( !IsValidWin(WIN_CONTROL) )
	{
		return ERROR;
	}
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[data_len++] = event;

	int listID_len = strlen(listID);
	data[data_len++] = listID_len;				//播放列表ID长度
	memcpy(data+data_len,listID,listID_len);	//播放列表ID
	data_len+=listID_len;

	int songNameLength = strlen(songName);
	data[data_len++] = songNameLength;				//上传歌曲文件名长度
	memcpy(data+data_len,songName,songNameLength);	//上传歌曲文件名	
	data_len+=songNameLength;

	printf("Send_host_notfify_upload_status:listID=%s,songName=%s\n",listID,songName);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_HOST_NOTIFY_UPLOAD_STATUS,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}

/****************************************************
 * @fn      recv_upload_song_status
 *
 * @brief   //寻呼台接收通知服务器上传状态
 *
 * @param
 *
 * @return	int 应答包长度
 */
int recv_upload_song_status(unsigned char *Pkg)
{
	printf("recv_upload_song_status...\n");
	//如果未登录，不发送（因为需要登录用户名）
	if( !IsValidWin(WIN_CONTROL) )
	{
		return ERROR;
	}

	int pos=PAYLOAD_START;
	int result=0;

    result = Pkg[pos++];

	printf("recv_upload_song_status:result=%d\n",result);

	if(result == 0)
	{
		songUploadInfo.uploadStatus = UPLOAD_STATUS_SUCCEED; 
	}
	else if(result == 4)	//格式不符，歌曲异常
	{
		songUploadInfo.uploadStatus = UPLOAD_STATUS_FAILED_FORMAT; 
	}
	else if(result == 5)	//存储空间不足
	{
		songUploadInfo.uploadStatus = UPLOAD_STATUS_FAILED_NO_SPACE; 		
	}
	else	//其他异常
	{
		songUploadInfo.uploadStatus = UPLOAD_STATUS_FAILED_NORMAL; 
	}
	
	set_uploadSong_status_and_progress(songUploadInfo.uploadStatus,songUploadInfo.uploadProgress,0);

	return SUCCEED;
}

/****************************************************
 * @fn      recv_request_delete_song
 *
 * @brief   //寻呼台接收请求删除服务器歌曲应答
 *
 * @param
 *
 * @return	int 应答包长度
 */
int recv_request_delete_song(unsigned char *Pkg)
{
	printf("recv_request_delete_song...\n");
	//如果未登录，不接收（因为需要登录用户名）
	if( !IsValidWin(WIN_CONTROL) )
	{
		return ERROR;
	}

	int pos=PAYLOAD_START;
	int result=0;

    result = Pkg[pos++];

	printf("recv_request_delete_song:result=%d\n",result);

	lzy_serverList_delete_song_response(result);

	return SUCCEED;
}



/****************************************************
 * @fn      Send_host_request_delete_song
 *
 * @brief   //寻呼台请求删除服务器歌曲
 *
 * @param
 *
 * @return	int 应答包长度
 */
int Send_host_request_delete_song(unsigned char event,char *listID,char *songName)
{
	//如果未登录，不发送
	if( !IsValidWin(WIN_CONTROL) )
	{
		return ERROR;
	}
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[data_len++] = event;

	int listID_len = strlen(listID);
	data[data_len++] = listID_len;				//播放列表ID长度
	memcpy(data+data_len,listID,listID_len);	//播放列表ID
	data_len+=listID_len;

	int songNameLength = strlen(songName);
	data[data_len++] = songNameLength;				//上传歌曲文件名长度
	memcpy(data+data_len,songName,songNameLength);	//上传歌曲文件名	
	data_len+=songNameLength;

	printf("Send_host_request_delete_song:listID=%s,songName=%s\n",listID,songName);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_SEND_HOST_REQUEST_DELETE_SONG,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}

#endif


/****************************************************
 * @fn      host_set_broadcast_paging
 *
 * @brief   //响应主机控制寻呼台发起广播寻呼
 *
 * @param
 *
 * @return	int 应答包长度
 */
int host_set_broadcast_paging(unsigned char *Pkg)
{
	printf("host_set_broadcast_paging...\n");

	int pos=PAYLOAD_START;
	int result=0;
	
	//判断MAC是否与本机匹配
	if(memcmp(Pkg+pos,MAC_ADDR,6))
	{
		printf("host_set_broadcast_paging:mac not match!");
		return ERROR;
	}
	pos+=6;
    
	int user_name_length = Pkg[pos++];
	char user_name[64]={0};
	//注意，这里一定要用memcpy,不能用strcpy,否则可能异常
	memcpy(user_name,Pkg+pos,user_name_length);
	pos+=user_name_length;

	if( !IsValidWin(WIN_CONTROL) )
	{
		printf("Not logined!\n");
		send_host_set_broadcast_paging_result(1);	//用户未登录
		return ERROR;
	}
	if(strcmp(m_stUser_Info.CurrentUserName,user_name)!=0)
	{
		printf("UserName Not match!\n");
		send_host_set_broadcast_paging_result(2);	//用户不符
		return ERROR;
	}

	int control_event = Pkg[pos++];	//控制类型	1：开始广播	2：结束广播	3：开始对讲	4：结束对讲
	
	bool is_all_zone=Pkg[pos++];	//是否全部分区
	int zone_count=0;
	unsigned char zoneMacs[256*6]={0};
	if(!is_all_zone)
	{
		zone_count = Pkg[pos++];
		memcpy(zoneMacs,Pkg+pos,zone_count*6);
		pos+=zone_count*6;
		
		if(!(zone_count>0 && zone_count<=200))
		{
			send_host_set_broadcast_paging_result(3);	//用户不符
		}
	}

	send_host_set_broadcast_paging_result(0);
	//发起寻呼
	Paging_control_by_host(control_event,is_all_zone,zone_count,zoneMacs);
	return SUCCEED;
}

int send_host_set_broadcast_paging_result(int result)
{
	unsigned char sendBuf[CLIENT_BUF_SIZE]={0};
	int i=0,k=0;
	unsigned char data[1024]={0};
	int data_len=0;

	data[data_len++] = result;

	printf("send_host_set_broadcast_paging_result:=%d\n",result);

	int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_SET_BROADCAST_PAGING,DEVICE_MODEL_PAGING_A,data_len,data);
	// 发送数据
	if(UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip) != SUCCEED)
	{
		return ERROR;
	}
	return SUCCEED;
}


/********************************************************
 * @fn      get_concentrated_pkg_pos
 *
 * @brief   获取集中模式/TCP发送包有效位置
 *
 * @param	none
 *
 * @return  none
 *
 */
/********************************************************/
int get_concentrated_pkg_valid_pos(int pkg_cmd)
{
	if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL && g_network_mode == NETWORK_MODE_LAN)
	{
		return 0;
	}
	int i;
	int index=-1;
	//先查找有无相同命令字
	for(i=0;i<MAX_CONCENTRATED_CMD;i++)
	{
		if(st_Concentrated_Info.Last_CMD[i] == pkg_cmd)
		{
			index=i;
			break;
		}
	}
	if(index == -1)
	{
		//如果没查到，找一个空闲位置
		for(i=0;i<MAX_CONCENTRATED_CMD;i++)
		{
			if(!st_Concentrated_Info.Last_CMD[i])
			{
				index=i;
				break;
			}
		}
	}

	if(index == -1)
		index=0;
	return index;
}



/********************************************************
 * @fn      get_concentrated_pkg_pos
 *
 * @brief   根据命令字设置集中模式/TCP命令包
 *
 * @param	none
 *
 * @return  none
 *
 */
/********************************************************/
void set_concentrated_pkg_cmd(int pkgCmd,unsigned char *sendBuf,unsigned char sendLen)
{
	if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL && g_network_mode == NETWORK_MODE_LAN)
	{
		return;
	}
	int index=get_concentrated_pkg_valid_pos(pkgCmd);
	st_Concentrated_Info.Last_CMD[index]=pkgCmd;
	memcpy(st_Concentrated_Info.Last_CMD_Pkg_Data[index],sendBuf,sendLen);
	st_Concentrated_Info.Last_CMD_Pkg_len[index]=sendLen;
	st_Concentrated_Info.Last_CMD_Pkg_SendCount[index]=0;

	if(st_Concentrated_Info.Host_RX_Zone_Flag)	//分区匹配
	{
		UDP_SendData(Send_UNICAST,st_Concentrated_Info.Last_CMD_Pkg_Data[index],st_Concentrated_Info.Last_CMD_Pkg_len[index],g_host_ip);
	}
	else	//分区表示不匹配
	{
		printf("\nPAGING_SEND_HOST_PLAY_SOURCE:Zone_Identify not match...\n");
		Paging_Send_ZoneList_To_Host_Concentrated();
	}
}



/********************************************************
 * @fn      set_concentrated_pkg_pos
 *
 * @brief   根据命令字清空集中模式/TCP命令包
 *
 * @param	none
 *
 * @return  none
 *
 */
void clear_concentrated_pkg_pos(int pkg_cmd)
{
	if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL && g_network_mode == NETWORK_MODE_LAN)
	{
		return;
	}
	int i;
	int index=-1;
	for(i=0;i<MAX_CONCENTRATED_CMD;i++)
	{
		if(st_Concentrated_Info.Last_CMD[i] == pkg_cmd)
		{
			index=i;
			break;
		}
	}
	if(index!=-1)
	{
		st_Concentrated_Info.Last_CMD[index] = 0;
		st_Concentrated_Info.Last_CMD_Pkg_SendCount[index]=0;
	}
}




/********************************************************
 * @fn      Send_Host_ZoneInfo
 *
 * @brief   分区信息下发至主机应答的检测线程
 *
 * @param	none
 *
 * @return  none
 *
 */
/********************************************************/

void *Send_Host_ZoneInfo()
{
	int i;

	while(1)
	{
		if(g_system_work_mode == WORK_MODE_DISTRIBUTIONAL && g_network_mode == NETWORK_MODE_LAN)	//当处于LAN时不为集中模式
		{
			usleep(500000);
			continue;
		}
		if(st_Concentrated_Info.Host_RX_Zone_Flag)		//分区标识匹配
		{
			int find_cmd=0;
			//printf("\nSend_Host_ZoneInfo 114\n");
			for(i=0;i<MAX_CONCENTRATED_CMD;i++)
			{
				if( st_Concentrated_Info.Last_CMD[i] )	//存在命令字
				{
					find_cmd=1;
					//printf("\nSend_Host_ZoneInfo 115\n");
					if(st_Concentrated_Info.Last_CMD_Pkg_SendCount[i]<10)//少于10次重发（命令字为TCP传输音频流的除外，此命令不需要应答）
					{
						//printf("\nSend_Host_ZoneInfo 116\n");
						st_Concentrated_Info.Last_CMD_Pkg_SendCount[i]++;
						if(st_Concentrated_Info.Last_CMD_Pkg_SendCount[i]>1)
						{
							//发送数据
							UDP_SendData(Send_UNICAST,st_Concentrated_Info.Last_CMD_Pkg_Data[i],st_Concentrated_Info.Last_CMD_Pkg_len[i],g_host_ip);
						}
					}
					else
					{
						clear_concentrated_pkg_pos(st_Concentrated_Info.Last_CMD[i]);
					}
				}
			}
			usleep(200000);
		}
		else
		{
			//printf("\nst_Concentrated_Info.Zone_Num=%d\n",st_Concentrated_Info.Zone_Num);
			if(st_Concentrated_Info.Zone_Num>0)	//选中分区数>0
			{
				//printf("\nSend_Host_ZoneInfo 118\n");
				Paging_Send_ZoneList_To_Host_Concentrated();
			}
			usleep(300000);
		}

	}
}




void Send_Host_ZoneInfo_THREAD()
{
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Send_Host_ZoneInfo, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}

