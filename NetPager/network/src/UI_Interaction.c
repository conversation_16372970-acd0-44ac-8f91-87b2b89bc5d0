/*
 * UI_Interaction.c
 *
 *  Created on: Aug 28, 2020
 *      Author: king
 */

#include<stdio.h>
#include<stdlib.h>
#include<unistd.h>
#include<string.h>
#include<sys/socket.h>
#include<arpa/inet.h>
#include<netinet/in.h>
#include<sys/types.h>
#include<netdb.h>
#include<sys/ioctl.h>
#include<net/if.h>
#include<pthread.h>
#include <fcntl.h>

#include "network_client.h"
#include "Auxdio_protocol.h"
#include "Paging_win.h"
#include "http_client.h"
#include "xml.h"
#include "MusicFile.h"
#include "TimerP.h"
#include "sysconf.h"
#include "NetPager/win/control.h"
#include "NetPager/win/win.h"
#include "NetPager/win/settings_main_win.h"
#include "NetPager/win/theme.h"
#include "call_process.h"

extern char g_host_tcp_addr_domain[64];
extern char g_host_tcp_addr_domain2[64];
extern char g_host_tcp_prase_ipAddress[16];		//解析后的主机IP

#if defined(USE_SSD212) || defined(USE_SSD202)
#define stUsbInfo 	udiskInfo
#else
#define stUsbInfo 	bp1048_usb_info
#endif

/*********************************************************************
 * @fn      Update_Selected_Zone
 *
 * @brief   更新选中的分区信息
 *
 * @param
 *
 * @return ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
void Update_Selected_Zone()
{
	if(!(g_system_work_mode == WORK_MODE_CONCENTRATED || g_network_mode == NETWORK_MODE_WAN))
		return;
	int i;
	st_Concentrated_Info.Zone_Num=0;
	memset(st_Concentrated_Info.Zone_MAC,0,sizeof(st_Concentrated_Info.Zone_MAC));
	memset(st_Concentrated_Info.Zone_id,0,sizeof(st_Concentrated_Info.Zone_id));
	for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
	{
		if(m_stZone_Info.zoneInfo[i].g_zone_isSelect)
		{
			st_Concentrated_Info.Zone_id[st_Concentrated_Info.Zone_Num]=m_stZone_Info.zoneInfo[i].g_zone_id;
			memcpy(st_Concentrated_Info.Zone_MAC+6*st_Concentrated_Info.Zone_Num,m_stZone_Info.zoneInfo[i].g_zone_mac,6);
			st_Concentrated_Info.Zone_Num++;
		}
	}
	st_Concentrated_Info.Zone_Identify++;		//分区标识改变
	st_Concentrated_Info.Host_RX_Zone_Flag=0;	//主机应答标志置0
	memset(st_Concentrated_Info.Host_respone_PkgId,0,sizeof(st_Concentrated_Info.Host_respone_PkgId));
	//发送给主机,等待主机应答,由检测线程发送
	if(st_Concentrated_Info.Zone_Num >0)
	{
		//Paging_Send_ZoneList_To_Host_Concentrated();
	}
}


void control_terminal_volume(int vol)
{
	int i;
	if(g_network_mode == NETWORK_MODE_LAN && g_system_work_mode == WORK_MODE_DISTRIBUTIONAL)
	{
		NetPkgMutex_Lock(1);
		for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
		{
			if(m_stZone_Info.zoneInfo[i].g_zone_isSelect)
			{
				SendToZone_SetQueryVol(CMD_SET,m_stZone_Info.zoneInfo[i].g_zone_ip,vol);
			}
		}
		NetPkgMutex_Lock(0);
	}else
	{
		SendToZone_SetQueryVol(CMD_SET,g_host_ip,vol);
	}
}


void musiclist_play(int dir_id,int song_id)
{
	int i;

	if(dir_id<serverMusicList.DirNum && song_id<serverMusicList.SongDir[dir_id].nFileNum)
	{
		 if(g_system_work_mode == WORK_MODE_CONCENTRATED || g_network_mode == NETWORK_MODE_WAN)
		 {
			 PAGING_SEND_HOST_PLAY_SOURCE(1,serverMusicList.SongDir[dir_id].id,serverMusicList.SongDir[dir_id].musicfile[song_id].cName);
		 }
		 else
		 {
			 #if 0
			 NetPkgMutex_Lock(1);
			 for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
			 {
				if(m_stZone_Info.zoneInfo[i].g_zone_isSelect)
				{
					host_play_local_music(serverMusicList.SongDir[dir_id].id,serverMusicList.SongDir[dir_id].musicfile[song_id].cName,m_stZone_Info.zoneInfo[i].g_zone_ip);
				}
			 }
			 NetPkgMutex_Lock(0);
			 #endif
		 }
	}
}



void control_terminal_idle()
{
	int i;

	if(g_network_mode == NETWORK_MODE_LAN && g_system_work_mode == WORK_MODE_DISTRIBUTIONAL)
	{
		NetPkgMutex_Lock(1);
		for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
		{
			if(m_stZone_Info.zoneInfo[i].g_zone_isSelect)
			{
				SendToZone_Idle(m_stZone_Info.zoneInfo[i].g_zone_ip);
			}
		}
		NetPkgMutex_Lock(0);
	}
	else
	{
		SendToZone_Idle(g_host_ip);
	}


	//判断选中的分区是否是全部寻呼的分区,如果是的话,则停止寻呼

	for(i=0;i<PagingZoneInfo.ZoneNum;i++)
	{
		int zoneId=Get_ZoneIndex_By_MAC(PagingZoneInfo.ZoneInfo[i].ZoneMac);
		if(zoneId == -1)
			continue;
		if(	!m_stZone_Info.zoneInfo[ zoneId ].g_zone_isSelect	)
		{
			return;
		}
	}
	//停止寻呼
	Paging_status = PAGING_STOP;
	memset(&PagingZoneInfo,0,sizeof(PagingZoneInfo));		//清除寻呼分区信息

}





/**
 * PAGING BELL CLOSE
 * @param param
 */
static void Paging_bell_task(lv_task_t *param) {

	if(param->user_data)
	{
		lv_obj_del(param->user_data);
	}
	lv_task_del(param);
}


#if ENABLE_CALL_FUNCTION

#if USE_SSD202

void VideoCallExit_clean()
{
	ffplay_video_exit();
	rtspShutdown();
	memset(&m_stPager_Info.stVideoStream,0,sizeof(m_stPager_Info.stVideoStream));
}


static void VideoCall_close_task(lv_task_t *param) {

	if(video_call_main_cont)
	{
		lv_obj_del(video_call_main_cont);
		video_call_main_cont=NULL;
		printf("close videoCall win ok...\n");
		#if USE_SSD202
		m_stPager_Info.self_video_callStatus=VIDEO_CALL_STATUS_FREE;
		Send_video_call_status_feedback(m_stPager_Info.self_video_callStatus);
		VideoCallExit_clean();
		#endif
	}
	lv_task_del(param);
}

void close_videoCall_win_extern(int IsSelfcall,int Isdelay,int delayTime)
{
	printf("close_videoCall_win_extern...\n");
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}
	if(video_call_main_cont)
	{
		if(Isdelay)
		{
			lv_task_create(VideoCall_close_task, delayTime, LV_TASK_PRIO_MID, NULL);
		}
		else
		{
			lv_obj_del(video_call_main_cont);
			video_call_main_cont=NULL;
		}
	}
	else
	{
		printf("video_call_main_cont has been closed...\n");
	}

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}
#endif

static void Calling_close_task(lv_task_t *param) {

	if(video_call_main_cont)
	{
		lv_obj_del(video_call_main_cont);
		video_call_main_cont=NULL;
		printf("close videoCall win ok...\n");
		#if USE_SSD202
		m_stPager_Info.self_video_callStatus=VIDEO_CALL_STATUS_FREE;
		Send_video_call_status_feedback(m_stPager_Info.self_video_callStatus);
		VideoCallExit_clean();
		#endif
	}

	if(call_main_cont)
	{
		lv_obj_del(call_main_cont);
		call_main_cont=NULL;
		printf("close call win ok...\n");

		m_stPager_Info.self_callStatus=CALL_STATUS_FREE;
		Send_callStatus_feedback(m_stPager_Info.self_callStatus);
	}
	lv_task_del(param);
}

void close_calling_win_extern(int IsSelfcall,int Isdelay,int delayTime)
{

	printf("close_calling_win_extern...\n");
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}
	if(call_main_cont)
	{
		if(Isdelay)
		{
			lv_task_create(Calling_close_task, delayTime, LV_TASK_PRIO_MID, NULL);
		}
		else
		{
			lv_obj_del(call_main_cont);
			call_main_cont=NULL;
		}
		#if USE_SSD202
		Paging_status = PAGING_STOP;
		#endif
	}
	else
	{
		printf("call_main_cont has been closed...\n");
	}

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}
#endif



void Paging_proc(int IsSelfcall)
{
	int i=0;
	int num=0;

	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}

	if(Paging_status == CALLING_START)
	{
		static const char * btns[] = {"OK", ""};
		lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
		lv_msgbox_add_btns(msg, btns);
		lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
		lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
		lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
		lv_msgbox_set_text(msg,language_control_message_stopIntercom_text);
		goto QUIT;
	}

	for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
	{
		if(!m_stZone_Info.zoneInfo[i].g_zone_isHide && m_stZone_Info.zoneInfo[i].g_zone_isSelect)
		{
			num++;
			break;
		}
	}
	printf("Paging_proc:num=%d,pageType=%d\n",num,control_page_type);
	if(Paging_status == PAGING_STOP && num == 0)
	{
		static const char * btns[] = {"OK", ""};
		lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
		lv_msgbox_add_btns(msg, btns);
		lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
		lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
		lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
		if(control_page_type == PAGE_ZONE)
		{
			lv_msgbox_set_text(msg,language_control_message_selectOnlineZone_text);
		}
		else if(control_page_type == PAGE_GROUP)
		{
			lv_msgbox_set_text(msg,language_control_message_selectGroupNoOnlineZone_text);
		}
		else
		{
			lv_obj_del(msg);
		}
		goto QUIT;
	}

	if(Paging_status == PAGING_START)
	{
		//if(g_network_mode == NETWORK_MODE_LAN)
		SendToZone_Paging_Ready_Cmd_select_multicast(PCM_SEND_END,1);
		//如果存在使用TCP模式的分区，发送停止
		SendToZone_Paging_Ready_Cmd_select(PCM_SEND_END,0);

		printf("Paging_proc:Stop...\n");
		//停止寻呼
		Paging_status = PAGING_STOP;
		
		memset(&PagingZoneInfo,0,sizeof(PagingZoneInfo));		//清除寻呼分区信息

	}
	else
	{
		//保存寻呼分区信息
		memset(&PagingZoneInfo,0,sizeof(PagingZoneInfo));
		int zoneNum=0;
		for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
		{
			if(m_stZone_Info.zoneInfo[i].g_zone_isSelect)
			{
				memcpy(PagingZoneInfo.ZoneInfo[zoneNum].ZoneMac,m_stZone_Info.zoneInfo[i].g_zone_mac,6);
				zoneNum++;
			}
		}
		PagingZoneInfo.ZoneNum=zoneNum;
		Paging_status = PAGING_START;

		printf("Paging_proc:Start...\n");
		//printf("Paging_proc:PagingZoneInfo.ZoneNum=%d\n",PagingZoneInfo.ZoneNum);
		//if(g_network_mode == NETWORK_MODE_LAN)
		{
			SendToZone_Paging_Ready_Cmd_select_multicast(PCM_SEND_START,0);
		}
		
		//如果存在使用TCP模式的分区，发送开始
		SendToZone_Paging_Ready_Cmd_select(PCM_SEND_START,0);
		
#if USE_ASM9260
		i2s_read_thread();
#endif

		if(GetCurrentWin() == WIN_CONTROL)
		{
			if(paging_bell_selected && !paging_alarm_press_flag)	//开启了钟声播报且本次不是警报声
			{
				//static const char * btns[] = {"OK", ""};
				lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
				//lv_msgbox_add_btns(msg, btns);
				//lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
				//lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_24);
				lv_msgbox_set_text(msg,language_control_message_talkAfterRing_text);
				
				lv_task_create(Paging_bell_task, 4000, LV_TASK_PRIO_MID, msg);

			}
		}

		if(!IsSelfcall)
		{
			pthread_mutex_unlock(&lvglMutex);
			lvglMutex_status=0;
		}
		//此处提前点亮寻呼按钮,避免偶发性的寻呼按钮没有点亮的问题
		paing_status_show(IsSelfcall);
		return;
	}

QUIT:
	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}



void Paging_control_by_host(int control_event,int isAllZone,int zone_count,unsigned char *zone_macs)
{
	printf("Paging_control_by_host:control_event=%d,isAllZone=%d,zone_count=%d\n",control_event,isAllZone,zone_count);
	int i=0,k=0;
	int num=0;

	pthread_mutex_lock(&lvglMutex);
	lvglMutex_status=1;

	if(Paging_status != PAGING_STOP)
	{
		//if(g_network_mode == NETWORK_MODE_LAN)
		SendToZone_Paging_Ready_Cmd_select_multicast(PCM_SEND_END,1);
		//如果存在使用TCP模式的分区，发送停止
		SendToZone_Paging_Ready_Cmd_select(PCM_SEND_END,1);

		printf("Paging_proc:Stop...\n");
		//停止寻呼
		Paging_status = PAGING_STOP;
		
		memset(&PagingZoneInfo,0,sizeof(PagingZoneInfo));		//清除寻呼分区信息
	}

	if(control_event == 2)	//停止广播，上面已经停止，直接退出即可
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;

		return;
	}

	//选择相关分区
	for(i=0;i<m_stZone_Info.OnlineTotalZone;i++)
	{
		bool found=false;
		if(isAllZone)
		{
			if(!m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_isHide)
			{
				m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_isSelect=1;
				found=true;
			}
		}
		else
		{
			for(k=0;k<zone_count;k++)
			{
				if(memcmp(m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_mac,zone_macs+6*k,6) == 0)
				{
					if(!m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_isHide)
					{
						m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_isSelect=1;
						found=true;
						break;
					}
				}
			}
		}
		if(!found)
		{
			m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_isSelect=0;
		}
	}

	for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
	{
		if(!m_stZone_Info.zoneInfo[i].g_zone_isHide && m_stZone_Info.zoneInfo[i].g_zone_isSelect)
		{
			num++;
			break;
		}
	}

	printf("Paging_control_by_host:num=%d\n",num);

	Update_Selected_Zone();	//更新分区信息并通知主机


	//保存寻呼分区信息
	memset(&PagingZoneInfo,0,sizeof(PagingZoneInfo));
	int zoneNum=0;
	for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
	{
		if(m_stZone_Info.zoneInfo[i].g_zone_isSelect)
		{
			memcpy(PagingZoneInfo.ZoneInfo[zoneNum].ZoneMac,m_stZone_Info.zoneInfo[i].g_zone_mac,6);
			zoneNum++;
		}
	}
	PagingZoneInfo.ZoneNum=zoneNum;
	Paging_status = PAGING_START;

	printf("Paging_proc:Start...\n");
	//printf("Paging_proc:PagingZoneInfo.ZoneNum=%d\n",PagingZoneInfo.ZoneNum);
	//if(g_network_mode == NETWORK_MODE_LAN)
	{
		SendToZone_Paging_Ready_Cmd_select_multicast(PCM_SEND_START,0);
	}
	
	//如果存在使用TCP模式的分区，发送开始
	SendToZone_Paging_Ready_Cmd_select(PCM_SEND_START,0);
	
#if USE_ASM9260
	i2s_read_thread();
#endif

	if(GetCurrentWin() == WIN_CONTROL)
	{
		if(paging_bell_selected && !paging_alarm_press_flag)	//开启了钟声播报且本次不是警报声
		{
			//static const char * btns[] = {"OK", ""};
			lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
			//lv_msgbox_add_btns(msg, btns);
			//lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
			//lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
			lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_24);
			lv_msgbox_set_text(msg,language_control_message_talkAfterRing_text);
			
			lv_task_create(Paging_bell_task, 4000, LV_TASK_PRIO_MID, msg);

		}
	}


	pthread_mutex_unlock(&lvglMutex);
	lvglMutex_status=0;

	//此处提前点亮寻呼按钮,避免偶发性的寻呼按钮没有点亮的问题
	paing_status_show(0);

	control_win_update(0XFF,0);	//刷新页面
	return;
	

QUIT:

	pthread_mutex_unlock(&lvglMutex);
	lvglMutex_status=0;
}


#if ENABLE_CALL_FUNCTION
void call_proc(int IsSelfcall)
{
	int i=0;
	int num=0;

	if(Paging_status == PAGING_START)
	{
		static const char * btns[] = {"OK", ""};
		lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
		lv_msgbox_add_btns(msg, btns);
		lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
		lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
		lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
		lv_msgbox_set_text(msg,language_control_message_stopPaging_text);
		return;
	}

	printf("call_proc:pageType=%d\n",control_page_type);
	if(Paging_status == PAGING_STOP)
	{
		static const char * btns[] = {"OK", ""};
		lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
		lv_msgbox_add_btns(msg, btns);
		lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
		lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
		lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);

		if(control_page_type == PAGE_ZONE || control_page_type == PAGE_GROUP)
		{
			int zoneIndex=0;
			for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
			{
				if(!m_stZone_Info.zoneInfo[i].g_zone_isHide && m_stZone_Info.zoneInfo[i].g_zone_isSelect)
				{
					num++;
					zoneIndex=i;
				}
			}

			if(num!=1)
			{
				lv_msgbox_set_text(msg,language_control_message_selectOneIntercomDevice_text);
			}
			else
			{
				if(!IsSupportCallDevice(m_stZone_Info.zoneInfo[zoneIndex].g_zone_device_feature))
				{
					lv_msgbox_set_text(msg,language_control_message_selectZoneNoIntercom_text);
				}
				else
				{
					lv_obj_del(msg);
					//todo开始对讲
					printf("ready call.\n");

					Send_calling_invation(m_stZone_Info.zoneInfo[zoneIndex].g_zone_mac);
					m_stPager_Info.self_callStatus = CALL_STATUS_WAIT_CALLED_ANSWER;						//自身状态设为响铃，等待应答
					m_stPager_Info.other_callStatus = CALL_STATUS_WAIT_CALLED_ANSWER;		//将被叫方也先设置为准备接听，避免对讲窗口关闭
					create_call_cont(IsSelfcall);
					Start_Call_Ring_Play_Thread();

				}
			}
		}
		else if(control_page_type == PAGE_PAGER && m_stPager_Info.self_callStatus == CALL_STATUS_FREE)
		{
			int pager_num=0;
			int pager_index=-1;
			for(i=0;i<m_stPager_Info.TotalPager;i++)
			{
				if(m_stPager_Info.pagerInfo[i].isSelect)
				{
					pager_num++;
					pager_index=i;
				}
			}
			if(pager_num == 0)
			{
				lv_msgbox_set_text(msg,language_control_message_selectPager_text);
			}
			else if(pager_num > 1)
			{
				lv_msgbox_set_text(msg,language_control_message_selectOnePagerToTalk_text);
			}
			else if(!m_stPager_Info.pagerInfo[pager_index].isSupportCall)
			{
				lv_msgbox_set_text(msg,language_control_message_selectPagerNoIntercom_text);
			}
			else if(m_stPager_Info.pagerInfo[pager_index].source != SOURCE_NULL)
			{
				lv_msgbox_set_text(msg,language_control_message_calledBusy_text);
			}
			else
			{
				lv_obj_del(msg);
				//发送指令，准备开始对讲
				Send_calling_invation(m_stPager_Info.pagerInfo[pager_index].mac);
				m_stPager_Info.self_callStatus = CALL_STATUS_WAIT_CALLED_ANSWER;						//自身状态设为响铃，等待应答
				m_stPager_Info.other_callStatus = CALL_STATUS_WAIT_CALLED_ANSWER;		//将被叫方也先设置为准备接听，避免对讲窗口关闭
				create_call_cont(IsSelfcall);
				Start_Call_Ring_Play_Thread();
			}
		}
		else
		{
			lv_obj_del(msg);
		}
	}
	else if(Paging_status == CALLING_START)
	{
		printf("call_proc:Stop...\n");
		//停止寻呼
		Paging_status = PAGING_STOP;
	}
}
#endif


#if SUPPORT_AUTO_TRIGGER
void Paging_auto_trigger_change(int IsSignal,int ChangeSignal)
{
	int i,k=0;
	if(g_AuxIn_signal == IsSignal)
	{
		if(ChangeSignal)
			printf("Paging_auto_trigger_change:equal\n");
		return;
	}
	if(ChangeSignal)
		printf("Paging_auto_trigger_change:NewSignal=%d\n",IsSignal);
	if(g_AuxIn_signal)	//原来有信号，现在没信号
	{
		if(ChangeSignal)
		{
			g_AuxIn_signal = IsSignal;
		}
		//判断是否处于寻呼状态
		if(Paging_status == PAGING_START)
		{
			//判断是否处于AUX自动触发模式,是的话强制退出寻呼
			if( g_paging_mode == 1)
			{
				Paging_status = PAGING_STOP;
				
				SendToZone_Paging_Ready_Cmd_select_multicast(PCM_SEND_END,1);
				//如果存在使用TCP模式的分区，发送停止
				SendToZone_Paging_Ready_Cmd_select(PCM_SEND_END,0);
		
				memset(&PagingZoneInfo,0,sizeof(PagingZoneInfo));		//清除寻呼分区信息
			}
		}
	}
	else					//原来无信号，现在有信号
	{
		if(ChangeSignal)
		{
			g_AuxIn_signal = IsSignal;
		}
		if(Paging_status == PAGING_STOP && GetCurrentWin() != WIN_LOGIN)
		{
			//保存寻呼分区信息
			_PagingZoneInfo tmpPagingZoneInfo;
			memset(&tmpPagingZoneInfo,0,sizeof(tmpPagingZoneInfo));
			int zoneNum=0;

			//判断是否开启了自动触发功能
			if(g_auto_trigger_enable)
			{
				//判断是否设置了触发分组
				int group_ori_index=-1;
    			if( (group_ori_index=Get_group_ori_index_by_realId(g_auto_trigger_groupId)) >=0 )
				{
					//判断触发分组内是否有分区在线
					int groupZoneCnt=m_stGroup_Info.groupInfo[group_ori_index].AccountZoneNum;

					unsigned char mac_buf[6]={0};
					for(i=0;i<groupZoneCnt;i++)
					{
						memset(mac_buf,0,sizeof(mac_buf));
						memcpy(mac_buf,m_stGroup_Info.groupInfo[group_ori_index].g_zone_mac+i*6,6);
						for(k=0;k<m_stZone_Info.ExistTotalZone;k++)
						{
							if(memcmp(mac_buf,m_stZone_Info.zoneInfo[k].g_zone_mac,6) == 0)
							{
								//找到
								if(m_stZone_Info.zoneInfo[k].g_zone_conection && !m_stZone_Info.zoneInfo[k].g_zone_isHide)
								{
									//加入
									memcpy(tmpPagingZoneInfo.ZoneInfo[zoneNum++].ZoneMac,m_stZone_Info.zoneInfo[k].g_zone_mac,6);
								}
							}
						}
					}
				}
				//均满足条件，则开始寻呼
				tmpPagingZoneInfo.ZoneNum=zoneNum;
				if(tmpPagingZoneInfo.ZoneNum>0)
				{
					memcpy(&PagingZoneInfo,&tmpPagingZoneInfo,sizeof(PagingZoneInfo));

					Paging_status = PAGING_START;
					//标记此次是自动AUX触发寻呼
					g_paging_mode = 1;

					SendToZone_Paging_Ready_Cmd_select_multicast(PCM_SEND_START,0);
					//如果存在使用TCP模式的分区，发送开始
					SendToZone_Paging_Ready_Cmd_select(PCM_SEND_START,0);
					
					//此处提前点亮寻呼按钮,避免偶发性的寻呼按钮没有点亮的问题
					paing_status_show(1);
					#if !defined(USE_PC_SIMULATOR) &&  !defined(USE_SSD212) && !defined(USE_SSD202)
					i2s_read_thread();
					#endif
				}
			}
		}
	}
}
#endif




int exit_i2s_test=0;
int test_paging=0;
void Test_Paging()
{
	int count=0;
	while(!exit_i2s_test)
	{
		Paging_proc(0);
		printf("I2S TEST COUNT=%d\n",++count);
		sleep(1);
	}
	test_paging=0;
}


int Test_Paging_Thread()
{
	if(test_paging)
	{
		exit_i2s_test=1;
		return SUCCEED;
	}
	exit_i2s_test=0;
	test_paging=1;
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Test_Paging, NULL);
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}



void UI_UpdateSystemTime(int IsSelfcall)
{
	char date[32]={0};
	sprintf(date,"%04d-%02d-%02d  %02d:%02d",CURRENT_TIME.year<2024?2024:CURRENT_TIME.year,CURRENT_TIME.mon,CURRENT_TIME.day,CURRENT_TIME.hour,CURRENT_TIME.min);

	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}
	int currentWin = GetCurrentWin();
	if(currentWin == WIN_CONTROL || currentWin == WIN_MUSICLIST || currentWin == WIN_UDISK)
	{
		if(strcmp(lv_label_get_text(control_head_label),date))
		{
			lv_label_set_text(control_head_label, date);
		}
	}
	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}



void UI_mic_switch_show(int IsShow)
{
	#if (APP_TYPE == APP_JUSBE_MIXER)
	return;
	#endif
	g_Mic_enable=1;
	if(IsValidWin(WIN_CONTROL))
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
		if(IsShow)
		{
			lv_obj_set_state(mic_switch_btn,LV_STATE_DEFAULT);
			lv_obj_set_hidden(mic_switch_btn,false);
		}
		else
		{
			lv_obj_set_hidden(mic_switch_btn,true);
		}
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}

void UI_local_listen_button_refresh()
{
	#if (APP_TYPE == APP_JUSBE_MIXER)
	return;
	#endif
	if(IsValidWin(WIN_CONTROL))
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
		if(control_button_list[CONTROL_BUTTON_LISTEN].obj)
		{
			lv_obj_t *btn=lv_obj_get_child(control_button_list[CONTROL_BUTTON_LISTEN].obj, NULL);
			#if ENABLE_CALL_FUNCTION
			if(control_page_type != PAGE_PAGER)	//对讲页面会禁用本地监听，所以此处不处理
			#endif
			{
				if(g_Enable_local_listening)
				{
					lv_obj_set_state(btn,LV_STATE_CHECKED);
				}
				else
				{
					lv_obj_set_state(btn,LV_STATE_DEFAULT);
				}
			}
		}
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}

void UI_Group_ExitExtraMode()
{
	if(IsValidWin(WIN_CONTROL))
	{
		if(group_enter_index!=-1)
		{
			Zone_Group_SelectOrCancel(0,0xFFFF);	//先取消当前界面类型的按钮选中状态
			group_enter_index=-1;
			control_win_current_page=1;
			lv_obj_set_state(group_enter_btn, LV_STATE_DEFAULT);
		}
	}
}


//重置分区数据，由UI调用
void Reset_Zone_proc()
{
	static const char * btns[] = {"OK", ""};
	lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
	lv_msgbox_add_btns(msg, btns);
	lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
	char tips[128]={0};

	if(Paging_status == PAGING_START)
	{
		sprintf(tips,"%s",language_control_message_stopPaging_text);
		lv_msgbox_set_text(msg,tips);
		return;
	}

	pthread_mutex_lock(&ZoneInfoMutex);
	memset(&m_stZone_Info,0,sizeof(m_stZone_Info));
	//重置分区，同时清除隐藏分区
	memset(m_stZone_Info.DateTime,0,sizeof(m_stZone_Info.DateTime));
	Save_Zone_Info();
	//如果处于集中模式，发送到主机以便获取分区信息
	if(g_system_work_mode == WORK_MODE_CONCENTRATED || g_network_mode == NETWORK_MODE_WAN)
	{
		response_send_host_xml_file_info(NULL,XML_FILE_ZONEINFO);
		response_send_host_xml_file_info(NULL,XML_FILE_GROUP);
		response_send_host_xml_file_info(NULL,XML_FILE_MUSICLIST);
		response_send_host_xml_file_info(NULL,XML_FILE_AUDIO_COLLECTOR);
		response_send_host_xml_file_info(NULL,XML_FILE_USER);
		#if ENABLE_CALL_FUNCTION
		response_send_host_xml_file_info(NULL,XML_FILE_PAGER);
		#endif
	}
	pthread_mutex_unlock(&ZoneInfoMutex);

	sprintf(tips,"%s",language_control_message_resetZone_text);
	lv_msgbox_set_text(msg,tips);

	printf("\nReset_Zone_proc...\n");
}






//刷新设备信息(IP、服务器连接状态) 外部调用
void refresh_deviceInfo()
{
    if( GetCurrentWin() != WIN_SETTINGS_MAIN )
    {
        return;
    }

	pthread_mutex_lock(&lvglMutex);
	lvglMutex_status=1;

	if(systemInfo_ip && systemInfo_serverOnline && systemInfo_networkMode)
	{
		if(eth_link_status)
    	{
        	lv_label_set_text_fmt(systemInfo_ip,"%s :    %s","IP",ipAddress);
    	}
		else
		{
			#if defined(USE_SSD212) || defined(USE_SSD202)
			if(g_module_4G_status <= MODULE_4G_OFF)
			{
				lv_label_set_text_fmt(systemInfo_ip,"%s :    %s","IP",language_settings_device_netCableNotInsert_text);
			}
			else
			{
				lv_label_set_text_fmt(systemInfo_ip,"%s :    %s","IP",language_settings_device_netCableNotInsertAndUse4G_text);
			}
			#else
			lv_label_set_text_fmt(systemInfo_ip,"%s :    %s","IP",language_settings_device_netCableNotInsert_text);
			#endif
		}
    	lv_label_set_text_fmt(systemInfo_serverOnline,"%s :  %s",language_settings_device_connectStatus_text,(g_system_work_mode == WORK_MODE_CONCENTRATED) ?language_settings_device_connected_text:language_settings_device_disconnected_text);
		if(g_network_mode == NETWORK_MODE_LAN)
		{
			lv_label_set_text_fmt(systemInfo_networkMode,"%s :  UDP",language_settings_device_networkMode_text);
		}
		else
		{
			if(if_a_string_is_a_valid_ipv4_address(g_host_tcp_addr_domain2) || is_valid_domain(g_host_tcp_addr_domain2))
			{
				lv_label_set_text_fmt(systemInfo_networkMode,"%s :  TCP (%s、%s)",language_settings_device_networkMode_text,g_host_tcp_addr_domain,g_host_tcp_addr_domain2);
			}
			else
			{
				lv_label_set_text_fmt(systemInfo_networkMode,"%s :  TCP (%s)",language_settings_device_networkMode_text,g_host_tcp_addr_domain);
			}
		}
	}
	if(systemInfo_SerialNum)
	{
		lv_label_set_text_fmt(systemInfo_SerialNum,"%s :  %s","SN",g_device_serialNum);
	}

	pthread_mutex_unlock(&lvglMutex);
	lvglMutex_status=0;
}






void Back_to_Login_win()
{
	pthread_mutex_lock(&lvglMutex);
    lvglMutex_status=1;
	if( GetCurrentWin() == WIN_SETTINGS_MAIN )
	{
		lv_scr_load(screen_control);
        lv_obj_del(screen_settings);
        DeleteWin(WIN_SETTINGS_MAIN);

        int i;
        for(i=0;i<MAX_SIDEBAR_BTN_NUM;i++)
        {
            setting_right_area[i]=NULL;

            if(i == SIDER_BTN_SYSINFO)
            {
                systemInfo_ip=NULL;
                systemInfo_serverOnline=NULL;
            }
        }
        settings_current_item=DEFAULT_SETTINGS_ITEM;

        control_win_update(control_page_type,1);
	}

	if(GetCurrentWin() == WIN_MUSICLIST)
	{
		musiclist_back_to_control_win(1);
	}
	
	if(GetCurrentWin() == WIN_CONTROL)
	{
		if(Paging_status == PAGING_START || Paging_status == CALLING_START)
		{
			Paging_status = PAGING_STOP;
			usleep(500000);
		}
		lv_obj_del(control_all_cont);
		DeleteWin(WIN_CONTROL);
		login_win_start();
	}
	pthread_mutex_unlock(&lvglMutex);
    lvglMutex_status=0;
}






void Account_Zone_Update()
{
	int i=0,k=0;
	pthread_mutex_lock(&ZoneInfoMutex);
	pthread_mutex_lock(&UserInfoMutex);
	for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
	{
		if(strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) == 0)
		{
			m_stZone_Info.zoneInfo[i].g_zone_isHide = 0;
		}
		else
		{
			int valid_zone_flag=0;
			for(k=0;k<m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneCount;k++)
			{
				if(	memcmp(m_stZone_Info.zoneInfo[i].g_zone_mac,&m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneMac[6*k],6 ) == 0 )
				{
					valid_zone_flag=1;
					break;
				}
			}
			m_stZone_Info.zoneInfo[i].g_zone_isHide = valid_zone_flag?0:1;
		}
	}
	pthread_mutex_unlock(&UserInfoMutex);
	pthread_mutex_unlock(&ZoneInfoMutex);
}


void Account_Group_Update()
{
	int i=0,k=0,t;
	pthread_mutex_lock(&GroupListMutex);
	pthread_mutex_lock(&UserInfoMutex);
	for(i=0;i<m_stGroup_Info.TotalGroup;i++)
	{
		int group_userName_len=strlen(m_stGroup_Info.groupInfo[i].g_group_UserName);
		if(!group_userName_len)
		{
			if(strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) == 0)
			{
				m_stGroup_Info.groupInfo[i].g_group_isHide = 0;
				m_stGroup_Info.groupInfo[i].AccountZoneNum = m_stGroup_Info.groupInfo[i].ZoneNum;
			}
			else
			{
				//查看分组内是否存在有效分区，如果不存在，则不显示
				int valid_group_flag=0;
				int valid_group_zone_cnt=0;
				for(t=0;t<m_stGroup_Info.groupInfo[i].ZoneNum;t++)
				{
					unsigned char *t_group_mac=m_stGroup_Info.groupInfo[i].g_zone_mac+t*6;
					for(k=0;k<m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneCount;k++)
					{
						if(	memcmp(t_group_mac,&m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneMac[6*k],6 ) == 0 )
						{
							valid_group_flag=1;
							valid_group_zone_cnt++;
							break;
						}
					}
					#if 0
					if(valid_group_flag)
					{
						break;
					}
					#endif
				}
				m_stGroup_Info.groupInfo[i].g_group_isHide = valid_group_flag?0:1;
				m_stGroup_Info.groupInfo[i].AccountZoneNum = valid_group_zone_cnt;
			}
		}
		else
		{
			//判断分组是不是属于本账户
			if(strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) == 0 || \
			   strcmp(m_stUser_Info.CurrentUserName,m_stGroup_Info.groupInfo[i].g_group_UserName) == 0 )
			{
				m_stGroup_Info.groupInfo[i].g_group_isHide = 0;
				m_stGroup_Info.groupInfo[i].AccountZoneNum = m_stGroup_Info.groupInfo[i].ZoneNum;
			}
			else
			{
				m_stGroup_Info.groupInfo[i].g_group_isHide = 1;
				m_stGroup_Info.groupInfo[i].AccountZoneNum=0;
			}
		}
		
	}
	pthread_mutex_unlock(&UserInfoMutex);
	pthread_mutex_unlock(&GroupListMutex);
}



//刷新usb播放
void refresh_usbPlay(int IsSelfcall)
{
	if(g_bp1048_info.firmware_type != BP1048_FW_UDISK_TYPE)
		return;
	int pre_paging_type=g_paging_type;
	//龙之音V1版本不直接传输UDISK音频流
	#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
	g_paging_type = PAGING_TYPE_MIC;
	#else
	if(!stUsbInfo.IsPlug || stUsbInfo.playStatus!=BP1048_MUSIC_STATUS_PLAY)
	{
		g_paging_type = PAGING_TYPE_MIC;
	}
	else
	{
		g_paging_type = PAGING_TYPE_MUSIC;
	}
	#endif
	if(pre_paging_type!=g_paging_type)
	{
		if(Paging_status == PAGING_START)
		{
			if(g_paging_type == PAGING_TYPE_MUSIC)	//如果是音乐寻呼，那么切换
			{
				set_dsp_source(g_Enable_local_listening?BP1048_SOURCE_DECODE_PAGER_MUSIC_LISTENING:BP1048_SOURCE_DECODE_PAGER_MUSIC);
			}
			else	//如果是MIC寻呼
			{
				set_dsp_source(g_Enable_local_listening?BP1048_SOURCE_DECODE_PAGER_MIC_LISTENING:BP1048_SOURCE_DECODE_PAGER_MIC);
			}
		}
	}

	if( !stUsbInfo.IsPlug	&& GetCurrentWin() == WIN_UDISK )
	{
		udisk_back_to_control_win(IsSelfcall);
	}

    if( GetCurrentWin() == WIN_CONTROL )
    {
		if(!IsSelfcall)
		{
			pthread_mutex_lock(&lvglMutex);
			lvglMutex_status=1;
		}

		//U盘插拔状态
		if(control_button_list[CONTROL_BUTTON_UDISK].obj)
		{
			lv_obj_t *btn=lv_obj_get_child(control_button_list[CONTROL_BUTTON_UDISK].obj, NULL);
			#if ENABLE_CALL_FUNCTION
			if(control_page_type!=PAGE_PAGER)
			{
			#else
			{
			#endif
				if(stUsbInfo.IsPlug)
				{
					lv_obj_set_state(btn, LV_STATE_CHECKED);
				}
				else
				{
					lv_obj_set_state(btn, LV_STATE_DEFAULT);
				}
			}
		}
		if(!IsSelfcall)
		{
			pthread_mutex_unlock(&lvglMutex);
			lvglMutex_status=0;
		}
	}
	else if( GetCurrentWin() == WIN_UDISK )
	{
		if(!IsSelfcall)
		{
			pthread_mutex_lock(&lvglMutex);
			lvglMutex_status=1;
		}
		#if (!IS_LZY_NEW_TRANSMITTER_OR_PAGER)
		lv_obj_t *btn=lv_obj_get_child(musiclist_button_list[MUSICLIST_BUTTON_PLAY].obj, NULL);
		switch(stUsbInfo.playStatus)
		{
			case BP1048_MUSIC_STATUS_PLAY:
				lv_obj_set_state(btn, LV_STATE_CHECKED);
				lv_label_set_text(musiclist_button_list[MUSICLIST_BUTTON_PLAY].label,language_musiclist_pause_text);
			break;
			case BP1048_MUSIC_STATUS_PAUSE:
				lv_obj_set_state(btn, LV_STATE_DEFAULT);
				lv_label_set_text(musiclist_button_list[MUSICLIST_BUTTON_PLAY].label,language_musiclist_play_text);
			break;
			case BP1048_MUSIC_STATUS_STOP:
				lv_obj_set_state(btn, LV_STATE_DEFAULT);
				lv_label_set_text(musiclist_button_list[MUSICLIST_BUTTON_PLAY].label,language_musiclist_play_text);
			break;
		}
		#else
		lv_obj_t *btn=lv_obj_get_child(lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAY].obj, NULL);
		switch(stUsbInfo.playStatus)
		{
			case BP1048_MUSIC_STATUS_PLAY:
				lv_obj_set_state(btn, LV_STATE_CHECKED);
				lv_label_set_text(lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAY].label,language_musiclist_pause_text);
			break;
			case BP1048_MUSIC_STATUS_PAUSE:
				lv_obj_set_state(btn, LV_STATE_DEFAULT);
				lv_label_set_text(lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAY].label,language_musiclist_play_text);
			break;
			case BP1048_MUSIC_STATUS_STOP:
				lv_obj_set_state(btn, LV_STATE_DEFAULT);
				lv_label_set_text(lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAY].label,language_musiclist_play_text);
			break;
		}
		#endif

		if(!IsSelfcall)
		{
			pthread_mutex_unlock(&lvglMutex);
			lvglMutex_status=0;
		}
	}
}


void start_udiskPlay(int IsSelfcall,int dir_index,int song_index)
{
	#if USE_ASM9260
	BP1048_Send_Set_Usb_Play_File(bp1048_usb_info.musicList.SongDir[dir_index].musicfile[song_index].realId);
	#elif defined(USE_SSD212) || defined(USE_SSD202)
	udisk_play(1,dir_index,song_index);
	refresh_usbPlay(IsSelfcall);
	#endif
}


void stop_udiskPlay(int IsSelfcall)
{
	//停止播放
	#if USE_ASM9260
	Bp1048_Send_Set_Usb_PlayStatus(BP1048_MUSIC_STATUS_STOP);
	#elif defined(USE_SSD212) || defined(USE_SSD202)
	udisk_stop();
	refresh_usbPlay(IsSelfcall);
	#endif
}

//刷新usb播放
void pauseOrResume_udiskPlay(int IsSelfcall)
{
	//停止播放
	#if USE_ASM9260
	if(bp1048_usb_info.playStatus == BP1048_MUSIC_STATUS_PLAY)
	{
		//当前是播放，直接发送暂停
		Bp1048_Send_Set_Usb_PlayStatus(BP1048_MUSIC_STATUS_PAUSE);
	}
	else if(bp1048_usb_info.playStatus == BP1048_MUSIC_STATUS_PAUSE)
	{
		//当前是暂停，直接发送播放
		Bp1048_Send_Set_Usb_PlayStatus(BP1048_MUSIC_STATUS_PLAY);
	}
	#elif defined(USE_SSD212) || defined(USE_SSD202)
	udisk_pauseOrResume();
	refresh_usbPlay(IsSelfcall);
	#endif
}



void set_output_volume()
{
	#if !USE_ASM9260
	return;
	#endif
	
	#if ENABLE_CALL_FUNCTION
	if(g_isCallRinging)			//响铃中，铃声音量
	{
		printf("set_output_volume:g_ring_vol=%d\n",g_ring_vol);
		set_dsp_volume(g_ring_vol);
	}
	else if(Paging_status == CALLING_START)	//对讲音量
	{
		printf("set_output_volume:g_call_vol=%d\n",g_call_vol);
		set_dsp_volume(g_call_vol);
	}
	else if(g_Enable_local_listening)
	#else
	if(g_Enable_local_listening)
	#endif
	{
		printf("set_output_volume:g_listen_vol=%d\n",g_listen_vol);
		//开启了监听，那么设置监听音量
		set_dsp_volume(g_listen_vol);
	}

}




#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)

void lzy_udisk_request_upload_change(int result)
{
	printf("lzy_udisk_request_upload_change,result=%d...\n",result);
	if(GetCurrentWin() != WIN_UDISK)
		return;
	
	pthread_mutex_lock(&lvglMutex);
	lvglMutex_status=1;

	printf("result=%d\n",result);
	if(result == 0 || result == 1)		//0-一切就绪，准备上传,1-无歌曲列表，WEB自动创建一个
	{
		printf("ready upload...\n");
		//curl_upload_file(songUploadInfo.uploadUrl,songUploadInfo.uploadSongPath);
		create_uploadSong_cont();
		Start_Upload_File_Thread();
	}
	else if(result == 2 || result == 3)	//2-歌曲已经上传,3-歌曲存在于媒体库，但未加入到歌曲列表，服务器将其加入，也认为上传成功
	{
		printf("song isExist...\n");
		static const char * btns[] = {"OK", ""};
		lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
		lv_msgbox_add_btns(msg, btns);
		lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
		lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
		lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
		lv_msgbox_set_text(msg,language_musiclist_uploaded_text);
	}
	else if(result == 4)	//没有多余的存储空间
	{
		static const char * btns[] = {"OK", ""};
		lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
		lv_msgbox_add_btns(msg, btns);
		lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
		lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
		lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
		lv_msgbox_set_text(msg,language_musiclist_insufficient_storage_text);
	}

	pthread_mutex_unlock(&lvglMutex);
	lvglMutex_status=0;
}


void lzy_serverList_delete_song_response(int result)
{
	printf("lzy_serverList_delete_song_response,result=%d...\n",result);
	if(GetCurrentWin() != WIN_MUSICLIST)
		return;
	
	pthread_mutex_lock(&lvglMutex);
	lvglMutex_status=1;

	static const char * btns[] = {"OK", ""};
	lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
	lv_msgbox_add_btns(msg, btns);
	lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
	lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
	lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
	if(result == 0)
		lv_msgbox_set_text(msg,language_musiclist_song_delete_success_text);
	else
		lv_msgbox_set_text(msg,language_musiclist_song_delete_failed_text);
	
	pthread_mutex_unlock(&lvglMutex);
	lvglMutex_status=0;

	if(result == 0)
	{
		//提前取消列表选择且清空歌曲列表
		early_clean_muiscList(1);
	}
}

#endif