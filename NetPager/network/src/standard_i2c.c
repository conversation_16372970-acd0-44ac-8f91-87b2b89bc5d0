/**************************************************************************
  Filename:			standard_i2c.c

  Author:			lixiangkai

  Create Date:      2013-10-28

  Description:		I2C读写操作
**************************************************************************/

#include<stdio.h>
#include<linux/types.h>
#include<stdlib.h>
#include<fcntl.h>
#include<unistd.h>
#include<sys/types.h>
#include<sys/ioctl.h>
#include<errno.h>
#include<assert.h>
#include<string.h>
#include <pthread.h>

#include "i2c.h"
#include "sysconf.h"
#include "system_command.h"
int g_i2c_fd;

pthread_mutex_t	i2c_mutex=PTHREAD_MUTEX_INITIALIZER;


#if defined(USE_SSD212) || defined(USE_SSD202)
static int i2c1_fd=-1;

bool i2c1_init()
{
    if(i2c1_fd <=0)
    {
        i2c1_fd = open(I2C1_FILE_NAME, O_RDWR);      
        if (i2c1_fd <=0)
        {
            printf("i2c1_init faild: %s\n", I2C1_FILE_NAME);  
            return false;  
        }
    }
    printf("i2c1_init:%s succeed!\n",I2C1_FILE_NAME);
    return true;
}

bool i2c1_deinit()
{
    if(i2c1_fd >0)
    {   
        close(i2c1_fd);
        i2c1_fd=-1;
    }
    printf("i2c1_deinit:%s succeed!\n",I2C1_FILE_NAME);
    return true;
}

bool i2c1_write(unsigned char slave_addr, unsigned char reg_addr, unsigned char value)
{
     if (i2c1_fd <=0)
        return false;
    return i2c_write(i2c1_fd, slave_addr, reg_addr, value);
}

bool i2c1_read(unsigned char slave_addr, unsigned char reg_addr, unsigned char *value)
{
     if (i2c1_fd <=0)
        return false;
    return i2c_read(i2c1_fd, slave_addr, reg_addr, value);
}

static bool i2c_write(int fd,unsigned char slave_addr, unsigned char reg_addr, unsigned char value)
{
    unsigned char outbuf[2];
    struct i2c_rdwr_ioctl_data packets;
    struct i2c_msg messages[1];

    messages[0].addr  = slave_addr;
    messages[0].flags = 0;
    messages[0].len   = sizeof(outbuf);
    messages[0].buf   = outbuf;

    /* The first byte indicates which register we‘ll write */
    outbuf[0] = reg_addr;

    /* 
     * The second byte indicates the value to write.  Note that for many
     * devices, we can write multiple, sequential registers at once by
     * simply making outbuf bigger.
     */
    outbuf[1] = value;

    /* Transfer the i2c packets to the kernel and verify it worked */
    packets.msgs  = messages;
    packets.nmsgs = 1;
    if(ioctl(fd, I2C_RDWR, &packets) < 0) 
    {
        perror("Unable to send data");
        return false;
    }
	
	//printf("write data 0x%02x to slave_addr 0x%02x reg_addr 0x%02x\n", value, slave_addr, reg_addr);

    return true;
}

static bool i2c_read(int fd, unsigned char slave_addr, unsigned char reg_addr, unsigned char *value)
{
    unsigned char inbuf, outbuf;
    struct i2c_rdwr_ioctl_data packets;
    struct i2c_msg messages[2];

    /*
     * In order to read a register, we first do a "dummy write" by writing
     * 0 bytes to the register we want to read from.  This is similar to
     * the packet in set_i2c_register, except it‘s 1 byte rather than 2.
     */
    outbuf = reg_addr;
    messages[0].addr  = slave_addr;
    messages[0].flags = 0;
    messages[0].len   = sizeof(outbuf);
    messages[0].buf   = &outbuf;

    /* The data will get returned in this structure */
    messages[1].addr  = slave_addr;
    messages[1].flags = I2C_M_RD/* | I2C_M_NOSTART*/;
    messages[1].len   = sizeof(inbuf);
    messages[1].buf   = &inbuf;

    /* Send the request to the kernel and get the result back */
    packets.msgs      = messages;
    packets.nmsgs     = 2;
    if(ioctl(fd, I2C_RDWR, &packets) < 0) 
    {
        perror("Unable to send data");
        return false;
    }
    *value = inbuf;
	
	//printf("read data 0x%02x from slave_addr 0x%02x reg_addr 0x%02x\n", *value, slave_addr, reg_addr);

    return true;
}

#endif

/*********************************************************************
 * @fn      open_i2cdev
 *
 * @brief   打开I2C设备
 *
 * @param   void
 *
 * @return  成功返回0 失败返回-1
 */
int open_i2cdev(char *interface)
{
	return 0;
	g_i2c_fd = open(interface, O_RDWR);

	if (g_i2c_fd < 0)
	{
		perror("Open i2c device failed!\n");
        return ERROR;
    }
	else
	{
		printf("Open i2c device succeed!\n");
        return SUCCEED;
	}
}

/*********************************************************************
 * @fn      close_i2cdev
 *
 * @brief   关闭I2C设备
 *
 * @param   void
 *
 * @return  NULL
 */
void close_i2cdev(void)
{
	return;
	close(g_i2c_fd);
}

/*********************************************************************
 * @fn      write_1byte_to_i2cdev
 *
 * @brief   从I2C设备的任意地址写入一个字节的数据
 *
 * @param   dev_addr - 设备地址
 *			reg_addr - 读开始地址
 *          data - 需写入的数据
 *
 * @return  SUCCEED - 成功
 *          ERROR - 失败
 */
int write_1byte_to_i2cdev(unsigned char dev_addr, int reg_addr, int data)
{
	return 0;
	 int ret;
	struct i2c_rdwr_ioctl_data i2c_data;

	pthread_mutex_lock(&i2c_mutex);
	if(dev_addr == 0x32)
	{
		ret = open_i2cdev(I2C_DEV_0);
	}
	else
	{
		ret = open_i2cdev(I2C_DEV_0);
	}
    if(ret == ERROR)
    {
        if(Paging_status != PAGING_START) printf("Read_STA309a_Reg open i2cdev error!!!\n");
        pthread_mutex_unlock(&i2c_mutex);
        return ERROR;
    }

	i2c_data.nmsgs = 1; //只有一个开始状态
	i2c_data.msgs = (struct i2c_msg*)malloc(i2c_data.nmsgs*sizeof(struct i2c_msg));
	if(!i2c_data.msgs)
	{
		goto FAIL;
	}

	ioctl(g_i2c_fd, I2C_TIMEOUT, SET_TIMEOUT); //设置超时时间
	ioctl(g_i2c_fd, I2C_RETRIES, SET_RETRIES); //设置重发次数

	/*填写I2C信息*/
	i2c_data.msgs[0].len = 2; //信息长度为2，设备地址不算，因为已经赋值给了addr，而len是指buf中的值的个数
	i2c_data.msgs[0].addr = dev_addr; //设备地址
	i2c_data.msgs[0].flags = 0; //写命令
	i2c_data.msgs[0].buf = (unsigned char*)malloc(2); //分配缓存
	i2c_data.msgs[0].buf[0] = (reg_addr<<1) | (data>>8); 	//(此处WM8731寄存器地址为7位，值为9位，所以第一个数据需要处理)
	i2c_data.msgs[0].buf[1] = data&0x0FF; 						//去掉最高位


	if(!i2c_data.msgs[0].buf )
	{
		goto FAIL;
	}

	/*开始写数据到I2C设备 测试去掉锁*/

	ret = ioctl(g_i2c_fd, I2C_RDWR, (unsigned long)&i2c_data);

	if(ret < 0)
	{
		if(Paging_status != PAGING_START) printf("I2C write error!\n");
		goto FAIL;
	}
	else
	{
		#if _I2C_DEBUG_
		if(Paging_status != PAGING_START) printf("Write succeed dev_addr=%02x, reg_addr=%02x, data=%02x\n", dev_addr, reg_addr, data);
		#endif
		goto SUCCESS;
	}


	goto SUCCESS;

	SUCCESS:
	if(i2c_data.msgs[0].buf)
	{
		free(i2c_data.msgs[0].buf); //释放内存
		i2c_data.msgs[0].buf=NULL;
	}
	if(i2c_data.msgs)
	{
		free(i2c_data.msgs);
		i2c_data.msgs=NULL;
	}
	close_i2cdev();
	pthread_mutex_unlock(&i2c_mutex);
	return SUCCEED;


	FAIL:
	if(i2c_data.msgs[0].buf)
	{
		free(i2c_data.msgs[0].buf); //释放内存
		i2c_data.msgs[0].buf=NULL;
	}
	if(i2c_data.msgs)
	{
		free(i2c_data.msgs);
		i2c_data.msgs=NULL;
	}
	close_i2cdev();
	pthread_mutex_unlock(&i2c_mutex);
	return ERROR;

}

/*********************************************************************
 * @fn      read_1byte_from_i2cdev
 *
 * @brief   从I2C设备的任意地址读取一个字节的数据
 *
 * @param   dev_addr - 设备地址
 *			reg_addr - 读开始地址
 *
 * @return  >=0 - 成功
 *          ERROR - 失败
 */
char read_1byte_from_i2cdev(unsigned char dev_addr, int reg_addr)
{
	return 0;
    int ret;
	struct i2c_rdwr_ioctl_data i2c_data;
#if (!test_NOI2C)

	pthread_mutex_lock(&i2c_mutex);
	if(dev_addr == 0x32)
	{
		ret = open_i2cdev(I2C_DEV_0);
	}
	else
	{
		ret = open_i2cdev(I2C_DEV_0);
	}
    if(ret == ERROR)
    {
        if(Paging_status != PAGING_START) printf("Read_STA309a_Reg open i2cdev error!!!\n");
        pthread_mutex_unlock(&i2c_mutex);
        return ERROR;
    }
	i2c_data.nmsgs = 2; //读数据有两个开始状态
	i2c_data.msgs = (struct i2c_msg*)malloc(i2c_data.nmsgs*sizeof(struct i2c_msg));
	if(!i2c_data.msgs)
	{
		goto FAIL;
	}

	ioctl(g_i2c_fd, I2C_TIMEOUT, SET_TIMEOUT); //设置超时时间
	ioctl(g_i2c_fd, I2C_RETRIES, SET_RETRIES); //设置重发次数

	i2c_data.msgs[0].len = 1;
	i2c_data.msgs[0].addr = dev_addr;
	i2c_data.msgs[0].flags = 0; //写命令，看读时序理解
	i2c_data.msgs[0].buf = (unsigned char*)malloc(1);
	i2c_data.msgs[0].buf[0] = reg_addr; //需读的寄存器地址

	i2c_data.msgs[1].len = 1;
	i2c_data.msgs[1].addr = dev_addr;
	i2c_data.msgs[1].flags = I2C_M_RD; //读命令
	i2c_data.msgs[1].buf = (unsigned char*)malloc(1);
	i2c_data.msgs[1].buf[0] = 0; //清空要读的缓冲区


	if(!i2c_data.msgs[0].buf)
	{
		goto FAIL;
	}
	if(!i2c_data.msgs[1].buf)
	{
		goto FAIL;
	}


	/*Read data from i2c device*/

	ret = ioctl(g_i2c_fd, I2C_RDWR, (unsigned long)&i2c_data);

	if(ret < 0)
	{
		if(Paging_status != PAGING_START) printf("I2C read error!\n");
		goto FAIL;
	}
	else
	{
		#if _I2C_DEBUG_
		if(Paging_status != PAGING_START) printf("Read succeed! dev_addr=%02x, reg_addr=%02x, data=%02x\n", dev_addr, reg_addr, i2c_data.msgs[1].buf[0]);
		#endif
	}

#endif

	goto SUCCESS;

	SUCCESS:
	if(i2c_data.msgs[0].buf)
	{
		free(i2c_data.msgs[0].buf); //释放内存
		i2c_data.msgs[0].buf=NULL;
	}
	if(i2c_data.msgs[1].buf)
	{
		free(i2c_data.msgs[1].buf); //释放内存
		i2c_data.msgs[1].buf=NULL;
	}
	if(i2c_data.msgs)
	{
		free(i2c_data.msgs);
		i2c_data.msgs=NULL;
	}
	close_i2cdev();
	pthread_mutex_unlock(&i2c_mutex);
	return SUCCEED;


	FAIL:
	if(i2c_data.msgs[0].buf)
	{
		free(i2c_data.msgs[0].buf); //释放内存
		i2c_data.msgs[0].buf=NULL;
	}
	if(i2c_data.msgs[1].buf)
	{
		free(i2c_data.msgs[1].buf); //释放内存
		i2c_data.msgs[1].buf=NULL;
	}

	if(i2c_data.msgs)
	{
		free(i2c_data.msgs);
		i2c_data.msgs=NULL;
	}
	close_i2cdev();
	pthread_mutex_unlock(&i2c_mutex);
	return ERROR;
}

/*********************************************************************
 * @fn      write_nbyte_to_i2cdev
 *
 * @brief   从I2C设备的某个地址开始连续写N个字节的数据
 *
 * @param   dev_addr - 设备地址
 *			reg_addr - 读开始地址
 *          nbyte - 写入的字节个数
 *          *data - 数据指针
 *
 * @return  SUCCEED - 成功
 *          ERROR - 失败
 */
int write_nbyte_to_i2cdev(unsigned char dev_addr, int reg_addr, unsigned int nbyte, unsigned char *data)
{
	return 0;
    int i,ret;
	struct i2c_rdwr_ioctl_data i2c_data;
#if (!test_NOI2C)
    /*打开i2c设备 写入数据*/
	pthread_mutex_lock(&i2c_mutex);
	if(dev_addr == 0x32)
	{
		ret = open_i2cdev(I2C_DEV_0);
	}
	else
	{
		ret = open_i2cdev(I2C_DEV_0);
	}
    if(ret == ERROR)
    {
        if(Paging_status != PAGING_START) printf("open i2cdev error!!!\n");
        pthread_mutex_unlock(&i2c_mutex);
        return ERROR;
    }
	i2c_data.nmsgs = 1; //只有一个开始状态
	i2c_data.msgs = (struct i2c_msg*)malloc(i2c_data.nmsgs*sizeof(struct i2c_msg));
	if(!i2c_data.msgs)
	{
		goto FAIL;
	}
	
	ioctl(g_i2c_fd, I2C_TIMEOUT, SET_TIMEOUT); //设置超时时间
	ioctl(g_i2c_fd, I2C_RETRIES, SET_RETRIES); //设置重发次数
	
	/*填写I2C信息*/
	i2c_data.msgs[0].addr = dev_addr; //设备地址
	i2c_data.msgs[0].flags = 0; //写命令

	if(reg_addr >0xff || (reg_addr == 0 && dev_addr == 0x34) )
	{
		i2c_data.msgs[0].len = nbyte+2; //信息长度，设备地址不算，因为已经赋值给了addr，而len是指buf中的值的个数
	    i2c_data.msgs[0].buf = (unsigned char*)malloc(nbyte+2); //分配缓存
	    i2c_data.msgs[0].buf[0] = reg_addr/256; //寄存器地址
	    i2c_data.msgs[0].buf[1] = reg_addr%256; //寄存器地址
	    //if(Paging_status != PAGING_START) printf("\ni2c_data.msgs[0].buf[0]=%d",i2c_data.msgs[0].buf[0]);
	    //if(Paging_status != PAGING_START) printf("\ni2c_data.msgs[0].buf[1]=%d",i2c_data.msgs[0].buf[1]);
	}
	else if(reg_addr != 0xff)
	{
		i2c_data.msgs[0].len = nbyte+1; //信息长度，设备地址不算，因为已经赋值给了addr，而len是指buf中的值的个数
	    i2c_data.msgs[0].buf = (unsigned char*)malloc(nbyte+1); //分配缓存
	    i2c_data.msgs[0].buf[0] = reg_addr; //寄存器地址
	}
	else
	{
		i2c_data.msgs[0].len =nbyte;
		i2c_data.msgs[0].buf = (unsigned char*)malloc(nbyte); //分配缓存
	}

	if(!i2c_data.msgs[0].buf)
	{
		goto FAIL;
	}

    for(i=0; i<nbyte; i++)
	{
    	if(reg_addr >0xff || (reg_addr == 0 && dev_addr == 0x34) )
    		i2c_data.msgs[0].buf[i+2] = *(data+i); //需写入的数据
    	else if(reg_addr != 255)
    		i2c_data.msgs[0].buf[i+1] = *(data+i); //需写入的数据
    	else
    		i2c_data.msgs[0].buf[i] = *(data+i); //需写入的数据
	}
	
	/*开始写数据到I2C设备*/

	ret = ioctl(g_i2c_fd, I2C_RDWR, (unsigned long)&i2c_data);

	if(ret < 0)
	{
		if(Paging_status != PAGING_START) printf("I2C write error!\n");
		goto FAIL;
	}
	else
	{
		#if _I2C_DEBUG_
		if(Paging_status != PAGING_START) printf("Write succeed dev_addr=%02x, reg_addr=%02x, data:\n", dev_addr, reg_addr, data);
		for(i=0;i<nbyte;i++)
		{
			if(Paging_status != PAGING_START) printf("0x%x ",data[i]);
		}
		if(Paging_status != PAGING_START) printf("\n");
		#endif
		goto SUCCESS;
	}

#endif

	goto SUCCESS;

	SUCCESS:
	if(i2c_data.msgs[0].buf)
	{
		free(i2c_data.msgs[0].buf); //释放内存
		i2c_data.msgs[0].buf=NULL;
	}
	if(i2c_data.msgs)
	{
		free(i2c_data.msgs);
		i2c_data.msgs=NULL;
	}
	close_i2cdev();
	 pthread_mutex_unlock(&i2c_mutex);
	return SUCCEED;


	FAIL:
	if(i2c_data.msgs[0].buf)
	{
		free(i2c_data.msgs[0].buf); //释放内存
		i2c_data.msgs[0].buf=NULL;
	}
	if(i2c_data.msgs)
	{
		free(i2c_data.msgs);
		i2c_data.msgs=NULL;
	}
	close_i2cdev();
	 pthread_mutex_unlock(&i2c_mutex);
	return ERROR;
}

/*********************************************************************
 * @fn      read_nbyte_from_i2cdev
 *
 * @brief   从I2C设备的某个地址开始连续读取N个字节的数据
 *
 * @param   dev_addr - 设备地址
 *			reg_addr - 读开始地址
 *          nbyte - 读取的字节个数
 *          *data - 数据指针
 *
 * @return  SUCCEED - 成功
 *          ERROR - 失败
 */
int read_nbyte_from_i2cdev(unsigned char dev_addr, int reg_addr, unsigned int nbyte, unsigned char *data)
{
	return 0;
    int i, ret;
	struct i2c_rdwr_ioctl_data i2c_data;

#if (!test_NOI2C)

	pthread_mutex_lock(&i2c_mutex);
	/*打开i2c设备*/
	if(dev_addr == 0x32)
	{
		ret = open_i2cdev(I2C_DEV_0);
	}
	else
	{
		ret = open_i2cdev(I2C_DEV_0);
	}
    if(ret == ERROR)
    {
        if(Paging_status != PAGING_START) printf("Read_STA309a_Reg open i2cdev error!!!\n");
        pthread_mutex_unlock(&i2c_mutex);
        return ERROR;
    }

	if(reg_addr!=0xff)
	{
		i2c_data.nmsgs = 2; //读数据有两个开始状态
	}
	else
	{
		i2c_data.nmsgs = 1; //读数据有1个开始状态
	}
	i2c_data.msgs = (struct i2c_msg*)malloc(i2c_data.nmsgs*sizeof(struct i2c_msg));

	if(!i2c_data.msgs)
	{
		goto FAIL;
	}

	ioctl(g_i2c_fd, I2C_TIMEOUT, SET_TIMEOUT); //设置超时时间
	ioctl(g_i2c_fd, I2C_RETRIES, SET_RETRIES); //设置重发次数

	if(reg_addr!=0xff)
	{
		i2c_data.msgs[0].len = 1;
		i2c_data.msgs[0].addr = dev_addr;
		i2c_data.msgs[0].flags = 0; //写命令，看读时序理解
		i2c_data.msgs[0].buf = (unsigned char*)malloc(1);
		i2c_data.msgs[0].buf[0] = reg_addr; //需读的寄存器地址

		i2c_data.msgs[1].len = nbyte;
		i2c_data.msgs[1].addr = dev_addr;
		i2c_data.msgs[1].flags = I2C_M_RD; //读命令
		i2c_data.msgs[1].buf = (unsigned char*)malloc(nbyte);
	}
	else
	{
		i2c_data.msgs[0].len = nbyte;
		i2c_data.msgs[0].addr = dev_addr;
		i2c_data.msgs[0].flags = I2C_M_RD; //读命令
		i2c_data.msgs[0].buf = (unsigned char*)malloc(nbyte);
	}



	if(!i2c_data.msgs[0].buf)
	{
		goto FAIL;
	}
	if(reg_addr!=0xff)
	{
		if(!i2c_data.msgs[1].buf)
		{
			goto FAIL;
		}
	}


	if(reg_addr!=0xff)
	{
		memset(i2c_data.msgs[1].buf, 0x00, nbyte); //清空要读的缓冲区
	}
	else
		memset(i2c_data.msgs[0].buf, 0x00, nbyte); //清空要读的缓冲区

	/*Read data from i2c device*/

	ret = ioctl(g_i2c_fd, I2C_RDWR, (unsigned long)&i2c_data);

	if(ret < 0)
	{
		if(Paging_status != PAGING_START) printf("I2C write error!\n");
		goto FAIL;
	}
	else
	{
		/*获取读取的数据*/
		for(i=0; i<nbyte; i++)
		{
			if(reg_addr!=0xff)
			{
				*data++ = i2c_data.msgs[1].buf[i];
				#if _I2C_DEBUG_
				if(Paging_status != PAGING_START) printf("Read succeed! data=%02x\n", i2c_data.msgs[1].buf[i]);
				#endif
			}
			else
			{
				*data++ = i2c_data.msgs[0].buf[i];
				#if _I2C_DEBUG_
				if(Paging_status != PAGING_START) printf("Read succeed! data=%02x\n", i2c_data.msgs[0].buf[i]);
				#endif
			}
		}
		goto SUCCESS;
	}
#endif

	goto SUCCESS;

	SUCCESS:
	if(i2c_data.msgs[0].buf)
	{
		free(i2c_data.msgs[0].buf); //释放内存
		i2c_data.msgs[0].buf=NULL;
	}
	if(reg_addr!=0xff)
	{
		if(i2c_data.msgs[1].buf)
		{
			free(i2c_data.msgs[1].buf); //释放内存
			i2c_data.msgs[1].buf=NULL;
		}
	}
	if(i2c_data.msgs)
	{
		free(i2c_data.msgs);
		i2c_data.msgs=NULL;
	}
	close_i2cdev();
    pthread_mutex_unlock(&i2c_mutex);
	return SUCCEED;


	FAIL:
	if(i2c_data.msgs[0].buf)
	{
		free(i2c_data.msgs[0].buf); //释放内存
		i2c_data.msgs[0].buf=NULL;
	}
	if(reg_addr!=0xff)
	{
		if(i2c_data.msgs[1].buf)
		{
			free(i2c_data.msgs[1].buf); //释放内存
			i2c_data.msgs[1].buf=NULL;
		}
	}

	if(i2c_data.msgs)
	{
		free(i2c_data.msgs);
		i2c_data.msgs=NULL;
	}
	close_i2cdev();
    pthread_mutex_unlock(&i2c_mutex);
	return ERROR;
}
