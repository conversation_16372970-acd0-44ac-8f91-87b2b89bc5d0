/**********************************************************
*   Copyright (c) 2013 ，广州深宝音响
*   All rights reserved.
*
*   文件名称： Gpio.c
*   摘要： gpio操作
*
*   当前版本： 0.0.1
*   作者： ysl ，修改日期： 2013 年 11 月 20 日
*/
#include <stdio.h>
#include <errno.h>
#include <fcntl.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/ioctl.h>
#include <linux/types.h>

#include "Gpio.h"
#include "sysconf.h"



/*********************************************************************
 * @fn      Write_Set_GPIO_Data
 *
 * @brief   写入 设定gpio数据
 *
 * @param	int u_port:端口号
 * 			int u_pin：引脚
 * 			int u_data：数据
 *
 * @return  成功	- id号
 * 			失败	-	ERROR
 */
int Write_Set_GPIO_Data(int u_port, int u_pin, int u_data)
{
	int fd, ret;
	/*打开设备节点*/
	fd=open(GPIO_NODE, O_RDWR);
	if(fd < 0)
	{
		if(Paging_status != PAGING_START) printf("open error!!!\n");
		return ERROR;
	}
	if(Paging_status != PAGING_START) printf("Write_Set_GPIO_Data %d %d %d\n",u_port, u_pin, u_data);


	/*传递数据赋值*/
	localArg.port = u_port;		//ADC复位引脚
	localArg.pin = u_pin;
	localArg.data = u_data;		//低电平复位

	//先设置为GPIO MODE
	ioctl(fd, IOCTL_GPIO_SETPINMUX,&localArg);

	/*写入*/
	ret = ioctl(fd, IOCTL_GPIO_SETVALUE, &localArg);
	if(ret < 0)
	{
		perror("ioctl error!!!\n");
		close(fd);
		return ERROR;
	}

	 printf("gpio[%d][%d]=%d\n", localArg.port, localArg.pin, localArg.data);
	close(fd);
	return SUCCEED;
}





/*********************************************************************
 * @fn      Get_GPIO_Value
 *
 * @brief   读取GPIO输入电平值
 *
 * @param	int u_port:端口号
 * 			int u_pin：引脚
 *
 * @return  成功	- 电平值
 * 			失败	- ERROR
 */
 int Get_GPIO_Value(int u_port, int u_pin)
 {
	int fd, ret = -1;
	/*打开设备节点*/
	fd = open(GPIO_NODE, O_RDWR);
	if(fd < 0)
	{
		perror("ERROR GPIO open:");
		return ERROR;
	}

	/*设置需读取的引脚*/
	localArg.port = u_port;
	localArg.pin = u_pin;

	/*读取*/
	ret = ioctl(fd, IOCTL_GPIO_GETVALUE, &localArg);
	if(ret < 0)
	{
		perror("GPIO ioctl error:");
		close(fd);

		return ERROR;
	}
	//printf("gpio[%d][%d]=%d\n", localArg.port, localArg.pin, localArg.data);

	close(fd);

	return localArg.data;
 }





void MIC_Led_Show(unsigned char IsShow)
{
#if defined(USE_PC_SIMULATOR)
	return;
#endif
	#if defined(USE_SSD212) || defined(USE_SSD202)
	{
		GPIO_OutPut_Pager_Mic_Led(IsShow);			//MIC LED
		GPIO_OutPut_Pager_Led(1,IsShow);			//KEY LED1
	}
	#elif(USE_ASM9260)
	{
		Write_Set_GPIO_Data(17,5,IsShow);	//MIC LED
		Write_Set_GPIO_Data(10,5,IsShow);	//LED1
	}
	#endif
}


void Led2_Alarm_Show(unsigned char IsShow)
{
#if defined(USE_PC_SIMULATOR)
	return;
#endif
	#if defined(USE_SSD212) || defined(USE_SSD202)
	{
		GPIO_OutPut_Pager_Led(2,IsShow);	//KEY LED2
	}
	#elif(USE_ASM9260)
	{
		Write_Set_GPIO_Data(10,3,IsShow);	//LED2
	}
	#endif
}

#if defined(USE_SSD212) || defined(USE_SSD202)
//运放开关
void Signal_OutPut_switch(unsigned char IsEnable)
{
	Enable_Signal_Output(IsEnable);
}
#endif

//功放开关：IO低电平工作，高电平静音
void Amp_Switch(unsigned char IsEnable)
{
#if defined(USE_PC_SIMULATOR)
	return;
#endif

	#if defined(USE_SSD212) || defined(USE_SSD202)
	{
		Enable_Amp_Output(IsEnable);
	}
	#elif(USE_ASM9260)
	{
		Write_Set_GPIO_Data(17,7,!IsEnable);
	}
	#endif
}

//继电器控制
void RelayCtrl(unsigned char IsEnable)
{
#if defined(USE_PC_SIMULATOR)
	return;
#endif
	#if defined(USE_SSD212) || defined(USE_SSD202)
	{
		GPIO_OutPut_Pager_Relay(IsEnable);
	}
	#elif(USE_ASM9260)
	{
		Write_Set_GPIO_Data(14,3,IsEnable);
	}
	#endif
}


//网络灯控制
void NetLedCtrl(unsigned char IsEnable)
{
#if defined(USE_PC_SIMULATOR)
	return;
#endif
	#if defined(USE_SSD212) || defined(USE_SSD202)
	{
		GPIO_OutPut_Server_Connection(IsEnable);
	}
	#elif(USE_ASM9260)
	{
		Write_Set_GPIO_Data(14,2,IsEnable);
	}
	#endif
}


int Get_Gpio_Key_Value(int keyId)
{
#if defined(USE_PC_SIMULATOR)
	return;
#endif
	#if defined(USE_SSD212) || defined(USE_SSD202)
	{
		return GPIO_Get_Pager_Key_Value(keyId);
	}
	#elif(USE_ASM9260)
	{
		if(keyId == 1)	//key1为寻呼键
			return Get_GPIO_Value(10,6);
		if(keyId == 2)	//key2为消防警报键
			return Get_GPIO_Value(10,4);
	}
	#endif
	return 1;
}

#if (IS_TRANSMITTER)
int Get_Gpio_Tx_Switch_value()
{
	#if defined(USE_PC_SIMULATOR)
	return;
	#endif

	#if defined(USE_SSD212) || defined(USE_SSD202)
	{
		return GPIO_Get_Pager_Tx_Switch_Value();
	}
	#else if(USE_ASM9260)
	{
		return Get_GPIO_Value(14,0);
	}
	#endif
	return 1;	
}
#endif