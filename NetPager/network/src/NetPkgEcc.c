#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <net/if.h>
#include <pthread.h>
#include "NetPkgEcc.h"
#include "sysconf.h"
#include "Auxdio_protocol.h"

#include "Paging_win.h"

extern int unicast_socket_pager;

void NetPkgMutex_Lock(int IsLock);

pthread_mutex_t NetPkgMutex=PTHREAD_MUTEX_INITIALIZER;		//互斥锁




/*********************************************************************
 * @fn      pNetPkg_Check_Thread
 *
 * @brief   网络包应答纠错线程
 *
 * @param	none
 *
 * @return	none
 */

void *pNetPkg_Check_Thread()
{
	int i=0,index=0;
	struct sockaddr_in UnicastsocketAddr;
	struct NetPkgNode *pkgNode=m_stNetPkgEcc.pkgList;
	struct NetPkgNode *preNode=pkgNode;
	while(1)
	{
		if(g_network_mode == NETWORK_MODE_WAN)	//WAN模式不检测
		{
			sleep(1);
			continue;
		}

		NetPkgMutex_Lock(1);
		if(m_stNetPkgEcc.pkgList == NULL )
		{
			NetPkgMutex_Lock(0);
			usleep(200000);
			continue;
		}
		pkgNode=m_stNetPkgEcc.pkgList;
		preNode=pkgNode;
		index=0;
		while(pkgNode)
		{
			//printf("\npkgNode_index=%d,ip=%s,timeCount=%d",index,IPIntToChar(pkgNode->send_ip),pkgNode->timeCount);
			if(pkgNode->timeCount>=1 && pkgNode->timeCount<=5)	//超过500ms重新发送
			{
				//重新发送此数据包
				UnicastsocketAddr.sin_family = AF_INET; 		//选用TCP/IP协议
				UnicastsocketAddr.sin_port = htons(pkgNode->port);//注册端口
				inet_pton(AF_INET, IPIntToChar(pkgNode->send_ip), &UnicastsocketAddr.sin_addr);		//将char*型IP转换为网络地址
				sendto(unicast_socket_pager, pkgNode->send_buf, pkgNode->sendlen, 0,(struct sockaddr*)&UnicastsocketAddr, sizeof(UnicastsocketAddr));
				if(Paging_status != PAGING_START) printf("\nNetPkg_Timeout=%d,cmd_word=0x%x,ip=%s\n",pkgNode->timeCount,(pkgNode->send_buf[0]<<8)+pkgNode->send_buf[1],IPIntToChar(pkgNode->send_ip));
			}
			else if(pkgNode->timeCount>5)		//5S没有应答判定掉线
			{
				//删除此结点，并打印出错消息
				int cmd=(pkgNode->send_buf[0]<<8)+pkgNode->send_buf[1];
				if(Paging_status != PAGING_START) printf("\nNetPkg_Timeout,Del:cmd_word=0x%x\n",cmd);


				if(preNode)	//存在前驱结点 不是头结点
				{
					preNode->nextNode=pkgNode->nextNode;
				}
				if(pkgNode->nextNode)	//存在后继结点
				{
					pkgNode->nextNode->preNode=preNode;
				}

				if(pkgNode == m_stNetPkgEcc.pkgList)	//删除的是第一个
				{
					m_stNetPkgEcc.pkgList=pkgNode->nextNode;	//链表头指向下一个
				}

				free(pkgNode->send_buf);
				free(pkgNode);

				if(preNode)	//存在前驱结点
				{
					pkgNode=preNode->nextNode;
				}
				else
				{
					pkgNode=NULL;
				}


				continue;
			}
			//if(Paging_status != PAGING_START) printf("\nNetPkg_Timeout=%d,cmd_word=0x%x,ip=%s\n",pkgNode->timeCount,(pkgNode->send_buf[0]<<8)+pkgNode->send_buf[1],IPIntToChar(pkgNode->send_ip));
			pkgNode->timeCount++;
			index++;
			preNode=pkgNode;
			pkgNode=pkgNode->nextNode;
		}
		//pthread_mutex_unlock(&NetPkgMutex);
		NetPkgMutex_Lock(0);
		sleep(1);
	}

}

void NetPkg_Check_Thread()
{
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)pNetPkg_Check_Thread, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}




/*********************************************************************
 * @fn      Add_NetPkgEcc
 *
 * @brief   增加网络纠错包
 *
 * @param	unsigned char *send_buf,int len,int ip,int port
 *
 * @return  成功	-	SUCCED
 * 			失败	-	ERROR
 */

void Add_NetPkgEcc( unsigned char *send_buf,int len,int cmd_word,long int ip,int port,int mutex_flag )
{
	if(len<9 || cmd_word == CMD_PLAY_LOCAL_MUSIC || cmd_word == CMD_SET_PLAYMODE)	//不满足长度要求
	{
		return;
	}
	//首先得到最后一个结点
	if(mutex_flag)
	{
		//pthread_mutex_lock(&NetPkgMutex);
		NetPkgMutex_Lock(1);
	}
	int i;
	struct NetPkgNode *pkgNode=m_stNetPkgEcc.pkgList;
	struct NetPkgNode *preNode=pkgNode;
	int found=0;
	//if(Paging_status != PAGING_START) printf("\nAdd_NetPkgEcc1,cmd_word=0x%x,ip=%s\n",cmd_word,IPIntToChar(ip));
	while(pkgNode)
	{
		//判断是否已在纠错列表里面，如果是，则重置信息
		if(pkgNode->send_ip == ip && pkgNode->port == port &&  pkgNode->cmd_word ==cmd_word)
		{
			found=1;
			free(pkgNode->send_buf);
			break;
		}

		preNode=pkgNode;
		pkgNode=pkgNode->nextNode;
	}
	if(!found)
	{
		pkgNode = (struct NetPkgNode *)calloc(1,sizeof(struct NetPkgNode));		//分配空间
		pkgNode->preNode=preNode;
		pkgNode->nextNode=NULL;

		if(preNode!=NULL)
			preNode->nextNode=pkgNode;
	}
	pkgNode->cmd_word=cmd_word;

	//(void *)memcpy(void *dest,const void *src,size_t);
	pkgNode->send_buf=(unsigned char *)calloc(1,len);
	memcpy(pkgNode->send_buf,send_buf,len);			//拷贝数据包
	pkgNode->port=port;
	pkgNode->send_ip=ip;
	pkgNode->sendlen=len;
	pkgNode->timeCount=0;

	if(m_stNetPkgEcc.pkgList == NULL)
	{
		m_stNetPkgEcc.pkgList=pkgNode;
	}

	pkgNode=m_stNetPkgEcc.pkgList;

	//if(Paging_status != PAGING_START) printf("\nAdd_NetPkgEcc2,cmd_word=0x%x,ip=%s\n",cmd_word,IPIntToChar(pkgNode->send_ip));

	if(mutex_flag)
	{
		//pthread_mutex_unlock(&NetPkgMutex);
		NetPkgMutex_Lock(0);
	}
}




/*********************************************************************
 * @fn      Del_NetPkgEcc
 *
 * @brief   删除网络纠错包
 *
 * @param	unsigned char *send_buf,int len,int ip,int port
 *
 * @return  成功	-	SUCCED
 * 			失败	-	ERROR
 */

void Del_NetPkgEcc( int cmd_word,int ip,int port )
{

	NetPkgMutex_Lock(1);
	struct NetPkgNode *pkgNode=m_stNetPkgEcc.pkgList;
	struct NetPkgNode *preNode=pkgNode;
	int i;
	while(pkgNode)
	{
		//判断是否已在纠错列表里面，如果是，则重置发送包timeCount
		//20200828 不判断应答duan
		//if(pkgNode->send_ip == ip && pkgNode->port == port && pkgNode->cmd_word == cmd_word )
		if(pkgNode->send_ip == ip && pkgNode->cmd_word == cmd_word )
		{

			//NetResponse_update(cmd_word,ip,port,pkgNode->send_buf,pkgNode->sendlen);

			if(preNode)	//存在前驱结点
			{
				preNode->nextNode=pkgNode->nextNode;
			}
			if(pkgNode->nextNode)
			{
				pkgNode->nextNode->preNode=preNode;
			}


			if(pkgNode == m_stNetPkgEcc.pkgList)	//删除的是第一个
			{
				m_stNetPkgEcc.pkgList=pkgNode->nextNode;
			}

			// printf("\nDel_NetPkgEcc,cmd_word=0x%x\n",cmd_word);
			//删除结点
			free(pkgNode->send_buf);
			free(pkgNode);
			pkgNode=NULL;
			break;
		}
		preNode=pkgNode;
		pkgNode=pkgNode->nextNode;
	}
	NetPkgMutex_Lock(0);
}





void NetPkgMutex_Lock(int IsLock)
{
	if(IsLock)
	{
		pthread_mutex_lock(&NetPkgMutex);
	}
	else
	{
		pthread_mutex_unlock(&NetPkgMutex);
	}

}



/*********************************************************************
 * @fn      NetResponse_update
 *
 * @brief  用于本机收到UDP应答包后处理,如果在设置终端参数后主动发送查询指令，则可省略此函数
 *
 * @param	unsigned char *send_buf,int len,int ip,int port
 *
 * @return  成功	-	SUCCED
 * 			失败	-	ERROR
 */

void NetResponse_update(int cmd_word,int ip,int port,unsigned char *send_buf,int len)
{
	//查找此IP在分区列表中的索引
#if 0
	int index=Get_ZoneIndex_By_Ip(ip);
	if(index == -1)
		return;
#endif
	if(len!=PAYLOAD_START+1)		//空包才处理
	{
		return;
	}
	switch(cmd_word)
	{
	    case CMD_SET_ZONE_VOL:
	    {
	    	int type=send_buf[PAYLOAD_START];
	    	if(type == CMD_SET)
	    	{
	    		//发送查询音量指令
	    		SendToZone_SetQueryVol(CMD_QUERY,ip,0);
	    	}
	    }
	    break;
	    case CMD_SET_ZONE_MUTE_STATUS:
	    {
	    	int type=send_buf[PAYLOAD_START];
	    	if(type == CMD_SET)
	    	{
	    		//发送查询音量指令
	    		SendToZone_SetQueryVol(CMD_QUERY,ip,0);
#if 0
				if(GetActiveWindow() == hPaging_Wnd)
					PostMessage(hPaging_Wnd,MSG_PAGING_GROUP_UPDATE,0xff,0);	//此处所传信息包括分区id,寻呼窗体收到以后需要判断该分区是否属于寻呼当前页面中，属于才刷新
#endif
	    	}
	    }
	    break;
	}
}







/*
 * 2016-6-23
 * 循环检测待发送命令包,为了解决界面卡的问题
 */
void p_NetPkg_SEND()
{
	sleep(1);
	struct sockaddr_in UnicastsocketAddr;
	pthread_mutex_lock(&NetPkgMutex);
	struct NetPkgNode *pkgNode=m_stNetPkgEcc.pkgList;
	while(pkgNode)
	{
		UnicastsocketAddr.sin_family = AF_INET; 		//选用TCP/IP协议
		UnicastsocketAddr.sin_port = htons(pkgNode->port);//注册端口
		inet_pton(AF_INET, IPIntToChar(pkgNode->send_ip), &UnicastsocketAddr.sin_addr);		//将char*型IP转换为网络地址
		sendto(unicast_socket_pager, pkgNode->send_buf, pkgNode->sendlen, 0,(struct sockaddr*)&UnicastsocketAddr, sizeof(UnicastsocketAddr));
		pkgNode=pkgNode->nextNode;
	}
	pthread_mutex_unlock(&NetPkgMutex);
	pthread_exit(NULL);
}


void NetPkg_SEND_THREAD()
{
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)p_NetPkg_SEND, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}

