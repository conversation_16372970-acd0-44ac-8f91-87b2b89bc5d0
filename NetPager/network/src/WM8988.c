/**************************************************************************
  Filename:			standard_i2c.c
  
  Author:			lixiangkai
  
  Create Date:      2013-10-28
  
  Description:		I2C读写操作
**************************************************************************/

#include<stdio.h>
#include<linux/types.h>
#include<stdlib.h>
#include<fcntl.h>
#include<unistd.h>
#include<sys/types.h>
#include<sys/ioctl.h>
#include<errno.h>
#include<assert.h>
#include<string.h>
#include<linux/i2c.h>
#include<linux/i2c-dev.h>
#include <pthread.h>
#include <linux/soundcard.h>

#include "WM8988.h"
#include "sysconf.h"


unsigned int g_dsp_volume_val[24] = {0x162,0X163,0x164, 0x165, 0x166, 0x167,0x168,0x169, 0x16A, 0x16B,
									0x16C,0x16D, 0x16E, 0x16F, 0x170, 0x171, 0x172,0x173, 0x174, 0x175,
									0x176,0x177, 0x178, 0x179};


unsigned int g_level_volume_val[15] ={
                                 0x100,0x172, 0x173,0x174,0x175,
                                 0x176, 0x177, 0x178,0x179, 0x17A,0x17B,0x17C, 0x17D, 0x17E, 0x17F};


/***
 *模拟信号直通
 *	0x22h,0x23h,0x24h,0x25h
 */
int wm8988_Analog_bypass(int type)
{
	return 0;
	int i,ret;
	int BUF[4]={0};
	char Addr[4]={0x22,0x23,0x24,0x25};
	if(type == WM8988_BYPASS)
	{
		BUF[0]= 0x1A0;
		BUF[1]= 0x1A0;
		BUF[2]= 0x1A1;
		BUF[3]= 0x1A0;
	}
	else if(type == WM8988_NOBYPASS)
	{
		BUF[0]= 0x100;
		BUF[1]= 0x100;
		BUF[2]= 0x100;
		BUF[3]= 0x100;
	}
	for(i=0;i<4;i++)
	{
		ret=write_1byte_to_i2cdev(wm8731_devAddr,Addr[i],BUF[i]);
		if(ret!=SUCCEED)
		{
			if(Paging_status != PAGING_START) printf("\nwm8988_Analog_bypass error!!!\n");
			return ERROR;
		}
	}
	return SUCCEED;
}



/***
 *MIC静音(RIN2)
 *	0x22h,0x23h,0x24h,0x25h
 */
int wm8988_RINSEL_MUTE(int type)
{
	#if 0
	return 0;
	int i,ret;
	int BUF[1]={0};
	char Addr[1]={0x01};
	if(type == WM8988_RINSEL_MUTE)
	{
		BUF[0]= 0x197;
	}
	else if(type == WM8988_RINSEL_NOMUTE)
	{
		int val=g_mic_vol*0.24;
		if(val>=24)
		{
			val=24;
		}
		val|=0x100;
		BUF[0]=val;
	}
	for(i=0;i<1;i++)
	{
		ret=write_1byte_to_i2cdev(wm8731_devAddr,Addr[i],BUF[i]);
		if(ret!=SUCCEED)
		{
			if(Paging_status != PAGING_START) printf("\nwm8988_RINSEL_MUTE error!!!\n");
			return ERROR;
		}
	}
	#endif
	return SUCCEED;
}



/***
 *线路输入静音(LIN1)
 *	0x22h,0x23h,0x24h,0x25h
 */
int wm8988_LINSEL_MUTE(int type)
{
	#if 0
	return 0;
	int i,ret;
	int BUF[1]={0};
	char Addr[1]={0x00};
	if(type == WM8988_LINSEL_MUTE)
	{
		BUF[0]= 0x197;
	}
	else if(type == WM8988_LINSEL_NOMUTE)
	{

		int val=g_mic_vol*0.23;
		if(val>=23)
		{
			val=23;
		}
		val|=0x100;
		BUF[0]= val;
	}
	for(i=0;i<1;i++)
	{
		ret=write_1byte_to_i2cdev(wm8731_devAddr,Addr[i],BUF[i]);
		if(ret!=SUCCEED)
		{
			if(Paging_status != PAGING_START) printf("\nwm8988_RINSEL_MUTE error!!!\n");
			return ERROR;
		}
	}
	#endif
	return SUCCEED;
}




/*********************************************************************
 * @fn      set_lineOut_volume
 *
 * @brief   设置lineOut音量
 *
 * @param   vol - 音量值
 *
 * @return  void
 */
void set_lineOut_volume(int vol)
{
	return;
	int fd;
	int ret = -1;

	int val=vol*0.24;

	if(val>=24)
	{
		val=23;
	}
	val=g_dsp_volume_val[val];

	printf("\nset_lineOut_volume:vol=%d,val=%d\n",vol,val);

	int i;
	int BUF[1]={0};
	char Addr[1]={0x03};
	BUF[0]= val;
	for(i=0;i<1;i++)
	{
		ret=write_1byte_to_i2cdev(wm8731_devAddr,Addr[i],BUF[i]);
		if(ret!=SUCCEED)
		{
			if(Paging_status != PAGING_START) printf("\nwm8988_set_lineOut_volume error!!!\n");
			return;
		}
	}

}





/*********************************************************************
 * @fn      set_mic_level_volume
 *
 * @brief   设置麦克风等级音量
 *
 * @param   vol - 音量值
 *
 * @return  void
 */
void set_mic_level_volume(int vol)
{
	return;
	int fd;
	int ret = -1;

	int val=vol*0.15;

	if(val>=15)
	{
		val=14;
	}
	val=g_level_volume_val[val];
	//val=0x179;

	printf("\nset_mic_level_volume:vol=%d,val=%d\n",vol,val);

	int i;
	int BUF[1]={0};
	char Addr[1]={0x29};
	BUF[0]= val;
	for(i=0;i<1;i++)
	{
		ret=write_1byte_to_i2cdev(wm8731_devAddr,Addr[i],BUF[i]);
		if(ret!=SUCCEED)
		{
			if(Paging_status != PAGING_START) printf("\nwm8988_set_lineOut_volume error!!!\n");
			return;
		}
	}

}





#if 0
/***
 *设置麦克风音量
 *	Rout2 0x29
 */
int wm8988_SET_MIC_volume(int vol)
{
	int fd;
	int ret = -1;

	int val=vol*0.3;

	if(val>=30)
	{
		val=30;
	}

	val|=0x100;
	int Addr=0X01;


	ret=write_1byte_to_i2cdev(wm8731_devAddr,Addr,val);
	if(ret!=SUCCEED)
	{
		if(Paging_status != PAGING_START) printf("\nwm8988_SET_MIC_volume error!!!\n");
		return ERROR;
	}

	return SUCCEED;
}
#else

/***
 *设置麦克风音量
 *	Rout2 0x29
 */
int wm8988_SET_MIC_volume(int vol)
{
	return 0;
	int fd;
	int ret = -1;
	int i;
	int val=vol*0.23;

	if(val>=23)
	{
		val=23;
	}

	val|=0x100;
	//val=0x117;

	int BUF[2]={0};
	char Addr[2]={0x00,0x01};
	BUF[0]= val;
	BUF[1]= val;

	for(i=0;i<2;i++)
	{
		ret=write_1byte_to_i2cdev(wm8731_devAddr,Addr[i],BUF[i]);
		if(ret!=SUCCEED)
		{
			if(Paging_status != PAGING_START) printf("\nwm8988_SET_MIC_volume error!!!\n");
			return 0;
		}
	}

	return SUCCEED;
}
#endif


int ENABLE_ADC_PATH()
{
	return 0;
	 int i,ret;
//      int BUF[5]={0x40,0x00,0xFC,0x117,0x11C,0X118}; //2017年7月12日09:55:41修改前数据
	 int BUF[2]={0x1c9,0x1c9}; //0x11B +3dB
	 // 0x20>>0x40:LINPUT2 bypassed
	 // 0x21>>0x40 RINPUT2 bypassed
	 // 0x19>>0xFC
	 // 0X00>>0x11F
	 // 0x01>>0x11F
	 //
	 char Addr[2]={0x15,0x16};
	 for(i=0;i<2;i++)
	 {
			   ret=write_1byte_to_i2cdev(wm8731_devAddr,Addr[i],BUF[i]);
			   if(ret!=SUCCEED)
			   {
						printf("\nENABLE_ADC_PATH error!!!\n");
						return ERROR;
			   }
	 }
	 return SUCCEED;
}
