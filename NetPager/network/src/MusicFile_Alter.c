/*
*   Copyright (c) 2013 ，广州深宝音响
*   All rights reserved.
*
*   文件名称： MusicFile.c
*   摘要：
*
*   当前版本： 0.0.1
*   作者： ysl ，修改日期： 2013 年 10 月 24 日
*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <dirent.h>
#include <ctype.h>
#include <pthread.h>

#include "MusicFile_Alter.h"
#include "MusicFile.h"
#include "sysconf.h"


/*******目录下的歌曲数量(用于保存USB插入时解码端首先发过来的歌曲数目）************/
int rec_filenum[20];






/*****************/
static char *convert_letter( char *string)
{
	printf("\nconvert_letter!\n ");
	static char result[128]={0};
	memset(result,0,sizeof(result));
	strcpy(result,string);
	char *p = result;
    while(*p++ != '\0')
	{
		if( *p>='A' && *p<='Z')
		{
			*p=(*p+32);
		}
	}
    return result;
}





/*处理中文字符*/
/*遍历字符串，非ASCII字符读取2个字节，ASCII读取一个字节，获取字符串长度*/
int StrLenU(const char* string)
{
     int len = 0 ;
     const char* p = string;
     while(*p++ != '\0')
     {
         if(*p > 0x80 || *p < 0)
         {
			 len++;
            p++;
         }
         len++;
     }
     return len;
}


int StrLenU_UTF8(const char* string)
{
     int len = 0 ;
     const char* p = string;
     while(*p++ != '\0')
     {
         if(*p > 0x80 || *p < 0)
         {
			 len+=2;
            p+=2;
         }
         len++;
     }
     return len;
}


/*遍历字符串，非ASCII字符读取2个字节，ASCII读取一个字节，返回指定位置的字符串指针，默认从1开始*/
char* StrSetPosU(const char* string,int pos)
{
     char* result;
     result = (char *)string;
     while (result != NULL && *result != '\0' && pos > 1)
     {
         if(*result > 0x80 || *result < 0)
         {
             result++;
         }
         result++;
         pos--;
     }
     if(pos!=0)
         return result;
     return '\0';
}


/*遍历字符串，非ASCII字符读取2个字节，ASCII读取一个字节，返回指定位置的字符串指针，默认从1开始*/
char* StrSetPosU_UTF8(const char* string,int pos)
{
     char* result;
     result = (char *)string;
     while (result != NULL && *result != '\0' && pos > 1)
     {
         if(*result > 0x80 || *result < 0)
         {
             result+=2;
         }
         result++;
         pos--;
     }
     if(pos!=0)
         return result;
     return '\0';
}


/*可取中文字符串，当number为-1等负数时，取从start开始的剩余所有字符，默认从1开始*/
char* StringSubU(const char* string,int start,int number)
{
     int len = StrLenU(string) ;
     if(start>len)
     {
         printf("Start %d is too big than string length %d!\n",start,len);
         return "";
     }
     int bufsize = 0;
     int num = 1;
     const char* p = string;
     const char* start_char =string;
     /*重置指针，获取指定开始位置*/
     p = StrSetPosU(string,start);
     start_char = p;
     /*当取值为负值或字符串长度和取长度相等时，则取全部值*/
     if(number < 0 || number>=len)
     {
         while(*p != '\0')
         {
            p++;
            bufsize++;
         }
     }
     else
     {
         while(1)
         {
            /*当指针移到末尾，而且还没有获取指定数的字符时，说明此时指定字符数过多，将会取剩下的所有值*/
            if(*p == '\0' && num > 0)
            {
            	//printf("Number : %d is to big!\n",number);
				return (char*)string;
            }

            /*当字符为ASCII时，*/

			if(*p > 0x80 || *p < 0 )
			{
				if(num<=number)
				{
					p+=2;
					num+=2;
					bufsize+=2;
				}
				else
				{
					break;
				}
			}
			else
			{
				if(num<number)
				{
					p++;
					num++;
					bufsize++;
				}
				else
				{
					bufsize++;
					break;
				}
			}
         }
     }
     num = bufsize;
     /*开始分配内存*/
#if 0
     char* result ;
     result = (char*)malloc(sizeof(char)*(bufsize+1));
     memset(result,0,sizeof(char)*(bufsize+1));
#endif
     static char result[128]={0};
     memset(result,0,sizeof(result));

     /*开始复制字符串*/
     int i = 0;
     int j = 0;
     while(num != 0)
     {
         result[i++] = start_char[j++];
         num--;
     }
     /*尾部置零*/
     result[bufsize] = '\0';
     return result;
}






/*可取中文字符串，当number为-1等负数时，取从start开始的剩余所有字符，默认从1开始*/
char* StringSubU_UTF8(const char* string,int start,int number)
{
     int len = StrLenU_UTF8(string) ;
     if(start>len)
     {
         printf("Start %d is too big than string length %d!\n",start,len);
         return "";
     }
     int bufsize = 0;
     int num = 1;
     const char* p = string;
     const char* start_char =string;
     /*重置指针，获取指定开始位置*/
     p = StrSetPosU_UTF8(string,start);
     start_char = p;
     /*当取值为负值或字符串长度和取长度相等时，则取全部值*/
     if(number < 0 || number>=len)
     {
         while(*p != '\0')
         {
            p++;
            bufsize++;
         }
     }
     else
     {
         while(1)
         {
            /*当指针移到末尾，而且还没有获取指定数的字符时，说明此时指定字符数过多，将会取剩下的所有值*/
            if(*p == '\0' && num > 0)
            {
            	//printf("Number : %d is to big!\n",number);
				return (char*)string;
            }

            /*当字符为ASCII时，*/

			if(*p > 0x80 || *p < 0 )
			{
				if(num<=number)
				{
					p+=3;
					num+=3;
					bufsize+=3;
				}
				else
				{
					break;
				}
			}
			else
			{
				if(num<number)
				{
					p++;
					num++;
					bufsize++;
				}
				else
				{
					bufsize++;
					break;
				}
			}
         }
     }
     num = bufsize;
     /*开始分配内存*/
#if 0
     char* result ;
     result = (char*)malloc(sizeof(char)*(bufsize+1));
     memset(result,0,sizeof(char)*(bufsize+1));
#endif
     static char result[128]={0};
     memset(result,0,sizeof(result));

     /*开始复制字符串*/
     int i = 0;
     int j = 0;
     while(num != 0)
     {
         result[i++] = start_char[j++];
         num--;
     }
     /*尾部置零*/
     result[bufsize] = '\0';
     return result;
}



char* Get_DirName_From_Path(char *path)
{
    int i=0,count=0;

	static char path_name[MUSICFILEDIRLEN]={0};
	sprintf(path_name,"%s",path);
	char *dir_name=NULL;

    #if defined(USE_SSD212) || defined(USE_SSD202)
    if(strcmp(path,UDISK_MOUNT_DIR) == 0)
	{
		sprintf(path_name,language_musiclist_root_text);
        return path_name;
	}
    #endif

	//找到最后一个/
	char *lastSlashPos=strrchr(path_name,'/');
	if(lastSlashPos)
	{
        dir_name=lastSlashPos+1;
	}
    else
    {
        dir_name = path_name;
    }
    
	return dir_name;
    
}



char* Check_FileName(char *path)
{
    static char path_convert[128]={0};
	static char file_name[128]={0};
	memset(file_name,0,sizeof(file_name));
	memset(path_convert,0,sizeof(path_convert));

	if( strlen(path) == 0 || strlen(path)>128+8 )
	{
		memset(path,0,sizeof(path));
		strcpy(path,"");

		strcpy(path_convert,"");
		return path_convert;
	}

    char *p=strrchr(path,'.');
    char temp_buf[128]={0};
    if(p!=NULL)
    {
        strncpy(file_name,path,p-path);
        strcpy(temp_buf,file_name);
    }
    else
    {
        strcpy(temp_buf,path);
    }

    strcpy(path_convert,StringSubU_UTF8(temp_buf,1,24));
    
    return path_convert;
}


char* space_change(char *src)
{
	int i=0,j=0;
	int com_len=0;
	int musicLen=strlen(src);
	static char tmp_buf[256]={0};
	memset(tmp_buf,0,sizeof(tmp_buf));
	for(i=0; i< musicLen;i++)
	{
		tmp_buf[com_len+j]=src[i];
		if(src[i]==' ')   //判断空格
		{
			tmp_buf[com_len+j]='\\';
			tmp_buf[com_len+j+1]=' ';
			j =j+1;
		}
		else if((src[i]=='(')||(src[i]==')')||(src[i]=='[')||(src[i]==']')||(src[i]=='{')||(src[i]=='}'))   //判断“(”
		{
			tmp_buf[com_len+j]='\\';
			tmp_buf[com_len+j+1]=src[i];
			j =j+1;
		}
		else if(ispunct(src[i]))  //测试字符是否为特殊符号
		{

			tmp_buf[com_len+j]='\\';
			tmp_buf[com_len+j+1]=src[i];
			j =j+1;
		}
		j++;
	}
	tmp_buf[com_len+j]='\0';

	#if _DEBUG_MP_
	printf("space_change:tmp_buf:%s\n", tmp_buf);
	#endif

	return tmp_buf;
}





char* url_change(char *src)
{
	int i=0,j=0;
	int com_len=0;
	int musicLen=strlen(src);
	static char tmp_buf[256]={0};
	memset(tmp_buf,0,sizeof(tmp_buf));

	char *p=NULL;
	p=strtok(src," ");
	if(p)
	{
		int bit=24;
		sprintf(tmp_buf,"%s",p);
		while( (p=strtok(NULL," ")) )
		{
			strcat(tmp_buf,"%20");
			strcat(tmp_buf,p);
		}
	}
	return tmp_buf;
}
