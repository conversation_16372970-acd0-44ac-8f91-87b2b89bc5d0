/*
 * tcp_host.c
 *
 *  Created on: 2017-5-24
 *      Author: Administrator
 */
#include <stdarg.h>
#include <errno.h>
#include <stdio.h>
#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <time.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <dirent.h>
#include <errno.h>
#include <netinet/in.h>
#include <sys/socket.h>
#include <resolv.h>
#include <arpa/inet.h>
#include <stdlib.h>
#include <signal.h>
#include <getopt.h>
#include <pthread.h>
#include <netdb.h>

#include "sysconf.h"
#include "record/type_define.h"
#include "system_command.h"
#include "tcp_host.h"
#include "network_client.h"
#include "Paging_win.h"

static void process_wan_tcp_pkg(unsigned char *Pkg);

extern char Local_IP[20];

struct sockaddr_in Tcp_Server_addr;
int TCP_sockfd;


char g_host_tcp_addr_domain[64];	//主机地址(域名或者IP)
int g_host_tcp_port;		//主机TCP端口
char g_host_tcp_prase_ipAddress[16];		//解析后的主机IP

int g_current_connect_tcp_id=1;  //当前连接的tcp_id，默认是1-表示主服务器，2表示备用服务器
char g_host_tcp_addr_domain2[64];	//主机地址2(域名或者IP)
int g_host_tcp_port2;				//主机TCP端口2

#define TCP_RECV_TIMEOUT 2

extern int g_send_decode_pcm_type;	//发送的编码算法类型

/*****************    本地变量声明    ******************/
int g_tcp_connect_status=0;		//与主机的TCP连接状态
int g_exit_tcp_flag=0xff;		//退出TCP标志

int g_tcp_reconnect_flag=0;		//TCP重连标志

struct tcp_client tcpInfo;

/*****************    本地函数声明    ******************/
void TCP_Server_Thread();


/*****************  外部函数和变量声明 *****************/

extern int Is_exist_gps;



void Reset_tcp_queue_buf()
{
	//printf("\nReset_tcp_queue_buf...\n");
	#if 0
	available_package_payload_length=-1;
	memset(queue_buf,0,sizeof(queue_buf));
	queue_buf_len=0;
	#endif
	memset(&tcpInfo,0,sizeof(tcpInfo));
}




void Pkg_handle(int recvLen,unsigned char *recvBuf)
{
	    int	pos = 0;
        int iLength = recvLen;
		unsigned char *pData=recvBuf;
		//printf("tcp_recv_len1=%d\n",iLength);
        while(pos < iLength)
        {
            if(tcpInfo.pkg_len + (iLength - pos) <= 8) // 原有的数据+收到的数据都不够包头长度
            {
                memcpy(tcpInfo.pkg_data + tcpInfo.pkg_len, pData + pos, iLength - pos);
                tcpInfo.pkg_len += (iLength - pos);
                pos += iLength - pos;
            }
            else
            {
                if(tcpInfo.pkg_len < 8) // 原有的数据不够包头长，所以要构成包头
                {
                    memcpy(tcpInfo.pkg_data + tcpInfo.pkg_len, pData + pos, 8-tcpInfo.pkg_len);
                    pos += 8-tcpInfo.pkg_len;
                    tcpInfo.pkg_len = 8; // 包头
                }
                unsigned int dataLen = (*(tcpInfo.pkg_data + 6))*256 + (*(tcpInfo.pkg_data + 7)); // 数据长度（+6位置是固定的）
                unsigned int idleLen	= dataLen - (tcpInfo.pkg_len - 8) + 1;	// 一个包剩余的长度,+1是检验位

                if(iLength - pos < idleLen) // 收到的数据部分凑不够完整的包
                {
                    memcpy(tcpInfo.pkg_data+tcpInfo.pkg_len, pData + pos, iLength - pos);
                    tcpInfo.pkg_len += iLength - pos;
                    pos += iLength - pos;
                }
                else	// 可以构成完整的包
                {
                    memcpy(tcpInfo.pkg_data+tcpInfo.pkg_len, pData + pos, idleLen);
                    tcpInfo.pkg_len += idleLen;
                    pos += idleLen;

                    unsigned int command = (tcpInfo.pkg_data[0]<<8)+tcpInfo.pkg_data[1];
                    //printf("tcp:recv command=0x%04x\r\n",command);

#if 0
					//处理有效包available_buf，需要先判断校验和是否正确
					int payload_length=(tcpInfo.pkg_data[6]<<8)+tcpInfo.pkg_data[7];
					int des_crc = Calculate_XorDat(tcpInfo.pkg_data+NET_PACKAGE_MIN_SIZE-1,payload_length );
					int source_crc = tcpInfo.pkg_data[ payload_length + NET_PACKAGE_MIN_SIZE-1];
					if(des_crc == source_crc)	//检验通过
					{
						process_wan_tcp_pkg(tcpInfo.pkg_data);
					}
					else
					{
						printf("\nTCP Verify Error...\n");
					}
#endif
					process_wan_tcp_pkg(tcpInfo.pkg_data);

                    tcpInfo.pkg_len = 0; // 构成一个包后，重置为0，准备下一个包
                }
            }
        }
		//printf("tcp_recv_len2=%d\n",iLength);

}





//配置工具向终端设置网络模式

void ProcessHostSetNetWork(u8 * pkg,int pkg_len,bool isMulticast)
{
	printf("ProcessHostSetNetWork...\n");
	switch (pkg[PAYLOAD_START])
	{
		case CMD_QUERY : // 查询
		{
			printf("ProcessHostSetNetWork Query...\n");
			unsigned char sendBuf[TCP_MAX_RECV_SIZE]={0};
			int i=0,data_pos=0;
			unsigned char data[128]={0};
			data[data_pos++]=g_network_mode;

			data[data_pos++]=strlen(g_host_tcp_addr_domain);
			memcpy(&data[data_pos],g_host_tcp_addr_domain,strlen(g_host_tcp_addr_domain));
			data_pos+=strlen(g_host_tcp_addr_domain);
			data[data_pos++]=g_host_tcp_port>>8;
			data[data_pos++]=g_host_tcp_port;

			//备用服务器信息
			data[data_pos++]=strlen(g_host_tcp_addr_domain2);
			memcpy(&data[data_pos],g_host_tcp_addr_domain2,strlen(g_host_tcp_addr_domain2));
			data_pos+=strlen(g_host_tcp_addr_domain2);
			data[data_pos++]=g_host_tcp_port2>>8;
			data[data_pos++]=g_host_tcp_port2;

			int data_len=data_pos;

			int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_SET_WORK_MODE,DEVICE_MODEL_PAGING_A,data_len,data);
			// 发送数据
			unsigned char client=pkg[4];
			if (client == DEVICE_MODEL_HOST)
			{
				UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
			}
			else if(client == DEVICE_MODEL_NETWORK_TOOLS)
			{
				printf("ProcessHostSetNetWork:send to network tools\n");
				UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_networktools_ip);

				//再发送组播
				sendLen=Network_Send_Compose_CMD(sendBuf,CMD_NETWORK_MODE_MULTICAST,DEVICE_MODEL_PAGING_A,data_len,data);
				host_udp_multicast_send_cmd_data(sendBuf, sendLen);
			}
		}
			break;
		case CMD_SET : 	//设置
		{
			int pos=PAYLOAD_START+1;
			int temp_network_mode=pkg[pos++];

			char temp_tcp_addr_domain[64]={0};	//主机地址(域名或者IP)
			int temp_host_tcp_port=0;			//主机TCP端口

			char temp_tcp_addr_domain2[64]={0};	//主机地址2(域名或者IP)
			int temp_host_tcp_port2=0;			//主机TCP端口2

			int ip_len=pkg[pos++];
			memset(temp_tcp_addr_domain,0,sizeof(temp_tcp_addr_domain));
			memcpy(temp_tcp_addr_domain,&pkg[pos],ip_len>64?64:ip_len);
			pos+=ip_len;
			temp_host_tcp_port=(pkg[pos]<<8)+pkg[pos+1];
			pos+=2;

			if(pos<pkg_len-1)
			{
				int ip_len=pkg[pos++];
				memset(temp_tcp_addr_domain2,0,sizeof(temp_tcp_addr_domain2));
				memcpy(temp_tcp_addr_domain2,&pkg[pos],ip_len>64?64:ip_len);
				pos+=ip_len;
				temp_host_tcp_port2=(pkg[pos]<<8)+pkg[pos+1];
				pos+=2;

				if(pos<pkg_len-1)
				{
					//存在mac时，判断mac是否和当前mac相同，如果不同，不处理
					if(	memcmp(pkg+pos,MAC_ADDR,6)!= 0)
					{
					  	printf("ProcessHostSetNetWork:mac error!\n");
						return;
					}
					else
					{
						printf("ProcessHostSetNetWork:mac match!\n");
					}
				}
			}
			else
			{
				printf("not found tcp2...\n");
			}

			if(temp_network_mode == NETWORK_MODE_WAN)
			{
				//判断temp_tcp_addr_domain和temp_tcp_addr_domain2是否是正确的IP地址或者域名
				if(!if_a_string_is_a_valid_ipv4_address(temp_tcp_addr_domain) &&
				!is_valid_domain(temp_tcp_addr_domain) )
				{
					printf("ProcessHostSetNetWork:temp_tcp_addr_domain error\n");
					return;
				}
				if(!if_a_string_is_a_valid_ipv4_address(temp_tcp_addr_domain2) &&
				!is_valid_domain(temp_tcp_addr_domain2) )
				{
					printf("ProcessHostSetNetWork:temp_tcp_addr_domain2 error\n");
					memset(temp_tcp_addr_domain2,0,sizeof(temp_tcp_addr_domain2));
					temp_host_tcp_port2=0;
				}
			}


			int mode_change=0;
			if(g_network_mode!=temp_network_mode)
			{
				mode_change=1;
				g_network_mode=temp_network_mode;
			}
			printf("set g_network_mode=%d\n",g_network_mode);


			if(g_network_mode == NETWORK_MODE_LAN)
			{
				if(mode_change)
				{
					if(g_exit_tcp_flag!=0xff)	//TCP线程存在
					{
						g_exit_tcp_flag=1;
						int count=15;
						while(g_exit_tcp_flag!=0xff && count--)
						{
							usleep(200000);
						}
					}

					//网络模式变更，刷新
					refresh_deviceInfo();

					//关闭寻呼
					if(Paging_status == PAGING_START || Paging_status == CALLING_START)
					{
						Paging_status = PAGING_STOP;
						usleep(500000);
					}
					#if !NETWORK_VPN_INTERNET
					g_Is_tcp_real_internet=0;
					#endif

					//网络模式变更为LAN，那么编解码器需要恢复成PCM
					g_send_decode_pcm_type=DECODE_STANDARD_PCM;

					//先发送上线通知，告知服务器当前模式已改变
					send_online_info();

					save_sysconf("Network",NULL);
					//清除现有所有分区
					pthread_mutex_lock(&ZoneInfoMutex);
					memset(&st_Concentrated_Info,0,sizeof(st_Concentrated_Info));
					memset(&m_stZone_Info,0,sizeof(m_stZone_Info));
					memset(m_stZone_Info.DateTime,0,sizeof(m_stZone_Info.DateTime));
					Save_Zone_Info();
					pthread_mutex_unlock(&ZoneInfoMutex);
					
					usleep(200000);
					send_online_info();
					//更新文件
					response_send_host_xml_file_info(NULL,XML_FILE_ZONEINFO);
					response_send_host_xml_file_info(NULL,XML_FILE_GROUP);
					response_send_host_xml_file_info(NULL,XML_FILE_MUSICLIST);
					response_send_host_xml_file_info(NULL,XML_FILE_AUDIO_COLLECTOR);
					response_send_host_xml_file_info(NULL,XML_FILE_USER);
					#if ENABLE_CALL_FUNCTION
					response_send_host_xml_file_info(NULL,XML_FILE_PAGER);
					#endif
				}
			}
			else
			{
				if( mode_change || strcmp(temp_tcp_addr_domain,g_host_tcp_addr_domain)!=0 || temp_host_tcp_port != g_host_tcp_port ||\
					strcmp(temp_tcp_addr_domain2,g_host_tcp_addr_domain2)!=0 || temp_host_tcp_port2 != g_host_tcp_port2 )
				{
					printf("TCP INFO CHANGE!\n");

					//关闭寻呼
					if(Paging_status == PAGING_START || Paging_status == CALLING_START)
					{
						Paging_status = PAGING_STOP;
						usleep(500000);
					}

					sprintf(g_host_tcp_addr_domain,"%s",temp_tcp_addr_domain);
					g_host_tcp_port=temp_host_tcp_port;
					sprintf(g_host_tcp_addr_domain2,"%s",temp_tcp_addr_domain2);
					g_host_tcp_port2=temp_host_tcp_port2;

					if(g_exit_tcp_flag!=0xff)	//TCP线程存在
					{
						g_tcp_reconnect_flag=1;
					}
					else
					{
						TCP_Server_Thread();
					}
					//启动TCP 服务线程
					save_sysconf("Network",NULL);
					
					//网络模式变更，刷新
					refresh_deviceInfo();
				}
				else
				{
					printf("TCP INFO NOT CHANGE!\n");
				}
			}

			unsigned char sendBuf[TCP_MAX_RECV_SIZE]={0};
			int i=0,data_pos=0;
			unsigned char data[128]={0};
			data[data_pos++]=g_network_mode;

			data[data_pos++]=strlen(g_host_tcp_addr_domain);
			memcpy(&data[data_pos],g_host_tcp_addr_domain,strlen(g_host_tcp_addr_domain));
			data_pos+=strlen(g_host_tcp_addr_domain);
			data[data_pos++]=g_host_tcp_port>>8;
			data[data_pos++]=g_host_tcp_port;

			//备用服务器信息
			data[data_pos++]=strlen(g_host_tcp_addr_domain2);
			memcpy(&data[data_pos],g_host_tcp_addr_domain2,strlen(g_host_tcp_addr_domain2));
			data_pos+=strlen(g_host_tcp_addr_domain2);
			data[data_pos++]=g_host_tcp_port2>>8;
			data[data_pos++]=g_host_tcp_port2;

			int data_len=data_pos;

			int sendLen=Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_SET_WORK_MODE,DEVICE_MODEL_PAGING_A,data_len,data);
			// 发送数据
			unsigned char client=pkg[4];
			if (client == DEVICE_MODEL_HOST)
			{
				UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_host_ip);
			}
			else if(client == DEVICE_MODEL_NETWORK_TOOLS)
			{
				printf("ProcessHostSetNetWork:send to network tools\n");
				UDP_SendData(Send_UNICAST,sendBuf,sendLen,g_networktools_ip);
				//再发送组播
				sendLen=Network_Send_Compose_CMD(sendBuf,CMD_NETWORK_MODE_MULTICAST,DEVICE_MODEL_PAGING_A,data_len,data);
				host_udp_multicast_send_cmd_data(sendBuf, sendLen);
			}

		}
			break;
		default :
			break;
	}
}




/*
Linux下常见的socket错误码：
EACCES, EPERM：用户试图在套接字广播标志没有设置的情况下连接广播地址或由于防火墙策略导致连接失败。
EADDRINUSE 98：Address already in use（本地地址处于使用状态）
EAFNOSUPPORT 97：Address family not supported by protocol（参数serv_add中的地址非合法地址）
EAGAIN：没有足够空闲的本地端口。
EALREADY 114：Operation already in progress（套接字为非阻塞套接字，并且原来的连接请求还未完成）
EBADF 77：File descriptor in bad state（非法的文件描述符）
ECONNREFUSED 111：Connection refused（远程地址并没有处于监听状态）
EFAULT：指向套接字结构体的地址非法。
EINPROGRESS 115：Operation now in progress（套接字为非阻塞套接字，且连接请求没有立即完成）
EINTR：系统调用的执行由于捕获中断而中止。
EISCONN 106：Transport endpoint is already connected（已经连接到该套接字）
ENETUNREACH 101：Network is unreachable（网络不可到达）
ENOTSOCK 88：Socket operation on non-socket（文件描述符不与套接字相关）
ETIMEDOUT 110：Connection timed out（连接超时）
*/

void * TCP_COMMUNICATION(void * arg)
{
	int i;

	int ret = -1;
	fd_set readfd;
	int pkg_count = 0;
	struct timeval timeout;
	unsigned long count = 0;

	g_exit_tcp_flag=0;			//0代表TCP线程正常工作  		1表示需要关闭        0xFF表示没有启动


	g_tcp_connect_status=0;

	/*套接字信息赋值*/
	memset(&Tcp_Server_addr,0,sizeof(struct sockaddr_in));
	Tcp_Server_addr.sin_family = AF_INET; // IPV4

	//默认第一个tcp地址
	g_current_connect_tcp_id=1;
	bool isSlaveTcpAddrValid = false;
	if((if_a_string_is_a_valid_ipv4_address(g_host_tcp_addr_domain2) || is_valid_domain(g_host_tcp_addr_domain2)) && g_host_tcp_port2>0)
	{
		isSlaveTcpAddrValid = true;
	}
	while( g_network_mode == NETWORK_MODE_WAN && !g_exit_tcp_flag )
	{
		char *tcp_addr_domian=(g_current_connect_tcp_id == 1)?g_host_tcp_addr_domain:g_host_tcp_addr_domain2;
		int tcp_port=(g_current_connect_tcp_id == 1)?g_host_tcp_port:g_host_tcp_port2;

		struct hostent *host = gethostbyname(tcp_addr_domian);
		if(host == NULL) {
			if(isSlaveTcpAddrValid)
			{
				if(g_current_connect_tcp_id == 1)
				{
					g_current_connect_tcp_id=2;
					printf("DNS resolve failed for %s,try to connect TCP server2!\n", tcp_addr_domian);
				}
				else if(g_current_connect_tcp_id == 2)
				{
					g_current_connect_tcp_id=1;
					printf("DNS resolve failed for %s,try to connect TCP server1!\n", tcp_addr_domian);
				}
			}
			else
			{
				printf("DNS resolve failed for %s\n", tcp_addr_domian);
			}
			sleep(3);
			continue;
		}
		//将解析后的地址给到tcp_info->tcp_client_addr
		memset(&Tcp_Server_addr,0,sizeof(struct sockaddr_in));
		Tcp_Server_addr.sin_family = AF_INET;
		memcpy(&Tcp_Server_addr.sin_addr, host->h_addr_list[0], host->h_length);
		Tcp_Server_addr.sin_port = htons(tcp_port);
		//打印解析后的地址
		char ipAddress[INET_ADDRSTRLEN];
		inet_ntop(AF_INET, &Tcp_Server_addr.sin_addr,
			ipAddress, INET_ADDRSTRLEN);
		printf("tcp_client_communication:DNS resolve success for %s,ipAddress=%s,port=%d\n", tcp_addr_domian,ipAddress,tcp_port);

		//将解析后的地址赋值给g_host_tcp_prase_ipAddress
		inet_ntop(AF_INET, &Tcp_Server_addr.sin_addr, 
			g_host_tcp_prase_ipAddress, INET_ADDRSTRLEN);

		g_tcp_connect_status=0;
		g_exit_tcp_flag=0;
		Reset_tcp_queue_buf();
		/*创建套接字，SERVER_IPV4，TCP*/
		TCP_sockfd = socket(AF_INET, SOCK_STREAM, 0);
		if (TCP_sockfd >= 0)
		{
			printf("TCP_COMMUNICATION:Creat Socket Succeed!\n");
		}
		else
		{
			perror("TCP_COMMUNICATION:socket failed,exit thread!\n");
			close(TCP_sockfd);
			g_exit_tcp_flag=0xff;
			pthread_exit(NULL);
		}


		//设置为非阻塞
		unsigned long ul = 1;
        ioctl(TCP_sockfd, FIONBIO, &ul); //设置为非阻塞模式
#if 1
		int nRecvBuf=128*1024;//设置为128KB
		setsockopt(TCP_sockfd,SOL_SOCKET,SO_RCVBUF,(const char*)&nRecvBuf,sizeof(int));
#endif
		//connect会立即返回，可能返回成功，也可能返回失败。如果连接的服务器在同一台主机上，那么在调用connect 建立连接时，连接通常会立即建立成功。
		int conn = connect(TCP_sockfd, (struct sockaddr *)&Tcp_Server_addr, sizeof(struct sockaddr_in));
		if (conn == 0) {
			printf("Socket Connect Success Immediately.\n");
		}
		else 
		{
			printf("Get The Connect Result by select().\n");
			int connect_flag=0;
			if (errno == EINPROGRESS)
			{
				fd_set rfds, wfds;
				struct timeval tv;

				FD_ZERO(&rfds);
				FD_ZERO(&wfds);
				FD_SET(TCP_sockfd, &rfds);
				FD_SET(TCP_sockfd, &wfds);

				/* set select() time out */
				tv.tv_sec = 6;
				tv.tv_usec = 0;
				int selres = select(TCP_sockfd + 1, &rfds, &wfds, NULL, &tv);
				switch (selres)
				{
					case -1:
						printf("select error\n");
						ret = -1;
						break;
					case 0:
						printf("select time out\n");
						ret = -1;
						break;
					default:
					if (FD_ISSET(TCP_sockfd, &rfds) || FD_ISSET(TCP_sockfd, &wfds))
					{
						#if 0
						// not useable in linux environment, suggested in <<Unix network programming>> 
						int errinfo, errlen;
						if (-1 == getsockopt(TCP_sockfd, SOL_SOCKET, SO_ERROR, &errinfo, &errlen))
						{
							printf("getsockopt return -1.\n");
							ret = -1;
							break;
						}
						else if (0 != errinfo)
						{
							printf("getsockopt return errinfo = %d.\n", errinfo);
							ret = -1;
							break;
						}
						ret = 0;
						printf("connect ok?\n");
						#else
						if( connect(TCP_sockfd, (struct sockaddr *)&Tcp_Server_addr, sizeof(struct sockaddr_in)) !=0 )
						{
							int err = errno;
							if (err == EISCONN)
							{
								printf("TCP:Connect Succeed1!\n");
								ret = 0;
							}
							else
							{
								printf("TCP:connect failed,errno = %d\n", errno);
								ret = errno;
							}
						}
						else
						{
							printf("TCP:Connect Succeed2!\n");
							ret = 0;
						}
						
						#endif
					}
					else
					{
						printf("haha\n");
					}
					break;
				}
				if ( ret != 0)
				{
					if(ret == ECONNREFUSED)
					{
						int count=30;		//6s
						while( count-- && !g_exit_tcp_flag )
						{
							usleep(200000);
						}
					}
				}
				else
				{
					connect_flag=1;
				}
			}

			if(!connect_flag)
			{
				printf("\nTCP_COMMUNICATION:Connect failed,retry...\n");
				if(g_exit_tcp_flag)
				{
					printf("need exit_tcp,close tcp thread.\n");
				}
				else
				{
					close(TCP_sockfd);
				}

				//切换TCP主备连接
				if(g_current_connect_tcp_id == 1)
				{
					//备用服务器的IP是合法的IPV4地址，且端口号有效，那么切换到备用服务器
					if(isSlaveTcpAddrValid)
					{
						g_current_connect_tcp_id=2;
					}
					else
					{
						printf("Not found vaild tcp2\n");
					}
				}
				else if(g_current_connect_tcp_id == 2)
				{
					g_current_connect_tcp_id=1;
				}

				sleep(1);
				continue;
			}
		}

		if( g_exit_tcp_flag || g_network_mode == NETWORK_MODE_LAN) //模式改变或者需要退出
		{
			printf("g_exit_tcp_flag || g_network_mode == NETWORK_MODE_LAN,exit\n");
			continue;
		}

		//判断服务器IP地址是否处于公网
		#if !NETWORK_VPN_INTERNET
			if(strncmp(g_host_tcp_prase_ipAddress,"192",3) && strncmp(g_host_tcp_prase_ipAddress,"169",3) && strncmp(g_host_tcp_prase_ipAddress,"127",3) && strncmp(g_host_tcp_prase_ipAddress,"10.",3) && strncmp(g_host_tcp_prase_ipAddress,"29.",3) && strncmp(g_host_tcp_prase_ipAddress,"172",3) )	//既不是192开头也不是169、127、10、29开头，代表是公网IP
			{
				g_Is_tcp_real_internet=1;
			}
			else
			{
				g_Is_tcp_real_internet=0;
			}
		#endif

		#if SUPPORT_CODEC_G722
			#if 0
			g_send_decode_pcm_type=(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet)?DECODE_STANDARD_PCM:DECODE_G722;
			#else
			g_send_decode_pcm_type=(g_network_mode == NETWORK_MODE_LAN) ?DECODE_STANDARD_PCM:DECODE_G722;
			#endif
		#else
			g_send_decode_pcm_type=(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet)?DECODE_STANDARD_PCM:DECODE_G722_1;
		#endif

		printf("g_send_decode_pcm_type=%d\n",g_send_decode_pcm_type);

		//已经连接上，重新设置为阻塞模式
		ul = 0;
        ioctl(TCP_sockfd, FIONBIO, &ul);

		g_tcp_reconnect_flag=0;
		while(g_network_mode == NETWORK_MODE_WAN && !g_exit_tcp_flag)
		{
			if(g_tcp_reconnect_flag)
			{
				g_current_connect_tcp_id=1;	//默认第一个tcp地址
				g_tcp_connect_status=0;
				close(TCP_sockfd);
				break;
			}

			//开始读数据
			timeout.tv_sec = TCP_RECV_TIMEOUT; //获取数据超时时间设置
			timeout.tv_usec = 0;

			FD_ZERO(&readfd); //清空读文件描述集合
			FD_SET(TCP_sockfd, &readfd); //注册套接字文件描述符


			ret = select(TCP_sockfd+1, &readfd, NULL, NULL, &timeout);

			int connect_error=0;
			switch(ret)
			{
				case -1 : //调用出错
					perror("tcp:select error,reconnect");
					g_tcp_reconnect_flag=1;
					g_tcp_connect_status=0;
					connect_error=1;
					close(TCP_sockfd);
					break;

				case 0 : //超时
					//perror("TCP select TimeOut");
					Reset_tcp_queue_buf();
					break;

				default : //有数据可读 - 需要考虑粘包情况
					if (FD_ISSET(TCP_sockfd, &readfd))
					{
						/*读取数据*/
						unsigned char recvBuf[TCP_MAX_RECV_SIZE]={0};
						int recv_len = recv(TCP_sockfd, recvBuf, TCP_MAX_RECV_SIZE, 0);
						if( recv_len == 0 || recv_len == -1 )
						{
							//断开连接，重连
							printf("tcp_recv_error,reconnect\n");
							connect_error=1;
							g_tcp_reconnect_flag=1;
							g_tcp_connect_status=0;

							int count=30;		//3s
							while( count-- && !g_exit_tcp_flag )
							{
								usleep(100000);
							}

							break;
						}
						else
							Pkg_handle(recv_len,recvBuf);
					}
					break;
			}
			if(connect_error == 0 && g_tcp_connect_status == 0)
			{
				printf("connect_succeed OK!!!\n");
				g_tcp_connect_status=1;
				send_online_info();
				//重新连接上TCP后，发送账户信息,避免服务器重启后没有账户信息的问题；
				SendTo_Host_Account_Info();
				//龙之音V1版本重新获取存储空间信息
				#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
				Send_host_get_account_storageCapacity(m_stUser_Info.CurrentUserName);
				#endif
			}
		}
	}
	close(TCP_sockfd);
	g_tcp_connect_status=0;
	g_exit_tcp_flag=0xff;
	printf("TCP thread closed...\n");
	pthread_exit(NULL);
}



static void process_wan_tcp_pkg(unsigned char *Pkg)
{
	int cmd_word=GET_PKG_CMD(Pkg);
	//printf("\nprocess_wan_tcp_pkg:cmd_word=0x%04x\n",cmd_word);

	int device_model = Pkg[4];
	if( device_model == DEVICE_MODEL_HOST )
	{
		 g_host_device_TimeOut=0;

		 clear_concentrated_pkg_pos(cmd_word);
	}

	int Pkg_Length = (Pkg[6]<<8)+Pkg[7]+NET_PACKAGE_MIN_SIZE;	//总包长


	switch(cmd_word)
	{
		case CMD_SEND_SEARCH_ZONE_INFO :
			printf("HOST search device!\n");
			send_online_info();
			break;
		case CMD_TIME_SYNC :
			pkg_set_local_time(Pkg);
			break;
		case CMD_QUERY_TIME_INFO:			//主机向设备请求查询设备日期时间
			HOST_QUERY_TIME_INFO(Pkg);
			break;
		case CMD_HOST_QUERY_SET_IP_INFO:	//主机向终端设置IP属性
			HOST_QUERY_SET_IP_INFO(Pkg);
			break;
    	case CMD_SET_ALIAS :        		// 设置设备别名
    		pkg_set_device_alias(Pkg);
    		break;
    	case CMD_VERSION_QUERY : 	  		// 查询本机固件版本
    		pkg_query_firmware_version(Pkg);
    		break;
    	case CMD_FIRWARE_UPDATE :      		// 获取主机推送的固件升级信息
    		pkg_get_update_info(Pkg);
    		break;
    	case CMD_GET_HOST_FILE_SYNC_INFO:		//主机向终端请求更新文件
    		Get_UpdateXML_Info_From_Host(Pkg);
    		break;
		case CMD_QUERY_FILE:				//主机向终端查询存储在本地的文件信息
			response_send_host_xml_file_info(Pkg,NULL);
			break;

    	case CMD_SET_ZONE_IDLE_STATUS:		//主机设置终端空闲模式
    		response_cmd_set_zone_idle_status(Pkg);
    		break;
    	case CMD_SET_ZONE_MAC:				//主机请求重新分配MAC
    		host_set_terminal_mac(g_host_ip);
    		break;
    	case CMD_CONTROL_REBOOT:		//主机向终端发送重启指令
    		host_control_reboot(Pkg);
    		break;
    	case CMD_CONTROL_FORMAT:		//主机向设备请求清除数据
    		host_control_format(Pkg);
    		break;
    	case CMD_QUERY_FLASH_INFO:		//主机向设备请求查询FLASH信息
    		HOST_QUERY_FLASH_INFO(Pkg);
    		break;
    	case CMD_QUERY_MAC_INFO:		//主机向设备请求查询/设置MAC地址
    		HOST_QUERY_SET_MAC_INFO(Pkg);
    		break;
		case CMD_HOST_SEND_AUDIO_COLLECTOR_DEVICE_LIST:	//主机向其他控制设备下发音频采集器设备列表
    		HOST_SEND_AUDIO_COLLECTOR_DEVICE_LIST(Pkg);
    		break;
    	case CMD_QUERY_SET_WORK_MODE:	//主机向设备查询/设置工作模式
    		HOST_QUERY_SET_WORK_MODE(Pkg);
    		break;
    	case CMD_HOST_QUERY_SET_WORK_MODE:	//主机向终端设置网络模式
    		ProcessHostSetNetWork(Pkg,Pkg_Length,false);
    		break;
    	case CMD_HOST_QUERY_RECORD_LIST:	//主机获取设备记录文件列表
    		TcpSendRecordListToHost(Pkg);
			break;
		case CMD_SEND_RECORD_FILE_CONTENT:	//主机获取设备记录文件
			TcpSendRecordFileContent(Pkg);
			break;
		case CMD_TCP_HOST_SEND_ZONE_DETAIL:	//TCP模式下接收主机下发的分区状态命令
			pthread_mutex_lock(&ZoneInfoMutex);
			TCP_HANDLE_CMD_RECV_ZONE_DETAIL_INFO(Pkg);
			pthread_mutex_unlock(&ZoneInfoMutex);
			break;
		case CMD_PAGING_SEND_HOST_SELECTED_ZONE:
			Host_Response_ZoneList_Concentrated(Pkg);
			break;
		case CMD_SET_PLAYMODE:						//主机设置终端播放模式
			//需要判断是主机应答还是寻呼台主动向主机设置
			Host_Response_SET_Play_Mode_Concentrated(Pkg);
			break;
#if ENABLE_CALL_FUNCTION
		case CMD_ALL_PAGER_STATUS:
			pthread_mutex_lock(&PagerInfoMutex);
			GetAllPagerDeviceInfo(Pkg);
			pthread_mutex_unlock(&PagerInfoMutex);
		break;
		case CMD_CALLING_INVITATION:
			Recv_calling_invation(Pkg);
		break;
		case CMD_CALLED_RESPONSE:
			Recv_called_response(Pkg);
		break;
		case CMD_CALLED_STATUS:
			Recv_callStatus_feedback(Pkg);
		break;
		case CMD_CALLING_AUDIOSTREAM:
			Recv_Call_Audio_Stream(Pkg);
		break;

	#if USE_SSD202
		/*视频对讲*/
		case CMD_CALL_REQUEST_VIDEO:
			Recv_request_video_call(Pkg);
		break;
		case CMD_CALL_VIDEO_PARM:
			Recv_video_call_codecs_parm(Pkg);
		break;
		case CMD_CALL_VIDEO_STATUS:
			Recv_video_call_status(Pkg);
		break;
		case CMD_CALL_VIDEO_STREAM:
			Recv_Call_Video_Stream(Pkg);
		break;
	#endif
	
#endif

#if ENABLE_LISTEN_FUNCTION
		case CMD_LISTEN_RESPONSE:
			Recv_listen_event(Pkg);
		break;
		case CMD_LISTEN_STREAM_UPLOAD:
			Recv_Listen_Audio_Stream(Pkg);
		break;
#endif

#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
		case CMD_SEND_HOST_GET_ACCOUNT_STORAGE_CAPACITY:
			recv_get_account_storageCapacity(Pkg);
			break;
		case CMD_SEND_HOST_REQUEST_UPLOAD_SONG_FILE:
			recv_request_upload_song(Pkg);
			break;
		case CMD_SEND_HOST_NOTIFY_UPLOAD_STATUS:
			recv_upload_song_status(Pkg);
			break;
		case CMD_SEND_HOST_REQUEST_DELETE_SONG:
			recv_request_delete_song(Pkg);
			break;
#endif
		case CMD_HOST_SET_BROADCAST_PAGING:
			host_set_broadcast_paging(Pkg);
			break;
	}

}



void TCP_Server_Thread()
{
	int ret=-1;
	pthread_t reset_udp_Pthread;
	pthread_attr_t reset_pthread_attr;
	pthread_attr_init(&reset_pthread_attr);
	pthread_attr_setdetachstate(&reset_pthread_attr, PTHREAD_CREATE_DETACHED);
	ret = pthread_create(&reset_udp_Pthread, &reset_pthread_attr, (void *)TCP_COMMUNICATION, NULL);
	if (ret < 0)
	{
		perror("TCP_Server_Thread");
	}
	else
	{
		 printf("TCP_Server_Thread success!\n");
	}
	pthread_attr_destroy(&reset_pthread_attr);
}




void host_tcp_send_data(unsigned char *send_buf, int send_len)
{
	if(g_network_mode == NETWORK_MODE_WAN)
	{
		if(g_tcp_connect_status)
		{
			// 获取两个字节的命令字
			int cmd_word=(send_buf[0]<<8) + send_buf[1];
			//printf("Send TCP CMD_WORD = 0x%04x\n", cmd_word);
			int txlen = sendto(TCP_sockfd, send_buf, send_len, 0, (struct sockaddr *)&Tcp_Server_addr,
					sizeof(Tcp_Server_addr));
			if (txlen <= 0)
			{
				perror("host_tcp_send_data");
				printf("\nERROR:cmd_word = 0x%04x\n", cmd_word);
			}
		}
	}
}

