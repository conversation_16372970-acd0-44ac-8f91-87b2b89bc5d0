#if defined(USE_SSD212) || defined(USE_SSD202)
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <semaphore.h>
#include <fcntl.h>
#include <sys/socket.h>
#include <linux/netlink.h>
#include "sysconf.h"
#include "uart.h"
#include "tcp_host.h"
#include "module4G.h"

int g_module_4G_status=MODULE_4G_NOTFOUND;    //4G模块状态
static int module_4g_tty_at_fd=-1;               //4G模块usb tty节点
static pthread_t t_at_tty_Pthread;

static pthread_mutex_t mutex_4gInit=PTHREAD_MUTEX_INITIALIZER;	//4g初始化锁

int module_4g_event=MODULE_4G_EVENT_NONE;

void Create_Module4G_Event_Task(void);
void Create_Module4G_UsbCheck_task(void);
int Module4G_AT_Init(void);
void Module4G_AT_unInit();
void Module_4G_reset();

void Send_AT_Command(char *cmd);

// AT指令定义
#define AT_CMD_ECHO_OFF "ATE0"
#define AT_CMD_ECHO_ON "ATE1"
#define AT_CMD_CFUN "AT+CFUN"
#define AT_CMD_COPS "AT+COPS?"
#define AT_CMD_CPIN "AT+CPIN?"
#define AT_CMD_CSQ "AT+CSQ"
#define AT_CMD_CCID "AT+ICCID"
#define AT_CMD_CREG "AT+CREG?"
#define AT_CMD_CGSN "AT+CGSN"
#define AT_CMD_CIMI "AT+CIMI"

_stModule4GInfo stModule4GInfo;

void InitModule4G()
{
    //GPIO_OUTPUT_Module4G_Led(0);
    g_module_4G_status = MODULE_4G_OFF; //设置为关机状态
    Create_Module4G_Event_Task();
}



/*********************************************************************
 * @fn      Create_Module4G_Event
 *
 * @brief  	4G模块事件线程
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Module4G_Event(void *p_arg)
{
    //先创建4G模块USB检测线程
    Create_Module4G_UsbCheck_task();

    //判断是否已经插入，通过ttyusb0判断
    int cnt=4;
    int found_4g=0;
    while( cnt-- )
    {
        if(Module4G_AT_Init())
        {
            found_4g=1;
            break;
        }
        usleep(100000);
    }
    
    
    //如果没有4G模块，则需要控制开机
    //把PWRKEY管脚拉低1.2秒以上之后模块会进入开机流程，理论上是1.2秒，但是实际需要1.5秒，否则也会停止开机
    //软件会检测VBAT管脚电压， 若VBAT管脚电压大于软件设置的开机电压（3.1V），会继续开机动作直至系统开
    //机完成；否则，会停止执行开机动作，系统会关机，开机成功后PWRKEY管脚可以释放。
    //if(g_module_4G_status <= MODULE_4G_OFF)
    if(!found_4g)
    {
        GPIO_OutPut_Module4G_POWER(1);
        usleep(1600*1000);
        GPIO_OutPut_Module4G_POWER(0);
    }
    
    int insertCnt=0;
    unsigned int runtime_no_signal=0;
    unsigned int runtime_1s=0;
    unsigned int runtime_100ms=0;
    while(1)
    {
        switch(module_4g_event)
        {
            case MODULE_4G_EVENT_INSERT:
                if(insertCnt<10)
                {
                    insertCnt++;
                    if(Module4G_AT_Init())
                    {
                        module_4g_event = MODULE_4G_EVENT_NONE;
                        insertCnt=0;
                    }
                }
                else
                {
                    //System_Reboot();
                    System_Reboot();
                }
            break;
            case MODULE_4G_EVENT_REMOVE:
                Module4G_AT_unInit();
                module_4g_event = MODULE_4G_EVENT_NONE;
                insertCnt=0;
            break;
            default:
                insertCnt=0;
            break;
        }

        if( (++runtime_100ms)%10 == 0 )
        {
            runtime_1s++;
            if(runtime_1s == 15 && g_module_4G_status <= MODULE_4G_OFF)   //15秒还没有找到4G模块，重新开机
            {
                GPIO_OutPut_Module4G_POWER(1);
                usleep(1600*1000);
                GPIO_OutPut_Module4G_POWER(0);
            }

            //连续1小时未连接到服务器，对4G模块进行复位
            //连续24小时未连接到服务器，对网络板进行重启(暂仅限4G)
            runtime_no_signal = IS_SERVER_CONNECTED?0:runtime_no_signal+1;
            int readyToRebootOrReset=0;
            if( runtime_no_signal )
            {
                if( (runtime_no_signal % 86400) == 0 )
                {
                    if(!eth_link_status && g_network_mode == NETWORK_MODE_WAN)
                    {
                        readyToRebootOrReset=1;
                        System_Reboot();
                    }
                }
                else if( (runtime_no_signal % 3600) == 0 )
                {
                    if(!eth_link_status && g_network_mode == NETWORK_MODE_WAN)
                    {
                        readyToRebootOrReset=1;
                        Module_4G_reset();
                    }
                }
            }

            if((runtime_100ms%30) == 0)
            {
                if(!readyToRebootOrReset)
                {
                    static int canGetCCIDCnt=0;
                    static int canGetCSQCnt=0;
                    if(g_module_4G_status == MODULE_4G_PREPARING)
                    {
                        if(stModule4GInfo.at_send_cnt>stModule4GInfo.at_receive_cnt+8)
                        {
                            Module_4G_reset();
                        }
                        else
                        {
                            //printf("stModule4GInfo.at_send_cnt=%d,at_receive_cnt=%d\n",stModule4GInfo.at_send_cnt,stModule4GInfo.at_receive_cnt);
                            if(stModule4GInfo.creg_status == 1)
                            {
                                //如果还未获取过号码，那么获取号码
                                if(strlen(stModule4GInfo.iccid) == 0 && ++canGetCCIDCnt>=2)     //creg_status变成1注册成功后至少间隔5秒获取号码，否则此指令会延时返回，但影响不大
                                {
                                    canGetCCIDCnt=0;
                                    Send_AT_Command(AT_CMD_CCID);
                                }
                            }
                            if(!IS_SERVER_CONNECTED || strlen(stModule4GInfo.iccid) == 0)
                            {
                                Send_AT_Command(AT_CMD_CREG);
                            }
                            if(IS_SERVER_CONNECTED && stModule4GInfo.creg_status!=1)
                            {
                                Send_AT_Command(AT_CMD_CREG);
                            }

                            #if 1
                            if(++canGetCSQCnt>=5 || stModule4GInfo.csq_rssi == 99 || stModule4GInfo.csq_rssi == 0)    //99代表未知或不可测
                            {
                                //获取信号强度
                                canGetCSQCnt=0;
                                Send_AT_Command(AT_CMD_CSQ);
                            }
                            #endif
                        }
                    }
                    else
                    {
                        canGetCCIDCnt=0;
                        canGetCSQCnt=0;
                    }
                }
            }
            //printf("runtime_no_signal=%d\n",runtime_no_signal);
        }


        //printf("runtime_no_signal=%d\n",runtime_no_signal);
        usleep(100000);
    }
}

/*********************************************************************
 * @fn      Create_Module4G_Event_Task
 *
 * @brief  	创建4G模块事件线程
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Module4G_Event_Task(void)
{
    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_Module4G_Event, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}





/*********************************************************************
 * @fn      Create_Module4G_Event
 *
 * @brief  	4G模块事件线程
 *
 * @param   void
 *
 * @return  none
*********************************************************************/
void Create_Module4G_UsbCheck(void *p_arg)
{
    struct sockaddr_nl client;
    struct timeval tv;
    int CppLive, rcvlen, ret;
    fd_set fds;
    int buffersize = 1024;
    CppLive = socket(AF_NETLINK, SOCK_RAW, NETLINK_KOBJECT_UEVENT);
    memset(&client, 0, sizeof(client));
    client.nl_family = AF_NETLINK;
    client.nl_pid = getpid();
    client.nl_groups = 1; /* receive broadcast message*/
    setsockopt(CppLive, SOL_SOCKET, SO_RCVBUF, &buffersize, sizeof(buffersize));
    bind(CppLive, (struct sockaddr*)&client, sizeof(client));
    char buf[1024] = { 0 };
    while (1) {
        FD_ZERO(&fds);
        FD_SET(CppLive, &fds);
        memset(buf,0,sizeof(buf));
        //tv.tv_sec = 0;
        //tv.tv_usec = 100 * 1000;
        //ret = select(CppLive + 1, &fds, NULL, NULL, &tv);
        ret = select(CppLive + 1, &fds, NULL, NULL, NULL);
        if(ret < 0)
            continue;
        if(!(ret > 0 && FD_ISSET(CppLive, &fds)))
            continue;
        /* receive data */
        rcvlen = recv(CppLive, &buf, sizeof(buf), 0);
        if (rcvlen > 0) 
        {
            printf("hotplug:%s\n",buf);
            if(strstr(buf, "remove@")) // 拔出时内核会发送消息为:remove@/.../ttyUSBx
			{
                //printf("remove...\n");
                if(strstr(buf, "ttyUSB2"))
                {
                    if(module_4g_event!=MODULE_4G_EVENT_REMOVE)
                    {
                        printf("Air724UG Remove...\n");
                        g_module_4G_status = MODULE_4G_OFF;
                        module_4g_event = MODULE_4G_EVENT_REMOVE;
                    }
                }
                else if(strstr(buf, "virtual/bdi"))
                {
                    printf("Udisk Remove...\n");
                    Set_Udisk_Mount_Status(UDISK_STATUS_HOTPLUG_UNPLUG);
                }
            }
            if(strstr(buf, "add@"))
            {
                //printf("Add...\n");
                if(strstr(buf, "tty/ttyUSB2"))
                {
                    if(module_4g_event != MODULE_4G_EVENT_INSERT)
                    {
                        printf("Air724UG Insert...\n");
                        module_4g_event = MODULE_4G_EVENT_INSERT;
                    }
                }
                else if(strstr(buf, "virtual/bdi"))
                {
                    printf("Udisk Insert...\n");
                    Set_Udisk_Mount_Status(UDISK_STATUS_HOTPLUG_INSERT);
                }
            }
            /*You can do something here to make the program more perfect!!!*/
        }
    }
    close(CppLive);
}

void Create_Module4G_UsbCheck_task(void)
{
    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Create_Module4G_UsbCheck, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}



// 发送AT指令
void Send_AT_Command(char *cmd)
{
    if(g_module_4G_status <= MODULE_4G_OFF || cmd == NULL)
        return;
    
    // 按照 AT 指令格式发送数据，并添加 \r\n 结尾
    char buf[256];
    snprintf(buf, sizeof(buf), "%s\r\n", cmd);
    //tcflush(module_4g_tty_at_fd, TCIOFLUSH); // 清空串口缓存
    stModule4GInfo.at_send_cnt++;
    int len = write(module_4g_tty_at_fd, buf, strlen(buf));
    if (len < 0) {
        printf("Failed to Send_AT_Command\n");
        return;
    }
    else
    {
        printf("Send_AT_Command:%s\n",cmd);
    }
}


/*********************************************************************
 * @fn      Recv_Module4G_AT
 *
 * @brief   AT接收线程(回调函数)
 *
 * @param   void
 *
 * @return  none
 */
void *Recv_Module4G_AT(void)
{
	printf("Enter Recv_Module4G_AT...\n");
	int Rxlen;
	int ret, i;
	int Index = 0;
	fd_set readfd;
	struct timeval timeout;
	int max_fd;
	int Pkg_Length=0;
	int Read_Size = 1024;

	char Rxbuf[1024]={0};

	FD_ZERO(&readfd);               //清空读文件描述集合
	FD_SET(module_4g_tty_at_fd, &readfd);  //注册套接字文件描述符

    //首先关闭回显
    Send_AT_Command(AT_CMD_ECHO_OFF);

	while (g_module_4G_status != MODULE_4G_OFF)
	{
		timeout.tv_sec = 0;      // 获取数据超时时间设置
		timeout.tv_usec = 100000;

        memset(Rxbuf,0, MAX_UART_BUF_SIZE);

		FD_ZERO(&readfd);               //清空读文件描述集合
		FD_SET(module_4g_tty_at_fd, &readfd);  //注册套接字文件描述符
		ret = select(module_4g_tty_at_fd+1, &readfd, NULL, NULL, &timeout);
		switch(ret)
		{
			case -1 : //调用出错
				perror("select");
				break;

			case 0 : //超时
				//printf("timeout!\n");
				break;

			default : //判断是否有数据可读

				/*接收对应串口的数据*/
                memset(Rxbuf,0,sizeof(Rxbuf));
				Rxlen = read(module_4g_tty_at_fd, &Rxbuf[Index], Read_Size-1);
                if(Rxlen > 2)
                {
                    printf("4G AT Recive:%s\n",Rxbuf);
                    stModule4GInfo.at_receive_cnt++;

                    char *p = Rxbuf;
                    while (p < Rxbuf + Rxlen) {
                        char *q = strchr(p, '\r');
                        if (q != NULL) {
                            *q = '\0';
                            if (*(q + 1) == '\n') {
                                *q = '\0';
                                
                                int creg_urc_val=0,creg_stat_val=0;
                                int csq_rssi_val=0,csq_ber_val=0;
                                char iccid[32]={0};
                                int match_num=0;
                                if ((match_num=sscanf(p,"+CREG:%d,%d", &creg_urc_val, &creg_stat_val)) == 2) {
                                    printf("value1=%u value2=%u\n", creg_urc_val, creg_stat_val);
                                    if(creg_stat_val!=stModule4GInfo.creg_status)
                                    {
                                        stModule4GInfo.creg_status=creg_stat_val;
                                        printf("stModule4GInfo.creg_status=%d\n",stModule4GInfo.creg_status);
                                    }
                                    break;
                                }else if ((match_num=sscanf(p,"+ICCID:%s", &iccid)) == 1) {
                                    if(strcmp(iccid,stModule4GInfo.iccid))
                                    {
                                        sprintf(stModule4GInfo.iccid,iccid);
                                        printf("stModule4GInfo.iccid=%s\n",stModule4GInfo.iccid);
                                    }
                                }else if ((match_num=sscanf(p,"+CSQ:%d,%d", &csq_rssi_val, &csq_ber_val)) == 2) {
                                    if(csq_rssi_val!=stModule4GInfo.csq_rssi)
                                    {
                                        stModule4GInfo.csq_rssi=csq_rssi_val;
                                        printf("stModule4GInfo.csq_rssi=%d\n",stModule4GInfo.csq_rssi);
                                    }
                                }


                                p = q + 2;
                                continue;
                            }
                        }
                        break;
                    }
                }

				break;
		}
	}
    printf("Exit Recv_Module4G_AT...\n");
	pthread_exit(NULL);
}




void Module4G_AT_unInit()
{
    pthread_mutex_lock(&mutex_4gInit);
    if(module_4g_tty_at_fd>=0)
    {
        //20230627 重要，此处一定要再次OFF,否则在特定情况下会异常，造成死锁
        g_module_4G_status = MODULE_4G_OFF;
        pthread_join(t_at_tty_Pthread, NULL);
        close(module_4g_tty_at_fd);
        module_4g_tty_at_fd=-1;

        //如果没有连接有线网卡，且TCP已经处于连接的状态下，需要重连
        if(!eth_link_status)
        {
            if(g_network_mode == NETWORK_MODE_WAN && g_tcp_connect_status)
            {
                //tcp_client_reconnect();
                g_tcp_reconnect_flag=1;
            }
        }
    }
    pthread_mutex_unlock(&mutex_4gInit);
}


void config_module_4g_network()
{
    if(g_module_4G_status <= MODULE_4G_OFF )
    {
        return;
    }

    //如果有线网卡已经连接，关闭4G网卡
    if(eth_link_status)
    {
        g_module_4G_status = MODULE_4G_UNUSED;
        pox_system("ifconfig eth1 down");

        //如果是静态模式，需要重新设置一遍DNS
        if(g_IP_Assign == IP_ASSIGN_STATIC)
        {
            if(strlen(g_Primary_DNS) >=7)
            {
                char cmd[128]={0};
                sprintf(cmd,"echo \"nameserver %s\" > /etc/resolv.conf",g_Primary_DNS);
                pox_system(cmd);
                if(strlen(g_Alternative_DNS) >=7)
                {
                    sprintf(cmd,"echo \"nameserver %s\" >> /etc/resolv.conf",g_Alternative_DNS);
                    pox_system(cmd);
                }
            }
        }
    }
    else
    {
        //配置4G网卡地址
        pox_system("ifconfig eth1 down");
        pox_system("ifconfig eth1 up");

        char cmd[128]={0};
        //设置静态IP、子网掩码
        sprintf(cmd,"ifconfig %s %s netmask %s","eth1","***********","*************");
        pox_system(cmd);

        //设置网关
        sprintf(cmd,"route add default gw %s","***********");
        pox_system(cmd);

        //设置DNS
        sprintf(cmd,"echo \"nameserver %s\" > /etc/resolv.conf","*******");
        pox_system(cmd);

        sprintf(cmd,"echo \"nameserver %s\" >> /etc/resolv.conf","***************");
        pox_system(cmd);

        refresh_deviceInfo();

        if(g_module_4G_status == MODULE_4G_UNUSED)
        {
            g_module_4G_status = MODULE_4G_PREPARING;
        }
    }
}

/*********************************************************************
 * @fn      Module4G_AT_Init
 *
 * @brief   初始化4G AT模块
 *
 * @param   void
 *
 * @return  ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
int Module4G_AT_Init(void)
{
    Module4G_AT_unInit();

    pthread_mutex_lock(&mutex_4gInit);

    static int at_init_cnt=0;
    at_init_cnt++;
    module_4g_tty_at_fd=Init_Serial_Port(MODULE_4G_USB_AT_TTY_NAME, BAUD_115200, 8, 'N', 1);
    if(module_4g_tty_at_fd>=0)
    {
        //代表已经存在usb tty，可能是软件reboot后
        g_module_4G_status = MODULE_4G_PREPARING;
    }
    else
    {
        module_4g_tty_at_fd=Init_Serial_Port(MODULE_4G_USB_AT_TTY1_NAME, BAUD_115200, 8, 'N', 1);
        if(module_4g_tty_at_fd>=0)
        {
            //代表已经存在usb tty，可能是软件reboot后
            g_module_4G_status = MODULE_4G_PREPARING;
            printf("Module4G_AT_Init:Try TTY1 OK!\n");
        }
        else
        {
            pthread_mutex_unlock(&mutex_4gInit);
            return 0;
        }
    }

    if(at_init_cnt == 1 && g_module_4G_status == MODULE_4G_PREPARING)
    {
        g_module_4G_status = MODULE_4G_OFF;
        close(module_4g_tty_at_fd);
        module_4g_tty_at_fd=-1;
        Module_4G_reset();
        pthread_mutex_unlock(&mutex_4gInit);
        return true;
    }

    //重置4G模块信息
    memset(&stModule4GInfo,0,sizeof(stModule4GInfo));

	/*创建一个线程单独接收AT串口数据*/
    pthread_create(&t_at_tty_Pthread, NULL, (void *)Recv_Module4G_AT, NULL);

    config_module_4g_network();

    pthread_mutex_unlock(&mutex_4gInit);
    return 1;
}


//4G模块复位
void Module_4G_reset()
{
    printf("Module_4G_reset...\n");
    GPIO_OutPut_Module4G_Reset(1);
    usleep(200000);
    GPIO_OutPut_Module4G_Reset(0);
}

#endif