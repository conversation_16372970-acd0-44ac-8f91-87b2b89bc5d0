#ifndef _INTERCOM_PROCESS_H_
#define _INTERCOM_PROCESS_H_

#if defined(USE_SSD212) || defined(USE_SSD202)

#define MODULE_4G_USB_AT_TTY_NAME   "/dev/ttyUSB0"
#define MODULE_4G_USB_AT_TTY1_NAME  "/dev/ttyUSB1"

enum{
    MODULE_4G_NOTFOUND=-1,  //未找到4G模块
    MODULE_4G_OFF,          //关机
    MODULE_4G_PREPARING,    //准备中
    MODULE_4G_WORKING,      //工作中
    MODULE_4G_UNUSED,       //未使用（有线网卡插入)
};

enum{
    MODULE_4G_EVENT_NONE=0,  //无
    MODULE_4G_EVENT_INSERT,  //插入
    MODULE_4G_EVENT_REMOVE,  //拔出
};

typedef struct{
    int creg_status;      //当前网络注册状态
    int csq_rssi;         //信号强度
    char iccid[32];       //iccid号码,共20位数字
    int at_send_cnt;      //AT发送计数
    int at_receive_cnt;   //AT接收计算
}_stModule4GInfo;

extern _stModule4GInfo stModule4GInfo;

extern int g_module_4G_status;    //4G模块状态

void InitModule4G();
void config_module_4g_network();

#endif

#endif