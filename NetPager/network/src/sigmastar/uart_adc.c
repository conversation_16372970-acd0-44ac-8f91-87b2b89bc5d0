#if defined(USE_SSD212) || defined(USE_SSD202)

#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <sys/time.h>
#include <unistd.h>
#include "sysconf.h"
#include "uart.h"

#include "uart_adc.h"

static int adc_tty_fd=-1;               //4G模块usb tty节点

int adc_mic1_vol=100;
int adc_mic2_vol=100;
int adc_aux_vol=100;
int adc_allIn_vol=100;

#define ADC_MAX_VAL 1000
#define ADC_MIN_VAL 10

#define ADC_SECTOR_VAL  10      //10为0,20为1...

static void Adc_Uart_Command_Proc( unsigned char cmd, unsigned short datalen,unsigned char *data )
{
    int dataPos=0;
    int parmId=0;
	switch(cmd)
	{
		case ADC_UART_CMD_VAL:
        {
            parmId = data[dataPos++];
            int mic1_val=data[dataPos]+(data[dataPos+1]<<8);
            dataPos+=2;
            parmId = data[dataPos++];
            int mic2_val=data[dataPos]+(data[dataPos+1]<<8);
            dataPos+=2;
            parmId = data[dataPos++];
            int aux_val=data[dataPos]+(data[dataPos+1]<<8);
            dataPos+=2;
            parmId = data[dataPos++];
            int allIn_val=data[dataPos]+(data[dataPos+1]<<8);

            //需要先确定最大、最小值
            int adc_val[4]={mic1_val,mic2_val,aux_val,allIn_val};
            int i=0;
            for(i=0;i<4;i++)
            {
                if(adc_val[i]>=1000)
                {
                    adc_val[i]=100;
                }
                else if(adc_val[i]<=10)
                {
                    adc_val[i]=0;
                }
                else
                {
                    adc_val[i]=adc_val[i]/ADC_SECTOR_VAL;
                    if(adc_val[i]<0)
                        adc_val[i]=0;
                    if(adc_val[i]>100)
                        adc_val[i]=100;
                }
            }
            adc_mic1_vol  = adc_val[0];
            adc_mic2_vol  = adc_val[1];
            adc_aux_vol   = adc_val[2];
            adc_allIn_vol = adc_val[3];

            //printf("mic1_val=%d,mic2_val=%d,aux_val=%d,allIn_val=%d\n",mic1_val,mic2_val,aux_val,allIn_val);
            //printf("mic1_vol=%d,mic2_vol=%d,aux_vol=%d,allIn_vol=%d\n",adc_mic1_vol,adc_mic2_vol,adc_aux_vol,adc_allIn_vol);

            g_stc_IsInit=1;
        }
        break;
    }
}

/*********************************************************************
 * @fn      Recv_Uart_Adc_Pthread
 *
 * @brief   ADC数据串口接收线程
 *
 * @param   void
 *
 * @return  none
 */
void *Recv_Uart_Adc_Pthread(void)
{
	printf("Enter Recv_Uart_Adc_Pthread...\n");
	int Rxlen;
	int ret, i;
	int Index = 0;
	fd_set readfd;
	struct timeval timeout;
	int max_fd;
	int Pkg_Length=0;
	int Read_Size = 512;

	unsigned char Rxbuf[512]={0};

	FD_ZERO(&readfd);               //清空读文件描述集合
	FD_SET(adc_tty_fd, &readfd);  //注册套接字文件描述符

    while(1)
    {
        FD_ZERO(&readfd);               //清空读文件描述集合
        FD_SET(adc_tty_fd, &readfd);  //注册套接字文件描述符
        timeout.tv_sec = 0;      // 获取数据超时时间设置
        timeout.tv_usec = 80000;
        ret = select(adc_tty_fd+1, &readfd, NULL, NULL, &timeout);
        switch(ret)
        {
            case -1 : //调用出错
                perror("select");
                break;

            case 0 : //超时
                //printf("timeout!\n");
                break;

            default : //判断是否有数据可读

                /*接收对应串口的数据*/
                memset(Rxbuf,0, sizeof(Rxbuf));
                Rxlen = read(adc_tty_fd, Rxbuf, Read_Size);
                #if 0
                printf("adc tty recive:%d\n",Rxlen);
                for(int i=0;i<Rxlen;i++)
                {
                    printf("%x ",Rxbuf[i]);
                }
                printf("\n");
                #endif
                if(Rxlen == 18)
                {
                    int data_len=Rxlen-6;
                    int cmd=Rxbuf[2];
                    if ( Checksum(cmd, data_len,Rxbuf+5) == Rxbuf[Rxlen-1] ) //校验数据
                    {
                        //printf("UART ADC check succeed:0x%x\n",cmd);
                        Adc_Uart_Command_Proc(cmd,data_len,Rxbuf+5);
                    }
                }

                break;
        }
    }
	
    printf("Exit Recv_Uart_Adc_Pthread...\n");
	pthread_exit(NULL);
}







void Create_Uart_Adc_task(void)
{
    adc_tty_fd=Init_Serial_Port(ADC_TTY_NAME, BAUD_115200, 8, 'N', 1);
    system("/customer/riu_w 0x103e 0x3a 0x0075");

    pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Recv_Uart_Adc_Pthread, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}





#endif