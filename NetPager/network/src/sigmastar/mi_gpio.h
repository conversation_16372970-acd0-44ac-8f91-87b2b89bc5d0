/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> 
 * @Date: 2022-02-16 20:44:11 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-17 10:55:43
 */


#ifndef _MI_GPIO_H_
#define _MI_GPIO_H_

#define BASE_RIU_PA                 0x1F000000
#define CHIPTOP_BANK                0x101E00
#define PM_PADTOP_BANK              0x003F00
#define UTMI0_BANK                  0x142100
#define ETH_BANK                    0x003300

#if USE_SSD212
#define PADTOP_BANK                 0x103C00
#define PADGPIO_BANK                0x103E00
#elif USE_SSD202
#define PADTOP_BANK                 0x103C00
#define PADGPIO_BANK                0x103C00
#endif

#define PM_SAR_BANK                 0x001400
#define PMSLEEP_BANK                0x000E00
#define PM_GPIO_BANK                0x000F00


#define PAD_SAR_GPIO0               0x11
#define PAD_PM_SD_CDZ               0x47


#define PAD_GPIO0                   0x3D    //4G RESET
#define PAD_GPIO1                   0x3E    //4G pwrKey
#define PAD_GPIO2                   0x3F    //Transmit_SWITCH INPUT
#define PAD_GPIO3                   0x40    //Relay Ctrl
#define PAD_GPIO4                   0x41    //Trigger Signal INPUT
#define PAD_GPIO5                   0x42    //Trigger Mode
#define PAD_GPIO6                   0x43    //MIC_LED
#define PAD_GPIO7                   0x44    //MUX CTRL(SINGAL_MUX(MIC2 OR signal loop))
#define PAD_GPIO8                   0x45    //SYS RESET INPUT

#define PAD_KEY1                    0x28    
#define PAD_KEY2                    0x29    
#define PAD_KEY3                    0x2A    
#define PAD_KEY4                    0x2B    
#define PAD_KEY5                    0x2C    
#define PAD_KEY6                    0x2D    
#define PAD_KEY8                    0x2F    //KEY1 INPUT
#define PAD_KEY9                    0x30    //KEY2 INPUT
#define PAD_KEY10                   0x31
#define PAD_KEY11                   0x32    
#define PAD_KEY12                   0x33    
#define PAD_KEY13                   0x34    

#define PAD_SD_GPIO1                0x3C    //Transmit_LED
#define PAD_SD_GPIO0                0x3B    //NET LED
#define PAD_SD_D2                   0x3A
#define PAD_SD_D3                   0x39
#define PAD_SD_CMD                  0x38
#define PAD_SD_CLK                  0x37
#define PAD_SD_D0                   0x36    //KEY2 LED
#define PAD_SD_D1                   0x35    //KEY1 LED

#define GPIO_HIGH_VAL               0x7B
#define GPIO_LOW_VAL                0x78
#define GPIO_INPUT_VAL              0x7C

#define GET_BANK_ADDR(base,bank,offset)   ((base) + ((bank) << 1) + ((offset) << 2))

#define REG_W_WORD(addr,val)              {(*(unsigned short *)(addr)) = (unsigned short)(val);}
#define REG_W_WORD_MASK(addr,val,mask)    {(*(unsigned short *)(addr)) = ((*(unsigned short *)(addr)) & ~(mask)) | ((unsigned short)(val) & (mask));}
#define REG_R_WORD(addr)                  (*(unsigned short *)(addr))
#define REG_R_WORD_MASK(addr,mask)        (*(unsigned short *)(addr)) & (mask))

#define BIT8        0x100

#define BANK_TO_ADDR32(b) (b<<9)
#define REG_ADDR(riu_base,bank,reg_offset) ((riu_base)+BANK_TO_ADDR32(bank)+(reg_offset*4))












void Set_Gpio_Input(int offset);
int Get_Gpio_Value(int offset);

void Enable_Amp_Output(int IsEnable);
void Enable_Signal_Output(int IsEnable);



void GPIO_Set_Pager_Init();
int GPIO_Get_Pager_Key_Value(int keyId);
int GPIO_Get_Pager_Ext_Value();
void GPIO_OutPut_Server_Connection(int outPut);
void GPIO_OutPut_Pager_Led(int ledId,int outPut);
void GPIO_OutPut_Pager_Mic_Led(int outPut);
void GPIO_OutPut_Pager_Relay(int outPut);


void GPIO_OutPut_Module4G_POWER(int isOn);
void GPIO_OutPut_Module4G_Reset(int isReset);

#endif
