/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> 
 * @Date: 2021-09-21 15:59:27 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-21 16:02:13
 */

#ifndef _AUDIO_PARAMS_H_
#define _AUDIO_PARAMS_H_

#if defined(USE_SSD212) || defined(USE_SSD202)

#define INI_KEY_DSP_MODEL_ID         "MODULE_ID"
#define INI_KEY_DSP_AMIC0_SWITCH     "AMIC0_SWITCH"
#define INI_KEY_DSP_AMIC0_GAIN       "AMIC0_GAIN"
#define INI_KEY_DSP_AMIC1_SWITCH     "AMIC1_SWITCH"
#define INI_KEY_DSP_AMIC1_GAIN       "AMIC1_GAIN"
#define INI_KEY_DSP_AMIC2_SWITCH     "AMIC2_SWITCH"
#define INI_KEY_DSP_AMIC2_GAIN       "AMIC2_GAIN"
#define INI_KEY_DSP_DAC0L_SWITCH     "DAC0L_SWITCH"
#define INI_KEY_DSP_DAC0L_GAIN       "DAC0L_GAIN"
#define INI_KEY_DSP_DAC0R_SWITCH     "DAC0R_SWITCH"
#define INI_KEY_DSP_DAC0R_GAIN       "DAC0R_GAIN"
#define INI_KEY_DSP_BT_SWITCH     	 "BT_SWITCH"
#define INI_KEY_DSP_BT_GAIN       	 "BT_GAIN"

enum
{
	DSP_AUDIO_MODULE_ADC0_L,					//AMIC0
	DSP_AUDIO_MODULE_ADC0_R,					//AMIC1
	DSP_AUDIO_MODULE_ADC1_L,					//AMIC2
	DSP_AUDIO_MODULE_ADC1_R,					//None
	DSP_AUDIO_MODULE_DAC0_L,					//DAC0_L
	DSP_AUDIO_MODULE_DAC0_R,					//DAC0_R
	DSP_AUDIO_MODULE_BT,						//BT(NONE)
	DSP_AUDIO_MODULE_MAX,                    
};



typedef struct {
	unsigned char module_switch[DSP_AUDIO_MODULE_MAX];	//模块开关
	unsigned short module_gain[DSP_AUDIO_MODULE_MAX];	//模块增益
}st_dsp_firmware_feature_info;
extern st_dsp_firmware_feature_info dsp_firmware_feature;

extern const st_dsp_firmware_feature_info dsp_default_firmware_feature;


#define DSP_MAX_EQ_MODE_NUM			8
#define INI_DSP_EQ_VALUE_DEFAULT 	0

#define INI_KEY_DSP_EQ_MODE     	"EQ_MODE"
#define INI_KEY_DSP_EQ_GAIN1       	"GAIN1"
#define INI_KEY_DSP_EQ_GAIN2     	"GAIN2"
#define INI_KEY_DSP_EQ_GAIN3       	"GAIN3"
#define INI_KEY_DSP_EQ_GAIN4     	"GAIN4"
#define INI_KEY_DSP_EQ_GAIN5       	"GAIN5"
#define INI_KEY_DSP_EQ_GAIN6       	"GAIN6"
#define INI_KEY_DSP_EQ_GAIN7    	"GAIN7"
#define INI_KEY_DSP_EQ_GAIN8       	"GAIN8"
#define INI_KEY_DSP_EQ_GAIN9     	"GAIN9"
#define INI_KEY_DSP_EQ_GAIN10       "GAIN10"

typedef struct {
	unsigned char eq_mode;				//EQ模式,0为关闭，1为自定义
	unsigned char gain[10];				//10段均衡的增益值
}st_dsp_eq_info;
extern st_dsp_eq_info dsp_eq_info;

extern unsigned short EqFreqArray[10];
extern unsigned char EqGainArray[DSP_MAX_EQ_MODE_NUM][10];

#endif

#endif