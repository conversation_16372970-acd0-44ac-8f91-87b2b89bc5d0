#if defined(USE_SSD212) || defined(USE_SSD202)

#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include <stdlib.h>
#include <pthread.h>
#include <unistd.h>
#include <signal.h>
#include <regex.h>
#include <time.h>
#include "sysconf.h"

//将日期字符串转换为时间戳，格式如20230215
long char_to_timestamp(char *str_time)
{
    struct tm stm;
    int iY,iM,iD,iH,iMin,iS;
    memset(&stm,0,sizeof(stm));
 
    char cY[5]={0};
    char cM[3]={0};
    char cD[3]={0};
    strncpy(cY,str_time,4);
    strncpy(cM,str_time+4,2);
    strncpy(cD,str_time+6,2);

    //printf("Y=%s,M=%s,D=%s\n",cY,cM,cD);

    iY = atoi(cY);
    iM = atoi(cM);
    iD = atoi(cD);
    //printf("Y=%d,M=%d,D=%d\n",iY,iM,iD);
    iH = 0;
    iMin = 0;
    iS = 0;
    stm.tm_year=iY-1900;
    stm.tm_mon=iM-1;
    stm.tm_mday=iD;
    stm.tm_hour=iH;
    stm.tm_min=iMin;
    stm.tm_sec=iS;
    return mktime(&stm);
}

//将标准日期转换为时间戳，格式如20230215
long standDate_to_timestamp(char *str_time)
{
    struct tm tmp_time;
    strptime(str_time,"%a %b %d %T %Z %Y",&tmp_time); //时间24时制
    time_t t = mktime(&tmp_time);
    return t;
}


void SearchLogoFileAndUpgrade()
{
    if(IsFileExist("/customer/App/misc/disp/dispChanged"))
    {
        Get_Screen_LCD_type_Value(true);
        pox_system("rm /customer/App/misc/disp/dispChanged");
        printf("logo file upgrade succeed!\n");
        System_Reboot();
    }
}

void SearchOtaFileAndUpgrade()
{
    char otaFile[128]={0};
    char* pattern = "*SStarOta*.bin.gz";
    bool upgradeOK=false;
    if( WildcardFileSearch(pattern,"/customer/App",otaFile) )
    {
        printf("Found ota file:%s\n",otaFile);
        //提取文件的日期信息
        //SStarOta_20220509.bin.gz
        
        char OtaDate[10]={0};

        const char* p_regex_str = "[0-9]{8}";   //不能用\d,不识别
        regex_t oregex;   // 编译后的结构体
        regmatch_t pmatch[1];
    
        int ret = 0;

        if ((ret = regcomp(&oregex, p_regex_str, REG_EXTENDED)) == 0) {// 编译
            //成功编译
            if ((ret = regexec(&oregex, otaFile, 1, pmatch, 0)) == 0) {// 执行匹配不保存匹配的返回值
                memset(OtaDate,0,sizeof(OtaDate));
                strncpy(OtaDate,otaFile+pmatch->rm_so,pmatch->rm_eo-pmatch->rm_so);
                printf("%s matches:%s \n", otaFile, OtaDate);
            }
            else
            {
                printf("No match!\n");
            }
        }
        regfree(&oregex);


        long OtaTimeStamp=0;
        long curKernelTimeStamp=0;
        //找到日期后，将其转换为时间戳
        if(strlen(OtaDate)>0)
        {
            OtaTimeStamp = char_to_timestamp(OtaDate);
            printf("OtaTimeStamp=%ld\n",OtaTimeStamp);

            //提取当前内核的构建日期信息
            FILE *fp=NULL;
            char kernel_verisonInfo[256]={0};
            char kernel_dateInfo[30]={0};
            if((fp = fopen("/proc/version", "r")) != NULL)
            {
                fgets(kernel_verisonInfo,256,fp);	//读入一行
                fclose(fp);
            }
            //printf("versionInfo=%s\n",kernel_verisonInfo);
            //只取最后28个字符
            if(strlen(kernel_verisonInfo)>strlen("Wed Jan 25 09:41:30 UTC 2023"))
            {
                //此处-1是因为fgets最后还有一个换行符
                strcpy(kernel_dateInfo,kernel_verisonInfo+(strlen(kernel_verisonInfo)-strlen("Wed Jan 25 09:41:30 UTC 2023")-1));
                curKernelTimeStamp=standDate_to_timestamp(kernel_dateInfo);
            }
            //printf("versionInfo=%s,kernel_dateInfo=%s\n",kernel_verisonInfo,kernel_dateInfo);
            
            printf("curKernelTimeStamp=%ld\n",curKernelTimeStamp);

            //对比，新的日期>当前内核日期，则更新
            if(OtaTimeStamp>curKernelTimeStamp && OtaTimeStamp>1640966400 && curKernelTimeStamp>1640966400)
            {
                printf("ready to upgrade ota!\n");
                char upgradeCmd[256]={0};
                sprintf(upgradeCmd,"/customer/App/otaunpack -x /customer/App/%s",otaFile);
                pox_system(upgradeCmd);
                printf("OTA file upgrade succeed!\n");
                upgradeOK=true;
            }
            else
            {
                printf("OtaFile is old!\n");
            }
        }
    }
    #if 1
    pox_system("rm /customer/App/*.bin.gz");
    SearchLogoFileAndUpgrade();
    if(upgradeOK)
    {
        System_Reboot();
    }
    #else
    //MyExit (0);
    #endif
}

int Get_Screen_Rotations_Value()
{
    #if defined(USE_SSD202)
    return 1;   //SSD202默认旋转90度（正）
    #endif
    #if IS_AIPU_MIXER
    return 3;
    #endif
	static int rotation_value=-1;
	char readline[128]={0};
	static int PreNeed_rotation=-1;
    int dsp_value=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_BT];
    int need_rotaion=(dsp_value && (dsp_value & SSD_HARDWARE_LCD_Rotation_180))?1:0;
	if(PreNeed_rotation == -1)
		PreNeed_rotation=need_rotaion;
	int curNeed_rotation=need_rotaion;
	if(rotation_value == -1 || PreNeed_rotation!=curNeed_rotation)
	{
		PreNeed_rotation = curNeed_rotation;
		printf("Get_Screen_Rotations_Value First Enter!\n");
		FILE* fp = popen("/etc/fw_printenv | grep \"logo_rot\"", "r" );
		if ( NULL == fp )
		{
			printf("popen screen rotation error!\n");
			return rotation_value;
		}
		else
		{
			memset( readline, 0, sizeof( readline ) );
			fgets( readline,sizeof( readline ),fp );
			if(readline[strlen(readline) - 1] == '\n')    //去掉换行符
				readline[strlen(readline) - 1] = '\0' ;
			pclose(fp);

			char *rotPos=NULL;
			if(	rotPos=strstr(readline,"logo_rot=") )
			{
				rotPos=rotPos+strlen("logo_rot=");
			}
			if(rotPos)
			{
				rotation_value = atoi(rotPos);
				if(curNeed_rotation)
				{
					if(rotation_value!=2)
					{
						printf("change rotation to 2!\n");
						pox_system("/etc/fw_setenv logo_rot 2");
						System_Reboot();
					}
					else
					{
						printf("rotation 2 not changed!\n");
					}
				}
				else
				{
					if(rotation_value!=0)
					{
						printf("change rotation to 0!\n");
						pox_system("/etc/fw_setenv logo_rot 0");
						System_Reboot();
					}
					else
					{
						printf("rotation 0 not changed!\n");
					}
				}
			}
			else
			{
				printf("Not found logo_rot!\n");
				if(curNeed_rotation)
				{
					printf("change rotation to 2!\n");
					pox_system("/etc/fw_setenv logo_rot 2");
					System_Reboot();
				}
				else
				{
					rotation_value=0;
					printf("Not need rotation!\n");
				}
			}
		}
	}
	return rotation_value;
}




int Get_Screen_LCD_type_Value(bool forceUpdate)
{
    #if defined(USE_SSD202)
    return SSD_HARDWARE_LCD_1280_800;
    #endif
	char readline[128]={0};
    int dsp_value=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_BT];
    int lcd_type=SSD_HARDWARE_LCD_1024_600;
    if(dsp_value == 0)
    {
        lcd_type=SSD_HARDWARE_LCD_1024_600;
    }
    else if(dsp_value & SSD_HARDWARE_LCD_1024_600)
    {
        lcd_type=SSD_HARDWARE_LCD_1024_600;
    }
    else if(dsp_value & SSD_HARDWARE_LCD_800_480)
    {
        lcd_type=SSD_HARDWARE_LCD_800_480;
    }
    else if(dsp_value & SSD_HARDWARE_LCD_600_1024)
    {
        lcd_type=SSD_HARDWARE_LCD_600_1024;
    }
    else
    {
        lcd_type=SSD_HARDWARE_LCD_1024_600;
    }

    if(lcd_type == SSD_HARDWARE_LCD_800_480)
    {
        g_screen_width=800;
        g_screen_height=480;
    }
    else if(lcd_type == SSD_HARDWARE_LCD_1024_600)
    {
        g_screen_width=1024;
        g_screen_height=600;
    }
    else if(lcd_type == SSD_HARDWARE_LCD_600_1024)
    {
        g_screen_width=600;
        g_screen_height=1024;
    }

    #if IS_AIPU_MIXER
    lcd_type=SSD_HARDWARE_LCD_600_1024;
    g_screen_width=600;
    g_screen_height=1024;
    #endif

    if(!forceUpdate)
    {
        printf("Get_Screen_LCD_type_Value:Not forceUpdate!\n");
        return lcd_type;
    }
    else
    {
        printf("Get_Screen_LCD_type_Value:ForceUpdate:%d!\n",lcd_type);
    }

#if 0
    //将Get_Screen_LCD_type_Value设置为-1，以便第一次更新
    pox_system("/etc/fw_setenv lcd_type 4096");

    printf("Get_Screen_LCD_type_Value Enter!\n");
    FILE* fp = popen("/etc/fw_printenv | grep \"lcd_type\"", "r" );
    if ( NULL == fp )
    {
        printf("popen lcd_type error!\n");
        return lcd_type;
    }
    else
    {
        printf("popen lcd_type succeed!\n");
        memset( readline, 0, sizeof( readline ) );
        fgets( readline,sizeof( readline ),fp );
        if(readline[strlen(readline) - 1] == '\n')    //去掉换行符
            readline[strlen(readline) - 1] = '\0' ;
        pclose(fp);

        char *rotPos=NULL;
        if(	rotPos=strstr(readline,"lcd_type=") )
        {
            rotPos=rotPos+strlen("lcd_type=");
        }
        if(rotPos)
        {
            int lcd_type_value = atoi(rotPos);
            printf("TTTTTT,lcd_type=%d,lcd_type_value=%d\n",lcd_type,lcd_type_value);
            if(lcd_type!=lcd_type_value)
            {
                if(lcd_type == SSD_HARDWARE_LCD_1024_600)
                {
                    printf("change lcd_type to 1!\n");
                    pox_system("/etc/fw_setenv lcd_type 1");
                    pox_system("cp /customer/App/misc/disp/1024x600/config.ini /misc/ -f");
                    pox_system("cp /customer/App/misc/disp/1024x600/logo.jpg /misc/ -f");
                    return lcd_type;
                }
                else if(lcd_type == SSD_HARDWARE_LCD_800_480)
                {
                    printf("change lcd_type to 2!\n");
                    pox_system("/etc/fw_setenv lcd_type 2");
                    pox_system("cp /customer/App/misc/disp/800x480/config.ini /misc/ -f");
                    pox_system("cp /customer/App/misc/disp/800x480/logo.jpg /misc/ -f");
                    return lcd_type;
                }
            }
        }
        else
        {
            printf("Not found lcd_type!\n");
            if(lcd_type==SSD_HARDWARE_LCD_1024_600)
            {
                printf("change lcd_type to 1!\n");
                pox_system("/etc/fw_setenv lcd_type 1");
                pox_system("cp /customer/App/misc/disp/1024x600/config.ini /misc/ -f");
                pox_system("cp /customer/App/misc/disp/1024x600/logo.jpg /misc/ -f");
                return lcd_type;
            }
            else if(lcd_type==SSD_HARDWARE_LCD_800_480)
            {
                printf("change lcd_type to 2!\n");
                pox_system("/etc/fw_setenv lcd_type 2");
                pox_system("cp /customer/App/misc/disp/800x480/config.ini /misc/ -f");
                pox_system("cp /customer/App/misc/disp/800x480/logo.jpg /misc/ -f");
                return lcd_type;
            }
        }
    }
#else

    if(lcd_type == SSD_HARDWARE_LCD_1024_600)
    {
        printf("change lcd_type to 1!\n");
        pox_system("/etc/fw_setenv lcd_type 1");
        pox_system("cp /customer/App/misc/disp/1024x600/config.ini /misc/ -f");
        pox_system("cp /customer/App/misc/disp/1024x600/logo.jpg /misc/ -f");
    }
    else if(lcd_type == SSD_HARDWARE_LCD_800_480)
    {
        printf("change lcd_type to 2!\n");
        pox_system("/etc/fw_setenv lcd_type 2");
        pox_system("cp /customer/App/misc/disp/800x480/config.ini /misc/ -f");
        pox_system("cp /customer/App/misc/disp/800x480/logo.jpg /misc/ -f");
    }
    else if(lcd_type == SSD_HARDWARE_LCD_600_1024)
    {
        printf("change lcd_type to 3!\n");
        pox_system("/etc/fw_setenv lcd_type 3");
        pox_system("cp /customer/App/misc/disp/1024x600/config.ini /misc/ -f");
        pox_system("cp /customer/App/misc/disp/1024x600/logo270.jpg /misc/logo.jpg -f");
    }
    
#endif

	return lcd_type;
}





#endif