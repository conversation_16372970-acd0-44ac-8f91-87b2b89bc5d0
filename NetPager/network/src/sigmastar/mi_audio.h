/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-04 16:18:56 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-24 18:03:12
 */

#ifndef _MI_AUDIO_H_
#define _MI_AUDIO_H_

#if defined(USE_SSD212) || defined(USE_SSD202)

#include "mi_sys.h"
#include "mi_common_datatype.h"
#include "mi_ao.h"
#include "mi_ai.h"
#include "audio_par_common.h"
#if USE_SSD212
#include "AudioAedProcess.h"
#include "AudioProcess.h"
#include "AudioAecProcess.h"
#include "AudioSRCProcess.h"
#endif
#include <pthread.h>



#define MI_AO_SAMPLE_PER_FRAME_NET_PLAY     1152    //1152/44100=26ms
#define MI_AO_SAMPLE_PER_FRAME_NET_PAGING   1024    //960/32000=30ms
#define MI_AO_SAMPLE_PER_FRAME_NET_CALL     512     //512/16000=32ms
#define MI_AO_SAMPLE_PER_FRAME_LOCAL   1024         //960/48000=20ms

#define MI_AI_SAMPLE_PER_FRAME_LOCAL  512     //512/48000=10ms
#define MI_AI_SAMPLE_PER_FRAME_PAGING  512     //512/32000=16ms
#define MI_AI_SAMPLE_PER_FRAME_CALL  256     //256/16000=16ms

#define AUX_VALID_THRESHOLD_DB  -65 //dB
#define MIC_FRONT_VALID_THRESHOLD_DB  -58 //dB
#define MAIC_REAR_VLID_THRESHOLD_DB   -58 //dB
#define AUX_INVLID_TIMEOUT  15      //秒

enum
{
    AI_MODE_NULL,
    AI_MODE_MUISC_48K,
    AI_MODE_CALL_16K,
    AI_MODE_PAGING_16K,
    AI_MODE_PAGING_32K
};


pthread_mutex_t mutex_audio_out;
pthread_mutex_t mutex_audio_in;

extern int mi_ao_init_flag;
extern int mi_ai_init_flag;
extern unsigned int mi_ao_sampleRate;

extern const uint16_t mSysVol[100 + 1];

extern int adc_signal_can_detect;    //ADC是否允许检测，开机前几秒不稳定，不能检测
extern int adc_signal_valid;        //ADC信号是否有效（三路通道至少存在一路）

void mi_audio_out_init(unsigned int sample_rate, unsigned char fmt, unsigned char channels);
void mi_audio_out_deinit();
//void mi_audio_in_init(unsigned int sample_rate, unsigned char fmt, unsigned char channels);
void mi_audio_in_init(unsigned int AiMode,unsigned int sample_rate, unsigned char fmt, unsigned char channels);
void mi_audio_in_deinit();

void mi_audio_write_callRing(const unsigned char *buffer, int count);
#endif
#endif
