/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-03 17:40:51 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2021-09-04 16:48:03
 */

#if defined(USE_SSD212) || defined(USE_SSD202)

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "pthread.h"

#include "mi_functions.h"

static pthread_mutex_t mutex_sys_init=PTHREAD_MUTEX_INITIALIZER;

static int mi_sys_init_flag=0;

void mi_sys_init()
{
    pthread_mutex_lock(&mutex_sys_init);
    if(mi_sys_init_flag)
    {
        pthread_mutex_unlock(&mutex_sys_init);
        return;
    }
    MI_S32 ret=MI_SYS_Init();
    if(MI_SUCCESS != ret)
    {
        printf("MI_SYS_Init err:0x%x\n", ret);
    }
    else
    {
        mi_sys_init_flag=1;
    }
    pthread_mutex_unlock(&mutex_sys_init);
}

void mi_sys_MyExit ()
{
    pthread_mutex_lock(&mutex_sys_init);
    if(!mi_sys_init_flag)
    {
        pthread_mutex_unlock(&mutex_sys_init);
        return;
    }
    MI_S32 ret=MI_SYS_Exit();
    if(MI_SUCCESS != ret)
    {
        printf("MI_SYS_Exit err:0x%x\n", ret);
    }
    pthread_mutex_unlock(&mutex_sys_init);
}

MI_U64 sstar_get_chip_uuid()
{
    MI_U64 u64Uuid=0;
    MI_S32 s32Ret = MI_ERR_SYS_FAILED;
    s32Ret = MI_SYS_ReadUuid (&u64Uuid);
    if(!s32Ret)
    {
        printf("uuid: %llx\n",u64Uuid);
    }
    return u64Uuid;
}

#endif