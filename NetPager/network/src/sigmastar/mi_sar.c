#ifndef USE_PC_SIMULATOR

#include <stdio.h>
#include <unistd.h>
#include <string.h>
#include <fcntl.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include "sysconf.h"
#include "mi_sar.h"

#if 0

typedef struct
{
    int channel_value;
    int adc_value;
} SAR_ADC_CONFIG_READ;


#define SARADC_IOC_MAGIC                     'a'
#define IOCTL_SAR_INIT                       _IO(SARADC_IOC_MAGIC, 0)
#define IOCTL_SAR_SET_CHANNEL_READ_VALUE     _IO(SARADC_IOC_MAGIC, 1)


void Handle_Signal_100V(int Is100V);

void* MI_SAR_CHECK()
{
    int i;
    SAR_ADC_CONFIG_READ  adcCfg;
    adcCfg.channel_value = 0;   //注意：PAD_SAR_GPIO0 = 0 PAD_SAR_GPIO1 = 1 PAD_SAR_GPIO2 = 2

    int fd = open("/dev/sar", O_WRONLY);
    if(fd == -1) {
        int err = errno;
        printf("\n!!! FAILED to open /dev/sar, errno: %d %s\n", err, strerror(err));
        return NULL;
    }

    if (ioctl(fd, IOCTL_SAR_INIT, NULL) < 0) {   
        int err = errno;
        printf("\n!!! IOCTL_SAR_INIT FAILED, errno: %d, %s\n", err, strerror(err));
    }

    int thread_delay_us=200000;    //200ms
    int signal_100v_no_signal_cnt=0;
    int signal_timeout_threshold= TRIGGER_100V_TIMEOUT*1000*1000/thread_delay_us;
    while(1)
    {
        if (ioctl(fd, IOCTL_SAR_SET_CHANNEL_READ_VALUE, &adcCfg) < 0) {
            int err = errno;
            printf("\n!!! IOCTL_SAR_SET_CHANNEL_READ_VALUE FAILED, errno: %d, %s\n", err, strerror(err));
        }
        else {
            //printf("SAR%d:value %04d\n", adcCfg.channel_value,adcCfg.adc_value);
        }

        int sar_value = adcCfg.adc_value;
        if( sar_value >= TRIGGER_100V_THRESHOLD )
        {
            signal_100v_no_signal_cnt = 0;
            if(g_signal_100v == 0)
            {
                g_signal_100v=1;
                Handle_Signal_100V(1);
            }
        }
        else
        {
            signal_100v_no_signal_cnt++;
            if( signal_100v_no_signal_cnt == signal_timeout_threshold )
            {
                if(g_signal_100v == 1)
                {
                    g_signal_100v=0;
                    Handle_Signal_100V(0);
                }
            }
        }

        usleep(thread_delay_us);
    }
}


void MI_SAR_CHECK_THREAD()
{
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)MI_SAR_CHECK, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}

#endif

#endif