/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-21 15:59:34 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2021-09-21 16:03:11
 */

#if defined(USE_SSD212) || defined(USE_SSD202)

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>

#include "sysconf.h"
#include "audio_params.h"

st_dsp_firmware_feature_info dsp_firmware_feature;
 
#if LZY_COMMERCIAL_LCD_800_480
const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x1,0x01,0x00,0x01,0x01,0x00}, \
  																			 {3100,18500,18500,0,2650,7100,2}  };
#else
const st_dsp_firmware_feature_info dsp_default_firmware_feature={ {0x01,0x1,0x01,0x00,0x01,0x01,0x00}, \
  																			 {3100,18500,18500,0,2650,7100,0}  };
#endif

st_dsp_eq_info dsp_eq_info;


/************************EQ模式***************************************
0: 关闭
1：自定义
2：流行
3：舞曲
4：摇滚
5：古典
6：人声
7：柔和
以下字段选择（自定义）有效
*/
unsigned short EqFreqArray[10]={ 31,62,125,250,500,1000,2000,4000,8000,16000 };
unsigned char EqGainArray[DSP_MAX_EQ_MODE_NUM][10]={
  										{0,0,0,0,0,0,0,0,0,0},	//关闭
  										{0,0,0,0,0,0,0,0,0,0},	//自定义
										{6,5,253,254,5,4,252,253,6,4},
										{4,3,252,250,0,0,3,4,4,5},
										{7,6,2,1,253,252,2,1,4,5},
										{6,7,1,12,255,1,252,250,249,248},
										{251,250,252,253,3,4,5,4,253,253},
										{251,251,252,252,3,2,4,4,0,0},
									 };


#endif