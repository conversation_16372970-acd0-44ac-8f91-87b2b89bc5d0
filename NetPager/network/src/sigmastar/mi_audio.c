/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2021-09-03 17:40:51 
 * @Last Modified by: <PERSON><PERSON><PERSON>
 * @Last Modified time: 2022-02-24 18:05:24
 */

#if defined(USE_SSD212) || defined(USE_SSD202)

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <stdint.h>
#include <limits.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <semaphore.h>

#include "sysconf.h"
#include "audioProcess.h"
#include "mi_audio.h"
#include "sourceControl.h"
#include "uart_adc.h"
#include "call_process.h"
#include "I2S.h"
#if SUPPORT_CODEC_G722
#include "g722/g722_encoder.h"
#include "g722/g722_decoder.h"
#endif
#if defined(USE_SSD212)
#include "es7210.h"
#elif defined(USE_SSD202)
#include "es7243.h"
#endif

#if USE_SSD202
#include "speex/speex_preprocess.h"
#include "speex/speex_echo.h"
#endif


#define USER_BUF_DEPTH      (4)
#define TOTAL_BUF_DEPTH     (8)


#define MAX_ADC_DATA_PKG_NUM    4
typedef struct
{
    int16_t adc0_data[MAX_ADC_DATA_PKG_NUM][MI_AI_SAMPLE_PER_FRAME_LOCAL];
    int16_t adc1_data[MAX_ADC_DATA_PKG_NUM][MI_AI_SAMPLE_PER_FRAME_LOCAL];
    int16_t adc2_data[MAX_ADC_DATA_PKG_NUM][MI_AI_SAMPLE_PER_FRAME_LOCAL];
    int16_t adc3_data[MAX_ADC_DATA_PKG_NUM][MI_AI_SAMPLE_PER_FRAME_LOCAL];
    int16_t adc_data_len[MAX_ADC_DATA_PKG_NUM];
    int8_t adc_data_valid[MAX_ADC_DATA_PKG_NUM];
    int8_t write_pos;
    int8_t read_pos;
}_st_AdcData_Info;

_st_AdcData_Info st_AdcData_Info;


typedef struct AiChnPriv_s
{
    MI_AUDIO_DEV AiDevId;
    MI_AI_CHN AiChn;
    MI_S32 s32Fd;
    MI_U32 u32TotalSize;
    MI_U32 u32ChnCnt;
    pthread_t tid;
} AiChnPriv_t;

static AiChnPriv_t stAiChnPriv[MI_AUDIO_MAX_CHN_NUM];
pthread_t tid_local_write;
static MI_S32   AiChnFd[MI_AUDIO_MAX_CHN_NUM] = {[0 ... MI_AUDIO_MAX_CHN_NUM-1] = -1};


#define MI_DEFAULT_AO_DEV_ID    0
#define MI_DEFAULT_AI_DEV_ID    2       //AI_DEV_ID_ADC_0_1_2 (5) ;I2S RX(2)

#if USE_SSD212
#define MI_DEFAULT_AI_CHANNEL_CNT   4
#elif USE_SSD202
#define MI_DEFAULT_AI_CHANNEL_CNT   2
#endif


int mi_ao_init_flag=0;
int mi_ai_init_flag=0;   //0-AI未启动  1-音乐模式48K  2-对讲模式16K
static int mi_ao_channel_num=0;
static int mi_ai_channel_num=0;

unsigned int mi_ao_sampleRate=0;

static unsigned int mi_ao_totalByteNum=0;


//均衡器
#define EQ_FRAME_SIZE           128         //EQ每次处理的采样点个数
static char *mi_eq_workingBuffer = NULL;

static int  mi_eq_init_flag=0;

static short mi_eq_table[129];

static int16_t mi_eq_remain_data[512];     //EQ处理剩下的数据
static int mi_eq_remain_samples=0;             //EQ处理剩下的长度(以short为单位)

//互斥锁
pthread_mutex_t mutex_audio_out=PTHREAD_MUTEX_INITIALIZER;
pthread_mutex_t mutex_audio_in=PTHREAD_MUTEX_INITIALIZER;

extern int concentrated_write_need_wait;

int dac_sampers_cnt=0;


sem_t sem_aiReady;  //信号量-ai数据已准备好

int adc_signal_can_detect=0;    //ADC是否允许检测，开机前几秒不稳定，不能检测
int adc_signal_valid=0;         //ADC信号是否有效（三路通道至少存在一路）


void pkg_query_current_status(unsigned char *rxbuf);

void* mi_audio_local_write_thread();
void* mi_audio_call_write_thread();



#if USE_SSD212

typedef struct
{
    AedHandle aedHandle;
    char *workingBuf;
}MyAedStruct;

typedef struct
{
    ANR_HANDLE anrHandle;
    char *workingBuf;
}MyAnrStruct;

typedef struct
{
    SRC_HANDLE srcHandle;
    char *workingBuf;
}MySrcStruct;

typedef struct
{
    AEC_HANDLE aecHandle;
    char *workingBuf;
}MyAecStruct;


MyAedStruct mi_audio_alg_aed_init(int sampleRate,int channel)
{
    MyAedStruct aedStruct;
    memset(&aedStruct,0,sizeof(MyAedStruct));
    unsigned int aedBuffSize=0;  
    AedProcessStruct aed_params;

    AedSampleRate sample_rate = sampleRate;
    aed_params.channel = 1;
    aed_params.point_number = 128;

    ALGO_AED_RET ret = ALGO_AED_RET_SUCCESS;

    aedBuffSize = IaaAed_GetBufferSize();  
    aedStruct.workingBuf = (char *)malloc(aedBuffSize);  
    if(NULL == aedStruct.workingBuf)  
    {  
        printf("malloc workingBuffer failed !\n");   
    }  
    aedStruct.aedHandle = IaaAed_Init(aedStruct.workingBuf, &aed_params);  
    if(NULL == aedStruct.aedHandle)  
    {  
        printf("IaaAed_Init faild !\n");  
    }  
    ret = IaaAed_Config(aedStruct.aedHandle);  
    if(ALGO_AED_RET_SUCCESS != ret)  
    {  
        printf("IaaAed_Config failed !, ret = %d\n", ret);  
    }

    ret = IaaAed_SetSampleRate(aedStruct.aedHandle, sample_rate);  
    if(ALGO_AED_RET_SUCCESS != ret)
    {  
        printf("IaaAed_SetSampleRate failed !, ret = %d\n", ret);  
    }

    printf("mi_audio_alg_aed_init OK...\n");
    return aedStruct;
}

void mi_audio_alg_aed_deinit(MyAedStruct aedStruct)
{
    if(aedStruct.aedHandle!=NULL)
    {
        IaaAed_Release(aedStruct.aedHandle);
        aedStruct.aedHandle=NULL;
    }
    if(aedStruct.workingBuf!=NULL)
    {
        free(aedStruct.workingBuf);
        aedStruct.workingBuf=NULL;
    }
    printf("mi_audio_alg_aed_deinit OK...\n");
}


//支持8K,16K,48K
MyAnrStruct mi_audio_alg_anr_init(int sampleRate,int channel)
{
    MyAnrStruct anrStruct;
    memset(&anrStruct,0,sizeof(MyAnrStruct));
    AudioProcessInit anr_init;
    AudioAnrConfig anr_config;

    unsigned int anr_workingBufferSize;

    int intensity_band[6] = {3,24,40,64,80,128};  
    int intensity[7] = {10,10,10,10,10,10,10};

    anr_init.point_number = 128;
    anr_init.channel = channel;
    anr_init.sample_rate = sampleRate;

        /******ANR Config*******/  
    anr_config.anr_enable = 1;  
    anr_config.user_mode = 2;  
    memcpy(anr_config.anr_intensity_band, intensity_band, sizeof(intensity_band));  
    memcpy(anr_config.anr_intensity, intensity, sizeof(intensity));  
    anr_config.anr_smooth_level = 10;  
    anr_config.anr_converge_speed = 0;

    anr_workingBufferSize = IaaAnr_GetBufferSize();
    anrStruct.workingBuf = (char *)malloc(anr_workingBufferSize);

    //(2)IaaAnr_Init  
    anrStruct.anrHandle = IaaAnr_Init(anrStruct.workingBuf, &anr_init);  
    if(NULL == anrStruct.anrHandle)  
    {
        printf("IaaAnr_Init failed !\n");  
        return anrStruct;  
    }
    //(3)IaaAnr_Config  
    MI_S32 ret = IaaAnr_Config(anrStruct.anrHandle, &anr_config);  
    if(ret)
    {  
        printf("IaaAnr_Config failed !\n");  
        return anrStruct;  
    }  
    printf("mi_audio_alg_anr_init OK...\n");
    return anrStruct;
}


void mi_audio_alg_anr_deinit(MyAnrStruct anrStruct)
{
    if(anrStruct.anrHandle!=NULL)
    {
        IaaAnr_Free(anrStruct.anrHandle);  
    }
    if(anrStruct.workingBuf!=NULL)
    {
        free(anrStruct.workingBuf);
        anrStruct.workingBuf=NULL;
    }
    printf("mi_audio_alg_anr_deinit OK...\n");
}



MySrcStruct mi_audio_alg_src_init(SrcInSrate inSampleRate,SrcConversionMode mode,int channel,int inSamplePonits)
{
    MySrcStruct srcStruct;
    memset(&srcStruct,0,sizeof(MySrcStruct));
    
    SRCStructProcess src_struct;  
    /*********************User change section start*******************/  
    /* 
    *  The user modifies as needed,for example:1, 2 
    */  
    src_struct.channel = channel;
    /* 
    *  The user modifies as needed,for example: 
    *  SRATE_8K, SRATE_16K, SRATE_32K, SRATE_48K 
    */  
    src_struct.WaveIn_srate = inSampleRate;
    /* 
    *  The user modifies as needed,for example: 
    *  SRC_8k_to_16k, SRC_8k_to_32k, SRC_48k_to_8k ... 
    */  
    src_struct.mode = mode;
    /* 
    *  The user modifies as needed,for example: 
    *  256, 512, 1024 and 1536 (Please select among these values) 
    */  
    src_struct.point_number = inSamplePonits;
    /*********************User change section end*******************/  
    ALGO_SRC_RET ret1;
    int output_size1;
    unsigned int workingBufferSize;  

    //(1)IaaSrc_GetBufferSize  
    workingBufferSize =  IaaSrc_GetBufferSize(mode);  
    srcStruct.workingBuf = (char *)malloc(sizeof(char) * workingBufferSize);  
    if(NULL == srcStruct.workingBuf)  
    {  
        printf("malloc SRC workingBuffer failed !\n");  
        return srcStruct;  
    }  
    //(2)IaaSrc_Init  
    srcStruct.srcHandle = IaaSrc_Init(srcStruct.workingBuf, &src_struct);
    if(NULL == srcStruct.srcHandle)  
    {  
        printf("SRC:IaaSrc_Init  failed !\n");  
        return srcStruct;  
    }  
    printf("mi_audio_alg_src_init OK...\n");

    return srcStruct;
}


void mi_audio_alg_src_deinit(MySrcStruct srcStruct)
{
    if(srcStruct.srcHandle!=NULL)
    {
        IaaSrc_Release(srcStruct.srcHandle);  
    }
    if(srcStruct.workingBuf!=NULL)
    {
        free(srcStruct.workingBuf);
        srcStruct.workingBuf=NULL;
    }
    printf("mi_audio_alg_src_deinit OK...\n");
}




MyAecStruct mi_audio_alg_aec_init(int sampleRate,int channel)
{
    MyAecStruct aecStruct;
    memset(&aecStruct,0,sizeof(aecStruct));

    AudioAecInit aec_init;
    AudioAecConfig aec_config;
    
    /*********************User change section start*******************/
    unsigned int supMode_band[6] = {20,40,60,80,100,120};   //todo校正
    unsigned int supMode[7] = {13,13,13,13,13,13,13};              //强度
    aec_init.point_number = 128; //AEC算法每次处理的采样点个数
    aec_init.nearend_channel = channel;
    aec_init.farend_channel = channel;
    aec_init.sample_rate = sampleRate;
    aec_config.delay_sample = 0;
    aec_config.comfort_noise_enable = IAA_AEC_FALSE;
    /*********************User change section end*******************/
    memcpy(&(aec_config.suppression_mode_freq[0]), supMode_band, sizeof(supMode_band));
    memcpy(&(aec_config.suppression_mode_intensity[0]), supMode, sizeof(supMode));

    //(1)IaaAec_GetBufferSize
    unsigned int aecWorkingBufferSize = IaaAec_GetBufferSize();
    aecStruct.workingBuf = (char*)malloc(aecWorkingBufferSize);
    if(NULL == aecStruct.workingBuf)
    {
        printf("malloc AEC workingBuffer failed !\n"); 
        return aecStruct;
    }
    //(2)IaaAec_Init
    aecStruct.aecHandle = IaaAec_Init(aecStruct.workingBuf, &aec_init);
    if (NULL == aecStruct.aecHandle)
    {
        printf("AEC init failed !\r\n");
        return aecStruct;
    }
    //(3)IaaAec_Config
    MI_S32 ret = IaaAec_Config(aecStruct.aecHandle, &aec_config);
    if(ret)
    {
        printf("IaaAec_Config failed !\n");
        return aecStruct;
    }
    printf("mi_audio_alg_aec_init OK...\n");

    return aecStruct;
}


void mi_audio_alg_aec_deinit(MyAecStruct aecStruct)
{
    if(aecStruct.aecHandle!=NULL)
    {
        IaaAec_Free(aecStruct.aecHandle);  
    }
    if(aecStruct.workingBuf!=NULL)
    {
        free(aecStruct.workingBuf);
        aecStruct.workingBuf=NULL;
    }
    printf("mi_audio_alg_aec_deinit OK...\n");
}

#endif



void mi_audio_out_init(unsigned int sample_rate, unsigned char fmt, unsigned char channels)
{
    int sysSource = get_system_source();

    //mi_sys_init();
    //退出之前的audio
    mi_audio_out_deinit();

    dac_sampers_cnt=0;

    pthread_mutex_lock(&mutex_audio_out);
    MI_S32 ret = MI_SUCCESS;
    MI_AUDIO_Attr_t stAoSetAttr, stAoGetAttr;
    MI_S32 s32AoGetVolume;
    MI_AO_ChnParam_t stAoChnParam;
    MI_U32 u32DmaBufSize;
    MI_BOOL bInitAdec = FALSE, bInitRes = FALSE;

    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    MI_AO_CHN AoChn = 0;


    memset(&stAoSetAttr, 0x0, sizeof(MI_AUDIO_Attr_t));
    #if USE_SSD212
    stAoSetAttr.eBitwidth = E_MI_AUDIO_BIT_WIDTH_16;
    stAoSetAttr.eWorkmode = E_MI_AUDIO_MODE_I2S_MASTER;
    stAoSetAttr.WorkModeSetting.stI2sConfig.bSyncClock = FALSE;
    stAoSetAttr.WorkModeSetting.stI2sConfig.eFmt = E_MI_AUDIO_I2S_FMT_I2S_MSB;
    stAoSetAttr.WorkModeSetting.stI2sConfig.eMclk = E_MI_AUDIO_I2S_MCLK_0;

    stAoSetAttr.u32ChnCnt = 1;
    #elif USE_SSD202
    stAoSetAttr.eBitwidth = E_MI_AUDIO_BIT_WIDTH_16;
    stAoSetAttr.eWorkmode = E_MI_AUDIO_MODE_I2S_MASTER;
    stAoSetAttr.WorkModeSetting.stI2sConfig.bSyncClock = TRUE;
    stAoSetAttr.WorkModeSetting.stI2sConfig.eMclk = E_MI_AUDIO_I2S_MCLK_12_288M;
    
    stAoSetAttr.u32ChnCnt = 2;
    #endif
    if( sysSource == SOURCE_CALL || g_isCallRinging )
    {
        stAoSetAttr.u32PtNumPerFrm = MI_AO_SAMPLE_PER_FRAME_NET_CALL;
    }
    else
    {
        stAoSetAttr.u32PtNumPerFrm = MI_AO_SAMPLE_PER_FRAME_NET_PAGING;
    }

    mi_ao_channel_num=channels;
    mi_ao_sampleRate=sample_rate;

    stAoSetAttr.eSoundmode = E_MI_AUDIO_SOUND_MODE_STEREO;  //永远保持双通道输出

    stAoSetAttr.eSamplerate = sample_rate;

    

    ret = MI_AO_SetPubAttr(AoDevId, &stAoSetAttr);

    if(MI_SUCCESS != ret)
    {
        printf("set ao %d attr err:0x%x\n", AoDevId, ret);
        pthread_mutex_unlock(&mutex_audio_out);
        return;
    }

    ret = MI_AO_GetPubAttr(AoDevId, &stAoGetAttr);
    if(MI_SUCCESS != ret)
    {
        printf("get ao %d attr err:0x%x\n", AoDevId, ret);
        pthread_mutex_unlock(&mutex_audio_out);
        return;
    }
    /* enable ao device */
    ret = MI_AO_Enable(AoDevId);
    if(MI_SUCCESS != ret)
    {
        printf("enable ao dev %d err:0x%x\n", AoDevId, ret);
        pthread_mutex_unlock(&mutex_audio_out);
        return;
    }
    ret = MI_AO_EnableChn(AoDevId, AoChn);
    if (MI_SUCCESS != ret)
    {
        printf("enable ao dev %d chn %d err:0x%x\n", AoDevId, AoChn, ret);
        pthread_mutex_unlock(&mutex_audio_out);
        return;
    }
    printf("mi_audio_out_init succeed:%d...\n",sample_rate);

    #if USE_SSD212
    MI_AO_SetVolume(AoDevId, AoChn, 0, E_MI_AO_GAIN_FADING_OFF);
    #elif USE_SSD202
    MI_AO_SetVolume(AoDevId, 0);
    #endif

    mi_ao_init_flag=1;

    pthread_mutex_unlock(&mutex_audio_out);
}


void mi_audio_out_deinit()
{
    pthread_mutex_lock(&mutex_audio_out);
    MI_S32 ret;
    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    MI_AO_CHN AoChn = 0;
    if(mi_ao_init_flag)
    {
        //ExecFuncNoExit(MI_AO_DisableChn(0, 0), MI_SUCCESS,ret);
        //ExecFuncNoExit(MI_AO_Disable(0), MI_SUCCESS,ret);
#if 0
        /* get chn stat */  
        MI_AO_ChnState_t stStatus;
        ret = MI_AO_QueryChnStat(AoDevId, AoChn, &stStatus);  
        if (MI_SUCCESS != ret)  
        {  
            printf("query chn status ao dev %d chn %d err:0x%x\n", AoDevId, AoChn, ret);   
        }  
        
    /* clear chn buf */  
        ret = MI_AO_ClearChnBuf(AoDevId, AoChn);  
        if (MI_SUCCESS != ret)  
        {
            printf("clear chn buf ao dev %d chn %d err:0x%x\n", AoDevId, AoChn, ret);  
        }
#endif

        /* clear chn buf */  
        ret = MI_AO_ClearChnBuf(AoDevId, AoChn);  
        if (MI_SUCCESS != ret)  
        {  
            printf("clear chn buf ao dev %d chn %d err:0x%x\n", AoDevId, AoChn, ret);  
            return;  
        }  

        ret = MI_AO_DisableChn(AoDevId, AoChn);
        if(MI_SUCCESS != ret)
        {
            printf("disable ao dev %d chn %d err:0x%x\n", 0, 0, ret);
            pthread_mutex_unlock(&mutex_audio_out);
            return;
        }
        ret = MI_AO_Disable(AoDevId);
        if (MI_SUCCESS != ret)
        {
            printf("disable ao dev %d err:0x%x\n", 0, ret);
            pthread_mutex_unlock(&mutex_audio_out);
            return;
        }
        mi_ao_init_flag=0;
        mi_ao_totalByteNum=0;

        //重新初始化EQ（重要），否则切换到本地会有冲击，或者使用IaaEq_Reset
        if(mi_eq_init_flag)
        {
            mi_audio_eq_init(1);
        }
        printf("mi_audio_out_deinit ok...\n");
    }
    pthread_mutex_unlock(&mutex_audio_out);
}




static void* aiGetChnPortBuf(void* data)
{
    AiChnPriv_t* priv = (AiChnPriv_t*)data;
    MI_AUDIO_Frame_t stAiChFrame;
    MI_AUDIO_AecFrame_t stAecFrame;
    MI_S32 s32Ret;
    struct timeval tv_before, tv_after;
    MI_S64 before_us, after_us;
    MI_U32 u32ChnIdx, u32ChnIdxStart,u32ChnIdxEnd;

    memset(&stAiChFrame, 0, sizeof(MI_AUDIO_Frame_t));
    memset(&stAecFrame, 0, sizeof(MI_AUDIO_AecFrame_t));

    int can_play=0;
    while(mi_ai_init_flag)
    {
        //gettimeofday(&tv_before, NULL);
        u32ChnIdxStart = 0;
        u32ChnIdxEnd = priv->u32ChnCnt;
        int u32_len=0;
        for (u32ChnIdx = u32ChnIdxStart; u32ChnIdx < u32ChnIdxEnd; u32ChnIdx++)
        {
            #if USE_SSD212
            s32Ret = MI_AI_GetFrame(priv->AiDevId, u32ChnIdx, &stAiChFrame, NULL, -1);
            u32_len = stAiChFrame.u32Len[0];
            if(u32ChnIdx == 0)  //回路
                memcpy(st_AdcData_Info.adc0_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            else if(u32ChnIdx == 1) //channel1、channel2数据对调,原因不明   //MIC1
                memcpy(st_AdcData_Info.adc2_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            else if(u32ChnIdx == 2) //LINE IN
                memcpy(st_AdcData_Info.adc1_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            else if(u32ChnIdx == 3) //MIC2
                memcpy(st_AdcData_Info.adc3_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            #elif USE_SSD202
            s32Ret = MI_AI_GetFrame(priv->AiDevId, u32ChnIdx, &stAiChFrame, &stAecFrame, -1);
            u32_len = stAiChFrame.u32Len;
            if(u32ChnIdx == 0)  //line in
            {
                if(mi_ai_init_flag != AI_MODE_CALL_16K)  //非对讲时，左声道是线路输入
                {
                    memcpy(st_AdcData_Info.adc1_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len);
                }
                else    //对讲时，左声道是MIC
                {
                    memcpy(st_AdcData_Info.adc2_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len);
                }
            }
            else if(u32ChnIdx == 1) //MIC
            {
                if(mi_ai_init_flag != AI_MODE_CALL_16K)  //非对讲时，右声道是MIC
                {
                    memcpy(st_AdcData_Info.adc2_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len);
                }
                else    //对讲时，右声道是回路
                {
                    memcpy(st_AdcData_Info.adc0_data[st_AdcData_Info.write_pos],stAiChFrame.apVirAddr[0],stAiChFrame.u32Len);
                }
            }
            #endif
            MI_AI_ReleaseFrame(priv->AiDevId, u32ChnIdx,  &stAiChFrame,  NULL);
        }

        if (MI_SUCCESS == s32Ret)
        {
            //printf("u32=%d\n",u32_len);
            #if 1
            st_AdcData_Info.adc_data_len[st_AdcData_Info.write_pos] = u32_len;
            st_AdcData_Info.adc_data_valid[st_AdcData_Info.write_pos] = 1;
            st_AdcData_Info.write_pos++;
            if(st_AdcData_Info.write_pos>=MAX_ADC_DATA_PKG_NUM)
            {
                st_AdcData_Info.write_pos=0;
            }
            #endif
            #if 0
            //第一个包延迟发送，避免卡顿
            if(can_play<1) //first can_play=0,enter this=1
            {
               can_play++;
            }
            else
            #endif
            {
                sem_post(&sem_aiReady);
            }
            
            //printf("write: %x,len: %d\n",stAiChFrame.apVirAddr[0],stAiChFrame.u32Len[0]);
            //write(priv->s32Fd, stAiChFrame.apVirAddr[0], stAiChFrame.u32Len[0]);
            #if 0
            gettimeofday(&tv_after, NULL);
            before_us = tv_before.tv_sec * 1000000 + tv_before.tv_usec;
            after_us = tv_after.tv_sec * 1000000 + tv_after.tv_usec;
            //if (after_us - before_us > 10 * 1000)
            {
                //printf("Chn:%d,len=%d,cost time:%lldus = %lldms.\n", priv->AiChn, stAiChFrame.u32Len[0], after_us - before_us, (after_us - before_us) / 1000);
            }
            priv->u32TotalSize += stAiChFrame.u32Len[0];
            {
                //printf("Chn:%d,len=%d,cost time:%lldus = %lldms.\n");
            }
            #endif
        }
        else
        {
            printf("Dev%dChn%d get frame failed!!!error:0x%x\n", priv->AiDevId, priv->AiChn, s32Ret);
        }
    }
    return NULL;
}

#if 1
void mi_audio_in_init(unsigned int AiMode,unsigned int sample_rate, unsigned char fmt, unsigned char channels)
{
    #if USE_SSD202
    MI_AI_VqeConfig_t stVqeConfig;
    #endif

    pthread_mutex_lock(&mutex_audio_in);
    //判断是否需要再次初始化
    if(AiMode != AI_MODE_MUISC_48K && AiMode != AI_MODE_CALL_16K && AiMode != AI_MODE_PAGING_16K && AiMode != AI_MODE_PAGING_32K )
    {
        printf("AiMode error\n");
        return;
    }
    if(mi_ai_init_flag == AiMode)
    {
        printf("aiMode:%d not changed,return!\n",AiMode);
        pthread_mutex_unlock(&mutex_audio_in);
        return;
    }

    //mi_sys_init();
    //退出之前的audio
    mi_audio_in_deinit();

#if USE_SSD212
    if(sample_rate == 32000)
    {
        es7210_adc_config_i2s(AUDIO_HAL_I2S_NORMAL,AUDIO_HAL_32K_SAMPLES,AUDIO_HAL_BIT_LENGTH_16BITS);
    }
    else if(sample_rate == 16000)
    {
        es7210_adc_config_i2s(AUDIO_HAL_I2S_NORMAL,AUDIO_HAL_16K_SAMPLES,AUDIO_HAL_BIT_LENGTH_16BITS);
    }
#endif

    sem_init(&sem_aiReady,0,0);

    MI_S32 ret = MI_SUCCESS;
    MI_AUDIO_Attr_t stAiSetAttr, stAiGetAttr;
    MI_SYS_ChnPort_t    stAiChnOutputPort0[MI_AUDIO_MAX_CHN_NUM];
    MI_AI_ChnParam_t stAiChnParam[MI_AUDIO_MAX_CHN_NUM];
    MI_BOOL bInitAdec = FALSE, bInitRes = FALSE;

    MI_AUDIO_DEV AiDevId = MI_DEFAULT_AI_DEV_ID;
    MI_AO_CHN AiChn = 0;

    MI_U32  u32ChnIdx;

    memset(&stAiSetAttr, 0x0, sizeof(MI_AUDIO_Attr_t));
    stAiSetAttr.eBitwidth = E_MI_AUDIO_BIT_WIDTH_16;
    if(sample_rate == 48000)
        stAiSetAttr.u32PtNumPerFrm = MI_AI_SAMPLE_PER_FRAME_LOCAL;
    else if(sample_rate == 16000)
        stAiSetAttr.u32PtNumPerFrm = MI_AI_SAMPLE_PER_FRAME_CALL;
    else if(sample_rate == 32000)
        stAiSetAttr.u32PtNumPerFrm = MI_AI_SAMPLE_PER_FRAME_PAGING;
    stAiSetAttr.u32ChnCnt = MI_DEFAULT_AI_CHANNEL_CNT;
    stAiSetAttr.eSoundmode = E_MI_AUDIO_SOUND_MODE_MONO;
    stAiSetAttr.eSamplerate = sample_rate;

    #if USE_SSD212
    stAiSetAttr.eWorkmode = E_MI_AUDIO_MODE_TDM_MASTER;
    stAiSetAttr.WorkModeSetting.stI2sConfig.eI2sBitWidth = E_MI_AUDIO_BIT_WIDTH_16;
    stAiSetAttr.WorkModeSetting.stI2sConfig.u32TdmSlots = MI_DEFAULT_AI_CHANNEL_CNT;
    #elif USE_SSD202
    stAiSetAttr.eWorkmode = E_MI_AUDIO_MODE_I2S_MASTER;
    #endif
    stAiSetAttr.WorkModeSetting.stI2sConfig.bSyncClock = TRUE;
    stAiSetAttr.WorkModeSetting.stI2sConfig.eFmt = E_MI_AUDIO_I2S_FMT_I2S_MSB;
    stAiSetAttr.WorkModeSetting.stI2sConfig.eMclk = E_MI_AUDIO_I2S_MCLK_12_288M;


    ret = MI_AI_SetPubAttr(AiDevId, &stAiSetAttr);

    if(MI_SUCCESS != ret)
    {
        printf("set ao %d attr err:0x%x\n", AiDevId, ret);
        pthread_mutex_unlock(&mutex_audio_in);
        return;
    }

    ret = MI_AI_GetPubAttr(AiDevId, &stAiGetAttr);
    if(MI_SUCCESS != ret)
    {
        printf("get ai %d attr err:0x%x\n", AiDevId, ret);
        pthread_mutex_unlock(&mutex_audio_in);
        return;
    }
    /* enable ai device */
    ret = MI_AI_Enable(AiDevId);
    if(MI_SUCCESS != ret)
    {
        printf("enable ai dev %d err:0x%x\n", AiDevId, ret);
        pthread_mutex_unlock(&mutex_audio_in);
        return;
    }

    mi_ai_init_flag=AiMode;

    memset(&stAiChnParam, 0x0, sizeof(MI_AI_ChnParam_t));

    for(u32ChnIdx=0;u32ChnIdx<stAiSetAttr.u32ChnCnt;u32ChnIdx++)
    {
        stAiChnParam[u32ChnIdx].stChnGain.bEnableGainSet = TRUE;
        if(u32ChnIdx == 0)      //LINE
        {
            stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 2;    //0dB
            stAiChnParam[u32ChnIdx].stChnGain.s16RearGain = 0;
        }
        else if(u32ChnIdx == 1) //NetSpeakerD:LOCAL MIC    NetSpeakerE:REFER ECHO
        {
            stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 2;    //9dB,20mv-60mv
            stAiChnParam[u32ChnIdx].stChnGain.s16RearGain = 0;
        }
        else if(u32ChnIdx == 2 || u32ChnIdx == 3)   //NetSpeakerD:Remote MIC or environment monitor NetSpeakerE:LOCAL MIC
        {
            stAiChnParam[u32ChnIdx].stChnGain.s16FrontGain = 2;    //9dB,20mv-60mv
            stAiChnParam[u32ChnIdx].stChnGain.s16RearGain = 0;
        }
    }


#if USE_SSD202
    if(Paging_status == CALLING_START)
    {
        /* set ext Aec Chn */
        //MI_AI_SetExtAecChn(AiDevId, 0,1);
    }

    //自动处理，无需操作，此处可以考虑进行7210软件复位，去除上一次I2S总线多余的数据
    if(mi_ai_init_flag == AI_MODE_CALL_16K)
    {
        //SDI切换成ES7210的AD2/AD3
         I2S_SDI_SELECT_MODE(1);
    }
    else
    {
        //SDI切换成ES7210的AD0/AD1
        I2S_SDI_SELECT_MODE(0);
    }

    #if 1
    es7210_reset();
    #endif

#endif


    for(u32ChnIdx=0;u32ChnIdx<stAiSetAttr.u32ChnCnt;u32ChnIdx++)
    {
        stAiChnPriv[u32ChnIdx].AiChn = u32ChnIdx;
        stAiChnPriv[u32ChnIdx].AiDevId = AiDevId;
        stAiChnPriv[u32ChnIdx].s32Fd = AiChnFd[u32ChnIdx];
        stAiChnPriv[u32ChnIdx].u32ChnCnt = MI_DEFAULT_AI_CHANNEL_CNT; 
        stAiChnPriv[u32ChnIdx].u32TotalSize = 0;
        #if USE_SSD212
        MI_AI_SetChnParam( AiDevId,  u32ChnIdx, &stAiChnParam[u32ChnIdx]);
        #endif
        if(u32ChnIdx<stAiChnPriv[u32ChnIdx].u32ChnCnt)
        {
            stAiChnOutputPort0[u32ChnIdx].eModId = E_MI_MODULE_ID_AI;
            stAiChnOutputPort0[u32ChnIdx].u32DevId = AiDevId;
            stAiChnOutputPort0[u32ChnIdx].u32ChnId = u32ChnIdx;
            stAiChnOutputPort0[u32ChnIdx].u32PortId = 0;
            
            //ExecFunc(MI_AI_SetVqeVolume(AiDevId, u32ChnIdx, s32AiVolume), MI_SUCCESS);
            MI_SYS_SetChnOutputPortDepth(&stAiChnOutputPort0[u32ChnIdx], USER_BUF_DEPTH, TOTAL_BUF_DEPTH);
        }

        MI_AI_EnableChn(AiDevId, u32ChnIdx);
    }


    struct sched_param sched3;
    sched3.sched_priority = 99;
    if(mi_ai_init_flag == AI_MODE_MUISC_48K || mi_ai_init_flag == AI_MODE_PAGING_16K || mi_ai_init_flag == AI_MODE_PAGING_32K)
    {
        pthread_create(&tid_local_write, NULL, mi_audio_local_write_thread, NULL);
    }
    else if(mi_ai_init_flag == AI_MODE_CALL_16K)
    {
        pthread_create(&tid_local_write, NULL, mi_audio_call_write_thread, NULL);
    }
    
    pthread_create(&stAiChnPriv[0].tid, NULL, aiGetChnPortBuf, &stAiChnPriv[0]);
    pthread_setschedparam(tid_local_write, SCHED_RR,  &sched3);
    pthread_setschedparam(stAiChnPriv[0].tid, SCHED_RR,  &sched3);


#if 0//USE_SSD202
    /* set vqe attr */
    memset(&stVqeConfig, 0x0, sizeof(stVqeConfig));
    stVqeConfig.bAecOpen = false;
    stVqeConfig.bAnrOpen = true;
    stVqeConfig.u32ChnNum = 1;
    stVqeConfig.s32WorkSampleRate = 16000;
    stVqeConfig.s32FrameSample = 128;

    stVqeConfig.stAecCfg.bComfortNoiseEnable = false;
    stVqeConfig.stAecCfg.s16DelaySample = 0;
    unsigned int supMode_band[6] = {20,40,60,80,100,120};   //todo校正
    unsigned int supMode_intensity[7] = {13,13,13,13,13,13,13};              //强度
    memcpy(stVqeConfig.stAecCfg.u32AecSupfreq, supMode_band, sizeof(supMode_band));
    memcpy(stVqeConfig.stAecCfg.u32AecSupIntensity, supMode_intensity, sizeof(supMode_intensity));
        
        MI_AUDIO_AnrConfig_t stAnrCfg = {
        .eMode = E_MI_AUDIO_ALGORITHM_MODE_USER,
        .u32NrIntensityBand = {3,24,40,64,80,128},
        .u32NrIntensity = {15,15,15,15,15,15,15},
        .u32NrSmoothLevel = 10,
        .eNrSpeed = E_MI_AUDIO_NR_SPEED_MID,
        };
        memcpy(&stVqeConfig.stAnrCfg, &stAnrCfg, sizeof(MI_AUDIO_AnrConfig_t));

    if(mi_ai_init_flag != AI_MODE_CALL_16K)  //非对讲时，VQE针对右声道(MIC)
    {
        ret = MI_AI_SetVqeAttr(AiDevId, 1, MI_DEFAULT_AO_DEV_ID, 0, &stVqeConfig);
    }
    else    //对讲时，VQE针对左声道(MIC)
    {
        ret = MI_AI_SetVqeAttr(AiDevId, 0, MI_DEFAULT_AO_DEV_ID, 0, &stVqeConfig);
    }
    if (MI_SUCCESS != ret)
    {
        printf("set vqe attr Dev%d err:0x%x\n", AiDevId, ret);
    }
    else
    {
        printf("MI_AI_SetVqeAttr succeed!!!\n");
    }
#endif


    printf("mi_audio_in_init succeed...\n");

    pthread_mutex_unlock(&mutex_audio_in);
}


void mi_audio_in_deinit()
{
    MI_S32 ret;
    if(mi_ai_init_flag)
    {
        MI_U32 u32ChnIdx, u32ChnIdxStart,u32ChnIdxEnd;
        u32ChnIdxStart = 0;
        u32ChnIdxEnd = MI_DEFAULT_AI_CHANNEL_CNT;

        mi_ai_init_flag=0;
        sem_post(&sem_aiReady);

        pthread_join(tid_local_write, NULL);
        pthread_join(stAiChnPriv[0].tid, NULL);

        memset(&st_AdcData_Info,0,sizeof(st_AdcData_Info));

        for (u32ChnIdx = u32ChnIdxStart; u32ChnIdx < u32ChnIdxEnd; u32ChnIdx++)
        {
            ret = MI_AI_DisableChn(MI_DEFAULT_AI_DEV_ID, u32ChnIdx);
            if(MI_SUCCESS != ret)
            {
                printf("disable ai dev %d chn %d err:0x%x\n", 0, 0, ret);
                return;
            }
        }
        ret = MI_AI_Disable(MI_DEFAULT_AI_DEV_ID);
        if (MI_SUCCESS != ret)
        {
            printf("disable ai dev %d err:0x%x\n", 0, ret);
            return;
        }

        sem_destroy(&sem_aiReady);
        printf("mi_audio_in_deinit ok...\n");
    }
}
#endif



int Calculate_amplitude(int16_t* pcmData, int sampleCnt)
{
    if (sampleCnt > 0){
        int sum = 0;
        for (int i = 0; i < sampleCnt; i++){
            sum += abs(pcmData[i]);
        }
        sum/=sampleCnt;
        return sum;
   }
   else
        return 0;
}



//音量表中数据表示的是音频通路数字部分的Gain值
//4095表示0dB,为0时表示Mute。音量可调整增益表中只做负增益
//需要正增益设置每个source源的预增益
//两级音量之间的计算公式为 "20*log(Vol1/Vol2)"，单位dB
const uint16_t mSysVol[100 + 1] =
{
	/* 0-100级音量控制, 0.25dB等级 */
	0,
	5, 10, 18, 27, 38, 50, 63, 79, 94, 112, /*-31.25dB*/
	126, 141, 135, 178, 199, 217, 237, 258, 282, 307, /*-22.5dB*/
	325, 345, 365, 387, 410, 434, 460, 487, 516, 546, /*-17.5dB*/
	562, 579, 595, 613, 631, 649, 668, 688, 708, 728, /*-15dB*/
	750, 772, 794, 817, 841, 866, 891, 917, 944, 971, /*-12.5db*/
	1000, 1029, 1059, 1090, 1122, 1154, 1188, 1223, 1259, 1295, /*-10db*/
	1333, 1372, 1412, 1453, 1496, 1539, 1584, 1631, 1678, 1727, /*-7.5dB*/
	1778, 1830, 1883, 1938, 1995, 2053, 2113, 2175, 2238, 2303, /*-5dB*/
	2371, 2440, 2511, 2584, 2660, 2738, 2817, 2900, 2984, 3072, /*-2.5dB*/
	3161, 3254, 3349, 3446, 3547, 3651, 3757, 3867, 3980, 4095/*0db*/
//	/*32级音量控制*/
//	0/*-72db*/,
//	3/*-56db*/,		6/*-56db*/,		15/*-49db*/,	26/*-44db*/,	41/*-40db*/,	65/*-36db*/,	103/*-32db*/,	145/*-29db*/,
//	205/*-26db*/,	258/*-24db*/,	325/*-22db*/,	410/*-20db*/,	460/*-19db*/,	516/*-18db*/,	576/*-17db*/,	649/*-16db*/,
//	728/*-15db*/,	817/*-14db*/,	917/*-13db*/,	1029/*-12db*/,	1154/*-11db*/,	1295/*-10db*/,	1453/*-9db*/,	1631/*-8db*/,
//	1830/*-7db*/,	2053/*-6db*/,	2303/*-5db*/,	2584/*-4db*/,	2900/*-3db*/,	3254/*-2db*/,	3651/*-1db*/,	4095/*0db*/
};


int16_t  buffer_stereo[16384]={0};

void mi_audio_write_callRing(const unsigned char *buffer, int count)
{
    if(!mi_ao_init_flag)
    {
        return;
    }
    if(count == 0)
        return;
    //printf("mi_audio_write:%d...\n",count);
    MI_AUDIO_Frame_t stAoSendFrame;
    memset(&stAoSendFrame, 0x0, sizeof(MI_AUDIO_Frame_t));
    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    int i=0;
    
    int16_t *buffer_pcm_16;
    int buffer_len;
    int SamplesPreFrame=0;

    if(mi_ao_channel_num == 2)//立体声
    {
        buffer_len=count;
        SamplesPreFrame=buffer_len/2/2;
        memcpy(buffer_stereo,buffer,buffer_len);
        buffer_pcm_16=buffer_stereo;
    }
    else
    {
        buffer_len=count*2;
        SamplesPreFrame=buffer_len/2/2;
        buffer_pcm_16=(int16_t *)buffer;
        //单声道转双声道
        for(i=0;i<SamplesPreFrame;i++)
        {
            buffer_stereo[2 * i + 0] =  buffer_pcm_16[i];
            buffer_stereo[2 * i + 1] =  buffer_pcm_16[i];
        }
        buffer_pcm_16=buffer_stereo;
    }

    //根据铃声音量变化
    int t_sysvol=mSysVol[g_ring_vol];
    if(t_sysvol)
    {
        //降低10dB
        t_sysvol = t_sysvol/3.2;
    }

    for(i=0; i<SamplesPreFrame; i++)
    {
        buffer_pcm_16[2 * i + 0] = limit_value_16bit( (((((int32_t)buffer_pcm_16[2 * i + 0]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L] + 2048) >> 12 );
        buffer_pcm_16[2 * i + 1] = limit_value_16bit( (((((int32_t)buffer_pcm_16[2 * i + 1]) * 4096 + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R] + 2048) >> 12 );
    }
    
    //#if AUDIO_PHASE_INVERT
    PhaseInvert(buffer_pcm_16,buffer_len/2,PHASE_INVERT_STERO);
    //#endif

    #if USE_SSD212
    stAoSendFrame.u32Len[0] = buffer_len;
    #elif USE_SSD202
    stAoSendFrame.u32Len = buffer_len;
    #endif
    stAoSendFrame.apVirAddr[0] = buffer_pcm_16;
    stAoSendFrame.apVirAddr[1] = NULL;

    pthread_mutex_lock(&mutex_audio_out);
    if(mi_ao_init_flag && mi_ao_sampleRate==16000)
    {
        int s32Ret = MI_SUCCESS;
        do
        {
            s32Ret = MI_AO_SendFrame(AoDevId, 0, &stAoSendFrame, -1);
        }
        while (s32Ret == MI_AO_ERR_NOBUF);
    }
    pthread_mutex_unlock(&mutex_audio_out);

    //printf("mi_audio_write ok...\n");
}




void* mi_audio_local_write_thread()
{
    MI_S32 ret;

#if USE_SSD212
    MyAedStruct aedStruct;
    aedStruct=mi_audio_alg_aed_init(32000,1);

    MyAnrStruct anrStruct_mic1,anrStruct_mic2;
    anrStruct_mic1=mi_audio_alg_anr_init(16000,1);
    anrStruct_mic2=mi_audio_alg_anr_init(16000,1);

    MySrcStruct srcStruct_mic1_32k_to_16k,srcStruct_mic1_16k_to_32k;
    srcStruct_mic1_32k_to_16k=mi_audio_alg_src_init(SRATE_32K,SRC_32k_to_16k,1,MI_AI_SAMPLE_PER_FRAME_PAGING);
    srcStruct_mic1_16k_to_32k=mi_audio_alg_src_init(SRATE_16K,SRC_16k_to_32k,1,MI_AI_SAMPLE_PER_FRAME_PAGING/2);

    MySrcStruct srcStruct_mic2_32k_to_16k,srcStruct_mic2_16k_to_32k;
    srcStruct_mic2_32k_to_16k=mi_audio_alg_src_init(SRATE_32K,SRC_32k_to_16k,1,MI_AI_SAMPLE_PER_FRAME_PAGING);
    srcStruct_mic2_16k_to_32k=mi_audio_alg_src_init(SRATE_16K,SRC_16k_to_32k,1,MI_AI_SAMPLE_PER_FRAME_PAGING/2);
#elif USE_SSD202
    SpeexPreprocessState *speexPreState_LocalMic = speex_preprocess_state_init(MI_AI_SAMPLE_PER_FRAME_PAGING, 32000);
    int IsEnableDeNoise=1;//1表示开启，0表示关闭
    //SPEEX_PREPROCESS_SET_DENOISE表示降噪
    speex_preprocess_ctl(speexPreState_LocalMic, SPEEX_PREPROCESS_SET_DENOISE, &IsEnableDeNoise);//降噪
    int noiseSuppress = -45;//噪音分贝数，是一个负值
    //Speex的降噪是通过简单的设置音频数据的阀值，过滤掉低分贝的声音来实现的
    //优点是简单，使用Speex编解码库时可以直接使用
    //缺点是会把声音细节抹掉
    speex_preprocess_ctl(speexPreState_LocalMic, SPEEX_PREPROCESS_SET_NOISE_SUPPRESS, &noiseSuppress);
    printf("init speex ok.\n");
#endif



    //printf("mi_audio_write:%d...\n",count);
    MI_AUDIO_Frame_t stAoSendFrame;
    memset(&stAoSendFrame, 0x0, sizeof(MI_AUDIO_Frame_t));
    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    int i=0;
    
    int16_t *adc0_ori_pcm_16,*adc1_ori_pcm_16,*adc2_ori_pcm_16;

    int16_t  adc0_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  adc1_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  adc2_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};

    int16_t  adc1_src_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  adc2_src_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};

    int16_t mono_mix[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t mono_netSend_mix[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  mix_progcess_stero[MI_AI_SAMPLE_PER_FRAME_LOCAL*2]={0};
    
    int buffer_len;
    int SamplesPreFrame=0;

    buffer_len=MI_AI_SAMPLE_PER_FRAME_PAGING*2*2;
    SamplesPreFrame=buffer_len/2/2;

    int is_local_source=0;
    int local_source_cnt=0;

    int thread_delay_us=2000;    //2ms
    double perSamples_delay_ms = (double)MI_AI_SAMPLE_PER_FRAME_PAGING / 32000 *1000;
    int enter_local_source_threshold=1000/perSamples_delay_ms;
    int signal_db_print_threshold=500/perSamples_delay_ms;
    int signal_timeout_threshold=AUX_INVLID_TIMEOUT*1000/perSamples_delay_ms;

    int signal_paging_mic_timeOutSamplesCnt=0;
    int signal_paging_mic_check_threshold=15*60*1000/perSamples_delay_ms;    //每隔15分钟开始检测一次信号,无信号时强制休息1秒钟
    int signal_paging_mic_sleep_threshold=1000/perSamples_delay_ms;          //时间到了，休息1秒
    int signal_paging_mic_sleep_cnt=0;


    printf("perSamples_delay_ms=%f,signal_db_print_threshold=%d,signal_timeout_threshold=%d\n",perSamples_delay_ms,signal_db_print_threshold,signal_timeout_threshold);

    //需要独立检测判断阈值
    int adc_threshold_array[3]={AUX_VALID_THRESHOLD_DB,MIC_FRONT_VALID_THRESHOLD_DB,MIC_FRONT_VALID_THRESHOLD_DB};
    int adc_valid_array[3]={0};
    int adc_timeoutCnt_array[3]={0};

    while(mi_ai_init_flag)
    {
        sem_wait(&sem_aiReady);
        if(!mi_ai_init_flag)
            break;

        int sysSource = get_system_source();
        if( sysSource == SOURCE_NULL || sysSource == SOURCE_NET_PAGING )
        {
            
        }
        else
        {
            
        }

        if(st_AdcData_Info.adc_data_valid[st_AdcData_Info.read_pos])
        {
            adc0_ori_pcm_16=(int16_t *)st_AdcData_Info.adc1_data[st_AdcData_Info.read_pos]; //LINE1
            adc1_ori_pcm_16=(int16_t *)st_AdcData_Info.adc2_data[st_AdcData_Info.read_pos]; //MIC1
            adc2_ori_pcm_16=(int16_t *)st_AdcData_Info.adc3_data[st_AdcData_Info.read_pos]; //MIC2(SSD202无)

            int16_t *adc_ori_buf_array[3]={adc0_ori_pcm_16,adc1_ori_pcm_16,adc2_ori_pcm_16};
            int16_t *adc_pro_buf_array[3]={adc0_progcess_mono,adc1_progcess_mono,adc2_progcess_mono};

            int db_val=-96;
            if(adc_signal_can_detect)
            //if(1)
            {
                for(int i=0;i<3;i++)
                {
                    switch(i)
                    {
                        case 0:
                            if(!dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L])
                            {
                                continue;
                            }
                        break;
                        case 1:
                            if(!dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_R])
                            {
                                continue;
                            }
                        break;
                        case 2:
                            if(!dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_L])
                            {
                                continue;
                            }
                        break;
                    }
                    #if USE_SSD212
                    IaaAed_RunLsd(aedStruct.aedHandle,*(adc_ori_buf_array+i), &db_val);
                    #elif USE_SSD202
                    get_signal_db(*(adc_ori_buf_array+i),SamplesPreFrame,&db_val);
                    #endif
                    if(i==0 || i==1)
                    {
                        //printf("ADC%d:%d dB\n",i,db_val);
                    }
                    if(db_val>adc_threshold_array[i])
                    {
                        if(!adc_valid_array[i])
                        {
                            printf("ADC%d is valid:%d dB\n",i,db_val);
                            adc_valid_array[i]=1;
                        }
                        adc_timeoutCnt_array[i]=0;
                    }
                    else
                    {
                        if(adc_valid_array[i])
                        {
                            adc_timeoutCnt_array[i]++;
                            if(adc_timeoutCnt_array[i] == signal_timeout_threshold)
                            {
                                printf("ADC%d is invaild:%d dB\n",i,db_val);
                                adc_valid_array[i]=0;
                            }
                        }
                    }
                }
            }

            //三路ADC通道信号均超时，认为ADC无信号
            if(!adc_valid_array[0] && !adc_valid_array[1] && !adc_valid_array[2])
            {
                adc_signal_valid=0;
            }
            else
            {
                adc_signal_valid=1;
            }

            //量化各路ADC音量
            for(i=0;i<3;i++)
            {
                #if 1
                if(!adc_valid_array[i])
                {
                    memset(*(adc_pro_buf_array+i),0,MI_AI_SAMPLE_PER_FRAME_PAGING*2);
                }
                else
                #endif
                {
                    int module_gain = 0;
                    if(i == 0)
                    {
                        module_gain=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L];
                    }
                    else if(i == 1)
                    {
                        module_gain=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R];
                    }
                    else if(i == 2)
                    {
                        module_gain=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L];
                    }

                    int t_adcVol=mSysVol[100];
                    if(i == 0)
                        t_adcVol=mSysVol[adc_aux_vol];
                    else if(i == 1)
                        t_adcVol=mSysVol[adc_mic1_vol];
                    else if(i == 2)
                        t_adcVol=mSysVol[adc_mic2_vol];
                    
                    for(int k=0; k<SamplesPreFrame; k++)
                    {
                        //先设定基础增益
                        *(*(adc_pro_buf_array+i)+k) = limit_value_16bit( (((int32_t)(*(*(adc_ori_buf_array+i)+k))) * module_gain + 2048) >> 12 );
                        //如果是MIC1或者MIC2通道，需要根据系统设置内MIC增益设定值再次量化
                        if((i == 1 || i == 2) && g_mic_multiplier_val!=4096)
                        {
                            *(*(adc_pro_buf_array+i)+k) = limit_value_16bit( (((int32_t)(*(*(adc_pro_buf_array+i)+k))) * g_mic_multiplier_val + 2048) >> 12 );
                        }
                        //todo最后根据电位器设定的音量值设定增益；另外发送出去的数据还需要根据电位器总音量值设定
                        if(t_adcVol!=mSysVol[100])
                        {
                            *(*(adc_pro_buf_array+i)+k) = limit_value_16bit( (((int32_t)(*(*(adc_pro_buf_array+i)+k))) * t_adcVol + 2048) >> 12 );
                        }
                    }
                }
            }

#if USE_SSD212       
            if(adc_valid_array[1])
            {
                int output_size1,output_size2;
                //将32K转换为16K,以便进行ANR算法降噪
                IaaSrc_Run(srcStruct_mic1_32k_to_16k.srcHandle,adc1_progcess_mono, adc1_src_progcess_mono, &output_size1);
                //printf("output_size1=%d\n",output_size1);
                //ANR降噪
                IaaAnr_Run(anrStruct_mic1.anrHandle, adc1_src_progcess_mono);
                IaaAnr_Run(anrStruct_mic1.anrHandle, adc1_src_progcess_mono+128);
                //ANR音频处理后再将16K转换为32K
                IaaSrc_Run(srcStruct_mic1_16k_to_32k.srcHandle,adc1_src_progcess_mono, adc1_progcess_mono, &output_size2);
                //printf("output_size2=%d\n",output_size2);
            }
            if(adc_valid_array[2])
            {
                int output_size1,output_size2;
                //将32K转换为16K,以便进行ANR算法降噪
                IaaSrc_Run(srcStruct_mic2_32k_to_16k.srcHandle,adc2_progcess_mono, adc2_src_progcess_mono, &output_size1);
                //printf("output_size1=%d\n",output_size1);
                //ANR降噪
                IaaAnr_Run(anrStruct_mic2.anrHandle, adc2_src_progcess_mono);
                IaaAnr_Run(anrStruct_mic2.anrHandle, adc2_src_progcess_mono+128);
                //ANR音频处理后再将16K转换为32K
                IaaSrc_Run(srcStruct_mic2_16k_to_32k.srcHandle,adc2_src_progcess_mono, adc2_progcess_mono, &output_size2);
                //printf("output_size2=%d\n",output_size2);
            }
#elif USE_SSD202
    memset(adc2_progcess_mono,0,SamplesPreFrame*2);

    speex_preprocess_run(speexPreState_LocalMic, adc1_progcess_mono);
#endif

            if(g_AuxIn_enable)
            {
                if(g_Mic_enable)
                {
                    pcmAudioMix3(mono_mix,adc0_progcess_mono,adc1_progcess_mono,adc2_progcess_mono,SamplesPreFrame);
                    //单独处理MIC，再加3dB，发送到网络 5800为3dB
                    pcmAudioMix2(mono_netSend_mix,adc1_progcess_mono,adc2_progcess_mono,SamplesPreFrame);
                    for(i=0; i<SamplesPreFrame; i++)
                    {
                        mono_netSend_mix[i] = limit_value_16bit( (((((int32_t)mono_netSend_mix[i]) * 5800 + 2048) >> 12) * 4400 + 2048) >> 12 );
                    }
                    pcmAudioMix2(mono_netSend_mix,adc0_progcess_mono,mono_netSend_mix,SamplesPreFrame);
                }
                else
                {
                    memcpy(mono_mix,adc0_progcess_mono,SamplesPreFrame*2);
                    memcpy(mono_netSend_mix,adc0_progcess_mono,SamplesPreFrame*2);
                }
            }
            else
            {
                if(g_Mic_enable)
                {
                    pcmAudioMix2(mono_mix,adc1_progcess_mono,adc2_progcess_mono,SamplesPreFrame);
                    //单独处理MIC，再加3dB，发送到网络 5800为3dB
                    pcmAudioMix2(mono_netSend_mix,adc1_progcess_mono,adc2_progcess_mono,SamplesPreFrame);
                    for(i=0; i<SamplesPreFrame; i++)
                    {
                        mono_netSend_mix[i] = limit_value_16bit( ((((int32_t)mono_netSend_mix[i]) * 5800 + 2048) >> 12) );
                    }
                    pcmAudioMix2(mono_netSend_mix,adc0_progcess_mono,mono_netSend_mix,SamplesPreFrame);
                }
                else
                {
                    memset(mono_mix,0,SamplesPreFrame*2);
                    memset(mono_netSend_mix,0,SamplesPreFrame*2);
                }
            }

            if(mi_ao_init_flag)
            {
                //将单声道数据转换为立体声
                MonoToStereo(mono_mix,SamplesPreFrame,mix_progcess_stero);
                //line 500mv进入ES7210后实际进入只有400mV,从ES7210采集再播放出来，只有200mv
                //200mv降至144mV，3100（-2.4dB），MIC增加22dB(12倍)约190mv相近。（ES7210 MIC3、MIC4通道增加3dB）
                //需要升到300mv发送到网络上（此处数字信号以为200mv基准，不能以CPU实际输出的144mv作为基准），增加约1.072倍(0.6dB),4400
                for(i=0; i<SamplesPreFrame; i++)
                {
                    mono_netSend_mix[i] = limit_value_16bit( ((((int32_t)mono_netSend_mix[i]) * 5800 + 2048) >> 12) );
                }
                #if 1
                if(sysSource == SOURCE_NET_PAGING && g_paging_type == PAGING_TYPE_MIC)
                {
                    int canSend=1;
                    //连续15分钟检测一次，如果没信号，休息1秒钟
                    if(signal_paging_mic_timeOutSamplesCnt == signal_paging_mic_check_threshold)
                    {
                        if(signal_paging_mic_sleep_cnt<signal_paging_mic_sleep_threshold)   //如果还没休息够
                        {
                            if(!adc_signal_valid)   //没有信号，开始休息
                            {
                                signal_paging_mic_sleep_cnt++;
                                canSend=0;
                            }
                        }
                        else    //已经休息够了，让其下次重新开始计时
                        {
                            signal_paging_mic_sleep_cnt=0;
                            signal_paging_mic_timeOutSamplesCnt=0;
                        }
                    }
                    else
                    {
                        signal_paging_mic_timeOutSamplesCnt++;
                    }
                    
                    if(canSend)   //ADC没有数据时也要发送，不然终端会退出
                        send_paging_stream(mono_netSend_mix,SamplesPreFrame*2);
                }
                else
                {
                    signal_paging_mic_sleep_cnt=0;
                    signal_paging_mic_timeOutSamplesCnt=0;
                }
                #endif

                //监听音量
                int t_sysvol=mSysVol[(unsigned int)(g_listen_vol)];
                if(t_sysvol)    //如果非0，那么放大3dB，兼容8欧输出
                {
                    t_sysvol = t_sysvol*1.42;
                }

                //L接功放（20倍），功放最终需要输出3W/4欧，得到电压为3.5V，/20=175mv，/2=87.5mV,144mv->87.5mv减少约1.6倍(约4dB,2580)
                //如果是寻呼台之间对讲，收到的数据是300mv的，需要降至约150mv(经损耗到90mv，因为内阻大),需要/2；如果是和对讲终端间对讲，收到的数据是190mv，需要降
                //R接线路输出，144mV需要放到到250mV，增加1.74倍(约4.8dB,7100)
                //如果是寻呼台之间对讲，收到的数据是300mv的，通过内阻损耗后接近250mv，不需要处理；如果是和对讲终端间对讲，收到的数据是190mv，增大到300mv（通过内阻损耗后接近250mv）
                for(i=0; i<SamplesPreFrame; i++)
                {
                    mix_progcess_stero[2 * i + 0] = limit_value_16bit( (((((int32_t)mix_progcess_stero[2 * i + 0]) * t_sysvol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L] + 2048) >> 12 );
                    mix_progcess_stero[2 * i + 1] = limit_value_16bit( (((((int32_t)mix_progcess_stero[2 * i + 1]) * 4096 + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R] + 2048) >> 12 );
                }
                
                //#if AUDIO_PHASE_INVERT
                PhaseInvert(mix_progcess_stero,buffer_len/2,PHASE_INVERT_STERO);
                //#endif

                #if USE_SSD212
                stAoSendFrame.u32Len[0] = buffer_len;
                #elif USE_SSD202
                stAoSendFrame.u32Len = buffer_len;
                #endif
                stAoSendFrame.apVirAddr[0] = mix_progcess_stero;
                stAoSendFrame.apVirAddr[1] = NULL;


                pthread_mutex_lock(&mutex_audio_out);
                if(mi_ao_init_flag && mi_ao_sampleRate==32000 && adc_signal_valid)
                {
                    int s32Ret = MI_SUCCESS;
                    if(udiskInfo.playStatus != BP1048_MUSIC_STATUS_PLAY)
                    {
                        do
                        {
                            s32Ret = MI_AO_SendFrame(AoDevId, 0, &stAoSendFrame, -1);
                        }
                        while (s32Ret == MI_AO_ERR_NOBUF);
                    }
                }
                pthread_mutex_unlock(&mutex_audio_out);
            }

            #if 1
            st_AdcData_Info.adc_data_valid[st_AdcData_Info.read_pos] = 0;
            st_AdcData_Info.read_pos++;
            if(st_AdcData_Info.read_pos>=MAX_ADC_DATA_PKG_NUM)
            {
                st_AdcData_Info.read_pos=0;
            }
            #endif
        }
    }

#if USE_SSD212
    mi_audio_alg_aed_deinit(aedStruct);
    mi_audio_alg_anr_deinit(anrStruct_mic1);
    mi_audio_alg_anr_deinit(anrStruct_mic2);
    mi_audio_alg_src_deinit(srcStruct_mic1_32k_to_16k);
    mi_audio_alg_src_deinit(srcStruct_mic1_16k_to_32k);
    mi_audio_alg_src_deinit(srcStruct_mic2_32k_to_16k);
    mi_audio_alg_src_deinit(srcStruct_mic2_16k_to_32k);
#elif USE_SSD202
    speex_preprocess_state_destroy(speexPreState_LocalMic);
#endif
}








void* mi_audio_call_write_thread()
{
    MI_S32 ret;
	
    #if USE_SSD212
    MyAecStruct aecStruct;
    aecStruct=mi_audio_alg_aec_init(16000,1);

    MyAnrStruct anrStruct_mix_near;
    anrStruct_mix_near=mi_audio_alg_anr_init(16000,1);
    #elif USE_SSD202
    SpeexPreprocessState *speexPreState_LocalMic = speex_preprocess_state_init(MI_AI_SAMPLE_PER_FRAME_CALL, 16000);
    int IsEnableDeNoise=1;//1表示开启，0表示关闭
    //SPEEX_PREPROCESS_SET_DENOISE表示降噪
    speex_preprocess_ctl(speexPreState_LocalMic, SPEEX_PREPROCESS_SET_DENOISE, &IsEnableDeNoise);//降噪
    int noiseSuppress = -50;//噪音分贝数，是一个负值
    //Speex的降噪是通过简单的设置音频数据的阀值，过滤掉低分贝的声音来实现的
    //优点是简单，使用Speex编解码库时可以直接使用
    //缺点是会把声音细节抹掉
    speex_preprocess_ctl(speexPreState_LocalMic, SPEEX_PREPROCESS_SET_NOISE_SUPPRESS, &noiseSuppress);

    //回声消除
    SpeexEchoState *st;
    SpeexPreprocessState *den;
    st = speex_echo_state_init(MI_AI_SAMPLE_PER_FRAME_CALL, MI_AI_SAMPLE_PER_FRAME_CALL*4);
    den = speex_preprocess_state_init(MI_AI_SAMPLE_PER_FRAME_CALL, 16000);
    int sampleRate = 16000;
    speex_echo_ctl(st, SPEEX_ECHO_SET_SAMPLING_RATE, &sampleRate);
    speex_preprocess_ctl(den, SPEEX_PREPROCESS_SET_ECHO_STATE, st);
    
    #if 0
    speex_preprocess_ctl(den, SPEEX_PREPROCESS_SET_DENOISE, &IsEnableDeNoise);//降噪
    speex_preprocess_ctl(den, SPEEX_PREPROCESS_SET_NOISE_SUPPRESS, &noiseSuppress);
    #endif

    printf("init speex ok.\n");
    #endif

    #if SUPPORT_CODEC_G722
	uint8_t ibuf[G722_CALL_BUFFER_SIZE];
	int16_t obuf[G722_CALL_BUFFER_SIZE * 2];	//此处可确保还原成原始数据G722_CALL_BUFFER_SIZE*2*sizeof(int16_t)
	G722_ENC_CTX *g722_ectx =NULL;
	G722_DEC_CTX *g722_dctx =NULL;
	int srate = ~ G722_SAMPLE_RATE_8000;
	size_t oblen = sizeof(obuf);	//512bytes

	if(m_stPager_Info.self_audioCoding == DECODE_G722)
	{
		g722_ectx = g722_encoder_new(64000, srate);
		if (g722_ectx == NULL) {
			fprintf(stderr, "g722_encoder_new() failed\n");
			MyExit (1);
		}
		g722_dctx = g722_decoder_new(64000, srate);
		if (g722_dctx == NULL) {
			fprintf(stderr, "g722_decoder_new() failed\n");
			MyExit (1);
		}
	}
	#endif




    MI_AUDIO_Frame_t stAoSendFrame;
    memset(&stAoSendFrame, 0x0, sizeof(MI_AUDIO_Frame_t));
    MI_AUDIO_DEV AoDevId = MI_DEFAULT_AO_DEV_ID;
    int i=0;
    
    int16_t *adc0_ori_pcm_16,*adc1_ori_pcm_16,*adc2_ori_pcm_16;

    int16_t  adc0_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  adc1_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  adc2_progcess_mono[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  mono_mix_near[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  mono_far[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  mono_ring[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  mono_far_mix_ring[MI_AI_SAMPLE_PER_FRAME_LOCAL]={0};
    int16_t  mix_progcess_stero[MI_AI_SAMPLE_PER_FRAME_LOCAL*2]={0};
    
    int buffer_len=MI_AI_SAMPLE_PER_FRAME_CALL*2*2;
    int SamplesPreFrame=buffer_len/2/2; //MI_AI_SAMPLE_PER_FRAME_CALL

    pthread_mutex_lock(&call_data_mutex);
	memset(&rx_call_stream,0,sizeof(rx_call_stream));
	pthread_mutex_unlock(&call_data_mutex);
    int kkk=0;
    
    while(mi_ai_init_flag && m_stPager_Info.self_callStatus == CALL_STATUS_CONNECT)
    {
        sem_wait(&sem_aiReady);
        if(!mi_ai_init_flag || m_stPager_Info.self_callStatus != CALL_STATUS_CONNECT)
            break;

        int sysSource = get_system_source();
        if( sysSource != SOURCE_CALL)
        {
            break;
        }

        if(st_AdcData_Info.adc_data_valid[st_AdcData_Info.read_pos])
        {
            adc0_ori_pcm_16=(int16_t *)st_AdcData_Info.adc0_data[st_AdcData_Info.read_pos]; //对应近端回声
            adc1_ori_pcm_16=(int16_t *)st_AdcData_Info.adc2_data[st_AdcData_Info.read_pos]; //对应MIC1,暂对讲只取MIC1信号
            adc2_ori_pcm_16=(int16_t *)st_AdcData_Info.adc3_data[st_AdcData_Info.read_pos]; //对应MIC2

            int16_t *adc_ori_buf_array[3]={adc0_ori_pcm_16,adc1_ori_pcm_16,adc2_ori_pcm_16};
            int16_t *adc_pro_buf_array[3]={adc0_progcess_mono,adc1_progcess_mono,adc2_progcess_mono};

            //量化各路ADC音量
            for(i=0;i<3;i++)
            {
                int module_gain = 0;
                if(i == 0)
                {
                    module_gain=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L];
                }
                else if(i == 1)
                {
                    module_gain=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R];
                }
                else if(i == 2)
                {
                    module_gain=dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L];
                }

                int t_adcVol=mSysVol[100];
                if(i == 1)
                    t_adcVol=mSysVol[adc_mic1_vol];
                else if(i == 2)
                    t_adcVol=mSysVol[adc_mic2_vol];
                
                for(int k=0; k<SamplesPreFrame; k++)
                {
                    //先设定基础增益
                    *(*(adc_pro_buf_array+i)+k) = limit_value_16bit( (((int32_t)(*(*(adc_ori_buf_array+i)+k))) * module_gain + 2048) >> 12 );
                    //如果是MIC1或者MIC2通道，需要根据系统设置内MIC增益设定值再次量化
                    if((i == 1 || i == 2) && g_mic_multiplier_val!=4096)
                    {
                        *(*(adc_pro_buf_array+i)+k) = limit_value_16bit( (((int32_t)(*(*(adc_pro_buf_array+i)+k))) * g_mic_multiplier_val + 2048) >> 12 );
                    }
                    //todo最后根据电位器设定的音量值设定增益；另外发送出去的数据还需要根据电位器总音量值设定
                    if(t_adcVol!=mSysVol[100])
                    {
                        *(*(adc_pro_buf_array+i)+k) = limit_value_16bit( (((int32_t)(*(*(adc_pro_buf_array+i)+k))) * t_adcVol + 2048) >> 12 );
                    }
                }
            }

            #if USE_SSD202
            speex_preprocess_run(speexPreState_LocalMic, adc1_progcess_mono);
            #endif

            
            #if 1
                        //将mic1与mic2的数据合成作为近端音频
                        pcmAudioMix2(mono_mix_near,adc1_progcess_mono,adc2_progcess_mono,SamplesPreFrame);
            #else       //暂不叠加,只取MIC1
                        memcpy(mono_mix_near,adc1_progcess_mono,sizeof(mono_mix_near));
            #endif

            #if USE_SSD212
            //将mic1远端音频做aec处理
            //由于aec算法一次只能处理128采样点，但SamplesPreFrame是256，所以需要分两次完成
            ret = IaaAec_Run(aecStruct.aecHandle, mono_mix_near, adc0_progcess_mono);
            if(ret < 0)
            {
                printf("IaaAec_Run1 failed !\n");
            }
            ret = IaaAec_Run(aecStruct.aecHandle, mono_mix_near+128, adc0_progcess_mono+128);
            if(ret < 0)
            {
                printf("IaaAec_Run2 failed !\n");
            }

            IaaAnr_Run(anrStruct_mix_near.anrHandle, mono_mix_near);
            IaaAnr_Run(anrStruct_mix_near.anrHandle, mono_mix_near+128);

            //现在的mono_mix_near已经是回声消除后的数据,将其发送给服务器
            //memset(mono_mix_near,0,sizeof(mono_mix_near));
            #elif USE_SSD202
            // 先进行噪声抑制
            speex_preprocess_run(speexPreState_LocalMic, adc1_progcess_mono);
            // 再进行回声消除
            speex_echo_cancellation(st, adc1_progcess_mono, adc0_progcess_mono, mono_mix_near);
            // 将回声消除后的音频进行最终处理
            //speex_preprocess_run(den, mono_mix_near);
            #endif

            //需要升到300mv发送到网络上（此处数字信号以为200mv基准，不能以CPU实际输出的144mv作为基准），增加约1.5倍(3.5dB),6100
            for(i=0; i<SamplesPreFrame; i++)
            {
                mono_mix_near[i] = limit_value_16bit(((((int32_t)mono_mix_near[i]) * 6100 + 2048) >> 12) );
            }


            if(m_stPager_Info.self_audioCoding == DECODE_STANDARD_PCM)
            {
                Send_Call_Audio_Stream((unsigned char*)mono_mix_near,SamplesPreFrame*2);
            }
            else if(m_stPager_Info.self_audioCoding == DECODE_G722)
            {
                int encode_buf_len=g722_encode(g722_ectx, (short*)mono_mix_near, (oblen / sizeof(obuf[0])), ibuf);
			    memcpy(mono_mix_near,ibuf,encode_buf_len);	//128bytes
                Send_Call_Audio_Stream((unsigned char*)mono_mix_near,encode_buf_len);
            }

            //todo 获取远端数据，将单声道数据转换为立体声后输出
            if(mi_ao_init_flag)
            {
                int decode_buf_len = 0;
                int cnt=0;
                //printf("rx_call_write_pos=%d,valid=%d\n",rx_call_stream.rx_call_write_pos,rx_call_stream.rx_call_valid[rx_call_stream.rx_call_write_pos]);
                while(cnt<4 && rx_call_stream.rx_call_valid[rx_call_stream.rx_call_write_pos] )
                {
                    if(m_stPager_Info.self_audioCoding == DECODE_STANDARD_PCM)
                    {
                        decode_buf_len = rx_call_stream.rx_call_len[rx_call_stream.rx_call_write_pos];
                        memcpy(mono_far,rx_call_stream.rx_call_data[rx_call_stream.rx_call_write_pos],decode_buf_len);
                    }
                    else if(m_stPager_Info.self_audioCoding == DECODE_G722)
                    {
                        int outlen=g722_decode(g722_dctx, rx_call_stream.rx_call_data[rx_call_stream.rx_call_write_pos], rx_call_stream.rx_call_len[rx_call_stream.rx_call_write_pos], obuf);
                        decode_buf_len = outlen*sizeof(obuf[0]);
                        memcpy(mono_far,obuf,decode_buf_len);	//512bytes
                    }

                    //printf("rx_call_stream.rx_call_write_pos=%d,len=%d\n",rx_call_stream.rx_call_write_pos,decode_buf_len);


                    rx_call_stream.rx_call_valid[rx_call_stream.rx_call_write_pos]=0;
                    rx_call_stream.rx_call_write_pos++;
                    if(rx_call_stream.rx_call_write_pos >= RX_CALL_BUF_PKG_MAX)
                    {
                        rx_call_stream.rx_call_write_pos=0;
                    }
                    cnt++;

                    
                    //将单声道数据转换为立体声
                    MonoToStereo(mono_far,decode_buf_len/2,mix_progcess_stero);
                    
                    //如果是寻呼台之间对讲，收到的数据是300mv的，通过内阻损耗后接近250mv，不需要处理；如果是和对讲终端间对讲，收到的数据是190mv，增大到300mv（通过内阻损耗后接近250mv）
                    //20230408如果不是寻呼台之间对讲，放大3dB
                    if(m_stPager_Info.other_deviceModel != DEVICE_MODEL_PAGING_A && m_stPager_Info.other_deviceModel != DEVICE_MODEL_PAGING_B && m_stPager_Info.other_deviceModel != DEVICE_MODEL_PAGING_C)
                    {
                        for(i=0; i<SamplesPreFrame; i++)
                        {
                            mix_progcess_stero[2 * i + 0] = limit_value_16bit( ((((int32_t)mix_progcess_stero[2 * i + 0]) * 5800 + 2048) >> 12) );
                            mix_progcess_stero[2 * i + 1] = limit_value_16bit( ((((int32_t)mix_progcess_stero[2 * i + 1]) * 5800 + 2048) >> 12) );
                        }
                    }

                    //再设置对讲音量
                    int t_callVol=mSysVol[g_call_vol];
                    if(t_callVol)    //如果非0，那么放大3dB，兼容8欧输出，对讲本地需要再放大3dB，共6dB,约2倍，因为MIC原来本地输出就是偏小，+4dB时终端到达满功率，+10dB时本地才到达满功率
                    {
                        t_callVol = t_callVol*2;
                    }
                    for(i=0; i<SamplesPreFrame; i++)
                    {
                        mix_progcess_stero[2 * i + 0] = limit_value_16bit( (((((int32_t)mix_progcess_stero[2 * i + 0]) * t_callVol + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L] + 2048) >> 12 );
                        mix_progcess_stero[2 * i + 1] = limit_value_16bit( (((((int32_t)mix_progcess_stero[2 * i + 1]) * 4096 + 2048) >> 12) * dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R] + 2048) >> 12 );
                    }

                    //#if AUDIO_PHASE_INVERT
                    PhaseInvert(mix_progcess_stero,buffer_len/2,PHASE_INVERT_STERO);
                    //#endif

                    #if USE_SSD212
                    stAoSendFrame.u32Len[0] = buffer_len;
                    #elif USE_SSD202
                    stAoSendFrame.u32Len = buffer_len;
                    #endif
                    stAoSendFrame.apVirAddr[0] = mix_progcess_stero;
                    stAoSendFrame.apVirAddr[1] = NULL;

                    int s32Ret = MI_SUCCESS;
                    int k=0;
                    do
                    {
                        s32Ret = MI_AO_SendFrame(AoDevId, 0, &stAoSendFrame, -1);
                    }
                    while (s32Ret == MI_AO_ERR_NOBUF);
                }
            }

            #if 1
            st_AdcData_Info.adc_data_valid[st_AdcData_Info.read_pos] = 0;
            st_AdcData_Info.read_pos++;
            if(st_AdcData_Info.read_pos>=MAX_ADC_DATA_PKG_NUM)
            {
                st_AdcData_Info.read_pos=0;
            }
            #endif
        }
    }

#if USE_SSD212
   mi_audio_alg_aec_deinit(aecStruct);
   mi_audio_alg_anr_deinit(anrStruct_mix_near);
#elif USE_SSD202
    speex_preprocess_state_destroy(speexPreState_LocalMic);
    speex_echo_state_destroy(st);
    speex_preprocess_state_destroy(den);
#endif

    #if SUPPORT_CODEC_G722
	if(g722_ectx)
	{
		g722_encoder_destroy(g722_ectx);
	}
	if(g722_dctx)
	{
		g722_decoder_destroy(g722_dctx);
	}
	#endif

    if(Paging_status == CALLING_START)
	    Paging_status = PAGING_STOP;
}

#endif