/*
 * @Author: <PERSON><PERSON><PERSON> 
 * @Date: 2022-02-16 20:44:05 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-02-17 11:04:59
 */

#if defined(USE_SSD212) || defined(USE_SSD202)
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/mman.h>
#include "mi_gpio.h"
#include "sysconf.h"




#if USE_SSD202

#define GPIO_LCD_RESET_PIN 22       //mipi电源复位脚，低电平复位，高电平正常工作
#define GPIO_I2S_SDI_SELECT_PIN 54  //SDI选择，高电平时SD2有效，低电平时SD1有效(默认需要低电平)
#define GPIO_SIGNAL_OUTPUT_PIN 13  //运放信号脚 高电平有效
#define GPIO_AMP_OUTPUT_PIN 86      //功放待机脚(高电平时静音，低电平有效)

// 检查是否已经导出GPIO
int checkExported(unsigned char gpioNumber) {
    FILE* fp;
    char path[256];

    sprintf(path, "/sys/class/gpio/gpio%d/direction", gpioNumber);
    fp = fopen(path, "r");
    if (fp) {
        // 已经导出了
        fclose(fp);
        return 1;
    }
    // 尚未导出
    return 0;
}

// 导出GPIO
int exportGPIO(unsigned char gpioNumber) {
    FILE* fp;

    // 检查是否已经导出
    if (checkExported(gpioNumber)) {
        printf("GPIO %d 已经导出\n", gpioNumber);
        return 0;
    }

    fp = fopen("/sys/class/gpio/export", "w");
    if(fp == NULL) {
        printf("无法导出GPIO\n");
        return 1;
    }
    fprintf(fp, "%d", gpioNumber);
    fclose(fp);

    return 0;
}

// 写GPIO方向
int writeGPIODirection(unsigned char gpioNumber, bool isGpioOut) {
    FILE* fp;
    char path[256];

    sprintf(path, "/sys/class/gpio/gpio%d/direction", gpioNumber);
    fp = fopen(path, "w");
    if(fp == NULL) {
        printf("无法打开GPIO方向文件\n");
        return 1;
    }
    fprintf(fp, "%s", isGpioOut ? "out" : "in");
    fclose(fp);

    return 0;
}

// 读GPIO方向
int readGPIODirection(unsigned char gpioNumber, int *isGpioOut) {
    FILE* fp;
    char path[256];
    char dir[4];

    sprintf(path, "/sys/class/gpio/gpio%d/direction", gpioNumber);
    fp = fopen(path, "r");
    if(fp == NULL) {
        printf("无法打开GPIO方向文件\n");
        return 1;
    }
    fscanf(fp, "%s", dir);
    fclose(fp);

    if (strcmp(dir, "in") == 0) {
        *isGpioOut = 0;
    }
    else {
        *isGpioOut = 1;
    }

    return 0;
}

// 写GPIO
int writeGPIO(unsigned char gpioNumber, int value) {
    FILE* fp;
    char path[256];

    sprintf(path, "/sys/class/gpio/gpio%d/direction", gpioNumber);
    fp = fopen(path, "r");
    if(fp == NULL) {
        printf("无法打开GPIO方向文件\n");
        return 1;
    }

    char direction[6];
    fscanf(fp, "%s", direction);
    fclose(fp);

    if (strcmp(direction, "out") == 0) {
        sprintf(path, "/sys/class/gpio/gpio%d/value", gpioNumber);
        fp = fopen(path, "w");
        if(fp == NULL) {
            printf("无法打开GPIO电平文件\n");
            return 1;
        }
        fprintf(fp, "%d", value);
        fclose(fp);
    }
    else {
        sprintf(path, "/sys/class/gpio/gpio%d/direction", gpioNumber);
        fp = fopen(path, "w");
        if(fp == NULL) {
            printf("无法打开GPIO方向文件\n");
            return 1;
        }
        fprintf(fp, "%s", value ? "high" : "low");
        fclose(fp);
    }

    return 0;
}

// 读GPIO
int readGPIO(unsigned char gpioNumber, int* value) {
    FILE* fp;
    char path[256];

    sprintf(path, "/sys/class/gpio/gpio%d/direction", gpioNumber);
    fp = fopen(path, "r");
    if(fp == NULL) {
        printf("无法打开GPIO方向文件\n");
        return 1;
    }

    char direction[6];
    fscanf(fp, "%s", direction);
    fclose(fp);

    if (strcmp(direction, "in")) {
        writeGPIODirection(gpioNumber,0);
    }
    
    sprintf(path, "/sys/class/gpio/gpio%d/value", gpioNumber);
    fp = fopen(path, "r");
    if(fp == NULL) {
        printf("无法打开GPIO电平文件\n");
        return 1;
    }
    fscanf(fp, "%d", value);
    fclose(fp);
    

    return 0;
}



//mipi电源复位脚，低电平复位，高电平正常工作
void MIPI_POWER_SWITCH(int isOn)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    printf("MIPI_POWER_SWITCH:%d\n",isOn);

    writeGPIO(GPIO_LCD_RESET_PIN,isOn);
#if 0
    static int exportGpio23 = 0;
	
    if(!exportGpio23)
    {
        exportGpio23=1;
        pox_system("echo 23 > /sys/class/gpio/export");
        pox_system("echo out > /sys/class/gpio/gpio23/direction");
    }
    if(isOn)
    {
        pox_system("echo 1 > /sys/class/gpio/gpio23/value");
    }
    else
    {
        pox_system("echo 0 > /sys/class/gpio/gpio23/value");
    }
#endif


}

int MIPI_POWER_GetDirection()
{
    int gpioIsOut=0;
    readGPIODirection(GPIO_LCD_RESET_PIN,&gpioIsOut);
    printf("MIPI_POWER_GetDirection:%d\n",gpioIsOut);
    return gpioIsOut;
}


void I2S_SDI_SELECT_MODE(int isCalling)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    printf("I2S_SDI_SELECT_MODE:%d\n",isCalling);

    writeGPIO(GPIO_I2S_SDI_SELECT_PIN,isCalling);
}


#endif



typedef struct
{
    unsigned char *virt_addr;
    unsigned char *mmap_base;
    unsigned int mmap_length;
}MmapHandle;

static unsigned int const page_size_mask = 0xFFF;

MmapHandle* devMemMMap(unsigned int phys_addr, unsigned int length)
{
    int fd;
    unsigned int phys_offset = 0;

    fd = open("/dev/mem", O_RDWR|O_SYNC);
    if (fd == -1)
    {
        printf("open /dev/mem fail\n");
        return NULL;
    }

    MmapHandle *handle = malloc(sizeof(MmapHandle));
    //phys_offset =(phys_addr & (page_size_mask));
    //phys_addr &= ~(page_size_mask);
    handle->mmap_length = length + phys_offset;
    handle->mmap_base = mmap(NULL, handle->mmap_length, PROT_READ|PROT_WRITE, MAP_SHARED, fd, phys_addr);
    handle->virt_addr = handle->mmap_base + phys_offset;
    //printf("phys_addr: %#x\n", phys_addr);
    //printf("virt_addr: %p\n", handle->virt_addr);
    //printf("phys_offset: %#x\n", phys_offset);

    if (handle->mmap_base == MAP_FAILED)
    {
        printf("mmap fail\n");
        close(fd);
        free(handle);
        return NULL;
    }

    close(fd);
    return handle;
}

int devMemUmap(MmapHandle* handle)
{
    int ret = 0;

    ret = munmap(handle->mmap_base, handle->mmap_length);
    if(ret != 0)
    {
        printf("munmap fail\n");
        return ret;
    }
    free(handle);
    return ret;
}

//设置GPIO为输入
void Set_Gpio_Input(int offset)
{
    #if defined(USE_SSD202)
    return;
    #endif

    #ifdef USE_PC_SIMULATOR
    return;
    #endif
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, offset), GPIO_INPUT_VAL);
    devMemUmap(riu_base);
}

// 获取GPIO电平状态（GPIO输入时有效） 1为高电平,0为低电平
int Get_Gpio_Value(int offset)
{
        #if defined(USE_SSD202)
    return;
    #endif

    #ifdef USE_PC_SIMULATOR
    return;
    #endif
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    unsigned short content=REG_R_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, offset));
    int value = (content & 0x0001) ? 1 : 0;
    devMemUmap(riu_base);
    return value;
}


//功放信号脚 低电平有效
void Enable_Amp_Output(int IsEnable)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    printf("Enable_Amp_Output:%d\n",IsEnable);

#if USE_SSD212
    static int exportGpio71 = 0;
	
    if(!exportGpio71)
    {
        exportGpio71=1;
        pox_system("echo 71 > /sys/class/gpio/export");
        pox_system("echo out > /sys/class/gpio/gpio71/direction");
    }
    if(IsEnable)
    {
        pox_system("echo 0 > /sys/class/gpio/gpio71/value");
    }
    else
    {
        pox_system("echo 1 > /sys/class/gpio/gpio71/value");
    }
#elif USE_SSD202
    writeGPIO(GPIO_AMP_OUTPUT_PIN,IsEnable?0:1);
#endif
}

//运放信号脚 高电平有效
void Enable_Signal_Output(int IsEnable)
{
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    printf("Enable_Signal_Output:%d\n",IsEnable);

#if USE_SSD212
    static int exportGpio84 = 0;
	
    if(!exportGpio84)
    {
        exportGpio84=1;
        pox_system("echo 84 > /sys/class/gpio/export");
        pox_system("echo out > /sys/class/gpio/gpio84/direction");
    }
    if(IsEnable)
    {
        pox_system("echo 1 > /sys/class/gpio/gpio84/value");
    }
    else
    {
        pox_system("echo 0 > /sys/class/gpio/gpio84/value");
    }
#elif USE_SSD202
    writeGPIO(GPIO_SIGNAL_OUTPUT_PIN,IsEnable);
#endif
}


//GPIO输出网络连接状态
void GPIO_OutPut_Server_Connection(int outPut)
{

        #if defined(USE_SSD202)
    return;
    #endif

    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    printf("GPIO_OutPut_Server_Connection:%d\n",outPut);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(outPut)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_GPIO0), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_GPIO0), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}


void GPIO_OutPut_Pager_Led(int ledId,int outPut)
{

        #if defined(USE_SSD202)
    return;
    #endif

    #ifdef USE_PC_SIMULATOR
    return;
    #endif
    if(ledId!=1 && ledId!=2)
    {
        return;
    }
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    //printf("GPIO_OutPut_Call_Led:ledId=%d,output=%d\n",ledId,outPut);
    if(outPut)
    {
        if(ledId == 1)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D1), GPIO_HIGH_VAL);
            //加入发射灯
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_GPIO1), GPIO_HIGH_VAL);
        }
        else if(ledId == 2)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D0), GPIO_HIGH_VAL);
        }
    }
    else
    {
        if(ledId == 1)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D1), GPIO_LOW_VAL);
            //加入发射灯
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_GPIO1), GPIO_LOW_VAL);
        }
        else if(ledId == 2)
        {
            REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_SD_D0), GPIO_LOW_VAL);
        }
    }

    devMemUmap(riu_base);
}

void GPIO_OutPut_Pager_Mic_Led(int outPut)
{

        #if defined(USE_SSD202)
    return;
    #endif

    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    //printf("GPIO_OutPut_Call_Relay1:%d\n",outPut);
    if(outPut)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO6), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO6), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}

void GPIO_OutPut_Pager_Relay(int outPut)
{

        #if defined(USE_SSD202)
    return;
    #endif

    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    //printf("GPIO_OutPut_Call_Relay1:%d\n",outPut);
    if(outPut)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO3), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO3), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}



void GPIO_Set_Pager_Init()
{
    #if USE_SSD212
    //启动后将相关引脚设置为输入
    Set_Gpio_Input(PAD_GPIO2);  //Transmit_SWITCH INPUT
    Set_Gpio_Input(PAD_GPIO4);  //Trigger Signal INPUT
    Set_Gpio_Input(PAD_GPIO8);  //SYS RESET INPUT
    Set_Gpio_Input(PAD_KEY8);   //Key1
    Set_Gpio_Input(PAD_KEY9);  //Key2

    //初始化输出
    GPIO_set_signal_mutex(0);   //默认选择MIC2通道而不是回路信号
    GPIO_OutPut_Pager_Mic_Led(0);
    GPIO_OutPut_Pager_Led(1,0);
    GPIO_OutPut_Pager_Led(2,0);
    #elif USE_SSD202

    exportGPIO(GPIO_LCD_RESET_PIN);
    exportGPIO(GPIO_I2S_SDI_SELECT_PIN);
    exportGPIO(GPIO_SIGNAL_OUTPUT_PIN);
    exportGPIO(GPIO_AMP_OUTPUT_PIN);

    #endif
}

int GPIO_Get_Pager_Key_Value(int keyId)
{
        #if defined(USE_SSD202)
    return;
    #endif

    #ifdef USE_PC_SIMULATOR
    return;
    #endif
    if(keyId!=1 && keyId!=2)
    {
        return -1;
    }
    if(keyId == 1)
        return Get_Gpio_Value(PAD_KEY8);
    else if(keyId == 2)
        return Get_Gpio_Value(PAD_KEY9);
}


int GPIO_Get_Pager_Ext_Value()
{
        #if defined(USE_SSD202)
    return;
    #endif

    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    return Get_Gpio_Value(PAD_GPIO4);
}


int GPIO_Get_Pager_Tx_Switch_Value()
{
    #if defined(USE_SSD202)
    return;
    #endif

    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    return Get_Gpio_Value(PAD_GPIO2);
}




//GPIO控制4G模块开机键，低电平保持1.5秒+开机
void GPIO_OutPut_Module4G_POWER(int isOn)
{
        #if defined(USE_SSD202)
    return;
    #endif

    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    printf("GPIO_OutPut_Module4G_PWRKEY:%d\n",isOn);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(isOn)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO1), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO1), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}

//GPIO控制4G模块复位键，低电平复位
void GPIO_OutPut_Module4G_Reset(int isReset)
{
        #if defined(USE_SSD202)
    return;
    #endif

    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    printf("GPIO_OutPut_Module4G_Reset:%d\n",isReset);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(isReset)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO0), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO0), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}



//信号选择（低电平为选择MIC2(默认)，高电平为对讲时使用的回声参考信号）
void GPIO_set_signal_mutex(int IsSignalLoop)
{
        #if defined(USE_SSD202)
    return;
    #endif

    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    printf("GPIO_set_signal_mutex:%d\n",IsSignalLoop);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(IsSignalLoop)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO7), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO7), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}


//gpio设置触发模式（1:电平输入; 0:短路输入）
void GPIO_set_trigger_mode(int mode)
{
        #if defined(USE_SSD202)
    return;
    #endif
    
    #ifdef USE_PC_SIMULATOR
    return;
    #endif

    printf("GPIO_set_trigger_mode:%d\n",mode);
    /* RIU mapping*/
    MmapHandle *riu_base = devMemMMap(BASE_RIU_PA, 0x400000);
    if(mode)
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO5), GPIO_HIGH_VAL);
    }
    else
    {
        REG_W_WORD(GET_BANK_ADDR(riu_base->virt_addr, PADGPIO_BANK, PAD_GPIO5), GPIO_LOW_VAL);
    }

    devMemUmap(riu_base);
}



#endif