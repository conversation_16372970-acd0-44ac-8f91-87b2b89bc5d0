#include "sysconf.h"
#include "I2S.h"
#include "call_ring.h"
#include "call_process.h"
#if defined(USE_SSD212) || defined(USE_SSD202)
#include "mi_audio.h"
#endif


#if ENABLE_CALL_FUNCTION

int g_isCallRinging=0;	//对讲是否正在响铃

extern stRx_call_stream rx_call_stream;
//主动呼叫、主动挂断时应直接调用，不要用线程，加快响应
void* Call_Status_Check()
{
	int i;
	int pre_selfCallStatus=CALL_STATUS_FREE;
	int pre_desCallStatus=CALL_STATUS_FREE;
	int noResponeTimeount=0;
	int pre_call_stream_pos=0;
	int pre_call_stream_timeout=0;
	while(1)
	{
		pthread_mutex_lock(&PagerInfoMutex);
		//printf("gggg1,m_stPager_Info.self_callStatus=%d\n",m_stPager_Info.self_callStatus);
		//如果当前自己的对讲状态不是空闲（处于空闲状态）
		if( m_stPager_Info.self_callStatus != CALL_STATUS_FREE)
		{
			if(pre_selfCallStatus!=m_stPager_Info.self_callStatus)
				pre_selfCallStatus=m_stPager_Info.self_callStatus;

			if(m_stPager_Info.other_callStatus != pre_desCallStatus)
			{
				printf("new_desCallStatus=%d,old_desCallStatus=%d\n",m_stPager_Info.other_callStatus,pre_desCallStatus);
				noResponeTimeount=0;
				pre_desCallStatus=m_stPager_Info.other_callStatus;
				set_call_status_show(pre_desCallStatus,0);

				if(pre_desCallStatus == CALL_STATUS_FREE || pre_desCallStatus == CALL_STATUS_BUSY || pre_desCallStatus == CALL_STATUS_REJECT || \
					pre_desCallStatus == CALL_STATUS_CODECES_NOT_SUPPORT || pre_desCallStatus == CALL_STATUS_HANGUP)
				{
					//1s后关闭对讲窗口
					printf("ready close calling win1,pre_desCallStatus=%d...\n",pre_desCallStatus);
					pre_desCallStatus=CALL_STATUS_FREE;
					pre_selfCallStatus=CALL_STATUS_FREE;
					m_stPager_Info.self_callStatus = CALL_STATUS_FREE;
					close_calling_win_extern(0,1,1000);
				}
			}
			else if(pre_desCallStatus == CALL_STATUS_WAIT_CALLED_ANSWER)
			{
				if(noResponeTimeount++>40*10)	//40秒没有应答,结束通话
				{
					set_call_status_show(CALL_STATUS_WAIT_CALLED_ANSWER,0);
					//1s后关闭对讲窗口
					printf("ready close calling win2...\n");
					pre_desCallStatus=CALL_STATUS_FREE;
					pre_selfCallStatus=CALL_STATUS_FREE;
					noResponeTimeount=0;
					m_stPager_Info.self_callStatus = CALL_STATUS_FREE;
					close_calling_win_extern(0,1,1000);
				}
			}
			else if(pre_desCallStatus == CALL_STATUS_CONNECT)
			{
				//如果接收指针一直没变过，代表没有数据流
				if(pre_call_stream_pos != rx_call_stream.rx_call_read_pos )
				{
					pre_call_stream_timeout=0;
					pre_call_stream_pos=rx_call_stream.rx_call_read_pos;
				}
				else if(pre_call_stream_timeout++ > 5*10)	//连续5秒没有数据流关闭,结束通话
				{	
					pre_call_stream_timeout=0;
					//1s后关闭对讲窗口
					printf("ready close calling win timeout...\n");
					pre_desCallStatus=CALL_STATUS_FREE;
					pre_selfCallStatus=CALL_STATUS_FREE;
					noResponeTimeount=0;
					m_stPager_Info.self_callStatus = CALL_STATUS_FREE;
					close_calling_win_extern(0,1,1000);
				}
			}

			if(pre_desCallStatus != CALL_STATUS_CONNECT)
			{
				pre_call_stream_timeout=0;
			}
		}
		#if 1
		else
		{
			//printf("pre_selfCallStatus=CALL_STATUS_FREE,close...\n");
			pre_desCallStatus=CALL_STATUS_FREE;
			pre_selfCallStatus=CALL_STATUS_FREE;
			noResponeTimeount=0;
			//关闭对讲窗口
			//close_calling_win_extern(0,1,1000);
		}
		#endif

		pthread_mutex_unlock(&PagerInfoMutex);

		usleep(100000);
	}
}


int Call_Status_Check_Thread()
{
	if(!g_isSupportCall)
		return;
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Call_Status_Check, NULL);
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}



void *Call_Ring_Play()
{
	g_isCallRinging=1;
    printf("enter Call_Ring_Play...\n");
    int callingParty = m_stPager_Info.self_isCallingParty;
    unsigned char *callRingBuf = callingParty?_acCalling_ring:_acCalled_Incoming_ring;
    int callRing_bytes = callingParty?sizeof(_acCalling_ring):sizeof(_acCalled_Incoming_ring);

    if( m_stPager_Info.self_callStatus != CALL_STATUS_WAIT_CALLED_ANSWER )
    {
		g_isCallRinging=0;
		if(m_stPager_Info.self_callStatus == CALL_STATUS_CONNECT)
		{
			Amp_Switch(1);
		}
        pthread_exit(NULL);
    }

#if defined(USE_SSD212) || defined(USE_SSD202)
	//开启音频
	mi_audio_out_init(16000, 16, 1);
	//等待被叫接听后再打开audio_in
	//mi_audio_in_init(AI_MODE_CALL_16K,16000, 16, 1);
#else
    //打开dsp
    int ret=open_and_init_dsp(16000,16,1);
    if(ret!=SUCCEED)
    {
		g_isCallRinging=0;
    	pthread_exit(NULL);
    }
    
    //设置音源
    set_dsp_sampleRate(16000);	//16Khz
	//打开或关闭功放
	set_output_volume();
	set_dsp_source(BP1048_SOURCE_DECODE_PAGER_CALL);
#endif

	//20230222 播放铃声前直接关闭本地监听
	g_Enable_local_listening=0;
	UI_local_listen_button_refresh();
	Amp_Switch(1);


    int fileReadTotal=0;
    int fileReadOnce=1024*4;
    while( m_stPager_Info.self_callStatus == CALL_STATUS_WAIT_CALLED_ANSWER && Paging_status == PAGING_STOP)   //响铃中
    {
        if(fileReadTotal<callRing_bytes)
        {
            int readBytes=(callRing_bytes-fileReadTotal>=fileReadOnce)?fileReadOnce:callRing_bytes-fileReadTotal;
            #if defined(USE_SSD212) || defined(USE_SSD202)
			mi_audio_write_callRing(callRingBuf+fileReadTotal,readBytes);
			#else
			if(write_i2s_data(callRingBuf+fileReadTotal,readBytes) == ERROR)
				break;
			#endif
            fileReadTotal+=readBytes;
        }
        else
        {
            fileReadTotal=0;
        }
        usleep(10000);
    }
	g_isCallRinging=0;
    if(m_stPager_Info.self_callStatus != CALL_STATUS_CONNECT)
    {
        //关闭功放，todo 还要判断本地监听状态，或者进入对讲页面，直接关闭本地监听
		//20230222 播放铃声前直接关闭本地监听
        Amp_Switch(0);
		printf("ready close dsp2...\n");
		#if defined(USE_SSD212) || defined(USE_SSD202)
		mi_audio_out_init(32000, 16, 1);
		#else
		close_dsp(); //关闭dsp
		//退出对讲模式
		BP1048_Send_Enter_CALL(0,0,0);
        set_dsp_source(BP1048_SOURCE_DECODE_PAGER_MIC);
		#endif
    }
    printf("exit Call_Ring_Play...\n");
}


int Start_Call_Ring_Play_Thread()
{ 
	if(g_isCallRinging)
		return;
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Call_Ring_Play, NULL);
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}







#if USE_SSD202

/*以下内容为可视对讲*/
void* Video_Call_Status_Check()
{
	int i;
	int pre_selfVideoCallStatus=VIDEO_CALL_STATUS_FREE;
	int pre_desVideoCallStatus=VIDEO_CALL_STATUS_FREE;
	int noResponeTimeount=0;
	int pre_VideoCall_stream_pos=0;
	int pre_VideoCall_stream_timeout=0;
	while(1)
	{
		pthread_mutex_lock(&PagerInfoMutex);
		//printf("gggg1,m_stPager_Info.self_callStatus=%d\n",m_stPager_Info.self_callStatus);
		//如果当前自己的视频对讲状态不是空闲
		if( m_stPager_Info.self_callStatus == CALL_STATUS_CONNECT && m_stPager_Info.self_video_callStatus != VIDEO_CALL_STATUS_FREE)
		{
			if(pre_selfVideoCallStatus!=m_stPager_Info.self_video_callStatus)
			{
				pre_selfVideoCallStatus=m_stPager_Info.self_video_callStatus;
			}
			if(m_stPager_Info.other_video_callStatus != pre_desVideoCallStatus)
			{
				printf("new_desVideoCallStatus=%d,old_desVideoCallStatus=%d\n",m_stPager_Info.other_video_callStatus,pre_desVideoCallStatus);
				noResponeTimeount=0;
				pre_desVideoCallStatus=m_stPager_Info.other_video_callStatus;
				
				if(pre_desVideoCallStatus == VIDEO_CALL_STATUS_FREE)
				{
					//500ms后关闭视频窗口
					printf("ready close videoCall,pre_desVideoCallStatus=%d...\n",pre_desVideoCallStatus);
					pre_desVideoCallStatus=VIDEO_CALL_STATUS_FREE;
					pre_selfVideoCallStatus=VIDEO_CALL_STATUS_FREE;
					m_stPager_Info.self_video_callStatus = VIDEO_CALL_STATUS_FREE;
					close_videoCall_win_extern(0,1,100);
				}
			}
			else if(pre_desVideoCallStatus == VIDEO_CALL_STATUS_FREE ||\
					pre_desVideoCallStatus == VIDEO_CALL_STATUS_RTSP_SEND_PARM)
			{
				if(noResponeTimeount++>3*10)	//2秒没有应答,结束视频
				{
					//500ms后关闭视频窗口
					printf("ready close video win,pre_desVideoCallStatus=%d\n",pre_desVideoCallStatus);
					pre_desVideoCallStatus=VIDEO_CALL_STATUS_FREE;
					pre_selfVideoCallStatus=VIDEO_CALL_STATUS_FREE;
					noResponeTimeount=0;
					m_stPager_Info.self_video_callStatus = VIDEO_CALL_STATUS_FREE;
					close_videoCall_win_extern(0,1,100);
				}
			}
			else if(pre_desVideoCallStatus == VIDEO_CALL_STATUS_TRY_OPEN_RTSP)	//2秒没有打开RTSP，退出
			{
				if(noResponeTimeount++>3*10)	//2秒没有应答,结束视频
				{
					//500ms后关闭视频窗口
					printf("ready close video win2,pre_desVideoCallStatus=%d\n",pre_desVideoCallStatus);
					pre_desVideoCallStatus=VIDEO_CALL_STATUS_FREE;
					pre_selfVideoCallStatus=VIDEO_CALL_STATUS_FREE;
					noResponeTimeount=0;
					m_stPager_Info.self_video_callStatus = VIDEO_CALL_STATUS_FREE;
					close_videoCall_win_extern(0,1,100);
				}
			}
			#if 0
			else if(pre_desVideoCallStatus == VIDEO_CALL_STATUS_ALL_READY)
			{
				//如果接收指针一直没变过，代表没有数据流
				if(pre_VideoCall_stream_pos != rx_call_stream.rx_call_read_pos )
				{
					pre_VideoCall_stream_timeout=0;
					pre_VideoCall_stream_pos=rx_call_stream.rx_call_read_pos;
				}
				else if(pre_VideoCall_stream_timeout++ > 5*10)	//连续5秒没有视频流,关闭视频
				{	
					pre_VideoCall_stream_timeout=0;
					//500ms后关闭视频窗口
					printf("ready close video win timeout...\n");
					pre_desVideoCallStatus=VIDEO_CALL_STATUS_FREE;
					pre_selfVideoCallStatus=VIDEO_CALL_STATUS_FREE;
					noResponeTimeount=0;
					m_stPager_Info.self_video_callStatus = VIDEO_CALL_STATUS_FREE;
					close_videoCall_win_extern(0,1,100);
				}
			}
			#endif
		}
		else
		{
			//printf("pre_selfCallStatus=CALL_STATUS_FREE,close...\n");
			pre_VideoCall_stream_pos=0;
			pre_desVideoCallStatus=VIDEO_CALL_STATUS_FREE;
			pre_selfVideoCallStatus=VIDEO_CALL_STATUS_FREE;
			noResponeTimeount=0;
			//关闭视频对讲窗口
			//close_videoCall_win_extern(0,1,100);
		}

		pthread_mutex_unlock(&PagerInfoMutex);

		usleep(100000);
	}
}


int Video_Call_Status_Check_Thread()
{
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Video_Call_Status_Check, NULL);
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}


#endif




#endif