/***************************************************
*   Copyright (c) 2015 ，广州深宝音响
*   All rights reserved.
*
*   文件名称： SaveUserInfo.c
*   摘要：
*
*   当前版本： 0.0.1
*   作者： jms ，修改日期： 2015.12.30
*/

#include <stdio.h>
#include <stdlib.h>
#include <malloc.h>
#include <sys/types.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <unistd.h>
#include <string.h>

#include "SaveUserInfo.h"
#include "sysconf.h"




/*********************************************************************
 * @fn      Scan_User_Info
 *
 * @brief   浏览用户信息
 *
 * @param
 *
 * @return ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
int Scan_User_Info()
{
	FILE *fps;
	int i;
    fps = fopen(USERINFOFILE, "r");
    if(fps == NULL)
    {
    	perror("Scan_Save_User_Info open error,Create!!!\n");
    	Set_Save_User_Info(0,SUPER_USER_NAME,SUPER_USER_NAME,0x1,0);
		m_stUser_Info.CurrentUserIndex=GetUserIndexByUserName(SUPER_USER_NAME);
		#if (!IS_APP_HIDE_LOGIN_ACCOUNT)
		sprintf(m_stUser_Info.CurrentUserName,"%s",SUPER_USER_NAME);
		#endif
		Save_User_Info();
    	fps = fopen(USERINFOFILE, "r");
    	if(fps == NULL)
    	{
    		perror("Scan_Save_User_Info open error2,exit!!!\n");
    		return ERROR;
    	}

		return SUCCEED;
    }

    if(!fread(&m_stUser_Info, sizeof(USERINFO), 1, fps))
    {
    	perror("Scan_Save_User_Info fread error,Create!!!\n");
    	fclose(fps);

		memset(&m_stUser_Info,0,sizeof(m_stUser_Info));
		Set_Save_User_Info(0,SUPER_USER_NAME,SUPER_USER_NAME,0x1,0);
		m_stUser_Info.CurrentUserIndex=GetUserIndexByUserName(SUPER_USER_NAME);
		#if (!IS_APP_HIDE_LOGIN_ACCOUNT)
		sprintf(m_stUser_Info.CurrentUserName,"%s",SUPER_USER_NAME);
		#endif
		Save_User_Info();
    	
		return SUCCEED;
    }

	//读出分区MAC信息
	for(i=0;i<m_stUser_Info.TotalUser;i++)
	{
		if(m_stUser_Info.userInfo[i].ZoneCount>0)
		{
			m_stUser_Info.userInfo[i].ZoneMac=malloc(6*m_stUser_Info.userInfo[i].ZoneCount);
			fread(m_stUser_Info.userInfo[i].ZoneMac, 6*m_stUser_Info.userInfo[i].ZoneCount, 1, fps);
		}
		else
		{
			m_stUser_Info.userInfo[i].ZoneMac = NULL;
		}
	}

#if 0
    m_stUser_Info.TotalUser=0;
    for(i= 0; i< MAX_USER_NUM; i++)
    {
    	if(strlen(m_stUser_Info.userInfo[i].name) == 0)
    		continue;

    	if(Paging_status != PAGING_START) printf("Scan_Save_User_Info,name=%s,password=%s,authority=%d\n",
    			m_stUser_Info.userInfo[i].name,m_stUser_Info.userInfo[i].password,m_stUser_Info.userInfo[i].authority);
    	m_stUser_Info.TotalUser++;
    }
#endif

#if 1
	printf("m_stUser_Info.CurrentUserIndex=%d,CurrentUserName=%s\n",m_stUser_Info.CurrentUserIndex,m_stUser_Info.CurrentUserName);
	//m_stUser_Info.CurrentUserIndex=GetUserIndexByUserName(SUPER_USER_NAME);
	//sprintf(m_stUser_Info.CurrentUserName,"%s",SUPER_USER_NAME);
#endif
    return SUCCEED;
}




/*********************************************************************
 * @fn      Save_User_Info
 *
 * @brief   保存用户信息
 *
 * @param
 *
 * @return ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
int Save_User_Info()
{
	pthread_mutex_lock(&UserInfoMutex);
	FILE *fpd;
	fpd = fopen(USERINFOFILE, "w+");
    if(fpd == NULL)
    {
    	perror("Save_User_Info open error!!!\n");
		pthread_mutex_unlock(&UserInfoMutex);
        return FILE_ERROE;
    }
	int wfd = fileno(fpd);
    if(!fwrite(&m_stUser_Info, sizeof(USERINFO), 1, fpd))
    {
        perror("Save_User_Info fwrite error!!!\n");
		fflush(fpd);
		fsync(wfd);
        fclose(fpd);
		pthread_mutex_unlock(&UserInfoMutex);
        return ERROR;
    }
	//写入分区MAC信息
	int i=0;
	for(i=0;i<m_stUser_Info.TotalUser;i++)
	{
		if(m_stUser_Info.userInfo[i].ZoneCount>0)
		{
			fwrite(m_stUser_Info.userInfo[i].ZoneMac, 6*m_stUser_Info.userInfo[i].ZoneCount, 1, fpd);
		}
	}

	fflush(fpd);
	fsync(wfd);
    fclose(fpd);
	pthread_mutex_unlock(&UserInfoMutex);
    return SUCCEED;
}





/*********************************************************************
 * @fn      Set_Save_User_Info
 *
 * @brief   设置保存用户信息
 *
 * @param	unsigned char *name: 	用户名字
 * 			unsigned char *password:用户密码
 * 			unsigned char authority：		用户权限
 * 			unsigned char
 *
 * @return ERROR - 设置失败
 *			SUCCEED - 设置成功
 */
int Set_Save_User_Info(int id,unsigned char *name, unsigned char *password, unsigned char authority, unsigned char passwordRemember)
{
	int ret;
	memset(m_stUser_Info.userInfo[id].name, 0,sizeof(m_stUser_Info.userInfo[id].name));
	strcpy(m_stUser_Info.userInfo[id].name, name);
	memset(m_stUser_Info.userInfo[id].password, 0,sizeof(m_stUser_Info.userInfo[id].password));
	strcpy(m_stUser_Info.userInfo[id].password, password);
	m_stUser_Info.userInfo[id].id=id;
	m_stUser_Info.userInfo[id].authority=authority;
	m_stUser_Info.userInfo[id].passwordRemember=passwordRemember;

	if(id>=m_stUser_Info.TotalUser)
	{
		m_stUser_Info.TotalUser++;
	}

	ret=Save_User_Info();
	if(ret < 0)
	{
		if(Paging_status != PAGING_START) printf("Save_User_Info error!!!\n");
		return ERROR;
	}
	else
		if(Paging_status != PAGING_START) printf("Save_User_Info succed!\n");

	return SUCCEED;
}


int GetUserIndexByUserName(unsigned char *name)
{
	int i=0;
	for(i=0;i<m_stUser_Info.TotalUser;i++)
	{
		if(strcmp(m_stUser_Info.userInfo[i].name,name) == 0)
		{
			return i;
		}
	}
	return -1;
}




/*********************************************************************
 * @fn      Delete_User_Info
 *
 * @brief   删除指定用户
 *
 * @param   nID删除序号
 *
 * @return  失败-1 成功0
 */
int Delete_User_Info(int nID)
{
    int i;
	if((nID >= m_stUser_Info.TotalUser) || nID == 0)
	{
		if(Paging_status != PAGING_START) printf("Delete_User_Info del ID error!!!\n");
		return ERROR;
	}

    /*保存和重新刷新*/
    memset(&m_stUser_Info.userInfo[nID],0,sizeof(m_stUser_Info.userInfo[nID]));
    for(i=nID; i<m_stUser_Info.TotalUser; i++)
    {
    	memset(m_stUser_Info.userInfo[i].name, 0,sizeof(m_stUser_Info.userInfo[i].name));
		strcpy(m_stUser_Info.userInfo[i].name, m_stUser_Info.userInfo[i+1].name);
		memset(m_stUser_Info.userInfo[i].password, 0,sizeof(m_stUser_Info.userInfo[i].password));
		strcpy(m_stUser_Info.userInfo[i].password, m_stUser_Info.userInfo[i+1].password);
		m_stUser_Info.userInfo[i].id=i;
		printf("\nID=%d\n",m_stUser_Info.userInfo[i].id);
		m_stUser_Info.userInfo[i].authority=m_stUser_Info.userInfo[i+1].authority;
		m_stUser_Info.userInfo[i].passwordRemember=m_stUser_Info.userInfo[i+1].passwordRemember;
    	memset(&m_stUser_Info.userInfo[i+1],0,sizeof(m_stUser_Info.userInfo[i+1]));
    }

    /*用户数量减1*/
	m_stUser_Info.TotalUser--;
	if(nID<m_stUser_Info.CurrentUserIndex)
	{
		m_stUser_Info.CurrentUserIndex--;
	}
    Save_User_Info();
    return SUCCEED;
}



/*********************************************************************
 * @fn      Delete_AllUser
 *
 * @brief   删除所有用户
 *
 * @param   none
 *
 * @return  none
 */
void Delete_AllUser()
{
	char cmd[128]={0};
	sprintf(cmd,"rm %s",USERINFOFILE);
    pox_system(cmd);
}



/*********************************************************************
 * @fn      OS_Init_Save_User_M
 *
 * @brief   初始化用户保存信息模块
 *
 * @param
 *
 * @return  成功返回 - 	0
 * 			失败返回 -	-1
 */
int OS_Init_User_M()
{
	int ret;
	ret=Scan_User_Info();
	if(ret < 0)
	{
		if(Paging_status != PAGING_START) printf("OS_Init_Save_User_M Scan_Save_User_Info error!!!\n");
		return ERROR;
	}


	return SUCCEED;
}
