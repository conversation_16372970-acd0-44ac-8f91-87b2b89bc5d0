#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdlib.h>
#include <linux/soundcard.h>
#include <linux/watchdog.h>
#include <strings.h>
#include <pthread.h>
#include <signal.h>
#include <math.h>
#include "network_client.h"
#include "Paging_win.h"
#include "record_list_win.h"
#include "Auxdio_protocol.h"
#include <sys/time.h>
#include "I2S.h"

#if ENABLE_CALL_FUNCTION
#include "call_process.h"
#endif

#if SUPPORT_CODEC_G722
#include "g722/g722_encoder.h"
#include "g722/g722_decoder.h"
#endif

#if SUPPORT_CODEC_G722_1
#include "g7221/defs.h"
#include "g7221/count.h"

#define MAX_SAMPLE_RATE 32000
#define MAX_FRAMESIZE   (MAX_SAMPLE_RATE/50)
#define MEASURE_WMOPS   0
#define WMOPS           0

typedef struct 
{
    Word16  syntax;
    Word32  bit_rate;
    Word16  bandwidth;
    Word16  number_of_bits_per_frame;
    Word16  number_of_regions;
    Word16  frame_size;
    FILE    *fpin;
    FILE    *fp_bitstream;
} ENCODER_CONTROL;

#endif

int read_i2s_thread=0xff;		//没有启动

int g_send_decode_pcm_type=DECODE_STANDARD_PCM;	//编码算法类型


pthread_mutex_t i2sMutex=PTHREAD_MUTEX_INITIALIZER;

/********以下是wave格式文件的文件头格式说明******/
/*------------------------------------------------
|             RIFF WAVE Chunk                  |
|             ID = 'RIFF'                     |
|             RiffType = 'WAVE'                |
------------------------------------------------
|             Format Chunk                     |
|             ID = 'fmt '                      |
------------------------------------------------
|             Fact Chunk(optional)             |
|             ID = 'fact'                      |
------------------------------------------------
|             Data Chunk                       |
|             ID = 'data'                      |
------------------------------------------------*/
/**********以上是wave文件格式头格式说明***********/
/*wave 文件一共有四个Chunk组成，其中第三个Chunk可以省略，每个Chunk有标示（ID）,
大小（size,就是本Chunk的内容部分长度）,内容三部分组成*/
struct fhead
{
	/****RIFF WAVE CHUNK*/
	unsigned char a[4];//四个字节存放'R','I','F','F'
	long int b;        //整个文件的长度-8;每个Chunk的size字段，都是表示除了本Chunk的ID和SIZE字段外的长度;
	unsigned char c[4];//四个字节存放'W','A','V','E'
	/****RIFF WAVE CHUNK*/
	/****Format CHUNK*/
	unsigned char d[4];//四个字节存放'f','m','t',''
	long int e;       //16后没有附加消息，18后有附加消息；一般为16，其他格式转来的话为18
	short int f;       //编码方式，一般为0x0001;
	short int g;       //声道数目，1单声道，2双声道;
	long int h;        //采样频率;
	long int i;        //每秒所需字节数;
	short int j;       //每个采样需要多少字节，若声道是双，则两个一起考虑;
	short int k;       //即量化位数
	/****Format CHUNK*/
	/***Data Chunk**/
	unsigned char p[4];//四个字节存放'd','a','t','a'
	long int q;        //语音数据部分长度，不包括文件头的任何部分
}wavehead;//定义WAVE文件的文件头结构体

int g_fd_dsp=-1;




int open_and_init_dsp(int rate,int afmt,int channels) 
{
	close_dsp();
	pthread_mutex_lock(&i2sMutex);
	if ((g_fd_dsp = open("/dev/dsp", O_RDWR)) < 0) 
	{
		printf("open dsp error,delay 200ms!\n");
		usleep(200000);
		if ((g_fd_dsp = open("/dev/dsp", O_RDWR)) < 0) 
		{
			perror("Open audio device error");
			pthread_mutex_unlock(&i2sMutex);
			return ERROR;
		}
    }

	ioctl(g_fd_dsp, SNDCTL_DSP_SETFMT, &afmt);            /*support 16/24/32*/
    ioctl(g_fd_dsp, SNDCTL_DSP_SPEED, &rate);             /*default is 16000*/
	ioctl(g_fd_dsp, SNDCTL_DSP_CHANNELS, &channels);      /*channels = 1*/

	pthread_mutex_unlock(&i2sMutex);
	return SUCCEED;
}


void close_dsp()
{
	pthread_mutex_lock(&i2sMutex);
	if(g_fd_dsp>=0)
	{
		close(g_fd_dsp);
		g_fd_dsp=-1;
	}
	pthread_mutex_unlock(&i2sMutex);
}


int write_i2s_data(char *buf,int len)
{
	if(g_fd_dsp<0)
	{
		return ERROR;
	}
	write(g_fd_dsp,buf,len);
	return SUCCEED;
}


/*
 *samples 音频数据
 *numSamples   音频数据长度
 *factor	振幅的系数 比如1.2
 */
int PcmAdjustmentVolume(short *samples,int numSamples,float factor)
{
	int i;
    int tmpValue;
    if (0 == factor)
    {
        memset((void *)samples,0,numSamples*sizeof(short));
        return numSamples;
    }
	else if(1.0 == factor)
	{
		return numSamples;
	}
	
    for (i = 0; i<numSamples; i++)
    {
        tmpValue = samples[i] * factor; //这样运算出来仍然是整数
        if(tmpValue < -32768)
        {
            tmpValue = -32768;
        }
        else if (tmpValue > 32767)
        {
            tmpValue = 32767;
        }
        samples[i] = tmpValue;
    }
    return numSamples;
}


/*
 *samples 音频数据
 *numSamples   音频数据长度
 *factor	振幅的系数 比如1.2
 */
void PcmMixed(short *samples1,short *samples2,int numSamples,float factor1,float factor2)
{
	int i;
    int tmpValue;
	
    for (i = 0; i<numSamples; i++)
    {
        tmpValue = samples1[i]*factor1 + samples2[i]*factor2; //这样运算出来仍然是整数
        if(tmpValue < -32768)
        {
            tmpValue = -32768;
        }
        else if (tmpValue > 32767)
        {
            tmpValue = 32767;
        }
        samples1[i] = tmpValue;
    }
}


/****************************************************
 * @fn      I2S_READ
 *
 * @brief   I2S数据流读取
 *
 * @param
 *
 * @return	int 应答包长度
 */
void pt_i2s_read()
{
	printf("pt_i2s_read1 ... \n");
	//22.05K 16bit 单通道  1S数据量 22050*16*1/8 = 44100 bytes
	char RX_BUF[RSIZE]={0};		//44.1K数据量,23ms时延,1024数据量
	char Alarm_BUF[RSIZE]={0};		//ALARM buf
	char ENCODE_BUF[RSIZE]={0};	//G711编码缓冲区
	int ret;
	int i;
	int total_readBytes_pre_second=0;	//1s内总共读取的字节数
	int duration=0;				//寻呼持续时间	//单位(秒)
	int readBytes = 0;			//单次读取到的字节数
	int one_second_size=(RATE*FMT*CHANNELS)/8;	//录音1秒钟的大小
	int rest_cnt=0;				//休息计数
	int mic_quite_cnt=0;		//麦克风静音计数
	int second_count=0;			//寻呼持续时间(1小时循环计数)	//单位(秒)

	read_i2s_thread=1;

	#if SUPPORT_CODEC_G722
	uint8_t ibuf[G722_BUFFER_SIZE];
	int16_t obuf[G722_BUFFER_SIZE * 2];
	G722_ENC_CTX *g722_ectx =NULL;
	int srate=G722_SAMPLE_RATE_8000;
	int enc = 1;
	srate &= ~ G722_SAMPLE_RATE_8000;
	size_t oblen = sizeof(obuf) / 2;		//BUFFER_SIZE*2
	oblen *= 2;								//BUFFER_SIZE*4
	if(g_send_decode_pcm_type == DECODE_G722)
	{
		g722_ectx = g722_encoder_new(64000, srate);
		if (g722_ectx == NULL) {
			fprintf(stderr, "g722_encoder_new() failed\n");
			MyExit (1);
		}
	}
	#endif

	#if SUPPORT_CODEC_G722_1
	ENCODER_CONTROL control;

	Word16  *mlt_coefs=NULL;
	Word16  mag_shift;
	Word16  *out_words=NULL;
	Word16  *history=NULL;
	
	control.bandwidth = 14000;
	control.bit_rate = 48000;
	control.frame_size = 640;
	control.number_of_bits_per_frame = 960;
	control.number_of_regions = 28;

	Word16 number_of_16bit_words_per_frame = (Word16)(control.number_of_bits_per_frame/16);

	if( g_send_decode_pcm_type == DECODE_G722_1 )
	{
		mlt_coefs = (Word16*)malloc(MAX_FRAMESIZE*sizeof(Word16));
		out_words = (Word16*)malloc(MAX_BITS_PER_FRAME*sizeof(Word16)/16);
		history = (Word16*)malloc(MAX_FRAMESIZE*sizeof(Word16));

		for (i=0;i<control.frame_size;i++)
			history[i] = 0;
		#if MEASURE_WMOPS
		Init_WMOPS_counter ();
		#endif
	}

	#endif



	send_online_info();			//状态变化通知主机

	set_dsp_sampleRate(RATE);	//22.05khz

	#if (APP_TYPE != APP_JUSBE_MIXER)
	//自动AUX触发时需要关闭MIC输入
		#if SUPPORT_AUTO_TRIGGER
		if(g_paging_mode == 1)
		{
			set_dsp_source(BP1048_SOURCE_DECODE_PAGER_ONLY_LINE);
		}
		else
		{
			if(g_paging_type == PAGING_TYPE_MUSIC)
			{
				set_dsp_source(g_Enable_local_listening?BP1048_SOURCE_DECODE_PAGER_MUSIC_LISTENING:BP1048_SOURCE_DECODE_PAGER_MUSIC);
			}
			else
			{
				set_dsp_source(g_Enable_local_listening?BP1048_SOURCE_DECODE_PAGER_MIC_LISTENING:BP1048_SOURCE_DECODE_PAGER_MIC);
			}
		}
		#else
		{
			if(g_paging_type == PAGING_TYPE_MUSIC)
			{
				set_dsp_source(g_Enable_local_listening?BP1048_SOURCE_DECODE_PAGER_MUSIC_LISTENING:BP1048_SOURCE_DECODE_PAGER_MUSIC);
			}
			else
			{
				set_dsp_source(g_Enable_local_listening?BP1048_SOURCE_DECODE_PAGER_MIC_LISTENING:BP1048_SOURCE_DECODE_PAGER_MIC);
			}
		}
		#endif
	#endif

	ret=open_and_init_dsp(RATE,FMT,CHANNELS);
	if(ret!=SUCCEED)
	{
		printf("OPEN DSP ERROR,EXIT!\n");
	   goto error;
	}

	RelayCtrl(1);

	//点亮寻呼图标和MIC指示灯
	paing_status_show(1);
	//手动寻呼才点亮MIC灯+显示MIC开关图标
	#if SUPPORT_AUTO_TRIGGER
	if(g_paging_mode == 0)
	#else
	{
		MIC_Led_Show(1);
		UI_mic_switch_show(1);
	}
	#endif

	printf("\nPaging_status:START,decode_codecs=%d...\n",g_send_decode_pcm_type);

	unsigned long paging_bell_data_read=0;
	unsigned long paging_alarm_data_read=0;
	int current_paging_bell_selected=paging_bell_selected;
	int current_paging_alarm_press=paging_alarm_press_flag;
	int enter_paging_alarm_press=paging_alarm_press_flag;
	int zero_signal_bytes=0;
	int signal_timeOut_count=0;
	int alarm_mode=1;		//1纯警报模式	0-纯人声模式  
	int alarm_pager_duration=0;	//警报时人声有效持续时间
	int temp_paging_type=g_paging_type;
	while(Paging_status == PAGING_START && signal_timeOut_count<g_Signal_Timeout_array[g_Signal_Timeout_level-1])
	{
		if(temp_paging_type != g_paging_type)
		{
			//寻呼类型切换后(从歌曲切换到其他)，需要等待一会才能向I2S获取音频流，否则会有时延(因为BP1048停止/暂停歌曲需要消耗将近1秒的时间)
			if(temp_paging_type == PAGING_TYPE_MUSIC)
			{
				int delay_cnt=24;	//50ms*24=1200ms
				while(delay_cnt--)
				{
					if(Paging_status != PAGING_START)
						break;
					usleep(50000);
				}
			}
			temp_paging_type = g_paging_type;
			continue;
		}
		readBytes = read(g_fd_dsp, RX_BUF, sizeof(RX_BUF)); // 从声卡中读取PCM码流放入buf中
		if (readBytes < 0)
		{
			perror("read wrong number of bytes");
			usleep(10000);
			continue;
		}
		//printf("readBytes=%d\n",readBytes);
		//write(g_fd_dsp,RX_BUF,readBytes);



		total_readBytes_pre_second+=readBytes;

		if(total_readBytes_pre_second>=one_second_size)
		{
			total_readBytes_pre_second=0;
			duration++;
			second_count++;
			if(g_Signal_Timeout_level!=6)
			{
				signal_timeOut_count++;
				printf("signal_timeOut_count=%d\n",signal_timeOut_count);
			}
		}

#if 1
		int ndb = 0;
		short int value;
		int i;
		long v = 0;
		for(i=0; i<readBytes; i+=2)
		{
			memcpy(&value, RX_BUF+i, 2); //获取2个字节的大小（值）
			v += abs(value);
		}
		v = v/(readBytes/2);
		if(v != 0) {
			ndb = (int)(20.0*log10((double)v / 32767.0 ));
		}
		else {
			ndb = -96;
		}
		//printf("dB=%d\n",ndb);

		if(g_Signal_Timeout_level!=6)
		{
			if(ndb >-50)	//超过-50dB，为有效寻呼
			{
				signal_timeOut_count=0;
			}
		}
		else
		{
			signal_timeOut_count=0;
		}
		


		//每隔1小时休息200ms(确保在没有信号且寻呼时长>60min时才休息)
		if( second_count>=3600 && rest_cnt <8 )
		{
			if(ndb >-50)	//超过-50dB，为有效寻呼，暂停休息
			{
				mic_quite_cnt=0;
			}
			else
			{
				mic_quite_cnt++;
			}

			if(mic_quite_cnt >=4)	//连续4次（约100ms）无信号才休息一次
			{
				rest_cnt++;
				printf("Rest count=%d\n",rest_cnt);
				continue;
			}
		}
		else if( second_count>=3600 )		//已经休息了150ms
		{
			second_count=0;
			rest_cnt=0;
			mic_quite_cnt=0;
			printf("Working Continue...\n");
		}
#endif



		if(current_paging_alarm_press!=paging_alarm_press_flag)
		{
			current_paging_alarm_press=paging_alarm_press_flag;
			paging_alarm_data_read=0;
			alarm_mode=1;	//纯警报模式
		}

		const unsigned char *paging_bell_pcm=NULL;
		int paging_bell_length=0;
		const unsigned char *paging_alarm_pcm=NULL;
		int paging_alarm_length=0;
		#if( RATE == 22050)
		{
			paging_bell_pcm = paging_bell;
			paging_bell_length = sizeof(paging_bell);
			paging_alarm_pcm = _acalarm_R_01;
			paging_alarm_length = sizeof(_acalarm_R_01);
		}
		#elif( RATE == 32000)
		{
			paging_bell_pcm = paging_bell_32k;
			paging_bell_length = sizeof(paging_bell_32k);
			paging_alarm_pcm = alarm_R_32K_20s;
			paging_alarm_length = sizeof(alarm_R_32K_20s);
		}
		#else
		{
			paging_bell_pcm = paging_bell;
			paging_bell_length = sizeof(paging_bell);
			paging_alarm_pcm = _acalarm_R_01;
			paging_alarm_length = sizeof(_acalarm_R_01);
		}
		#endif

		if(!enter_paging_alarm_press)		//消防警报紧急按键没有按下的时候才去播放钟声
		{
			#if SUPPORT_AUTO_TRIGGER
			if(current_paging_bell_selected && g_paging_mode == 0)
			#else
			if(current_paging_bell_selected)
			#endif
			{
				if(paging_bell_data_read<paging_bell_length)
				{
					//先传250ms空信号
					if(zero_signal_bytes<11264)
					{
						memset(RX_BUF,0,sizeof(RX_BUF));
						zero_signal_bytes+=RSIZE;
					}
					else
					{		
						memset(RX_BUF,0,sizeof(RX_BUF));
						readBytes=paging_bell_length-paging_bell_data_read>=RSIZE?RSIZE:paging_bell_length-paging_bell_data_read;
						memcpy(RX_BUF,paging_bell_pcm+paging_bell_data_read,readBytes);
						PcmAdjustmentVolume((short*)RX_BUF,RSIZE/2,0.1);
						paging_bell_data_read+=readBytes;
					}
					
					readBytes=RSIZE;	//统一整数倍
				}
				else
				{
					/* code */
				}
			}
		}

		if(current_paging_alarm_press)		//已经按下，循环播放消防信号
		{
			if(paging_alarm_data_read<paging_alarm_length)
			{
				memset(Alarm_BUF,0,sizeof(Alarm_BUF));
				readBytes=paging_alarm_length-paging_alarm_data_read>=RSIZE?RSIZE:paging_alarm_length-paging_alarm_data_read;
				memcpy(Alarm_BUF,paging_alarm_pcm+paging_alarm_data_read,readBytes);
				paging_alarm_data_read+=readBytes;
				PcmAdjustmentVolume((short*)Alarm_BUF,RSIZE/2,0.3);
				readBytes=RSIZE;	//统一整数倍
			}
			else
			{
				paging_alarm_data_read=0;	//循环
			}
		}

		//发送PCM数据
		if(g_send_decode_pcm_type==DECODE_STANDARD_PCM)
		{
			//如果消防警报已经按下,合成人声跟警报声
			if(current_paging_alarm_press)
			{
				#if 0
				if(ndb >= -35)	//瞬间进入人声模式	0.8,0.2
				{
					if(alarm_mode == 1)	//之前是警报模式
					{
						alarm_mode=0;		//人声模式
						alarm_pager_duration=duration;
					}
				}
				else if(ndb >= -45)	//大于等于-50dB，属于人声
				{
					alarm_pager_duration=0;
				}
				else	//小于-50
				{
					//5s内均低于-50dB代表没有人声，进入警报模式0,1
					if(duration-alarm_pager_duration>5)	//超过5s
					{
						alarm_mode=1;
					}
				}


				//根据人声大小动态合成音频
				if(alarm_mode == 0)			//人声模式
				{
					PcmMixed((short*)RX_BUF,(short*)Alarm_BUF,readBytes/2,0.8,0.2);
				}
				else if(alarm_mode == 1)	//警报模式
				{
					PcmMixed((short*)RX_BUF,(short*)Alarm_BUF,readBytes/2,0.2,0.8);
				}
				#endif
				PcmMixed((short*)RX_BUF,(short*)Alarm_BUF,readBytes/2,1,0.7);
			}
			
			SendToZone_PCM_Data_Cmd(RX_BUF,readBytes);
		}
		else if(g_send_decode_pcm_type==DECODE_G711)
		{
			//如果消防警报已经按下,合成人声跟警报声
			if(current_paging_alarm_press)
			{
				PcmMixed((short*)RX_BUF,(short*)Alarm_BUF,readBytes/2,1,0.7);
			}
			int encode_bytes=G711_encode(RX_BUF, ENCODE_BUF, sizeof(RX_BUF), 0);
			SendToZone_PCM_Data_Cmd(ENCODE_BUF,encode_bytes);
		}
		#if SUPPORT_CODEC_G722
		else if(g_send_decode_pcm_type==DECODE_G722)
		{
			//如果消防警报已经按下,合成人声跟警报声
			if(current_paging_alarm_press)
			{
				PcmMixed((short*)RX_BUF,(short*)Alarm_BUF,readBytes/2,1,0.7);
			}
			int encoder_bytes=g722_encode(g722_ectx, (short*)RX_BUF, (oblen / sizeof(obuf[0])), ibuf);
			//printf("encoder_bytes=%d,sizeof(ibuf)=%d\n",encoder_bytes,sizeof(ibuf));
			SendToZone_PCM_Data_Cmd(ibuf,sizeof(ibuf));
		}
		#endif

		#if SUPPORT_CODEC_G722_1
		else if(g_send_decode_pcm_type==DECODE_G722_1)
		{
			//如果消防警报已经按下,合成人声跟警报声
			if(current_paging_alarm_press)
			{
				PcmMixed((short*)RX_BUF,(short*)Alarm_BUF,readBytes/2,1,0.7);
			}

			#if MEASURE_WMOPS
        	Reset_WMOPS_counter ();
			#endif

			mag_shift = samples_to_rmlt_coefs((Word16*)RX_BUF, history, mlt_coefs, control.frame_size);

				/* Encode the mlt coefs */
			encoder(control.number_of_bits_per_frame,
					control.number_of_regions,
					mlt_coefs,
					mag_shift,
					out_words);
			//printf("encoder_bytes=%d,sizeof(ibuf)=%d\n",encoder_bytes,sizeof(ibuf));
			SendToZone_PCM_Data_Cmd((Word8*)out_words,2*number_of_16bit_words_per_frame);
		}
		#endif


#if 0
		unsigned long  j=0,k=0;
		k=GetTickCount();
		// /*****代码段**************/

		SendToZone_PCM_Data_Cmd(RX_BUF,readBytes);

		j=GetTickCount();
		double dfTim=((double)(j-k));
		if(Paging_status != PAGING_START) printf("\nTotal Time:%9fms\n",dfTim);
#endif
	}

	printf("\nPaging_status:i2s read END...\n");

#if SUPPORT_CODEC_G722
	if(g722_ectx)
	{
		g722_encoder_destroy(g722_ectx);
		g722_ectx=NULL;
	}
#endif

#if SUPPORT_CODEC_G722_1
	if(mlt_coefs)
	{
		free(mlt_coefs);
		mlt_coefs=NULL;
	}
	if(out_words)
	{
		free(out_words);
		out_words=NULL;
	}
	if(history)
	{
		free(history);
		history=NULL;
	}
	if(g_send_decode_pcm_type==DECODE_G722_1)
	{
		#if MEASURE_WMOPS
		WMOPS_output (0);
		#endif
	}
#endif

error:
printf("pt_i2s_read2 ... \n");
	close_dsp(); //关闭dsp

#if SUPPORT_AUTO_TRIGGER
	g_paging_mode=0;	//恢复成手动寻呼模式
#endif
    read_i2s_thread = 0xff;
    Paging_status = PAGING_STOP;
	RelayCtrl(0);
    paing_status_show(0);
    MIC_Led_Show(0);
	UI_mic_switch_show(0);
	if(g_network_mode == NETWORK_MODE_LAN)
	{
		SendToZone_Paging_Ready_Cmd_select_multicast(PCM_SEND_END,1);
	}
	//如果存在使用TCP模式的分区，发送停止
	SendToZone_Paging_Ready_Cmd_select(PCM_SEND_END,0);

	//退出寻呼后恢复监听所有音源
	#if (APP_TYPE != APP_JUSBE_MIXER)
	set_dsp_source(g_Enable_local_listening?BP1048_SOURCE_DECODE_PAGER_MIC_MUSIC_LISTENING:BP1048_SOURCE_DECODE_PAGER_MIC_MUSIC);
	#endif
	
	send_online_info();			//状态变化通知主机
	
   pthread_exit(NULL);
}



int i2s_read_thread()
{
	int ret;
	pthread_t i2s_thread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	if(read_i2s_thread != 0xff)
	{
		return 0;
	}
	ret = pthread_create(&i2s_thread, &Pthread_Attr, (void *)pt_i2s_read, NULL);
	if (ret == -1)
	{
		printf("Pthread Creat Failure!\n");
		return ERROR;
	}

	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;

}
















#if ENABLE_CALL_FUNCTION

int call_i2s_thread=0xff;		//没有启动

stRx_call_stream rx_call_stream;

pthread_mutex_t call_data_mutex=PTHREAD_MUTEX_INITIALIZER;

/****************************************************
 * @fn      pt_i2s_call
 *
 * @brief   I2S语音对讲
 *
 * @param
 *
 * @return	int 应答包长度
 */
void pt_i2s_call()
{
	//22.05K 16bit 单通道  1S数据量 16000*16*1/8 = 32000 bytes
	char RX_BUF[RSIZE_PAGER_CALL]={0};			//32K数据量,16ms时延,512数据量
	char ENCODE_BUF[RSIZE_PAGER_CALL]={0};		//编码缓冲区,编码后大小不可能超过原始PCM帧大小
	char DECODE_BUF[RSIZE_PAGER_CALL]={0};		//解码缓冲区,解码后大小不可能超过原始PCM帧大小
	int  encode_buf_len=0;					//编码缓冲区大小
	int  decode_buf_len=0;					//解码缓冲区大小
	int ret;
	int i;
	int total_readBytes_pre_second=0;	//1s内总共读取的字节数
	int duration=0;				//寻呼持续时间	//单位(秒)
	int readBytes = 0;			//单次读取到的字节数
	int one_second_size=(RATE_CALL*FMT_CALL*CHANNELS_CALL)/8;	//录音1秒钟的大小
	int rest_cnt=0;				//休息计数
	int mic_quite_cnt=0;		//麦克风静音计数
	int second_count=0;			//寻呼持续时间(1小时循环计数)	//单位(秒)

	call_i2s_thread=1;			//call_i2s_thread=1

	//时间戳置0
	m_stPager_Info.self_mediaTimeStamp = 0;

	int callfromRing=g_isCallRinging;
	Paging_status = CALLING_START;
	send_online_info();			//状态变化通知主机
	//这个时候可能正在响铃，需要等待
	if(callfromRing)
	{
		printf("callfromRing...\n");
		usleep(100000);
	}
	Amp_Switch(0);

	set_dsp_sampleRate(RATE_CALL);	//16Khz
	ret=open_and_init_dsp(RATE_CALL,FMT_CALL,CHANNELS_CALL);
	if(ret!=SUCCEED)
	{
		goto error;
	}

	RelayCtrl(1);

	//打开或关闭功放
	set_output_volume();
	set_dsp_source(BP1048_SOURCE_DECODE_PAGER_CALL);
	//20221018退出对讲模式再进入对讲模式,此处如果不进行这种操作，可能导致I2S IN数据为0,1048的问题，暂这样处理
	#if 1
	BP1048_Send_Enter_CALL(0,0,0);
	BP1048_Send_Enter_CALL(1,5,2);
	#endif
	usleep(50000);
	Amp_Switch(1);

	//点亮寻呼图标和MIC指示灯
	call_Btn_status_show(1);
	MIC_Led_Show(1);

	printf("pt_i2s_call START1...\n");

	pthread_mutex_lock(&call_data_mutex);
	memset(&rx_call_stream,0,sizeof(rx_call_stream));
	pthread_mutex_unlock(&call_data_mutex);

	int audioCodes = m_stPager_Info.self_audioCoding;


	#if SUPPORT_CODEC_G722
	uint8_t ibuf[G722_CALL_BUFFER_SIZE];
	int16_t obuf[G722_CALL_BUFFER_SIZE * 2];	//此处可确保还原成原始数据G722_CALL_BUFFER_SIZE*2*sizeof(int16_t)
	G722_ENC_CTX *g722_ectx =NULL;
	G722_DEC_CTX *g722_dctx =NULL;
	int srate = ~ G722_SAMPLE_RATE_8000;
	size_t oblen = sizeof(obuf);	//512bytes

	if(audioCodes == DECODE_G722)
	{
		g722_ectx = g722_encoder_new(64000, srate);
		if (g722_ectx == NULL) {
			fprintf(stderr, "g722_encoder_new() failed\n");
			MyExit (1);
		}
		g722_dctx = g722_decoder_new(64000, srate);
		if (g722_dctx == NULL) {
			fprintf(stderr, "g722_decoder_new() failed\n");
			MyExit (1);
		}
	}
	#endif
	printf("pt_i2s_call START2...\n");
	while(Paging_status == CALLING_START && m_stPager_Info.self_callStatus == CALL_STATUS_CONNECT)
	{
		readBytes = read(g_fd_dsp, RX_BUF, sizeof(RX_BUF)); // 从声卡中读取PCM码流放入buf中
		if (readBytes < 0)
		{
			perror("read wrong number of bytes");
			usleep(10000);
			continue;
		}


		int cnt=0;
		while(	cnt <4 && rx_call_stream.rx_call_valid[rx_call_stream.rx_call_write_pos] )
		{
			if(audioCodes == DECODE_STANDARD_PCM)
			{
				decode_buf_len = rx_call_stream.rx_call_len[rx_call_stream.rx_call_write_pos];
				memcpy(DECODE_BUF,rx_call_stream.rx_call_data[rx_call_stream.rx_call_write_pos],decode_buf_len);
			}
			else if(audioCodes == DECODE_G711)
			{
				decode_buf_len = G711_decode(rx_call_stream.rx_call_data[rx_call_stream.rx_call_write_pos],DECODE_BUF,rx_call_stream.rx_call_len[rx_call_stream.rx_call_write_pos], 0);
			}
			else if(audioCodes == DECODE_G722)
			{
				int outlen=g722_decode(g722_dctx, rx_call_stream.rx_call_data[rx_call_stream.rx_call_write_pos], rx_call_stream.rx_call_len[rx_call_stream.rx_call_write_pos], obuf);
				decode_buf_len = outlen*sizeof(obuf[0]);
				memcpy(DECODE_BUF,obuf,decode_buf_len);	//512bytes
			}

			write_i2s_data(DECODE_BUF,decode_buf_len);

			#if 0
			int ndb = 0;
			short int value;
			int i;
			long v = 0;
			for(i=0; i<decode_buf_len; i+=2)
			{
				memcpy(&value, DECODE_BUF+i, 2); //获取2个字节的大小（值）
				v += abs(value);
			}
			v = v/(decode_buf_len/2);
			if(v != 0) {
				ndb = (int)(20.0*log10((double)v / 32767.0 ));
			}
			else {
				ndb = -96;
			}
			printf("dB_in=%d\n",ndb);
			#endif


			rx_call_stream.rx_call_valid[rx_call_stream.rx_call_write_pos]=0;
			rx_call_stream.rx_call_write_pos++;
			if(rx_call_stream.rx_call_write_pos >= RX_CALL_BUF_PKG_MAX)
			{
				rx_call_stream.rx_call_write_pos=0;
			}
			cnt++;
		}

		//发送PCM数据
		encode_buf_len = 0;
		if(audioCodes==DECODE_STANDARD_PCM)
		{
			encode_buf_len = readBytes;
			memcpy(ENCODE_BUF,RX_BUF,encode_buf_len);
			
			#if 0
			int ndb = 0;
			short int value;
			int i;
			long v = 0;
			for(i=0; i<encode_buf_len; i+=2)
			{
				memcpy(&value, RX_BUF+i, 2); //获取2个字节的大小（值）
				v += abs(value);
			}
			v = v/(encode_buf_len/2);
			if(v != 0) {
				ndb = (int)(20.0*log10((double)v / 32767.0 ));
			}
			else {
				ndb = -96;
			}
			printf("dB_out=%d\n",ndb);
			#endif
		}
		else if(audioCodes==DECODE_G711)
		{
			encode_buf_len=G711_encode(RX_BUF, ENCODE_BUF, readBytes, 0);
		}
		else if(audioCodes==DECODE_G722)
		{
			encode_buf_len=g722_encode(g722_ectx, (short*)RX_BUF, (oblen / sizeof(obuf[0])), ibuf);
			memcpy(ENCODE_BUF,ibuf,encode_buf_len);	//128bytes
		}
		
		if(encode_buf_len>0)
			Send_Call_Audio_Stream(ENCODE_BUF,encode_buf_len);
	}

	printf("pt_i2s_call END...\n");

	close_dsp(); //关闭dsp

	if(m_stPager_Info.self_callStatus!=CALL_STATUS_FREE)
	{
		m_stPager_Info.self_callStatus=CALL_STATUS_FREE;
		Send_callStatus_feedback(CALL_STATUS_HANGUP);
		//如果是挂断，需要立即响应，避免线程轮询后接收多个状态后覆盖
		close_calling_win_extern(0,1,1000);
	}


	//关闭功放，todo 还要判断本地监听状态，或者进入对讲页面，直接关闭本地监听
	Amp_Switch(0);
	//退出对讲模式
	BP1048_Send_Enter_CALL(0,0,0);
	set_dsp_source(BP1048_SOURCE_DECODE_PAGER_MIC);

	Paging_status = PAGING_STOP;
	send_online_info();			//状态变化通知主机

	RelayCtrl(0);
	
    call_Btn_status_show(0);		//寻呼/对讲图标状态变化
    MIC_Led_Show(0);			//MIC灯关闭

	#if SUPPORT_CODEC_G722
	if(g722_ectx)
	{
		g722_encoder_destroy(g722_ectx);
		g722_ectx=NULL;
	}
	if(g722_dctx)
	{
		g722_decoder_destroy(g722_dctx);
    	g722_dctx=NULL;
	}
	#endif

	call_i2s_thread = 0xff;
   	pthread_exit(NULL);

error:
   printf("\nOPEN DSP ERROR,EXIT!");
   Paging_status = PAGING_STOP;
   call_Btn_status_show(0);
   call_i2s_thread = 0xff;
   pthread_exit(NULL);
}



int i2s_call_thread()
{
	#if defined(USE_PC_SIMULATOR) || defined(USE_SSD212) || defined(USE_SSD202)
	return ERROR;
	#endif
	int ret;
	pthread_t i2s_thread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	if(call_i2s_thread != 0xff)
	{
		return 0;
	}
	ret = pthread_create(&i2s_thread, &Pthread_Attr, (void *)pt_i2s_call, NULL);
	if (ret == -1)
	{
		printf("i2s_call_thread Creat Failure!\n");
		return ERROR;
	}

	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;

}

#endif





#if ENABLE_LISTEN_FUNCTION
int listen_i2s_thread=0xff;		//没有启动
stRx_monitor_stream rx_monitor_stream;


pthread_mutex_t listen_data_mutex=PTHREAD_MUTEX_INITIALIZER;

/****************************************************
 * @fn      pt_i2s_listen
 *
 * @brief   I2S监听
 *
 * @param
 *
 * @return	int 应答包长度
 */
void pt_i2s_listen()
{
	close_dsp(); //关闭dsp
	set_dsp_sampleRate(RATE_MONITOR);	//22.05khz
	set_dsp_source(BP1048_SOURCE_DECODE_PAGER_LISTEN);
	int ret=open_and_init_dsp(RATE_MONITOR,FMT_MONITOR,CHANNELS_MONITOR);
	if(ret!=SUCCEED)
	{
	listen_i2s_thread = 0xff;
	   goto error;
	}
	listen_i2s_thread=1;
	int invalid_cnt=0;
	unsigned int send_status_cnt=0;
	int sampleRate=0;
	
	Send_listen_status();


	int srate = ~ G722_SAMPLE_RATE_8000;

	G722_DEC_CTX *g722_dctx = NULL;
	int16_t *g722_obuf=NULL;
	
	g722_obuf=(int16_t *)malloc( 512 * 4 );
	if(g722_obuf == NULL)
	{
		printf("g722_obuf malloc failed\r\n");
		return;
	}
	g722_dctx=g722_decoder_new(64000, srate);
	if (g722_dctx == NULL) {
		printf("g722_decoder_new() failed\r\n");
		free(g722_obuf);
		g722_obuf=NULL;
		return;
	}
	

	
	while(m_stListen_Info.self_listenStatus == LISTEN_STATUS_START)
	{
		pthread_mutex_lock(&listen_data_mutex);
		while( rx_monitor_stream.rx_monitor_valid[rx_monitor_stream.rx_monitor_write_cnt] )
		{
			if(rx_monitor_stream.rx_monitor_stream_cnt>=5)		//23ms*10 = 	230ms	
			{
				invalid_cnt=0;
				if(rx_monitor_stream.rx_monitor_samplerate!=sampleRate)
				{
					sampleRate=rx_monitor_stream.rx_monitor_samplerate;
					printf("sampleRate=%d\n",sampleRate);
					ioctl(g_fd_dsp, SNDCTL_DSP_SPEED, &sampleRate);
					set_dsp_sampleRate(sampleRate);
				}
				 int outlen=g722_decode(g722_dctx, rx_monitor_stream.rx_monitor_data[rx_monitor_stream.rx_monitor_write_cnt], rx_monitor_stream.rx_monitor_len[rx_monitor_stream.rx_monitor_write_cnt], g722_obuf);
				write_i2s_data((char*)g722_obuf,outlen*2);
 
				rx_monitor_stream.rx_monitor_valid[rx_monitor_stream.rx_monitor_write_cnt]=0;
				rx_monitor_stream.rx_monitor_write_cnt++;
				if(rx_monitor_stream.rx_monitor_write_cnt >= RX_MONITOR_BUF_PKG_MAX)
				{
					rx_monitor_stream.rx_monitor_write_cnt=0;
				}

				if( send_status_cnt == 0 || send_status_cnt%40 == 0 )
				{
					Send_listen_status();
				}
				send_status_cnt++;
			}
			else
			{
				break;
			}
		}
		pthread_mutex_unlock(&listen_data_mutex);
		

		if(++invalid_cnt >= 2000)			//无数据超过6s，退出
		{
			m_stListen_Info.self_listenStatus = LISTEN_STATUS_STOP;
			break;
		}
		
		usleep(3000);
	}

	printf("pt_i2s_monitor END...\n");

	close_dsp(); //关闭dsp


	if(g722_obuf)
	{
		free(g722_obuf);
		g722_obuf=NULL;
	}
	if(g722_dctx)
	{
		g722_decoder_destroy(g722_dctx);
    	g722_dctx=NULL;
	}


	listen_i2s_thread = 0xff;
   	pthread_exit(NULL);

error:
   printf("\nOPEN DSP ERROR,EXIT!");
   paing_status_show(0);
   pthread_exit(NULL);
}



int i2s_listen_thread()
{
	printf("i2s_listen_thread...\n");
	#if defined(USE_PC_SIMULATOR) || defined(USE_SSD212) || defined(USE_SSD202)
	return ERROR;
	#endif


	if(listen_i2s_thread != 0xff)
	{
		return 0;
	}


	int ret;
	pthread_t i2s_thread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&i2s_thread, &Pthread_Attr, (void *)pt_i2s_listen, NULL);
	if (ret == -1)
	{
		printf("i2s_call_thread Creat Failure!\n");
		return ERROR;
	}

	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;

}


#endif