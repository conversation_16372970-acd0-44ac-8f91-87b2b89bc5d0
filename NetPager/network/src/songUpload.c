#include "sysconf.h"
#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
#include <curl/curl.h>
#include "songUpload.h"

st_songUpload songUploadInfo;

/* HTTP POST请求的内容类型 */
#define POST_CONTENT_TYPE "Content-Type: multipart/form-data"
/* 上传文件的字段名 */
#define FILE_FIELD_NAME "file"

//刷新上传状态
void set_uploadSong_status_and_progress(int status,int progress,int IsSelfcall);

/* 上传进度回调函数 */
static int progress_callback(void *clientp, double dltotal, double dlnow, double ultotal, double ulnow)
{
    //printf("progress_callback1\n");
    /* 计算上传进度百分比 */
    double progress = ulnow / ultotal * 100.0;
    #if 0
    if(ultotal)
        printf("Upload progress: %.2f%%\n", progress);
    #endif
    //回调函数则返回1，表示需要中止传输，返回0则表示继续传输。
    if(songUploadInfo.uploadProgress!=(int)progress)
    {
        songUploadInfo.uploadProgress = (int)progress;
        set_uploadSong_status_and_progress(songUploadInfo.uploadStatus,songUploadInfo.uploadProgress,0);
    }
    if(songUploadInfo.uploadStatus!=UPLOAD_STATUS_START)
    {
        printf("manual stop upload!\n");
        return 1;
    }
    return 0;
}

/* curl上传文件 */
//static int curl_upload_file(const char* url, const char* filePath)
void *curl_upload_file(void *parm)
{
    songUploadInfo.uploadStatus = UPLOAD_STATUS_FREE;       //空闲状态
    songUploadInfo.uploadProgress = 0;                      //上传进度归0

    char *url=songUploadInfo.uploadUrl;
    char *filePath=songUploadInfo.uploadSongPath;
    printf("curl_upload_file:url=%s,filePath=%s\n",url,filePath);
    CURL* curl = curl_easy_init();
    if(curl == NULL) {
        songUploadInfo.uploadStatus = UPLOAD_STATUS_FAILED_NORMAL;   //上传失败
        set_uploadSong_status_and_progress(songUploadInfo.uploadStatus,songUploadInfo.uploadProgress,0);
        return NULL;
    }

    songUploadInfo.uploadStatus = UPLOAD_STATUS_START;  //开始上传

    struct curl_httppost* post = NULL;
    struct curl_httppost* last = NULL;

    /* 添加文件上传字段 */
    curl_formadd(&post, &last,
                 CURLFORM_COPYNAME, FILE_FIELD_NAME,
                 CURLFORM_FILE, filePath,
                 CURLFORM_CONTENTTYPE, "application/octet-stream",
                 CURLFORM_END);

    /* 设置HTTP POST请求头部 */
    struct curl_slist* header = NULL;
    header = curl_slist_append(header, POST_CONTENT_TYPE);
    header = curl_slist_append(header, "Expect:");
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, header);

    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_POST, 1L);
    curl_easy_setopt(curl, CURLOPT_HTTPPOST, post);

    /* 设置上传进度回调函数 */
    curl_easy_setopt(curl, CURLOPT_PROGRESSFUNCTION, progress_callback);
    curl_easy_setopt(curl, CURLOPT_NOPROGRESS, 0L);

    //设置连接超时时间
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT_MS, 3000L);
    //设置整个传输过程超时时间
    curl_easy_setopt(curl, CURLOPT_TIMEOUT_MS, 180000L);

    char errorBuffer[1024];
    curl_easy_setopt(curl, CURLOPT_ERRORBUFFER, errorBuffer);
    CURLcode res = curl_easy_perform(curl);

    curl_slist_free_all(header);
    curl_easy_cleanup(curl);

    if (res == CURLE_OK) {
        printf("curl_upload_file ok!\n");
        //songUploadInfo.uploadStatus = UPLOAD_STATUS_SUCCEED;   //手动停止上传

        //上传成功后需要通知服务器，让其加入到列表文件
        Send_host_notfify_upload_status(UPLOAD_EVENT_SUCCEED,songUploadInfo.uploadListID,songUploadInfo.uploadSongName);
    } else {
        printf("curl_upload_file failed!,errorCode=%d\n",res);
        printf("errorBuffer=%s\n",errorBuffer);
        if(res == CURLE_ABORTED_BY_CALLBACK)
        {
            songUploadInfo.uploadStatus = UPLOAD_STATUS_STOP_MANUAL;   //手动停止上传
        }
        else
        {
            songUploadInfo.uploadStatus = UPLOAD_STATUS_FAILED_NORMAL;   //手动停止上传
        }

        //上传失败也要通知服务器，让其删除临时文件
        Send_host_notfify_upload_status(UPLOAD_EVENT_CANCELED,songUploadInfo.uploadListID,songUploadInfo.uploadSongName);
    }
    set_uploadSong_status_and_progress(songUploadInfo.uploadStatus,songUploadInfo.uploadProgress,0);
    return NULL;
}



int Start_Upload_File_Thread()
{
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)curl_upload_file, NULL);
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}


#endif