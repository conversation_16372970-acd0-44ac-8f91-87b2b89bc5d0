/*
 * system_bin.c
 *
 *  Created on: 2015-12-24
 *      Author: Administrator
 */

#include <string.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <dirent.h>
#include <malloc.h>
#include <sys/types.h>
#include <pthread.h>

#include "sysconf.h"
#include "system_bin.h"
#include "system_command.h"
typedef void (*sighandler_t)(int);


/*********************************************************************
 * @fn      kill_process
 *
 * @brief   kill掉进程
 *
 * @param	process - 需kill掉的进程名字
 *
 * @return  失败   - ERROR
 * 			成功   - SUCCEED
 */
int kill_process(char *process)
{
	int ret;
	pid_t pid;
	unsigned char cmdbuf[128] = {0};

	/*kill掉某个进程*/
	while(1)
	{
		pid = get_pid_by_name(process);
		if(pid > 0)
		{
			sprintf(cmdbuf, "kill -15 %d", pid);
			ret = pox_system(cmdbuf);
			if(ret < 0)
			{
				perror("kill process");
				return ERROR;
			}
			usleep(10);
			sprintf(cmdbuf, "kill -9 %d", pid);
			ret = pox_system(cmdbuf);
			if(ret < 0)
			{
				perror("kill process");
				return ERROR;
			}
			usleep(1000);
		}
		else
		{
			return SUCCEED;
		}
	}
}

/*********************************************************************
 * @fn      get_pid_by_name
 *
 * @brief    获取指定进程的PID
 *
 * @param   *process  - 需获取的进程名字
 *
 * @return  PID
 */
pid_t get_pid_by_name(char *process)
{
    pid_t retval;
	FILE *fp;
	char buf[32];
	unsigned char cmd_buf[64];

	/*初始化缓存*/
	memset(buf, 0x00, sizeof(buf));
	memset(cmd_buf, 0x00, sizeof(cmd_buf));
	sprintf(cmd_buf, "pidof %s", process);

	/*获取进程PID*/
	fp = popen(cmd_buf, "r");
	if (fp == NULL)
	{
		perror("ERROR popen");
		return 0;
	}
	fgets(buf, sizeof(buf), fp);
	if(Paging_status != PAGING_START) printf("Process->%s->PID=%d\n", process, atoi(buf));

	retval = (pid_t)atoi(buf);
	pclose(fp);

    return retval;
}

/*********************************************************************
 * @fn      remove_file
 *
 * @brief   删除指定文件
 *
 * @param   *file_path  - 需删除文件的绝对路径
 *
 * @return  void
 */
void remove_file(const char *file_path)
{
	unsigned char cmd_buf[512];

	memset(cmd_buf, 0x00, sizeof(cmd_buf));
	sprintf(cmd_buf, "rm -f %s", space_change(file_path));
	pox_system(cmd_buf);
}







/*********************************************************************
 * @fn      Get_SD_Disk_Space
 *
 * @brief   获取SD卡  剩余空间
 *
 * @param
 *
 * @return  none
 */
int Get_SD_Disk_Space()
{
#if 0
	int nErr, ndata;
	struct statfs sf;
	nErr=statfs("/mnt/yaffs2/",&sf);
	printf("Get_SD_Disk_Space size=%dM\n",sf.f_bsize*sf.f_bavail/1024/1024);
	return sf.f_bsize*sf.f_bavail/1024/1024-20;
#endif


		FILE *fp;
		char buf[512]={0};
		#if defined(USE_SSD212) || defined(USE_SSD202)
		fp = popen("df -m /customer/App/", "r");
		#else
		fp = popen("df -m /mnt/yaffs2/", "r");
		#endif
		if (fp == NULL)
		{
			perror("Get_SD_Disk_Space:popen ERROR!");
			return ERROR;
		}

		//首先查找/dev/mtdblock3

		int i;
		for( i=0;i<512;i++)
		{
			if( ( buf[i]= fgetc(fp)) !=EOF )
			{

			}
			else
				break;
		}

		char *p;
		p=strstr(buf,"/dev/mtdblock3");
		if( p == NULL )
		{
			perror("Get_SD_Disk_Space:strstr1 ERROR!");
			pclose(fp);
			return 0;
		}
		p=p+strlen("/dev/mtdblock3");
		int count=0;
		char space[10]={0};
		int k=0;
		for(i=0;i<200;i++)
		{
			p++;
			if( ( *p ==0x20 ) && ( *(p+1) !=0x20 ) )
			{
				count++;
			}
			if( count == 3 )
			{
				p++;
				for(k=0;k<10;k++)
				{
					if(p[k] == 0x20)
					{
						break;
					}
					space[k]=p[k];
				}
				break;
			}
		}

		pclose(fp);
		printf("\nGet_SD_Disk_Space:space=%s\n",space);
		return atoi(space);
}



/*********************************************************************
  * @fn      Get_FlashInfo
  *
  * @brief	  获取FLASH型号信息
  *
  * @param   char *flash_info FLASH型号信息
  *
  * @return  NULL
  */
 void Get_FlashInfo(char *flash_info)
 {
 	FILE *fp;
 	int eth0_speed = 0;
 	char buf[128]={0};

	memset(flash_info,0,sizeof(flash_info));

	fp = popen("dmesg | grep \"NAND device:\"", "r");
	if (fp == NULL)
	{
		perror("ERROR popen");
		return;
	}
	fgets(buf, sizeof(buf), fp);
	pclose(fp);
	if(strlen(buf)<10)
	{
		printf("\nGet_FlashInfo_buf_len=0,error!\n");
		return;
	}
	char *p1=strrchr(buf,'(');
	char *p2=strrchr(buf,')');
	if(p1!=NULL && p2!=NULL)
	{
		strncpy(flash_info,p1+1,p2-p1-1);
	}
	printf("\nGet_FlashInfo=%s\n", flash_info);

 }



 //转换为小写
 char *strlwr(char *s)
 {
  char *str;
  str = s;
  while(*str != '\0')
  {
   if(*str >= 'A' && *str <= 'Z') {
      *str += 'a'-'A';
  }
  str++;
  }
  return s;
  }

