#include "stdio.h"
#include "com_bp1048.h"
#include "uart.h"
#include <malloc.h>
#include <math.h>
#include <string.h>
#include <pthread.h>
#include "sysconf.h"


extern int g_Terminal_Uartfd;	//串口文件描述符

/*****
Ø  解码模块（P001）默认数据：
ADC0_L：0, 1, 4596(1 dB)；ADC0_R：1, 0, 0；
ADC1_L：2, 0, 0；ADC1_R：3, 0, 0；
DAC0_L：4, 1, 3651(-1dB)；DAC0_R：5, 1, 9383(7.2 dB)；BT：6, 1, 0；
Ø  解码模块（P002）默认数据：
ADC0_L：0, 1, 4596(1 dB)；ADC0_R：1, 0, 0；
ADC1_L：2, 0, 0；ADC1_R：3, 0, 0；
DAC0_L：4, 1, 3956(-0.3 dB)；DAC0_R：5, 1, 9383(7.2 dB)；BT：6, 1, 0；
Ø  解码模块（P006）默认数据：
ADC0_L：0, 1, 4596(1 dB)；ADC0_R：1, 1, 4596(1 dB)；
ADC1_L：2, 1, 4096(0 dB)；ADC1_R：3, 0, 0；
DAC0_L：4, 1, 6129(3.5 dB)；DAC0_R：5, 1, 9383(7.2 dB)；BT：6, 0, 0；
Ø  采集模块（P005）默认数据：
ADC0_L：0, 1, 4596(1 dB)；ADC0_R：1, 1, 4596(1 dB)；
ADC1_L：2, 2, 4191(0.2 dB)；ADC1_R：3, 2, 4191(0.2 dB)；
DAC0_L：4, 1, 1295(-10 dB)；DAC0_R：5, 0, 0；BT：6, 0, 0；
Ø  寻呼模块（P003）默认数据：
ADC0_L：0, 1, 4596(1 dB)；ADC0_R：1, 0, 0；
ADC1_L：2, 1, 4096(0 dB)；ADC1_R：3, 0, 0；
DAC0_L：4, 1, 3956(-0.3 dB)；DAC0_R：5, 1, 9383(7.2 dB)；BT：6, 0, 0；
***/

const st_bp1048_firmware_feature_info bp1048_default_firmware_feature={ {0x01,0x0,0x01,0x01,0x01,0x01,0x00}, \
  																			 {0xDEF,0x0,0xFFF,0xFFF,0x1964,0x2FC4,0xFFF}  };

const st_bp1048_bt_info  bp1048_default_bt_info={ "NetSpeaker",		\
													1,
													{'0','0','0','0'}  };


st_bp1048_pro g_bp1048_pro;
unsigned char Serial5_Receive_Buffer[BP1048_UART_RECEIVE_BUFFER_MAX];


unsigned char g_bp1048_IsInit=0;	//BP1048是否初始化
unsigned char g_stc_IsInit=0;		//STC单片是否初始化（只有SSD平台发射机才用到）

int sampleRate_array[9]={8000,11025,12000,16000,22050,24000,32000,44100,48000};
st_bp1048_info g_bp1048_info;

st_bp1048_firmware_feature_info bp1048_firmware_feature;
st_bp1048_bt_info bp1048_bt_info;

st_bp1048_upgrade_info bp1048_upgrade_info;

st_bp1048_usb_info bp1048_usb_info;
pthread_mutex_t BP1048_USB_Mutex=PTHREAD_MUTEX_INITIALIZER;

void Paging_auto_trigger_change(int IsSignal,int ChangeSignal);
void Bp1048_receive_Custom_Singal_Info(unsigned short datalen,unsigned char *rcbuf);

unsigned char Bp1048_get_sampleRate_index(int sampleRate)
{
	int i;
	for(i=0;i<sizeof(sampleRate_array);i++)
	{
		if( sampleRate_array[i] == sampleRate)
		  return i;
	}
	return 7;
}



void Bp1048_Command_Proc( unsigned char cmd, unsigned short datalen,unsigned char *data )
{
	switch(cmd)
	{
		case BP1048_CMD_CHIP_INFO:
		  Bp1048_receive_ChipInfo(datalen,data);
	  		break;
		case BP1048_CMD_FEATURES:
	 		break;
		case BP1048_CMD_SOURCE:
	  		break;
		case BP1048_CMD_VOLUME:
	 		break;
		case BP1048_CMD_BT_INFO:
	  		break;
		case BP1048_CMD_BOOT:
		  	g_bp1048_IsInit=0;
			Dsp_init();
	  		break;
		#if SUPPORT_AUTO_TRIGGER
		case BP1048_CMD_CUSTOM_SIGNAL:
			Bp1048_receive_Custom_Singal_Info(datalen,data);
			break;
		#endif
		case BP1048_CMD_UPGRADE:
			Bp1048_receive_UpgradeInfo(datalen,data);
			break;
		
		case BP1048_CMD_QUERY_USB_PLUG_INFO:
			if(bp1048_usb_info.IsPlug!=data[0])
			{
				pthread_mutex_lock(&BP1048_USB_Mutex);
				bp1048_usb_info.IsPlug=data[0];
				printf("bp1048_usb_info.IsPlug=%d\n",bp1048_usb_info.IsPlug);
				if(bp1048_usb_info.IsPlug)
				{
					//先发送停止，避免重启后1048还在播放
					Bp1048_Send_Set_Usb_PlayStatus(BP1048_MUSIC_STATUS_STOP);
					BP1048_Send_Set_Usb_Play_Mode(bp1048_usb_info.playMode);
					//获取USB歌曲列表
					//Bp1048_Send_Query_Usb_Dir(0XFF);
				}
				else
				{
					bp1048_usb_info.playStatus = BP1048_MUSIC_STATUS_STOP;
					//要清空USB播放列表
					Bp1048_free_udisk_musiclist();
				}
				pthread_mutex_unlock(&BP1048_USB_Mutex);
				refresh_usbPlay(0);
			}
			Bp1048_Send_Response_Usb_Plug();
			break;
		case BP1048_CMD_SET_USB_PLAY_STATUS:
			if(bp1048_usb_info.playStatus!=data[0])
			{
				printf("usb_PlayStatus:%d\n",bp1048_usb_info.playStatus);
				bp1048_usb_info.playStatus=data[0];
				refresh_usbPlay(0);
			}
			if (datalen == 3)
			{
				int new_fileId = data[1] + (data[2]<<8);
				if( bp1048_usb_info.current_playFileId != new_fileId )
				{
					udisk_current_playId_change(0,new_fileId);
				}
			}
			break;
		case BP1048_CMD_QUERY_USB_DIR:
			pthread_mutex_lock(&BP1048_USB_Mutex);
			BP1048_Handle_Get_Usb_Dir_Info(datalen,data);
			pthread_mutex_unlock(&BP1048_USB_Mutex);
			break;
		case BP1048_CMD_QUERY_USB_FILE:
			pthread_mutex_lock(&BP1048_USB_Mutex);
			BP1048_Handle_Get_Usb_File_Info(datalen,data);
			pthread_mutex_unlock(&BP1048_USB_Mutex);
			break;
	}
}




void Bp1048_uart_send(unsigned char cmd, unsigned short datalen,unsigned char *data) {
	if(g_Terminal_Uartfd == -1)
		return;
	unsigned char *send_buf=NULL;
	if(cmd == BP1048_CMD_UPGRADE)
	{
		send_buf= (unsigned char*)malloc(BP1048_UART_SEND_UPGRADE_BUFFER_MAX);
	}
	else
	{
		send_buf= (unsigned char*)malloc(BP1048_UART_RECEIVE_BUFFER_MAX);
	}

	int pos = 0, i;
	send_buf[pos++] = BP1048_UART_FRAME_HEAD1;
	send_buf[pos++] = BP1048_UART_FRAME_HEAD2;
	send_buf[pos++] = cmd;
	send_buf[pos++] = datalen;
	send_buf[pos++] = datalen>>8;

	for (i = 0; i < datalen; i++) {
		send_buf[pos++] = data[i];
	}

	send_buf[pos++] = Checksum(cmd,datalen,data);

	write(g_Terminal_Uartfd, send_buf, pos); //发送数据

#if 1
	printf("Bp1048_uart_send:cmd=0x%x,datalen=%d\r\n",cmd,datalen);

#else
	printf("Bp1048_uart_send:\r\n");
	for(i=0;i<pos;i++)
	{
		printf("%02x ",send_buf[i]);
	}
	printf("\r\n");
#endif

	free(send_buf);
}




void Bp1048_receive_ChipInfo(unsigned short datalen,unsigned char *rcbuf)
{
	int pos=0,i=0;
	while(pos<datalen)
	{
		int ref_type = rcbuf[pos++];
		int ref_len = rcbuf[pos++];

		switch(ref_type)
		{
			case 0:			//UUID数据
		  			memcpy(g_bp1048_info.uuid,rcbuf+pos,ref_len);
			  	break;
			case 1:			//版本信息
			 		g_bp1048_info.firmware_type=*(rcbuf+pos);
					#if (APP_TYPE == APP_JUSBE_MIXER)
					g_bp1048_info.firmware_type=BP1048_FW_NORMAL_TYPE;
					#endif
			  		memcpy(g_bp1048_info.version,rcbuf+pos+1,ref_len-1);
					sprintf(g_bp1048_info.version,"%d.%d.%d",g_bp1048_info.version[0],g_bp1048_info.version[1],g_bp1048_info.version[2]);
		  		break;
		}
		pos+=ref_len;
	}
	printf("g_bp1048_info.uuid:");
	for(i=0;i<8;i++)
	{
		printf("%x ",g_bp1048_info.uuid[i]);
	}
	printf("\r\n");
	printf("g_bp1048_info.version:%s,type=%d\r\n",g_bp1048_info.version,g_bp1048_info.firmware_type);
	if(g_bp1048_info.firmware_type > BP1048_FW_UDISK_TYPE)
	{
		g_bp1048_info.firmware_type = BP1048_FW_UDISK_TYPE;
	}
	
	#if DISABLE_UDISK_FUNCTION
	g_bp1048_info.firmware_type = BP1048_FW_NORMAL_TYPE;
	#endif
	
#if ENABLE_CALL_FUNCTION
	if( strcmp(g_bp1048_info.version,"1.5.0") >= 0 )
	{
		#if (IS_APP_SUPPORT_CALL)
		g_isSupportCall=1;
		#endif
	}
#endif

	//初始化设置参数
	Dsp_init();
	static int hasGetChipInfo=0;
	if(!hasGetChipInfo)
	{
		hasGetChipInfo=1;
		#if (APP_TYPE == APP_JUSBE_MIXER)
		set_dsp_source(BP1048_SOURCE_SOUND_CONTROL);
		#else
		if(g_bp1048_info.firmware_type == BP1048_FW_UDISK_TYPE)
		{
			set_dsp_source(BP1048_SOURCE_DECODE_PAGER_MIC_MUSIC);
		}
		else if(g_bp1048_info.firmware_type == BP1048_FW_NORMAL_TYPE)
		{
			set_dsp_source(BP1048_SOURCE_DECODE_PAGER_MIC);
		}
		if(g_bp1048_info.firmware_type == BP1048_FW_UDISK_TYPE)
		{
			BP1048_Send_Set_RP(0x00);	//没有电位器
			Bp1048_Send_Query_Usb_Plug();
			BP1048_Udisk_Info_thread();
		}
		#if ENABLE_CALL_FUNCTION
		//退出对讲模式
		BP1048_Send_Enter_CALL(0,0,0);
		#endif
		#endif
	}
}


void Bp1048_Send_Get_Info(unsigned char cmd)
{
  	//数据长度为0代表获取
	Bp1048_uart_send(cmd,0,NULL);
}


extern int g_mic_sensitivity;
extern int g_AuxIn_enable;

void set_mic_multiplier_val()
{
	//根据dB值计算数值
	double tem_num= (g_mic_sensitivity-11.0)/20.0;
	//printf("g_mic_sensitivity=%d,tem_num1=%f\n",g_mic_sensitivity,tem_num);
	tem_num=pow(10,tem_num);
	//printf("tem_num2=%f\n",tem_num);
	tem_num=4096*tem_num;

	bp1048_firmware_feature.module_gain[BP1048_AUDIO_MODULE_ADC1_L]=(short int)tem_num;

	g_mic_multiplier_val=(short int)tem_num;

	printf("MIC sensitivity=%d,MIC gain=%d\n",g_mic_sensitivity,g_mic_multiplier_val);
}

void Bp1048_Send_Set_Features()
{
	int i=0;
  	unsigned char cmd = BP1048_CMD_FEATURES;
	unsigned short datalen = 0;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };

	//default
	bp1048_firmware_feature=bp1048_default_firmware_feature;
	
	set_mic_multiplier_val();

	if(!g_AuxIn_enable)
		bp1048_firmware_feature.module_switch[BP1048_AUDIO_MODULE_ADC0_L]=0;	//关闭线路输入

	for(i=0;i<BP1048_AUDIO_MODULE_MAX;i++)
	{
		send_buf[datalen++] = BP1048_AUDIO_MODULE_ADC0_L+i;
		if( i == BP1048_AUDIO_MODULE_ADC1_L || i == BP1048_AUDIO_MODULE_ADC1_R )
		{
			send_buf[datalen++] = (bp1048_firmware_feature.module_switch[i] == 0? 0:1) ;	//0-关闭 1-MIC MODE 2-LINE MODE
		}
		else
		{
			send_buf[datalen++] = bp1048_firmware_feature.module_switch[i];
		}
		send_buf[datalen++] = bp1048_firmware_feature.module_gain[i];
		send_buf[datalen++] = bp1048_firmware_feature.module_gain[i]>>8;
	}

	Bp1048_uart_send(cmd,datalen,send_buf);
}


void Bp1048_Send_Set_Bluetooth()
{
	if(!bp1048_firmware_feature.module_switch[BP1048_AUDIO_MODULE_BT])
		return;
	int i=0;
  	unsigned char cmd = BP1048_CMD_BT_INFO;
	unsigned short datalen = 0;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };

	send_buf[datalen++] = 0;
	send_buf[datalen++] = strlen(bp1048_bt_info.name);
	memcpy(send_buf+datalen,bp1048_bt_info.name,strlen(bp1048_bt_info.name));
	datalen+=strlen(bp1048_bt_info.name);
	send_buf[datalen++] = 1;
	if(bp1048_bt_info.hasPassword)
	{
		send_buf[datalen++] = BP1048_BLUETOOTH_PASSWORD_MAX_LENGTH;
		memcpy(send_buf+datalen,bp1048_bt_info.password,BP1048_BLUETOOTH_PASSWORD_MAX_LENGTH);
		datalen+=BP1048_BLUETOOTH_PASSWORD_MAX_LENGTH;
	}
	else
	{
		send_buf[datalen++] = 0;
	}

	Bp1048_uart_send(cmd,datalen,send_buf);
}



void Bp1048_Send_Set_Volume(int volume)
{
  	unsigned char cmd = BP1048_CMD_VOLUME;
	unsigned short datalen = 1;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	send_buf[0]= volume;
	Bp1048_uart_send(cmd,datalen,send_buf);
}


void Bp1048_Send_Set_SampleRate(int sampleRate)
{
  	unsigned char cmd = BP1048_CMD_SAMPLERATE;
	unsigned short datalen = 1;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	send_buf[0]= Bp1048_get_sampleRate_index(sampleRate);
	Bp1048_uart_send(cmd,datalen,send_buf);
}


void Bp1048_Send_Set_Reboot()
{
	Bp1048_uart_send(BP1048_CMD_REBOOT,0,NULL);
}
unsigned char bp1048_output_channel_index[BP1048_MAX_OUTPUT_CHANNEL_COUNT]={BP1048_CHANNEL_DAC0_L,BP1048_CHANNEL_DAC0_R,BP1048_CHANNEL_I2S0_OUT_L,BP1048_CHANNEL_I2S0_OUT_R,
										BP1048_CHANNEL_I2S1_OUT_L,BP1048_CHANNEL_I2S1_OUT_R};

static void Bp1048_Send_Set_ChannelSource(st_bp1048_output_channel_info *output_channel_info)
{
	int i,j;
  	unsigned char cmd = BP1048_CMD_SOURCE;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };

	unsigned short datalen=0;
	for(i=0;i<BP1048_MAX_OUTPUT_CHANNEL_COUNT;i++)
	{
	  	int output_channel_id=0;
		switch(i)
		{
			case BP1048_OUTPUT_CHANNEL_DAC0_L:
			  output_channel_id=BP1048_CHANNEL_DAC0_L;
		  	break;
			case BP1048_OUTPUT_CHANNEL_DAC0_R:
			  output_channel_id=BP1048_CHANNEL_DAC0_R;
		  	break;
			case BP1048_OUTPUT_CHANNEL_I2S0_OUT_L:
			  output_channel_id=BP1048_CHANNEL_I2S0_OUT_L;
		  	break;
			case BP1048_OUTPUT_CHANNEL_I2S0_OUT_R:
			  output_channel_id=BP1048_CHANNEL_I2S0_OUT_R;
		  	break;
			case BP1048_OUTPUT_CHANNEL_I2S1_OUT_L:
			  output_channel_id=BP1048_CHANNEL_I2S1_OUT_L;
			break;
			case BP1048_OUTPUT_CHANNEL_I2S1_OUT_R:
			  output_channel_id=BP1048_CHANNEL_I2S1_OUT_R;
			break;
		}
		if(output_channel_info->output_channel_vaild[i])
		{
			send_buf[datalen++] = output_channel_id;
			int sub_len=datalen++;
			for(j=0;j<BP1048_MAX_CHANNEL_COUNT;j++)
			{
				if(output_channel_info->contain_channel_valid[i][j])
				{
					send_buf[datalen++] = BP1048_CHANNEL_Line5_L+j;
					send_buf[datalen++] = output_channel_info->contain_channel_gain[i][j];
					send_buf[sub_len]+=2;
				}
			}
			//由于BP1048判断逻辑错误，作如下变更：如果DAC0有效，但又没有选择LINE IN(LINE5 L)或者MIC1的情况下,需要将这两路输入增益设为-72dB，否则即使DAC没有选择LINE IN,也会有输出
			if(	i == BP1048_OUTPUT_CHANNEL_DAC0_L || i == BP1048_OUTPUT_CHANNEL_DAC0_R )
			{
				if(!output_channel_info->contain_channel_valid[i][BP1048_CHANNEL_Line5_L])
				{
					send_buf[datalen++] = BP1048_CHANNEL_Line5_L+BP1048_CHANNEL_Line5_L;
					send_buf[datalen++] = 72;	//-72dB
					send_buf[sub_len]+=2;
				}
				if(!output_channel_info->contain_channel_valid[i][BP1048_CHANNEL_MIC1])
				{
					send_buf[datalen++] = BP1048_CHANNEL_Line5_L+BP1048_CHANNEL_MIC1;
					send_buf[datalen++] = 72;	//-72dB
					send_buf[sub_len]+=2;
				}
			}
		}
		else if( i == BP1048_OUTPUT_CHANNEL_DAC0_L || i == BP1048_OUTPUT_CHANNEL_DAC0_R )//由于BP1048判断逻辑错误，作如下变更：如果DAC0有效，但又没有选择LINE IN(LINE5 L)或者MIC1的情况下,需要将这两路输入增益设为-72dB，否则即使DAC没有选择LINE IN,也会有输出
		{
			if(!output_channel_info->contain_channel_valid[i][BP1048_CHANNEL_Line5_L])
			{
				send_buf[datalen++] = output_channel_id;
				int sub_len=datalen++;

				send_buf[datalen++] = BP1048_CHANNEL_Line5_L+BP1048_CHANNEL_Line5_L;
				send_buf[datalen++] = 72;	//-72dB
				send_buf[sub_len]+=2;
			}
			if(!output_channel_info->contain_channel_valid[i][BP1048_CHANNEL_MIC1])
			{
				send_buf[datalen++] = output_channel_id;
				int sub_len=datalen++;

				send_buf[datalen++] = BP1048_CHANNEL_Line5_L+BP1048_CHANNEL_MIC1;
				send_buf[datalen++] = 72;	//-72dB
				send_buf[sub_len]+=2;
			}
		}
	}


#if 0
	printf("Bp1048_Send_Set_ChannelSource:\r\n");
	for(i=0;i<datalen;i++)
	{
		printf("0x%02x ",send_buf[i]);
	}
	printf("\r\n");

#endif

	Bp1048_uart_send(cmd,datalen,send_buf);
}




static void Bp1048_full_output_channel_struct(st_bp1048_output_channel_info *stInfo,unsigned char output_channel_index,unsigned char channel_index,unsigned char channel_gain)
{
	stInfo->contain_channel_valid[output_channel_index][channel_index]=1;
	stInfo->contain_channel_gain[output_channel_index][channel_index]=channel_gain;
}


void Bp1048_Send_Set_Source(unsigned long source_type)
{
	st_bp1048_output_channel_info output_channel_info;
	memset(&output_channel_info,0,sizeof(output_channel_info));
	unsigned char contain_channel_count[BP1048_MAX_OUTPUT_CHANNEL_COUNT]={0};

	int i2s_gain=-1;


	if( (source_type & BP1048_SOURCE_DECODE_DAC_I2S0_STEREO_MUSIC) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_STEREO_OTHER) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_MONO_MUSIC) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_MONO_OTHER) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_LINE) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_MIC1) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_MIC2) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_BT) || \
		(source_type & BP1048_SOURCE_DECODE_DAC_100V) || \
		(source_type & BP1048_SOURCE_PAGER_DAC_I2S)
	  )
	{
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_DAC0_L]=1;
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_DAC0_R]=1;
	}

	if( (source_type & BP1048_SOURCE_PAGER_I2S0_MIC1) || \
		(source_type & BP1048_SOURCE_PAGER_I2S0_LINE) || \
		(source_type & BP1048_SOURCE_DECODE_I2S0_MIC2) || \
		(source_type & BP1048_SOURCE_PAGER_I2S0_BT)
	  )
	{
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_I2S0_OUT_L]=1;
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_I2S0_OUT_R]=1;
	}

	if( (source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S0_MIC1) || \
		(source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S0_MIC2)
	  )
	{
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_I2S0_OUT_L]=1;
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_I2S0_OUT_R]=1;
	}

	if( (source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S1_LINEL) || \
		(source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S1_LINER)
	  )
	{
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_I2S1_OUT_L]=1;
		output_channel_info.output_channel_vaild[BP1048_OUTPUT_CHANNEL_I2S1_OUT_R]=1;
	}


	if(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_STEREO_MUSIC)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_I2S0_IN_L,BP1048_CHANNEL_GAIN_DOWN(I2S_MUSIC_PLAY_GAIN));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_I2S0_IN_R,BP1048_CHANNEL_GAIN_DOWN(I2S_MUSIC_PLAY_GAIN));
		i2s_gain=I2S_MUSIC_PLAY_GAIN;
	}
	else if(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_STEREO_OTHER)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_I2S0_IN_L,BP1048_CHANNEL_GAIN_DOWN(I2S_OTHER_GAIN));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_I2S0_IN_R,BP1048_CHANNEL_GAIN_DOWN(I2S_OTHER_GAIN));
		i2s_gain=0;
	}
	else if(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_MONO_MUSIC)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_I2S0_IN_L,BP1048_CHANNEL_GAIN_DOWN(I2S_MUSIC_PLAY_GAIN));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_I2S0_IN_L,BP1048_CHANNEL_GAIN_DOWN(I2S_MUSIC_PLAY_GAIN));
		i2s_gain=I2S_MUSIC_PLAY_GAIN;
	}
	else if(source_type & BP1048_SOURCE_DECODE_DAC_I2S0_MONO_OTHER)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_I2S0_IN_L,BP1048_CHANNEL_GAIN_DOWN(I2S_OTHER_GAIN));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_I2S0_IN_L,BP1048_CHANNEL_GAIN_DOWN(I2S_OTHER_GAIN));
		i2s_gain=0;
	}

	if(source_type & BP1048_SOURCE_DECODE_DAC_LINE)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_Line5_L,BP1048_CHANNEL_GAIN_DOWN(i2s_gain == -1? SINGLE_AUX_GAIN:MIX_AUX_GAIN+i2s_gain));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_Line5_L,BP1048_CHANNEL_GAIN_DOWN(i2s_gain == -1? SINGLE_AUX_GAIN:MIX_AUX_GAIN+i2s_gain));
	}

	if(source_type & BP1048_SOURCE_DECODE_DAC_MIC1)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_MIC1,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_MIC1,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
	}

	if(source_type & BP1048_SOURCE_DECODE_DAC_MIC2)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_MIC2,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_MIC2,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
	}

	if(source_type & BP1048_SOURCE_DECODE_DAC_BT)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_BT_L,BP1048_CHANNEL_GAIN_DOWN(i2s_gain == -1? SINGLE_AUX_GAIN:MIX_AUX_GAIN+i2s_gain));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_BT_R,BP1048_CHANNEL_GAIN_DOWN(i2s_gain == -1? SINGLE_AUX_GAIN:MIX_AUX_GAIN+i2s_gain));
	}

	if(source_type & BP1048_SOURCE_DECODE_DAC_100V)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_Line5_R,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_Line5_R,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
	}

	if(source_type & BP1048_SOURCE_DECODE_I2S0_MIC2)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_L,BP1048_CHANNEL_MIC2,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_R,BP1048_CHANNEL_MIC2,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
	}

	if(source_type & BP1048_SOURCE_PAGER_I2S0_BT)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_L,BP1048_CHANNEL_BT_L,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_R,BP1048_CHANNEL_BT_R,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
	}


	/****************Audio Collector********************************************/
	if(source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S0_MIC1)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_L,BP1048_CHANNEL_MIC1,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
	}
	if(source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S0_MIC2)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_R,BP1048_CHANNEL_MIC2,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
	}
	if(source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S1_LINEL)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S1_OUT_L,BP1048_CHANNEL_Line5_L,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
	}
	if(source_type & BP1048_SOURCE_AUDIO_COLLECTOR_I2S1_LINER)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S1_OUT_R,BP1048_CHANNEL_Line5_R,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
	}

	/************Net Pager*********************************************/
	if(source_type & BP1048_SOURCE_PAGER_I2S0_MIC1)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_L,BP1048_CHANNEL_MIC1,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_R,BP1048_CHANNEL_MIC1,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
	}
	if(source_type & BP1048_SOURCE_PAGER_I2S0_LINE)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_L,BP1048_CHANNEL_Line5_L,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_I2S0_OUT_R,BP1048_CHANNEL_Line5_L,BP1048_CHANNEL_GAIN_DOWN(SINGLE_AUX_GAIN));
	}
	if(source_type & BP1048_SOURCE_PAGER_DAC_I2S)
	{
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_L,BP1048_CHANNEL_I2S0_IN_L,BP1048_CHANNEL_GAIN_DOWN(0));
		Bp1048_full_output_channel_struct(&output_channel_info,BP1048_OUTPUT_CHANNEL_DAC0_R,BP1048_CHANNEL_I2S0_IN_R,BP1048_CHANNEL_GAIN_DOWN(0));
	}




	Bp1048_Send_Set_ChannelSource(&output_channel_info);
}







void Bp1048_Send_Firmware(unsigned char *dataBuf)
{
  	unsigned char cmd = BP1048_CMD_UPGRADE;
	unsigned char *send_buf = (unsigned char *)malloc(BP1048_UART_SEND_UPGRADE_BUFFER_MAX);
	int datalen=0;
	send_buf[datalen++]=bp1048_upgrade_info.Upgrade_process;
	printf("Bp1048_Send_Firmware:type=%d,index=%d\r\n",bp1048_upgrade_info.Upgrade_process,bp1048_upgrade_info.pkg_index);
	if(bp1048_upgrade_info.Upgrade_process == 0)	//准备包
	{
		send_buf[datalen++]=bp1048_upgrade_info.pkg_count;
		send_buf[datalen++]=bp1048_upgrade_info.pkg_count>>8;

		send_buf[datalen++]=bp1048_upgrade_info.file_length;
		send_buf[datalen++]=bp1048_upgrade_info.file_length>>8;
		send_buf[datalen++]=bp1048_upgrade_info.file_length>>16;
		send_buf[datalen++]=bp1048_upgrade_info.file_length>>24;

	}
	else if(bp1048_upgrade_info.Upgrade_process == 1)	//升级进行中
	{
		send_buf[datalen++]=bp1048_upgrade_info.pkg_index;
		send_buf[datalen++]=bp1048_upgrade_info.pkg_index>>8;

		memcpy(send_buf+datalen,dataBuf,BP1048_FIRMEARE_UPGRADE_ONCE_SIZE);
		datalen+=BP1048_FIRMEARE_UPGRADE_ONCE_SIZE;
	}
	else if(bp1048_upgrade_info.Upgrade_process == 2)	//升级结束
	{

	}
	Bp1048_uart_send(cmd,datalen,send_buf);
	free(send_buf);
}


void Bp1048_receive_UpgradeInfo(unsigned short datalen,unsigned char *rcbuf)
{
	if(rcbuf[0] == 0)
	{
		if(bp1048_upgrade_info.Upgrade_process == 0)
		{
			bp1048_upgrade_info.Upgrade_process=1;
			printf("bp1048_upgrade_info.Upgrade_process=1\r\n");
		}
		else if(bp1048_upgrade_info.Upgrade_process == 1)
		{
			if(bp1048_upgrade_info.pkg_index == bp1048_upgrade_info.pkg_count)
			{
				bp1048_upgrade_info.Upgrade_process=2;
				printf("bp1048_upgrade_info.Upgrade_process=2,pkg_index=%d\r\n",bp1048_upgrade_info.pkg_index);
			}
		}
		else if(bp1048_upgrade_info.Upgrade_process == 2)
		{
			//完成
			bp1048_upgrade_info.Upgrade_process = 3;
			printf("bp1048_upgrade succeed,reboot\r\n");
		}
		bp1048_upgrade_info.upgrade_valid=1;
	}
	else
	{
		bp1048_upgrade_info.upgrade_valid=0;
		printf("Bp1048_receive_UpgradeInfo error,code=%d\r\n",rcbuf[0]);
	}
	//printf("Bp1048_receive_UpgradeInfo,valid=%d\r\n",bp1048_upgrade_info.upgrade_valid);
}



void Bp1048_send_Custom_Singal_Info(int IsON[4],int threshold[4],int exitTime[4],int refresh_time[4])
{
	int i=0;
  	unsigned char cmd = BP1048_CMD_CUSTOM_SIGNAL;
	unsigned short datalen = 0;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	int adc_channel_num=4;
	for(i=0;i<adc_channel_num;i++)
	{
		send_buf[datalen++]=i;

		send_buf[datalen++] = 0;
		send_buf[datalen++] = IsON[i];	//low-8bit
		send_buf[datalen++] = 0;		//high-8bit

		send_buf[datalen++] = 1;
		send_buf[datalen++] = threshold[i];
		send_buf[datalen++] = threshold[i]>>8;

		send_buf[datalen++] = 2;
		send_buf[datalen++] = exitTime[i];
		send_buf[datalen++] = exitTime[i]>>8;

		send_buf[datalen++] = 3;
		send_buf[datalen++] = refresh_time[i];
		send_buf[datalen++] = refresh_time[i]>>8;
	}

	Bp1048_uart_send(cmd,datalen,send_buf);
}

#if SUPPORT_AUTO_TRIGGER
void Bp1048_receive_Custom_Singal_Info(unsigned short datalen,unsigned char *rcbuf)
{
	int pos=0;
	int signal[4]={0};
	int has_channel0=0;
	
	int i;
	printf("Bp1048_receive_Custom_Singal_Info:\n");
	for(i=0;i<datalen;i++)
	{
		printf("%d ",rcbuf[i]);
	}
	printf("\n");
	if(datalen>0)
	{
		for(pos=0;pos<datalen;)
		{
			int channelId = rcbuf[pos++];
			if(channelId<4)
			{
				signal[channelId] = rcbuf[pos++];
				if(channelId == 0)
				{
					has_channel0 =1;
				}
			}
			else
			{
				printf("Bp1048_receive_Custom_Singal_Info:ChannelId error!\n");
				break;
			}
		}
		if(has_channel0)
		{
			Paging_auto_trigger_change(signal[0],1);
		}
	}
}
#endif


void Bp1048_Send_Set_Usb_PlayStatus(unsigned char playStatus)
{
  	unsigned char cmd = BP1048_CMD_SET_USB_PLAY_STATUS;
	unsigned short datalen = 1;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	send_buf[0]= playStatus;
	Bp1048_uart_send(cmd,datalen,send_buf);
}

void Bp1048_Send_Query_Usb_Plug()
{
  	unsigned char cmd = BP1048_CMD_QUERY_USB_PLUG_INFO;
	unsigned short datalen = 0;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	Bp1048_uart_send(cmd,datalen,send_buf);
}

void Bp1048_Send_Response_Usb_Plug()
{
  	unsigned char cmd = BP1048_CMD_QUERY_USB_PLUG_INFO;
	unsigned short datalen = 1;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	send_buf[0]= bp1048_usb_info.IsPlug;
	Bp1048_uart_send(cmd,datalen,send_buf);
}

void Bp1048_Send_Query_Usb_PlayStatus()
{
  	unsigned char cmd = BP1048_CMD_SET_USB_PLAY_STATUS;
	unsigned short datalen = 0;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	Bp1048_uart_send(cmd,datalen,send_buf);
}

//清空USB播放列表
void Bp1048_free_udisk_musiclist()
{
	int i;
	for(i=0;i<bp1048_usb_info.musicList.DirNum;i++)
	{
		if(bp1048_usb_info.musicList.SongDir[i].musicfile)
		{
			free(bp1048_usb_info.musicList.SongDir[i].musicfile);
			bp1048_usb_info.musicList.SongDir[i].musicfile=NULL;
		}
		bp1048_usb_info.musicList.SongDir[i].ready_flag=0;
	}
	bp1048_usb_info.musicList.DirNum=0;
	bp1048_usb_info.current_playFileId=0;
	bp1048_usb_info.musicList.IsGetDirInfo=0;
	bp1048_usb_info.musicList.ready_flag=0;
}

void Bp1048_Send_Query_Usb_Dir(int dir_id)
{
	printf("Bp1048_Send_Query_Usb_Dir:%d\n",dir_id);
	unsigned char cmd = BP1048_CMD_QUERY_USB_DIR;
	unsigned short datalen = 1;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	send_buf[0]=dir_id;
	Bp1048_uart_send(cmd,datalen,send_buf);
}

void Bp1048_Send_Query_Usb_File(int dir_id)
{
	printf("Bp1048_Send_Query_Usb_File:%d\n",dir_id);
	unsigned char cmd = BP1048_CMD_QUERY_USB_FILE;
	unsigned short datalen = 1;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	send_buf[0]=dir_id;
	Bp1048_uart_send(cmd,datalen,send_buf);
}

void BP1048_Handle_Get_Usb_Dir_Info(unsigned short datalen,unsigned char *rcbuf)
{	
	if(bp1048_usb_info.IsPlug == 0)
		return;
	int i;
	int pos=0;
	unsigned int dir_num=rcbuf[pos++];
	bp1048_usb_info.musicList.DirNum = dir_num;
	char dirName[BP1048_MUSICDIRNAMELEN]={0};
	for(i=0;i<dir_num;i++)
	{
		int dir_id = rcbuf[pos++];
		int file_num = rcbuf[pos] + (rcbuf[pos+1]<<8);
		pos+=2;
		int file_start_index = rcbuf[pos] + (rcbuf[pos+1]<<8);
		pos+=2;
		int dir_name_len = rcbuf[pos++];
		memcpy(dirName,rcbuf+pos,dir_name_len);
		pos+=dir_name_len;

		bp1048_usb_info.musicList.SongDir[i].id = dir_id;
		bp1048_usb_info.musicList.SongDir[i].FileStartIndex = file_start_index;
		bp1048_usb_info.musicList.SongDir[i].nFileNum = file_num;
		sprintf(bp1048_usb_info.musicList.SongDir[i].cDirName,"%s",dirName);
		if(bp1048_usb_info.musicList.SongDir[i].musicfile!=NULL)
		{
			free(bp1048_usb_info.musicList.SongDir[i].musicfile);
			bp1048_usb_info.musicList.SongDir[i].musicfile=NULL;
		}
		bp1048_usb_info.musicList.SongDir[i].musicfile = malloc(bp1048_usb_info.musicList.SongDir[i].nFileNum*sizeof(BP1048_FILENAME));

		printf("dir_id:%d,name=%s,fisrt_pos=%d,fileNum=%d\n",dir_id,dirName,file_start_index,file_num);
	}
	bp1048_usb_info.musicList.IsGetDirInfo=1;
	//Bp1048_Send_Query_Usb_File(0xFF);
}


void BP1048_Handle_Get_Usb_File_Info(unsigned short datalen,unsigned char *rcbuf)
{
	if(!bp1048_usb_info.musicList.IsGetDirInfo)
		return;
	int i;
	int pos=0;
	unsigned int dir_id = rcbuf[pos++];
	unsigned int file_num = rcbuf[pos] + (rcbuf[pos+1]<<8);
	pos+=2;
	for(i=0;i<file_num;i++)
	{
		int file_index = rcbuf[pos] + (rcbuf[pos+1]<<8);
		pos+=2;
		int file_name_len = rcbuf[pos++];
		char fileName[BP1048_MUSICFILENAMELEN]={0};
		memcpy(fileName,rcbuf+pos,file_name_len);
		pos+=file_name_len;

		bp1048_usb_info.musicList.SongDir[dir_id-1].musicfile[i].realId = file_index;
		sprintf(bp1048_usb_info.musicList.SongDir[dir_id-1].musicfile[i].cName,"%s",fileName);
	}
	bp1048_usb_info.musicList.SongDir[dir_id-1].ready_flag = 1;

	printf("dir_id=%d,file_num=%d\n",dir_id,file_num);
	#if 0
	if(bp1048_usb_info.musicList.SongDir[bp1048_usb_info.musicList.DirNum-1].ready_flag)
	{
		int get_all_file_flag=1;
		for(i=0;i<bp1048_usb_info.musicList.DirNum;i++)
		{
			if(!bp1048_usb_info.musicList.SongDir[i].ready_flag)
			{
				get_all_file_flag=0;
				break;
			}
		}

		if(get_all_file_flag)
		{
			bp1048_usb_info.musicList.ready_flag=1;
		}
		else
		{
			printf("file Info error,tryAgain!\n");
			for(i=0;i<bp1048_usb_info.musicList.DirNum;i++)
			{
				bp1048_usb_info.musicList.SongDir[i].ready_flag=0;
			}
			Bp1048_Send_Query_Usb_File(0xFF);
		}
	}
	#endif
}

void BP1048_Send_Set_Usb_Play_File(int fileId)
{
	unsigned char cmd = BP1048_CMD_SET_USB_PLAY_FILE;
	unsigned short datalen = 2;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	send_buf[0]= fileId;
	send_buf[1]= fileId>>8;
	Bp1048_uart_send(cmd,datalen,send_buf);
}

void BP1048_Send_Set_Usb_Play_Mode(int playMode)
{
	unsigned char cmd = BP1048_CMD_SET_USB_PLAY_MODE;
	unsigned short datalen = 1;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	send_buf[0]= playMode;
	Bp1048_uart_send(cmd,datalen,send_buf);
}

void BP1048_Send_Set_RP(int IsRpVal)
{
	unsigned char cmd = BP1048_CMD_SET_RP;
	unsigned short datalen = 1;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	send_buf[0]= IsRpVal;
	Bp1048_uart_send(cmd,datalen,send_buf);
}


void BP1048_Send_Enter_CALL(int isCall,int echoLevel,int nsLevel)
{
	unsigned char cmd = BP1048_CMD_ENTER_CALL;
	unsigned short datalen = 3;
	unsigned char send_buf[BP1048_UART_RECEIVE_BUFFER_MAX] = { 0 };
	send_buf[0] = isCall;
	send_buf[1] = echoLevel;
	send_buf[2] = nsLevel;
	Bp1048_uart_send(cmd,datalen,send_buf);
}


void Dsp_init()
{
	if(!g_bp1048_IsInit)
	{
	    set_dsp_sampleRate(22050);	//22.05khz
		Bp1048_Send_Set_Features();
#if SUPPORT_AUTO_TRIGGER
		//设置ADC通道信号检测参数
		int signal_IsON[4]={1,0,0,0};
		int signal_threshold[4]={100,100,100,100};	//2mv(-53dB)触发
		int signal_exitTime[4]={1800,1800,1800,1800};	//15s退出
		int signal_refresh_time[4]={0,0,0,0};
		Bp1048_send_Custom_Singal_Info(signal_IsON,signal_threshold,signal_exitTime,signal_refresh_time);
#endif
		g_bp1048_IsInit=1;
	}
}





void Handle_Udisk_Info()
{
	int i=0;
	int get_dir_info_cnt=0;
	int lock_flag=0;
	while(1)
	{
		lock_flag=1;
		if(bp1048_usb_info.IsPlug)
		{
			if(get_dir_info_cnt>0)
			{
				get_dir_info_cnt--;
			}
			if(!bp1048_usb_info.musicList.IsGetDirInfo && get_dir_info_cnt == 0)
			{
				get_dir_info_cnt=25;
				Bp1048_Send_Query_Usb_Dir(0xFF);
			}
			else if(bp1048_usb_info.musicList.IsGetDirInfo && !bp1048_usb_info.musicList.ready_flag)
			{
				int dir_ok_cnt=0;
				int exit_flag=0;
				for(i=0;i<bp1048_usb_info.musicList.DirNum;i++)
				{
					if(bp1048_usb_info.IsPlug == 0)
					{
						exit_flag=1;
					}
					else 
					{
						if(!bp1048_usb_info.musicList.SongDir[i].ready_flag)
						{
							Bp1048_Send_Query_Usb_File(bp1048_usb_info.musicList.SongDir[i].id);
							int delay_cnt=0;

							while(delay_cnt<25)
							{
								if(!bp1048_usb_info.musicList.SongDir[i].ready_flag)
								{
									delay_cnt++;
									usleep(20000);
								}
								else
								{
									break;
								}
							}
						}
						else
						{
							dir_ok_cnt++;
						}
					}
					if(exit_flag)
					{
						break;
					}
				}
				if(bp1048_usb_info.IsPlug && dir_ok_cnt == bp1048_usb_info.musicList.DirNum)
				{
					bp1048_usb_info.musicList.ready_flag=1;
				}
			}
			//printf("IsGetDirInfo=%d,ready_flag=%d\n",bp1048_usb_info.musicList.IsGetDirInfo,bp1048_usb_info.musicList.ready_flag);
		}
		else
		{
			get_dir_info_cnt=0;
		}
		usleep(100000);
	}
	pthread_exit(NULL);
}


void BP1048_Udisk_Info_thread()
{
	int ret=-1;
	pthread_t pthread;
	pthread_attr_t pthread_Attr;
	pthread_attr_init(&pthread_Attr);
	pthread_attr_setdetachstate(&pthread_Attr, PTHREAD_CREATE_DETACHED);

	/*创建一个线程单独接收终端串口数据*/
	ret = pthread_create(&pthread, &pthread_Attr, (void *)Handle_Udisk_Info, NULL);
	pthread_attr_destroy(&pthread_Attr);
}