/*
 * TimerP.c
 *  @encod:             UTF-8
 *  @Created on:        2013-8-30
 *  @Author:            ysl
 */

#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <dirent.h>
#include <malloc.h>
#include <time.h>
#include <sys/types.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <ctype.h>
#include<sys/time.h>
#include<unistd.h>
#include<string.h>
#include <pthread.h>

#include "TimerP.h"

char date_str[20];
char weekday_str[20];
char time_str[20];

int pre_time_second=-1;		//前一次的秒数记录
int pre_time_min=-1;		//前一次的分钟记录
int pre_time_hour=-1;		//前一次的时数记录
int pre_time_day=-1;		//前一次的天数记录
int pre_time_month=-1;		//前一次的月数记录
int pre_time_year=-1;		//前一次的年数记录


const char WeekDay_CH[7][10]={"星期日","星期一","星期二", "星期三", "星期四", "星期五", "星期六"};
//const char WeekDay_EN[7][10]={"Sunday","Monday","Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};
const char WeekDay_EN[7][10]={"Sun","Mon","Tue", "Wed", "Thu", "Fri", "Sat"};

pthread_mutex_t TimeMutex=PTHREAD_MUTEX_INITIALIZER;


/***********************************************************************************************************/
/***         时间设置部分可以通过GetSystemTime(time_t *timep)获取硬件设定系统时间                            ****/
/***         通过调用SetSystemTime(struct tm Time_tm)设定时间                                                              ****/
/***         参考SysTimeSet_test()测试程序调用方法                                                                                        *****/
/**********************************************************************************************************/
/**********************************************************************************************************
 *  @File Function:   获取本地时间
 *  @Describe:
 *  @Vision
 */
struct tm* GetSystemTime(time_t *timep)
{
	int i;
    struct tm *p;
    time(timep);
    p=localtime(timep);


    CURRENT_TIME.hour=p->tm_hour;
    CURRENT_TIME.min=p->tm_min;
    CURRENT_TIME.sec=p->tm_sec;
    CURRENT_TIME.mon=p->tm_mon+1;
    CURRENT_TIME.day=p->tm_mday;
    CURRENT_TIME.year=p->tm_year+1900;
    CURRENT_TIME.weekday=p->tm_wday;

    if( CURRENT_TIME.min != pre_time_min || CURRENT_TIME.hour != pre_time_hour || CURRENT_TIME.year!=pre_time_year ||  CURRENT_TIME.mon!=pre_time_month || CURRENT_TIME.day!=pre_time_day )//&& p->tm_sec != pre_time_sec)		//分秒都不相同更新主页面并发送给终端
	{
		pre_time_min=CURRENT_TIME.min;
		pre_time_hour=CURRENT_TIME.hour;
		pre_time_day=CURRENT_TIME.day;
		pre_time_month=CURRENT_TIME.mon;
		pre_time_year=CURRENT_TIME.year;

		//更新界面
		UI_UpdateSystemTime(0);
	}



    return p;
}


/************************************************************************************************
 *  @File Function:   设置系统时间
 *  @Describe:        同步硬件时钟
 *  @Vision
 */
void SetSystemTime(struct tm Time_tm)
{
    char buf[50];
    memset(buf,0,sizeof(buf));
    sprintf(buf,"date %02d%02d%02d%02d%04d.%02d",Time_tm.tm_mon,Time_tm.tm_mday,Time_tm.tm_hour,
            Time_tm.tm_min,Time_tm.tm_year,Time_tm.tm_sec);

    system(buf);
    //system("hwclock -w");
}






/***********************************************************************************************************/

void *GetSystemTime_ntp()
{
    while(1)
    {
		time_t Time;
		pthread_mutex_lock(&TimeMutex);
		GetSystemTime(&Time);//获取时间
		pthread_mutex_unlock(&TimeMutex);
	    usleep(900000);
        //memory_print();
    }
}

void GetSystemTime_thread()
{
    pthread_t pid;
    pthread_attr_t Pthread_Attr;
    pthread_attr_init(&Pthread_Attr);
    pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&pid, &Pthread_Attr, (void *)GetSystemTime_ntp, NULL);
    pthread_attr_destroy(&Pthread_Attr);
}
