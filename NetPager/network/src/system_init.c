/***************************************************
 *   Copyright (c) 2015 ，广州深宝音响
 *   All rights reserved.
 *
 *   文件名称： login_win.c
 *   摘要：
 *
 *   当前版本： 0.0.1
 *   作者：蒋明书 ，修改日期： 2015年 12月 30日
 *
 */
#include <fcntl.h>
#include "sysconf.h"
#include "set_net_win.h"
#include <sys/socket.h>
#include "login_win.h"
#include "backlight.h"
#include "Paging_win.h"
#include "record/ReadINI.h"
#include "tcp_host.h"
#include <time.h>
#include "record/minIni.h"
#include "NetPager/win/win.h"
#include "SaveZoneInfo.h"
#include "I2S.h"
#include "call_process.h"
#include <signal.h>

#if defined(USE_SSD212) || defined(USE_SSD202)
#include "sourceControl.h"
#include "uart_adc.h"
#include "mi_gpio.h"
	#if defined(USE_SSD212)
	#include "es7210.h"
	#elif defined(USE_SSD202)
	#include "es7243.h"
	#endif
#endif

/***********函数声明**************/

void* save_sysconf(char *section,char *key);
void system_init();
void set_mac_address(void);
void get_randnum(void);
void Search_OnlineZone_THREAD();
void* read_sysconf(char *section,char *key);
void Static_Ip_Init();

/***********函数声明**************/

char SOFT_VERSION[50]; //固件版本

unsigned char MAC_BUF[64];   //写入的MAC地址char
unsigned char MAC_ADDR[6];   //MAC ADDR

unsigned char network_init_flag=0;	   //网络初始化完成标志
int eth_link_status=0;		//有线网卡连接状态

//线路输入开关
int g_AuxIn_enable=1;
//dsp音量
int g_dsp_volume=80;
//密码访问设置页面
int enable_password_access_settings=0;

//背光时间设置
int g_backlightTimeout=6;

//MIC超时时间设置
int g_Mic_TimeOut=6;
//MIC启用
int g_Mic_enable = 1;

int g_backlight_timer_count=0;
const unsigned long g_backlight_threshold_array[6]={300,600,1800,3600,7200,0x7FFFFFFF};

int g_current_record_id=0;	//当前录音id
int g_PlayMode=SEQUENCY_PLAY;	//播放模式
int Paging_status = PAGING_STOP;
int g_Signal_Timeout_level=3;	//信号超时时间等级（1~6）
#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
const unsigned long g_Signal_Timeout_array[6]={60,120,240,360,480,600};
#else
const unsigned long g_Signal_Timeout_array[6]={300,600,1800,2700,3600,0x7FFFFFFF};
#endif

int g_paging_recorder=0;		//默认不记录录音

#if SUPPORT_AUTO_TRIGGER
int g_auto_trigger_enable=0;		   //开启自动触发
char g_auto_trigger_groupId[40]={0};  //自动触发分组ID
char g_AuxIn_signal=0;				  //线路输入是否有信号
int  g_paging_mode=0;				  // 0-手动寻呼 1-AUX自动信号触发
#endif

int g_mic_sensitivity=11;	//麦克风灵敏度,+-10db,default 0dB
int g_mic_multiplier_val=4096;	//麦克风灵敏度量化数值，4096代表0dB,此数值加上ADC MIC通道增益值为最终数值

int g_paging_vol=DEFAULT_ZONE_PAGING_VOLUME;	//寻呼音量

int g_listen_vol=DEFAULT_LISTEN_OUTPUT_VOLUME;	//监听音量

#if ENABLE_CALL_FUNCTION
int g_call_vol=DEFAULT_LISTEN_CALL_VOLUME;	//对讲音量
int g_ring_vol=DEFAULT_LISTEN_RING_VOLUME;	//对讲音量
#endif

char ipAddress[20];

unsigned char g_device_alias[128];


int init_succeed=0;

int paging_all_press_flag=0;	//寻呼所有按键按下标志
int paging_alarm_press_flag=0;	//消防警报按键
int paging_alarm_valid_flag=0;	//消防警报有效标志

extern unsigned char zone_group_pagingType;

void GPS_Status_Check_Thread();

extern int paging_win_refresh_ing;


extern pthread_mutex_t ZoneInfoMutex;

int g_network_mode=NETWORK_MODE_LAN;	//网络模式



/*****IP属性 ************/
int  g_IP_Assign = IP_ASSIGN_DHCP;	//IP分配方式(static or DHCP)
char g_Static_ip_address[32];		//静态IP地址
char g_Subnet_Mask[32];				//子网掩码
char g_GateWay[32];					//网关
char g_Primary_DNS[32];				//主DNS服务器
char g_Alternative_DNS[32];			//备用DNS服务器


int g_system_work_mode=WORK_MODE_DISTRIBUTIONAL;	//系统工作模式(默认分布模式)

signed char host_ready_offline_flag = 1;			//主机即将离线标志
signed char g_host_device_TimeOut = -1;            	//主机离线计数,-1代表已经离线

void Paging_Key_Check_THREAD();



extern int g_send_decode_pcm_type;	//发送的编码算法类型

int g_Enable_local_listening=0;				//开启本地监听
int g_paging_type=PAGING_TYPE_MIC;			//呼叫类型 1-寻呼台MIC寻呼	2-APP广播寻呼  3-寻呼台音乐广播

#if NETWORK_VPN_INTERNET
int g_Is_tcp_real_internet=1;      //TCP模式是否处于internet公网上（主机IP是169开头或者192开头代表是内网TCP)
#else
int g_Is_tcp_real_internet=0;      //TCP模式是否处于internet公网上（主机IP是169开头或者192开头代表是内网TCP)
#endif

char g_device_serialNum[20]={0};    //设备序列号


int g_isSupportCall=0;				//是否支持对讲
int g_isSupportPagerCall=0;			//是否支持寻呼台之间对讲（在g_isSupportCall=1且收到pager.Xml的情况下支持）

int g_isShowPlayListDirAndSongAdmin=1;	//话筒任意用户可以看到管理员创建的歌曲列表

int language = DEFAULT_LANGUAGE;			   //语言

struct s_audio_collector_list Audio_collector_list;

#if defined(USE_SSD212) || defined(USE_SSD202)
int  g_device_moduleId=0;	//设备型号ID
#endif


#if defined(USE_SSD212) || defined(USE_SSD202)
#define stUsbInfo 	udiskInfo
#else
#define stUsbInfo 	bp1048_usb_info
#endif

#if defined(USE_SSD212) || defined(USE_SSD202)
void SearchOtaFileAndUpgrade();
void init_sysconf();
#endif


#if defined(USE_PC_SIMULATOR)
	#if IS_AIPU_MIXER
	int g_screen_width=600;
	int g_screen_height=1024;
	#else
	int g_screen_width=1024;
	int g_screen_height=600;
	#endif
#elif defined(USE_PC_SIMULATOR) || defined(USE_ASM9260) || defined(USE_SSD212)
	#if IS_AIPU_MIXER
	int g_screen_width=600;
	int g_screen_height=1024;
	#else
	int g_screen_width=1024;
	int g_screen_height=600;
	#endif
#elif defined(USE_SSD202)
int g_screen_width=1280;
int g_screen_height=800;
#endif

#define HOST_TIMEOUT_TCP	62
#define HOST_TIMEOUT_UDP	42
#define HOST_TIMEOUT_VALUE  60	//主机超时时间


unsigned char get_system_source()
{
	unsigned char media_source = SOURCE_NULL;
	if(Paging_status == PAGING_START)
		media_source = SOURCE_NET_PAGING; 
	else if(Paging_status == CALLING_START)
		media_source = SOURCE_CALL; 
	return media_source;
}


// 信号处理函数
void signal_handler(int signum);

/*********************************************************************
 * @fn      read_device_alias
 *
 * @brief  	读取已设置的设备别名
 *
 * @param   void
 *
 * @return  返回设备别名长度，否则返回错误
 */
int read_device_alias(void)
{
	FILE *fp;

	memset(g_device_alias, 0x00, sizeof(g_device_alias));

	if((fp = fopen(FILE_DEVICE_ALIAS, "r+")) != NULL)
	{
		//fscanf(fp, "%s", g_device_alias);	//%s是以空格为分隔单位，会被当做两个字符串。若要读入一行，可以使用fgets(fp,str);
		fgets(g_device_alias,128,fp);	//读入一行
		fclose(fp);
		return strlen(g_device_alias);
	}
	else
	{
		perror("Open device name file error");
		return ERROR;
	}
}

/*********************************************************************
 * @fn      save_device_alias
 *
 * @brief  	保存设备别名
 *
 * @param   void
 *
 * @return  void
 */
void save_device_alias(void)
{
	FILE *fp;

	if((fp = fopen(FILE_DEVICE_ALIAS, "w+")) != NULL)
	{
		fprintf(fp, "%s", g_device_alias);
		int wfd = fileno(fp);
		fflush(fp);
		fsync(wfd);
		fclose(fp);
	}
	else
	{
		perror("Open device name file error");
	}
}

#if DEBUG_CORE_MODE
void generate_segfault()
{
    char *ptr = NULL;
    *ptr = 0;
}
#endif


void system_init() {

#if defined(USE_PC_SIMULATOR) || defined(USE_SSD212) || defined(USE_SSD202)
	#if DISABLE_UDISK_FUNCTION
	g_bp1048_info.firmware_type = BP1048_FW_NORMAL_TYPE;
	#else
	g_bp1048_info.firmware_type = BP1048_FW_UDISK_TYPE;
	#endif
	#if ENABLE_CALL_FUNCTION
		#if (IS_APP_SUPPORT_CALL)
		g_isSupportCall = 1;
		#endif
	#endif
#endif

#if USE_SSD202
// 注册信号处理函数
signal(SIGTERM, signal_handler);
signal(SIGINT, signal_handler);
signal(SIGSEGV, signal_handler);
#endif

#if defined(USE_SSD212) || defined(USE_SSD202)
	mi_sys_init();
	GPIO_Set_Pager_Init();
#endif

#ifndef USE_PC_SIMULATOR
	//打开开门狗
	Init_watdog();
#endif

#ifndef USE_PC_SIMULATOR
#if DEBUG_CORE_MODE
	char command[] = "/bin/gzip";
	char option[] = "-1";
	char outfile[] = "/customer/App/coredump.gz";
	FILE* f = fopen("/customer/App/core.sh", "w");
	if (f) {
		fprintf(f, "#!/bin/sh\n");
		fprintf(f, "%s %s > %s\n", command, option, outfile);
		fprintf(f, "sync\n");
		fclose(f);
		system("chmod +x /customer/App/core.sh"); // 给 core.sh 文件添加执行权限
	}
#else
	system("rm /customer/App/core.sh");
	system("rm /customer/App/coredump.gz");
#endif
#endif

#if defined(USE_SSD212) || defined(USE_SSD202)
	Signal_OutPut_switch(0);	//关闭运放
#endif
	Amp_Switch(0);	//关闭功放
	NetLedCtrl(0);	//关闭网络灯
	MIC_Led_Show(0);//关闭LED灯

	#if defined(USE_SSD212) || defined(USE_SSD202)
	init_sysconf();

	GPIO_set_trigger_mode(0);	//默认短路触发
	#endif

	//读取配置文件
	read_sysconf(INI_SECTION_BASIC,NULL);
	read_sysconf(INI_SETCION_MFR,NULL);
	#if defined(USE_SSD212) || defined(USE_SSD202)
	read_sysconf(INI_SETCION_DSP_Firmware,NULL);
	#endif

#if defined(USE_SSD212) || defined(USE_SSD202)
	SearchOtaFileAndUpgrade();
	Get_Screen_Rotations_Value();
	Get_Screen_LCD_type_Value(false);
	#if defined(USE_SSD212)
	Create_Uart_Adc_task();
	es7210_adc_init(AUDIO_HAL_32K_SAMPLES);
	#elif defined(USE_SSD202)
	es7210_adc_init(AUDIO_HAL_32K_SAMPLES);
	#endif
#endif

#if LZY_COMMERCIAL_VERISON
	if(IS_DISP_RES_800)
	{
		g_bp1048_info.firmware_type = BP1048_FW_NORMAL_TYPE;
	}
#endif


	#if USE_ASM9260
	//设置背光
	init_backlight_module();
	#endif

	//扫描所有用户
	OS_Init_User_M();

	//扫描分区信息
	OS_Init_Zone_M();
	//扫描分组信息
	OS_Init_Group_M();
	//浏览歌曲文件信息			//不保存
	//Scan_MusicFile_Info();

	//读取设备别名
	read_device_alias();

	//初始化串口(BP1048)
#if !defined(USE_PC_SIMULATOR) &&  !defined(USE_SSD212) && !defined(USE_SSD202)
	Init_Uart();
#endif

	Language_Config();

	//初始化成功，重置检测次数
	system("echo 0 >reset_count");


	#if defined(USE_SSD212) || defined(USE_SSD202)
	Init_Usb_Check_Status_M();
	#endif


	//#if defined(USE_SSD212) || defined(USE_SSD202)		//放到最后系统复位后可能段错误，原因未知
	#if defined(USE_SSD212)
	mi_audio_out_init(32000, 16, 1);
	mi_audio_in_init(AI_MODE_PAGING_32K,32000, 16, 1);
	Signal_OutPut_switch(1);	//开启运放
	Create_udisk_play_task();
	Create_sourceControl_task();
	#elif defined(USE_SSD202)

	ffplay_init();

	mi_audio_out_init(32000, 16, 1);
	mi_audio_in_init(AI_MODE_PAGING_32K,32000, 16, 1);
	Signal_OutPut_switch(1);	//开启运放
	Create_udisk_play_task();
	Create_sourceControl_task();
	#endif

	GetSystemTime_thread();			//系统时间检测线程
	HOSTIP_Check_THREAD();			//IP检测线程
	ScreenTimeout_Thread();			//屏幕自动息屏检测线程
}







/********************************************************
 * @fn      HOSTIP_Check
 *
 * @brief   IP检测与各网络通讯的初始化线程
 *
 * @param	none
 *
 * @return  none
 *
 */
/********************************************************/
void *HOSTIP_Check()
{

	int link_status_change_cnt=0;

	int reLinked_flag=0;
	int dhcp_runCnt=0;
	char ipAddress_temp[20]={0};

	GetLocalMAC(MAC_ADDR,true);

	INIT_NETWORK();
	nif_init();

	#if defined(USE_SSD212) || defined(USE_SSD202)
	InitModule4G();
	#endif

#if defined(USE_PC_SIMULATOR)
	#if DEFAULT_PC_WAN_MODE_TEST
		g_network_mode = NETWORK_MODE_WAN;
		sprintf(g_host_tcp_addr_domain,"%s","************");
		g_host_tcp_port=49888;
	#endif
#endif

	 while(1)
	 {
		 if(!network_init_flag)
		 {
			memset(ipAddress,0,sizeof(ipAddress));
			GetLocalIp(ipAddress);
			if( ( strlen(ipAddress)>=7 && strcmp(ipAddress,"127.0.0.1")!=0 ))
			{
				network_init_flag=1;
				usleep(100000);	//延时100ms
				//启动各项网络线程
				#if !PC_MULTI_PROCESS_TEST
				socketInit();
				#endif
				Start_Client_RX_Pthread();
				NetPkg_Check_Thread();
				//开启集中模式下分区信息下发到主机的状态检测线程
				Send_Host_ZoneInfo_THREAD();
				OnlineZone_Check_thread();		//在线分区检测线程
				Zone_UpdateStatus_thread();		//分区状态刷新线程
				//主机在线超时检测线程
				HOST_Control_Device_Check_Thread();
				if(g_network_mode == NETWORK_MODE_WAN)
				{
					TCP_Server_Thread();
				}
				usleep(100000);
				send_online_info();
				SendToZone_Search_Cmd();
				//主动搜索分区命令-发送组播
				Search_OnlineZone_THREAD();

				//寻呼按键检测线程
				Paging_Key_Check_THREAD();

				#if ENABLE_CALL_FUNCTION
				Call_Status_Check_Thread();

				#if USE_SSD202
				Video_Call_Status_Check_Thread();
				#endif
				#endif

				refresh_deviceInfo();
			}

		 }
		 
		#if USE_PC_SIMULATOR
		usleep(200000);
        continue;
        #endif
		int temp_link_status=get_netlink_status("eth0");
		if(temp_link_status!=eth_link_status || (g_IP_Assign == IP_ASSIGN_DHCP && eth_link_status && dhcp_runCnt == 0))
		{
			link_status_change_cnt++;
			if(link_status_change_cnt>2)
			{
				link_status_change_cnt=0;
				eth_link_status=temp_link_status;
				printf("eth_link_status=%d\n",eth_link_status);
				if(eth_link_status == 1)
				{
					#if defined(USE_SSD212) || defined(USE_SSD202)
					config_module_4g_network();
					if(g_module_4G_status == MODULE_4G_UNUSED)
					{
						//如果WAN模式下已经连接，需要立即重连
						if(g_network_mode == NETWORK_MODE_WAN && g_tcp_connect_status)
						{
							//tcp_client_reconnect();
							g_tcp_reconnect_flag=1;
						}
					}
					#endif
					//ASM9260T重新进行DHCP时需要开关一次网卡，否则特殊情况下可能内核报错失败
					/*
					WARNING: at net/sched/sch_generic.c:261 dev_watchdog+0x14c/0x23c()
					NETDEV WATCHDOG: eth0 (mac9260): transmit queue 0 timed out
					Modules linked in:
					[<c00273e4>] (unwind_backtrace+0x0/0xdc) from [<c00388c8>] (warn_slowpath_common+0x48/0x60)
					[<c00388c8>] (warn_slowpath_common+0x48/0x60) from [<c0038918>] (warn_slowpath_fmt+0x24/0x30)
					[<c0038918>] (warn_slowpath_fmt+0x24/0x30) from [<c0195010>] (dev_watchdog+0x14c/0x23c)
					[<c0195010>] (dev_watchdog+0x14c/0x23c) from [<c00418d8>] (run_timer_softirq+0x154/0x1e8)
					[<c00418d8>] (run_timer_softirq+0x154/0x1e8) from [<c003d4d8>] (__do_softirq+0x78/0x104)
					[<c003d4d8>] (__do_softirq+0x78/0x104) from [<c002106c>] (asm_do_IRQ+0x6c/0x84)
					[<c002106c>] (asm_do_IRQ+0x6c/0x84) from [<c01e6a70>] (__irq_svc+0x30/0x80)
					Exception stack(0xc0325f80 to 0xc0325fc8)
					5f80: 00000000 0005717f 0005617f 60000013 c0324000 c0347cfc c001fe14 c0327b88
					5fa0: 2001d7f8 41069265 2001d7c4 00000000 600000d3 c0325fc8 c0022a1c c0022a28
					5fc0: 60000013 ffffffff
					[<c01e6a70>] (__irq_svc+0x30/0x80) from [<c0022a28>] (default_idle+0x2c/0x30)
					[<c0022a28>] (default_idle+0x2c/0x30) from [<c0022ef8>] (cpu_idle+0x58/0x9c)
					[<c0022ef8>] (cpu_idle+0x58/0x9c) from [<c0008a9c>] (start_kernel+0x268/0x2c0)
					[<c0008a9c>] (start_kernel+0x268/0x2c0) from [<20008034>] (0x20008034)
					---[ end trace 68305b93b6924d8c ]---
					*/
					if(g_IP_Assign == IP_ASSIGN_DHCP)
					{
						pox_system("ifconfig eth0 down");
						pox_system("ifconfig eth0 up");
						printf("ReStart DHCP...\n");
						#if USE_ASM9260
						pox_system("/mnt/yaffs2/update_dhcp &");
						#elif defined(USE_SSD212) || defined(USE_SSD202)
						pox_system("pidof zcip | xargs kill");
                        pox_system("pidof udhcpc | xargs kill");
						//pox_system("udhcpc -i eth0 -s /etc/init.d/udhcpc.script &");
						pox_system("udhcpc -i eth0 -T 6 -s /customer/App/udhcpc.script &");
						#endif
						dhcp_runCnt++;
					}
					reLinked_flag=1;
				}
				else
				{
					//todo 网线断开
					reLinked_flag=0;
					#if defined(USE_SSD212) || defined(USE_SSD202)
					if(g_module_4G_status <= MODULE_4G_OFF)
					#else
					if(1)
					#endif
					{
						#if defined(USE_SSD212) || defined(USE_SSD202)
						if(g_IP_Assign == IP_ASSIGN_DHCP)
						{
							pox_system("pidof zcip | xargs kill");
							pox_system("pidof udhcpc | xargs kill");
						}
						#endif
						//如果是WAN模式，需要断开TCP
						#if 0
						if(g_network_mode == NETWORK_MODE_WAN)
							tcp_client_reconnect();
						#endif
					}
					else
					{
						#if defined(USE_SSD212) || defined(USE_SSD202)
						config_module_4g_network();
						//如果WAN模式下已经连接，需要立即重连
						if(g_network_mode == NETWORK_MODE_WAN && g_tcp_connect_status)
						{
							//tcp_client_reconnect();
							g_tcp_reconnect_flag=1;
						}
						#endif
					}

					if( g_host_device_TimeOut >=0 && g_host_device_TimeOut < HOST_TIMEOUT_VALUE-10 )
					{
						g_host_device_TimeOut =  HOST_TIMEOUT_VALUE-10;
						host_ready_offline_flag = 1;
						//关闭网络连接状态IO输出
						NetLedCtrl(0);
					}

					Paging_status = PAGING_STOP;
				}
				refresh_deviceInfo();
			}
		}
		else
		{
			link_status_change_cnt=0;
			//if(g_IP_Assign == IP_ASSIGN_DHCP)
			{
				if(network_init_flag && eth_link_status)
				{
					if(reLinked_flag)		//如果已经连接连接，重新获取IP
					{
						memset(ipAddress_temp,0,sizeof(ipAddress_temp));
						GetLocalIp(ipAddress_temp);
						if( ( strlen(ipAddress_temp)>=7 && strcmp(ipAddress_temp,"127.0.0.1")!=0 ))
						{
							reLinked_flag=0;
							if( strcmp(ipAddress,ipAddress_temp) )
							{
								printf("IP not eqaul:%s...\n",ipAddress_temp);
								sprintf(ipAddress,"%s",ipAddress_temp);
								refresh_deviceInfo();
							}
							else
							{
								printf("IP eqaul...\n");
							}
							socket_join_multicast_membership();
						}
					}
				}
			}
		}
		usleep(200000);
	 }
}

void HOSTIP_Check_THREAD()
{
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)HOSTIP_Check, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}







/********************************************************
 * @fn      Search_OnlineZone
 *
 * @brief   定时发送搜索设备命令
 *
 * @param	none
 *
 * @return  none
 *
 */
/********************************************************/

void *Search_OnlineZone()
{
	int send_count=0;
	int paging_timeout_count=0;
	int i=0;
	while(1)
	{
		if( (++send_count)%10 == 0)
		{
			send_count=0;
			send_online_info();
			SendToZone_Search_Cmd();
		}
		//如果当前处于寻呼状态,判断是否有分区在寻呼,如果没有,超时关闭
		int zone_paging_num=0;
		if(Paging_status == PAGING_START)
		{
			printf("PagingZoneInfo.ZoneNum=%d\n",PagingZoneInfo.ZoneNum);
			for(i=0;i<PagingZoneInfo.ZoneNum;i++)
			{
				int zoneId=Get_ZoneIndex_By_MAC(PagingZoneInfo.ZoneInfo[i].ZoneMac);
				if(zoneId == -1)
					continue;
				if(	m_stZone_Info.zoneInfo[ zoneId ].g_zone_source == SOURCE_NET_PAGING	)
				{
					zone_paging_num++;
					break;
				}
			}
			if( zone_paging_num == 0 )
			{
				paging_timeout_count++;
				if(paging_timeout_count >=10)	//超过10s,退出寻呼
				{
					printf("No Paging Zone,exit Paging!\n");
					paging_timeout_count=0;
					//停止寻呼
					Paging_status = PAGING_STOP;
					memset(&PagingZoneInfo,0,sizeof(PagingZoneInfo));		//清除寻呼分区信息
				}
			}
			else
			{
				paging_timeout_count=0;
			}
		}
		else
		{
			paging_timeout_count=0;
		}

		sleep(1);
	}
}




void Search_OnlineZone_THREAD()
{
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)Search_OnlineZone, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}






void *Paging_Key_Check(void *parm)
{
	int i;
	int pager_start_with_alarm=0;
	while(1)
	{
		usleep(50000);


	   if(!paging_alarm_press_flag)	//如果消防警报灯已经按下，不检测一键寻呼按键
	   {
			#if (IS_TRANSMITTER)
			if(Get_Gpio_Tx_Switch_value() == 0)	//低电平按下
			#else
			if(Get_Gpio_Key_Value(1) == 0)	//低电平按下
			#endif
			{
				if(!paging_all_press_flag)	//当前不处于按下状态时才响应，去抖动
				{
					paging_all_press_flag=1;
					printf("Calling Paging Key...\n");
					if(Paging_status == PAGING_STOP)
					{
						if(!IsValidWin(WIN_CONTROL))
						{
							continue;
						}
						int callingAll_flag=0;
							for(i=0;i<m_stZone_Info.OnlineTotalZone;i++)
							{
								if(m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_isHide == 0)	//非隐藏才选中
								{
									m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_isSelect=1;
									callingAll_flag=1;
								}
							}

							for(i=0;i<m_stGroup_Info.TotalGroup;i++)
							{
								if(m_stGroup_Info.groupInfo[i].g_group_isHide == 0)	//非隐藏才选中
									m_stGroup_Info.groupInfo[i].g_group_isSelect=1;
							}

							Update_Selected_Zone();	//全选分区并通知主机

							control_win_update(0XFF,0);
							Paging_proc(0);
					}
					#if (!IS_TRANSMITTER)
					else	if(!paging_alarm_press_flag)	//已经处于寻呼状态，判断消防警报按键有没有按下,如果没有按下，关闭寻呼
					{

							//如果存在使用TCP模式的分区，发送停止
							SendToZone_Paging_Ready_Cmd_select(PCM_SEND_END,0);
							for(i=0;i<m_stZone_Info.OnlineTotalZone;i++)
							{
								m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_isSelect=0;
							}
							for(i=0;i<m_stGroup_Info.TotalGroup;i++)
							{
								m_stGroup_Info.groupInfo[i].g_group_isSelect=0;
							}

							Update_Selected_Zone();			//取消所有分区的选中状态并通知主机
							
							control_win_update(0XFF,0);
							
							//先发送停止
							if(g_network_mode == NETWORK_MODE_LAN)
							{
								SendToZone_Paging_Ready_Cmd_select_multicast(PCM_SEND_END,1);
							}	
							
							memset(&PagingZoneInfo,0,sizeof(PagingZoneInfo));		//清除寻呼分区信息
							Paging_status=PAGING_STOP;
					}
					#endif
				}
			}
			else
			{
				#if (IS_TRANSMITTER)
				if(paging_all_press_flag && !paging_alarm_press_flag && Paging_status == PAGING_START)	//已经处于寻呼状态，判断消防警报按键有没有按下,如果没有按下，关闭寻呼
				{
						//如果存在使用TCP模式的分区，发送停止
						SendToZone_Paging_Ready_Cmd_select(PCM_SEND_END,0);
						for(i=0;i<m_stZone_Info.OnlineTotalZone;i++)
						{
							m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_isSelect=0;
						}
						for(i=0;i<m_stGroup_Info.TotalGroup;i++)
						{
							m_stGroup_Info.groupInfo[i].g_group_isSelect=0;
						}

						Update_Selected_Zone();			//取消所有分区的选中状态并通知主机
						
						control_win_update(0XFF,0);
						
						//先发送停止
						if(g_network_mode == NETWORK_MODE_LAN)
						{
							SendToZone_Paging_Ready_Cmd_select_multicast(PCM_SEND_END,1);
						}	
						
						memset(&PagingZoneInfo,0,sizeof(PagingZoneInfo));		//清除寻呼分区信息
						Paging_status=PAGING_STOP;
				}
				#endif
				paging_all_press_flag = 0;
			}
	   }
	   
	   #if defined(USE_SSD212)
	   if(Get_Gpio_Key_Value(2) == 0 || Get_Gpio_Value(PAD_GPIO4) == 0)	//消防警报按键或者短路开关
	   #else
	   if(Get_Gpio_Key_Value(2) == 0)	//消防警报按键
	   #endif
	   {
		   if(paging_alarm_press_flag || !paging_alarm_valid_flag)	//已经处于按下状态或者消防警报键无效
		   {
			   continue;
		   }
			if(!IsValidWin(WIN_CONTROL))
			{
				continue;
			}
		   paging_alarm_press_flag=1;
		   printf("Alarm Paging Key...\n");
		   Led2_Alarm_Show(1);
		   if(Paging_status == PAGING_STOP)
		   {
			   pager_start_with_alarm=1;
			   int callingAll_flag=0;
				for(i=0;i<m_stZone_Info.OnlineTotalZone;i++)
				{
					if(m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_isHide == 0)	//非隐藏才选中
					{
						m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_isSelect=1;
						callingAll_flag=1;
					}
				}

				for(i=0;i<m_stGroup_Info.TotalGroup;i++)
				{
					if(m_stGroup_Info.groupInfo[i].g_group_isHide == 0)	//非隐藏才选中
						m_stGroup_Info.groupInfo[i].g_group_isSelect=1;
				}

				//寻呼警报前先停止USB
				stop_udiskPlay(0);

				Update_Selected_Zone();	//全选分区并通知主机

				control_win_update(0XFF,0);
				Paging_proc(0);
		   }
		   else		//已经处于寻呼状态，此时需要循环播放警报声
		   {
			   pager_start_with_alarm=0;
		   }
		}
	   #if defined(USE_SSD212)
	   else if(Get_Gpio_Key_Value(2)  ==  1 && Get_Gpio_Value(PAD_GPIO4) == 1)
	   #else
	   else if(Get_Gpio_Key_Value(2)  ==  1)
	   #endif
	   {
		   paging_alarm_valid_flag=1;
		   if(paging_alarm_press_flag)
		   {
		   	   paging_alarm_press_flag=0;
		   	   Led2_Alarm_Show(0);
			   
			   //如果是由警报发起的寻呼，需要关闭
			   if(pager_start_with_alarm)
			   {
				   	//如果存在使用TCP模式的分区，发送停止
					SendToZone_Paging_Ready_Cmd_select(PCM_SEND_END,0);

				    pager_start_with_alarm=0;
				    for(i=0;i<m_stZone_Info.OnlineTotalZone;i++)
					{
						m_stZone_Info.zoneInfo[m_stOnlineZone_Info.onlineZone[i]].g_zone_isSelect=0;
					}
					for(i=0;i<m_stGroup_Info.TotalGroup;i++)
					{
						m_stGroup_Info.groupInfo[i].g_group_isSelect=0;
					}

					Update_Selected_Zone();			//取消所有分区的选中状态并通知主机
					
					control_win_update(0XFF,0);
					
					//先发送停止
					if(g_network_mode == NETWORK_MODE_LAN)
					{
						SendToZone_Paging_Ready_Cmd_select_multicast(PCM_SEND_END,1);
					}
					
					memset(&PagingZoneInfo,0,sizeof(PagingZoneInfo));		//清除寻呼分区信息
					Paging_status=PAGING_STOP;
			   }
		   }
	   }

	}

	return NULL;
}



void Paging_Key_Check_THREAD()
{
#if defined(USE_PC_SIMULATOR)
	return;
#endif
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr,Paging_Key_Check, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}




/*********************************************************************
* @fn      get_randnum
*
* @brief    获取随机数
*
* @param   void
*
* @return  NULL
*/
void get_randnum(void)
{
   int i;
   struct timeval tpstart;
   gettimeofday(&tpstart, NULL);
   srand(tpstart.tv_usec);

   memset(MAC_ADDR, 0x0, sizeof(MAC_ADDR));


	/*获取时间系数设置随机种子*/
	gettimeofday(&tpstart, NULL);
	srand(tpstart.tv_usec);

   for(i=0; i< 6; i++)
   {
		if( i == 0)
			MAC_ADDR[i]=0x2C;
		else if(i == 1)
			MAC_ADDR[i]=0x21;
		else
		{
			srand((unsigned int)(time(0)+i+rand()));
			MAC_ADDR[i] = ((unsigned char)(rand()));
			usleep(rand()%256);
			printf("rand_buf[%d]=%d\n", i, MAC_ADDR[i]);
		}
   }

}

/*********************************************************************
* @fn      set_mac_address
*
* @brief    初始化MAC地址
*
* @param   void
*
* @return  NULL
*/
void set_mac_address(void)
{
   unsigned char t_mac_address[64];
   sprintf(MAC_BUF,"%02x:%02x:%02x:%02x:%02x:%02x",MAC_ADDR[0],MAC_ADDR[1],MAC_ADDR[2],MAC_ADDR[3],MAC_ADDR[4],MAC_ADDR[5]);
   printf("set_mac_address:%s\n", MAC_BUF);
   sprintf(t_mac_address, "ifconfig eth0 hw ether %s", MAC_BUF);

   Exec_System_CMD("ifconfig eth0 down");
   Exec_System_CMD(t_mac_address);
   Exec_System_CMD("ifconfig eth0 up");
}









/*********************************************************************
 * @fn      HOST_Control_Device_Check
 *
 * @brief   主机在线超时检测线程
 *
 * @param
 *
 *
 * @return
 *
 */

void* HOST_Control_Device_Check()
{
	while(1)
	{
		if(network_init_flag)
	    {
          if( host_ready_offline_flag == 1  && g_host_device_TimeOut == 0 )
          {
            host_ready_offline_flag = 0;
            //打开网络连接状态IO输出
			NetLedCtrl(1);
          }
          
          if(g_host_device_TimeOut>=0)
          {
            g_host_device_TimeOut++;
            if(g_host_device_TimeOut >= HOST_TIMEOUT_VALUE)
            {
				if(g_system_work_mode == WORK_MODE_CONCENTRATED)
				{
					printf("HOST disconnect,g_system_work_mode=WORK_MODE_DISTRIBUTIONAL\n");
					g_system_work_mode=WORK_MODE_DISTRIBUTIONAL;
					refresh_deviceInfo();
					#if ENABLE_CALL_FUNCTION
					//清除现有所有寻呼台
					pthread_mutex_lock(&PagerInfoMutex);
					memset(&m_stPager_Info,0,sizeof(m_stPager_Info));
					control_win_update(2,0);
					pthread_mutex_unlock(&PagerInfoMutex);
					#endif
					//清除现有所有分区
					pthread_mutex_lock(&ZoneInfoMutex);
					memset(&m_stZone_Info,0,sizeof(m_stZone_Info));
					memset(m_stZone_Info.DateTime,0,sizeof(m_stZone_Info.DateTime));
					Save_Zone_Info();

					control_win_update(0,0);

					pthread_mutex_unlock(&ZoneInfoMutex);

					send_online_info();

					Paging_status = PAGING_STOP;
					#if 0
					if(m_stUser_Info.CurrentUserIndex !=0 )		//非管理员，退出到登录界面
					{
						Back_to_Login_win();
					}
					#endif
				}
				if(g_network_mode == NETWORK_MODE_WAN)
				{
					Paging_status = PAGING_STOP;
					if(g_tcp_connect_status)
					{
						printf("g_host_device_TimeOut,exit_tcp!\n");
						//tcp_client_reconnect();
						g_tcp_reconnect_flag=1;
					}
				}
				
				g_host_device_TimeOut = -1; //离线
				host_ready_offline_flag = 1;

			  	//关闭网络连接状态IO输出
				NetLedCtrl(0);
            }
          }
	    }
		sleep(1);
	}
}



int HOST_Control_Device_Check_Thread()
{
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)HOST_Control_Device_Check, NULL);
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}





void INIT_NETWORK()
{
	#if USE_PC_SIMULATOR
	return;
	#endif

	//设置静态IP、子网掩码
	char cmd[64]={0};

	printf("Network_init...\n");
	#if USE_ASM9260
	system("ethtool -s eth0 speed 100");
	system("ethtool -s eth0 duplex full");
	system("ethtool -s eth0 autoneg on");
	system("ifconfig lo 127.0.0.1 up");

	//修改ip_conntrack CONNTRACK_MAX 以及 HASHSIZE 的值，避免出现ip_conntrack: table full, dropping packet
	system("echo 10240 > /proc/sys/net/netfilter/nf_conntrack_max");
	system("echo 4096 > /sys/module/nf_conntrack/parameters/hashsize");
	system("echo 1200 > /proc/sys/net/netfilter/nf_conntrack_tcp_timeout_established");

	//cat /proc/sys/net/core/rmem_max
	system("sysctl -w net.core.rmem_max=524288");		//设置UDP总缓冲区			//发包缓冲区与收包缓冲区一样，只不过参数名称不一样：SO_SNDBUF、wmem_max.
	system("sysctl -w net.core.wmem_max=262144");		//设置UDP总缓冲区			//发包缓冲区与收包缓冲区一样，只不过参数名称不一样：SO_SNDBUF、wmem_max.	
	#elif defined(USE_SSD212) || defined(USE_SSD202)
    pox_system("ifconfig eth0 up");
	pox_system("ifconfig lo 127.0.0.1 up");
	#endif

	#if USE_SSD202
	pox_system("ifconfig eth1 up");
	sprintf(cmd,"ifconfig %s %s netmask %s","eth1","***************","*************");
	pox_system(cmd);
	#endif

	usleep(10000);
    eth_link_status=get_netlink_status(NET_LOCAL_DEV_NAME);
    printf("init_network:eth_link_status=%d\n",eth_link_status);

	if(g_IP_Assign == IP_ASSIGN_STATIC)
	{
		if((strlen(g_Static_ip_address) == 0) || (strlen(g_Subnet_Mask) == 0) || (strlen(g_GateWay) == 0) )
			return;
		sprintf(cmd,"ifconfig %s %s netmask %s",NET_LOCAL_DEV_NAME,g_Static_ip_address,g_Subnet_Mask);
		pox_system(cmd);

		//设置网关
		sprintf(cmd,"route add default gw %s",g_GateWay);
		pox_system(cmd);

		//设置主DNS、备用DNS服务器
		if(strlen(g_Primary_DNS) >=7)
		{
			sprintf(cmd,"echo \"nameserver %s\" > /etc/resolv.conf",g_Primary_DNS);
			pox_system(cmd);
			if(strlen(g_Alternative_DNS) >=7)
			{
				sprintf(cmd,"echo \"nameserver %s\" >> /etc/resolv.conf",g_Alternative_DNS);
				pox_system(cmd);
			}
		}
	}
}



#if defined(USE_SSD212) || defined(USE_SSD202)
void init_sysconf()
{
	char create_config_path[64]={0};
	sprintf(create_config_path,"mkdir -p %s",CONFIG_DIR_PATH);
	pox_system(create_config_path);
}
#endif


void* save_sysconf(char *section,char *key)
{
#if defined(USE_PC_SIMULATOR)
	return NULL;
#endif
	printf("save_sysconf:sction=%s,key=%s\n",section,key);
	if(strcmp(section,"Network") == 0 && key == NULL)
	{
		ini_putl("Network","Connect_mode",g_network_mode,BASIC_INI_FILE);
		ini_puts("Network","TcpHost_addr",g_host_tcp_addr_domain,BASIC_INI_FILE);
		ini_putl("Network","TcpHost_port",g_host_tcp_port,BASIC_INI_FILE);

		ini_puts("Network","TcpHost_addr2",g_host_tcp_addr_domain2,BASIC_INI_FILE);
		ini_putl("Network","TcpHost_port2",g_host_tcp_port2,BASIC_INI_FILE);

		ini_putl("Network","Ip_mode",g_IP_Assign,BASIC_INI_FILE);
		ini_puts("Network","Static_IP",g_Static_ip_address,BASIC_INI_FILE);
		ini_puts("Network","Subnet_mask",g_Subnet_Mask,BASIC_INI_FILE);
		ini_puts("Network","GateWay",g_GateWay,BASIC_INI_FILE);
		ini_puts("Network","Primary_DNS",g_Primary_DNS,BASIC_INI_FILE);
		ini_puts("Network","Alternative_DNS",g_Alternative_DNS,BASIC_INI_FILE);
	}
	else if(strcmp(section,"MusicPlay") == 0)
	{
		if(strcmp(key,"PlayMode") == 0)	//播放模式
		{
			ini_putl(section,key,g_PlayMode,BASIC_INI_FILE);
		}
		else if(strcmp(key,"Udisk_PlayMode") == 0)	//Udisk播放模式
		{
			ini_putl(section,key,stUsbInfo.playMode,BASIC_INI_FILE);
		}
	}
	else if(strcmp(section,"Display") == 0)
	{
		if(strcmp(key,"Language") == 0)		//语言
		{
			ini_putl(section,key,language,BASIC_INI_FILE);
		}
		else if(strcmp(key,"Brightness") == 0)	//屏幕亮度
		{
			ini_putl(section,key,backlight_level,BASIC_INI_FILE);
		}
		else if(strcmp(key,"ScreenTimeOut") == 0)	//自动息屏
		{
			ini_putl(section,key,g_backlightTimeout,BASIC_INI_FILE);
		}
	}
	else if(strcmp(section,"Bell") == 0)
	{
		if(strcmp(key,"Paging_Bell_Selected") == 0)		   //寻呼钟声
		{
			ini_putl(section,key,paging_bell_selected,BASIC_INI_FILE);
		}
	}
#if SUPPORT_AUTO_TRIGGER
	//自动触发
	else if(strcmp(section,"Trigger") == 0)
	{
		if(strcmp(key,"IsEnable") == 0)
		{
			ini_putl(section,key,g_auto_trigger_enable,BASIC_INI_FILE);
		}
		else if(strcmp(key,"GroupId") == 0)
		{
			ini_puts(section,key,g_auto_trigger_groupId,BASIC_INI_FILE);
		}
	}
#endif
	else if(strcmp(section,"Other") == 0)
	{
		if(strcmp(key,"Paging_Zone_Volume") == 0)		   //分区寻呼默认音量
		{
			ini_putl(section,key,g_paging_vol,BASIC_INI_FILE);
		}
		if(strcmp(key,"SignalTimeOut") == 0)		   	  //信号超时时间
		{
			ini_putl(section,key,g_Signal_Timeout_level,BASIC_INI_FILE);
		}
		if(strcmp(key,"Listen_Volume") == 0)		   //监听音量
		{
			ini_putl(section,key,g_listen_vol,BASIC_INI_FILE);
		}

		#if ENABLE_CALL_FUNCTION
		if(strcmp(key,"Call_Volume") == 0)		   //对讲音量
		{
			ini_putl(section,key,g_call_vol,BASIC_INI_FILE);
		}
		if(strcmp(key,"Ring_Volume") == 0)		   //铃声音量
		{
			ini_putl(section,key,g_ring_vol,BASIC_INI_FILE);
		}
		#endif

		if(strcmp(key,"Settings_Password_Access") == 0)		   //设置页面需要密码访问
		{
			ini_putl(section,key,enable_password_access_settings,BASIC_INI_FILE);
		}
	}
	else if(strcmp(section,"Mic") == 0)
	{
		if(strcmp(key,"AuxIn_Enable") == 0)		   		//启用AuxIn
		{
			ini_putl(section,key,g_AuxIn_enable,BASIC_INI_FILE);
		}
		if(strcmp(key,"Mic_Sensitivity") == 0)		   //启用MIC灵敏度
		{
			ini_putl(section,key,g_mic_sensitivity,BASIC_INI_FILE);
		}
	}
	#if defined(USE_SSD212) || defined(USE_SSD202)
	else if( strcmp(section,INI_SETCION_DSP_Firmware) == 0 )
	{
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_MODEL_ID,g_device_moduleId,DSP_FIRMWARE_CONFIG_INI_FILE);

		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC0_SWITCH,dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC0_GAIN,dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC1_SWITCH,dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC1_GAIN,dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC2_SWITCH,dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC2_GAIN,dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L],DSP_FIRMWARE_CONFIG_INI_FILE);

		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0L_SWITCH,dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0L_GAIN,dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0R_SWITCH,dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0R_GAIN,dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_BT_SWITCH,dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_BT],DSP_FIRMWARE_CONFIG_INI_FILE);
		ini_putl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_BT_GAIN,dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_BT],DSP_FIRMWARE_CONFIG_INI_FILE);
	}
	#endif
	else if(strcmp(section,INI_SETCION_MFR) == 0)
	{
		if(key == NULL || strcmp(key,"SN") == 0)
			ini_puts(INI_SETCION_MFR,key,g_device_serialNum,MFR_CONFIG_INI_FILE);
	}
	return NULL;
}


void* read_sysconf(char *section,char *key)
{
#if defined(USE_PC_SIMULATOR)
	return NULL;
#endif
	//网络配置
	if( strcmp(section,INI_SECTION_BASIC) == 0 )
	{
		g_network_mode=ini_getl("Network","Connect_mode",NETWORK_MODE_LAN,BASIC_INI_FILE);
		ini_gets("Network","TcpHost_addr","************",g_host_tcp_addr_domain,63,BASIC_INI_FILE);
		g_host_tcp_port=ini_getl("Network","TcpHost_port",49888,BASIC_INI_FILE);

		ini_gets("Network","TcpHost_addr2","",g_host_tcp_addr_domain2,63,BASIC_INI_FILE);
		g_host_tcp_port2=ini_getl("Network","TcpHost_port2",0,BASIC_INI_FILE);

		g_IP_Assign=ini_getl("Network","Ip_mode",IP_ASSIGN_DHCP,BASIC_INI_FILE);
		
		#if DISBALE_NETWORK_SETTING_AND_FIXED_STATIC
		g_IP_Assign=IP_ASSIGN_STATIC;
		#endif

		ini_gets("Network","Static_IP","**************",g_Static_ip_address,30,BASIC_INI_FILE);
		ini_gets("Network","Subnet_mask","*************",g_Subnet_Mask,30,BASIC_INI_FILE);
		ini_gets("Network","GateWay","************",g_GateWay,30,BASIC_INI_FILE);
		ini_gets("Network","Primary_DNS","************",g_Primary_DNS,30,BASIC_INI_FILE);
		ini_gets("Network","Alternative_DNS","************",g_Alternative_DNS,30,BASIC_INI_FILE);

		printf("Network:Connect_mode=%d\n",g_network_mode);
		printf("Network:g_IP_Assign=%d\n",g_IP_Assign);
		printf("Network:Static_IP=%s\n",g_Static_ip_address);
		printf("Network:Subnet_mask=%s\n",g_Subnet_Mask);
		printf("Network:GateWay=%s\n",g_GateWay);
		printf("Network:Primary_DNS=%s\n",g_Primary_DNS);
		printf("Network:Alternative_DNS=%s\n",g_Alternative_DNS);


		g_PlayMode=ini_getl("MusicPlay","PlayMode",SEQUENCY_PLAY,BASIC_INI_FILE);
		stUsbInfo.playMode=ini_getl("MusicPlay","Udisk_PlayMode",BP1048_UDISK_PLAY_MODE_LIST_LOOP,BASIC_INI_FILE);
		language=ini_getl("Display","Language",DEFAULT_LANGUAGE,BASIC_INI_FILE);
		backlight_level=ini_getl("Display","Brightness",DEFAULT_BRIGHT_LEVEL,BASIC_INI_FILE);
		g_backlightTimeout=ini_getl("Display","ScreenTimeOut",6,BASIC_INI_FILE);	//默认禁止自动息屏
		paging_bell_selected=ini_getl("Bell","Paging_Bell_Selected",DEFAULT_PAGING_BELL,BASIC_INI_FILE);
		g_paging_vol=ini_getl("Other","Paging_Zone_Volume",DEFAULT_ZONE_PAGING_VOLUME,BASIC_INI_FILE);
		g_AuxIn_enable=ini_getl("Mic","AuxIn_Enable",1,BASIC_INI_FILE);			//默认启用
		g_mic_sensitivity=ini_getl("Mic","Mic_Sensitivity",11,BASIC_INI_FILE);	//默认0dB
		g_Signal_Timeout_level=ini_getl("Other","SignalTimeOut",3,BASIC_INI_FILE);	//信号超时时间

		g_listen_vol=ini_getl("Other","Listen_Volume",DEFAULT_LISTEN_OUTPUT_VOLUME,BASIC_INI_FILE);	//监听音量

		#if ENABLE_CALL_FUNCTION
		g_call_vol=ini_getl("Other","Call_Volume",DEFAULT_LISTEN_CALL_VOLUME,BASIC_INI_FILE);	 //对讲音量
		g_ring_vol=ini_getl("Other","Ring_Volume",DEFAULT_LISTEN_RING_VOLUME,BASIC_INI_FILE);	//铃声音量
		#endif

		enable_password_access_settings=ini_getl("Other","Settings_Password_Access",0,BASIC_INI_FILE);	//信号超时时间

#if SUPPORT_AUTO_TRIGGER
		//自动触发
		g_auto_trigger_enable=ini_getl("Trigger","IsEnable",0,BASIC_INI_FILE);
		ini_gets("Trigger","GroupId","",g_auto_trigger_groupId,sizeof(g_auto_trigger_groupId),BASIC_INI_FILE);
#endif

		#if defined(USE_SSD212) || defined(USE_SSD202)
		set_mic_multiplier_val();
		#endif

	}
	else if( strcmp(section,INI_SETCION_MFR) == 0 )
	{
		//读取工厂信息
		ini_gets(INI_SETCION_MFR,"SN","",g_device_serialNum,sizeof(g_device_serialNum)-1,MFR_CONFIG_INI_FILE);
	}
	#if defined(USE_SSD212) || defined(USE_SSD202)
	else if( strcmp(section,INI_SETCION_DSP_Firmware) == 0 )
	{
		g_device_moduleId=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_MODEL_ID,DEFAULT_MODULE_ID,DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC0_SWITCH,dsp_default_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC0_GAIN,dsp_default_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_R]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC1_SWITCH,dsp_default_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC1_GAIN,dsp_default_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_L]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC2_SWITCH,dsp_default_firmware_feature.module_switch[DSP_AUDIO_MODULE_ADC1_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_AMIC2_GAIN,dsp_default_firmware_feature.module_gain[DSP_AUDIO_MODULE_ADC1_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		
		dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_L]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0L_SWITCH,dsp_default_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0L_GAIN,dsp_default_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_L],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_R]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0R_SWITCH,dsp_default_firmware_feature.module_switch[DSP_AUDIO_MODULE_DAC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_DAC0R_GAIN,dsp_default_firmware_feature.module_gain[DSP_AUDIO_MODULE_DAC0_R],DSP_FIRMWARE_CONFIG_INI_FILE);
		
		dsp_firmware_feature.module_switch[DSP_AUDIO_MODULE_BT]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_BT_SWITCH,dsp_default_firmware_feature.module_switch[DSP_AUDIO_MODULE_BT],DSP_FIRMWARE_CONFIG_INI_FILE);
		dsp_firmware_feature.module_gain[DSP_AUDIO_MODULE_BT]=ini_getl(INI_SETCION_DSP_Firmware,INI_KEY_DSP_BT_GAIN,dsp_default_firmware_feature.module_gain[DSP_AUDIO_MODULE_BT],DSP_FIRMWARE_CONFIG_INI_FILE);
		
		//如果DSP_FIRMWARE_CONFIG_INI_FILE不存在，那么应立即将其保存下来，因为烧录程序需要区分是否具有相应的输入，如果不保存到文件，升级后可能会改变DSP配置
		if(!IsFileExist(DSP_FIRMWARE_CONFIG_INI_FILE))
		{
			save_sysconf(INI_SETCION_DSP_Firmware,NULL);
		}
	}
	#endif
	
#if SUPPORT_CODEC_G722
	#if 0
	g_send_decode_pcm_type=(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet)?DECODE_STANDARD_PCM:DECODE_G722;
	#else
	g_send_decode_pcm_type=(g_network_mode == NETWORK_MODE_LAN) ?DECODE_STANDARD_PCM:DECODE_G722;
	#endif
#else
	g_send_decode_pcm_type=(g_network_mode == NETWORK_MODE_LAN || !g_Is_tcp_real_internet)?DECODE_STANDARD_PCM:DECODE_G722_1;
#endif

	return NULL;
}






char *strupr(char *s)
{
	char *str;
	str = s;
	while(*str != '\0')
	{
		if(*str >= 'a' && *str <= 'z') {
			*str -= 'a'-'A';
		}
		str++;
	}
	return s;
}






/*********************************************************************
 * @fn      ScreenTimeout_Check
 *
 * @brief   屏幕自动息屏检测线程
 *
 * @param
 *
 *
 * @return
 *
 */

void* ScreenTimeout_Check()
{	
	while(1)
	{
		if(g_backlightTimeout == 6)
		{
			g_backlight_timer_count=0;
		}
		else
		{
			g_backlight_timer_count++;
			if(g_backlight_timer_count == g_backlight_threshold_array[g_backlightTimeout-1])
			{
				//close screen
				Contrl_LCDBacklight(0);
			}
		}
		
		sleep(1);
	}
}



int ScreenTimeout_Thread()
{
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)ScreenTimeout_Check, NULL);
	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}


/*
Linux 中，可以使用 kill 命令发送不同的信号类型，例如：
SIGINT（中断信号）：通常由Ctrl + C触发。用于请求进程终止。
SIGTERM（终止信号）：默认的终止信号，用于请求进程终止。(不带参数默认，和-15一样)
SIGKILL（强制终止信号）：无法被忽略或捕获的信号，用于强制中止进程。（-9）
SIGSEGV（段错误信号）：当发生无效的内存访问时，操作系统向进程发送该信号。
*/

#if defined(USE_SSD202)
// 信号处理函数
void signal_handler(int signum)
{
    // 在处理函数中执行你想要的操作，例如打印信息、记录日志等
	    if (signum == SIGTERM) {
        printf("Received SIGTERM signal\n");
        // 执行 SIGTERM 相关的操作
        
    } else if (signum == SIGINT) {
        printf("Received SIGINT signal\n");
        // 执行 SIGINT 相关的操作
        
    } else if (signum == SIGSEGV) {
        printf("Received SIGSEGV signal\n");
        // 执行 SIGSEGV 相关的操作
    }

	//设置PWM值为0，让其息屏，避免重启残影
	//backlight_adjust(0);
	//关闭屏幕
	ssd20x_mipi_powerDown();

	// 然后调用默认的信号处理函数
    struct sigaction sa;
    sa.sa_handler = SIG_DFL;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;
    sigaction(signum, &sa, NULL);
    raise(signum);
}

#endif


extern int disp_init_ok;
void MyExit(int exitId)
{
	#if defined(USE_SSD202)
	ssd20x_mipi_powerDown();
	#endif
	exit (exitId);
}