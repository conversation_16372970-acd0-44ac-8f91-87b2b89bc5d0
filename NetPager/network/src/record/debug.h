#ifndef __DEBUG_H_
#define __DEBUG_H_
 /*
#include <stdio.h>
#include <string.h>
#include "debug.h"
#include "uart.h"
//#include <sys/types.h>
//#include <sys/stat.h>

#define DBG_SEND_MAX_LEN 	30

#define DEBUG

#ifdef DEBUG

//	#define DBG(...) fprintf(stderr, " DBG(%s, %s(), %d): ", __FILE__, __FUNCTION__, __LINE__); fprintf(stderr, __VA_ARGS__)
#define DBG(...) { char _bf[DBG_SEND_MAX_LEN] = {0}; \
					fprintf(_bf, " DBG(%s, %s(), %d): ", __FILE__, __FUNCTION__, __LINE__);\
					send_array_DEBUG(_bf,strlen(_bf) ); \
					memset(_bf,0,sizeof(_bf)); \
					snprintf(_bf, sizeof(_bf)-1, __VA_ARGS__); \
					send_array_DEBUG(_bf,strlen(_bf) ); }

//	#define DBG(...) printf(__VA_ARGS__)
					
#else  
//#define DBG(...)   {send_byte('\n',UART_SEND_WAY_RS485,0);}

#endif


#endif
*/	




#include <sys/types.h>

#include <sys/stat.h>

//#define DEBUG_H

#ifdef DEBUG_H

#define DBG(...) {fprintf(stderr, " DBG(%s, %s(), %d): ", __FILE__, __FUNCTION__, __LINE__); fprintf(stderr, __VA_ARGS__);}

#else

#define DBG(...)

#endif

#define LOG(...) { \
char _bf[1024] = {0}; \
int len=snprintf(_bf, sizeof(_bf)-1,"(%s(),%d): ",__FUNCTION__, __LINE__);\
snprintf(&_bf[len],sizeof(_bf)-1-len,__VA_ARGS__);\
Systemlog(_bf);\
printf("\n%s",_bf);\
}

#endif

