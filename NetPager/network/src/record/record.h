
#ifndef __QUERYRECORD_TREATY_H__
#define __QUERYRECORD_TREATY_H__

#include "type_define.h"

//#define INT8U 	unsigned char 
//#define INT16U 	unsigned int
#define MY_SYSTEM(...) {char _bf[1024] = {0}; snprintf(_bf, sizeof(_bf)-1, __VA_ARGS__); system(_bf);}

#define PTHREAD_LOCK(MU) pthread_mutex_lock(&MU)
#define PTHREAD_UNLOCK(MU) pthread_mutex_unlock(&MU)
#define PTHREAD_INIT(MU) pthread_mutex_init(&MU, NULL)


#define RECORD_CLIENT_BUF_SIZE 	1024	//最大缓存数据
#define RECODE_QUERY_PORT 	6060

#define SUCCEED             0    /*函数返回成功*/
#define ERROR               -1   /*函数返回错误*/
#define BROADCAST_SUPPORT 	0  	//是否支持广播

#define FILE_NAME_MAX 		128

//#define RECORD_FILE_PATH 	"/mnt/yaffs2/PlayRecord/"
#define RECORD_FILE_PATH 	"/mnt/yaffs2/OperationRecord/"
#define RECORD_FILE_SIZE_MAX (512*1024) 	//记录的文件最大为512K
#define RECORD_DAY_MAX 		30

#define MERGE_FILE_PREFIX 	"Merge_Record_Play-"
#define RECORD_FILE_PREFIX 	"Record-"

#define SEND_PKG_SIZE 1024

#define RECORD_WRITE_WAIT_QUEUE_MAX 	1

typedef struct stRecord
{
	//日期,时间,AUX,节目名称,音量,
	char date[8];//20 16-10-26
	char time[8];//14:03:14
	char sourceName[10];
	char programName[128];
	char volStr[2];
}stRecord;

typedef struct MyStr
{
	char *str;
	unsigned int len;
	unsigned long fileSize;
}MyStr;

	

int IsWriteCheck();

//void WriteRecordInfo(u8 source,char *programName,char vol);
void WriteRecordInfo();


extern int Del_Record_Sort_File();


#endif

