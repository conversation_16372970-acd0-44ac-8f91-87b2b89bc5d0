

#include "ReadINI.h"
#include <string.h>
#include <stdlib.h>

#include "debug.h"

#define MYSYSTEM(...) {char _bf[1024] = {0}; snprintf(_bf, sizeof(_bf)-1, __VA_ARGS__); system(_bf);}



//List_Node * List_Head=NULL;	//头节点

/**
 * [Creat_Node 创建一个节点]
 * @return [返回一个节点的地址]
 */
List_Node * Creat_Node(unsigned char line_max)
{
	List_Node * Node;
//	char *str;
	Node = (List_Node *)malloc(sizeof(List_Node));
	if(Node == NULL)
	{
		perror("malloc");
		return Node;
	}
	Node->Str = (char *)malloc(line_max+1);
	if(Node->Str == NULL)
	{
		perror("malloc");
		free(Node);
		return NULL;
	}
	Node->Len = line_max;
//	Node->Str = str;
//	Node->Next = NULL;	//
//	DBG("Creat_Node addr:%p,str_addr_size:%d\n",Node,CONFIG_LINE_MAX+1);
	return Node;
}

int Init_List_Head(List_Node ** _head)
{
	List_Node * head;
	head = Creat_Node(1);	//头部无数据
	if(head == NULL)
		return OP_ERROR;	
	head->Prev = head;
	head->Next = head;	
	strcpy(head->Str,"");
	*_head = head;
//	DBG("head=%p, head->Next=%p\n",pNode ,pNode->Next);	
	return OP_SUCCEED;
}
/**
 * [Free_Node description]
 * @param  Node [description]
 * @return      [description]
 */
int Free_Node(List_Node ** node)
{
	List_Node * Node= *node;
	if(Node == NULL)
	{
		perror("free node");
		return OP_ERROR;
	}
	free(Node->Str);
	free(Node);
	return OP_SUCCEED;
}

/**
 * [Free_List description]
 * @param  Head [description]
 * @return      [description]
 */
int Free_List(List_Node ** head)
{
	List_Node * Head = *head;
	List_Node * pNode = Head->Next;
	List_Node * next;
	int cnt=1;
	while(strcmp(pNode->Str,"")!=0)
	{		
		next = pNode->Next;
		Free_Node(&(pNode));
		cnt++;
		pNode = next;
	}
	//printf("Free list:%d\n",cnt );
	return Free_Node(&Head);
}
/**
 * [Insert_Head 头部插入节点]
 * @param  Node [description]
 * @return      [succeed flag]
 */
int Insert_Head(List_Node ** Head,List_Node ** Node)
{
	List_Node * node = *Node;
	List_Node * head = *Head;
	if(node == NULL)
		return OP_ERROR;
	List_Node *pNode = head->Next;
	head->Next = node;
	node->Prev = head;
	node->Next = pNode;
	if(head->Prev == head)
		head->Prev = node;
	return OP_SUCCEED;

}
/**
 * [Insert_Head 尾部插入节点]
 * @param  Node [description]
 * @return      [succeed flag]
 */
int Insert_Tail(List_Node ** Head,List_Node ** Node)
{
	List_Node * node=*Node;
	List_Node * head=*Head;

	if(strcmp(head->Prev->Str,"")==0)	//空节点
	{	
		//DBG("空节点在末尾插入！\n");
		head->Next = node;
		head->Prev = node;
		node->Next = head;
		node->Prev = head;
		return OP_SUCCEED;
	}
	List_Node *pNode = head->Prev;	//保存尾节点
	head->Prev = node;	//加入新节点
	pNode->Next = node;
	node->Prev = pNode;

	node->Next = head;
	return OP_SUCCEED;
}


int Insert_Node(List_Node ** _targ,List_Node ** Node)
{
	List_Node * targ = *_targ;
	List_Node * next = targ->Next;
	List_Node * node=*Node;
	if(node == NULL)
		return OP_ERROR;
	targ->Next = node;
	node->Prev = targ;	
	next->Prev = node;
	node->Next = next;
	return OP_SUCCEED;
}

int Delete_Node(List_Node ** Node)
{
	List_Node * node=*Node;
	List_Node * prev = node->Prev;
	List_Node * next = node->Next;	
	prev->Next = next;
	next->Prev = prev;	
	
	Free_Node(Node);
	return OP_SUCCEED;

}


int  Read_File_To_List(char *path,List_Node ** head)
{
         FILE * fp;
         char str1[20];
         fp = fopen(path,"rt");
         if(fp == NULL)
         {
                   MYSYSTEM("touch %s",path);
                   fp = fopen(path,"rt");
                   if(fp == NULL)
                   {
                            sprintf(str1,"fopen %s",path);
                            perror(str1);
                            return OP_ERROR;
                   }

         }
         char str[CONFIG_LINE_MAX];                 //每行最大128
         List_Node *pNode;
         List_Node * L_Head=*head;
         int list_len=1;
         int ret=0;
         //char *pStr=NULL;
         while( !feof(fp) )
         {
                   memset(str,0,CONFIG_LINE_MAX);
                   if(fgets(str,CONFIG_LINE_MAX,fp)==NULL)  //无空格
                            break;
			    #if 0
                   pStr = str;
                   while(*pStr)
                   {
                            if(*pStr == '\n')
                                     *pStr = '\0';
                            pStr++;
                   }
		   #endif
         //      printf(" str:%s,len=%d\n",str,strlen(str));
                   pNode = Creat_Node(strlen(str));
                   if(pNode == NULL)
                   {
                            perror("Creat_Node");    //申请内存错误
                            fclose(fp);
                            Free_List(head);
                            return OP_ERROR;
                   }
                   strcpy(pNode->Str,str);
                   list_len++;
         //      DBG("Read file ok\n");
         //      DBG(" L_Head=%p,L_Head->Prev=%p,pNode->Next=%p\n",L_Head,L_Head->Prev,pNode->Next);
                   Insert_Tail(head,&pNode);
         //      DBG(" L_Head=%p,L_Head->Prev=%p,pNode->Next=%p\n",L_Head,L_Head->Prev,pNode->Next);
         }
         fclose(fp);
         DBG("Read_File_To_List done list_len:%d\n",list_len);
         return list_len;
}


void Display_List(List_Node * head)
{
	#ifndef DEBUG
		return;
	#endif
	List_Node *pNode = head->Next;
//	DBG("head->Str = %s\n",head->Str);
	DBG("List:\n");
	while(strcmp(pNode->Str,"")!=0)
	{
		printf(" %s",pNode->Str );
		pNode = pNode->Next;
	}	
	printf("----END\n");
}


int Save_List_To_File(char *path,List_Node ** head)
{
	FILE * fp;
	fp = fopen(path,"w");
	if(fp == NULL)
	{		
		char str[20];
		sprintf(str,"fopen %s",path);
		perror(str);
		return OP_ERROR;
	}
	List_Node *pNode = *head;
	pNode = pNode->Next;
	while(strcmp(pNode->Str,"") != 0 )
	{		
		fprintf(fp,"%s",pNode->Str);
		pNode = pNode->Next;	
	}
	fclose(fp);
	return OP_SUCCEED;
}

int _Read_Config_Key(const char * header,char const *_Key,unsigned int * value,char * str)
{
	char head[50];
	char Key[50];	
	List_Node * ListHead;
	Init_List_Head(&ListHead);
	if(ListHead == NULL)
		return OP_ERROR;
	sprintf(Key,"%s=",_Key);
//	DBG("ListHead=%p, ListHead->Next=%p\n",ListHead ,ListHead->Next);
	if(Read_File_To_List(CONFIG_PATH,&ListHead) < 0)
		return OP_ERROR;
//	Display_List(ListHead);
	sprintf(head,"[%s]",header);
	List_Node * pNode = ListHead;
	pNode=pNode->Next;
	int find_header_flag=0;
	char * pStr=NULL;
	while(strcmp(pNode->Str,"") != 0 )
	{		
		if(!find_header_flag)
		{
			if(strstr(pNode->Str,head))//find head
			{
				find_header_flag = 1;			
			}
			pNode = pNode->Next;
			continue;
		}
		if(strstr(pNode->Str,Key))//find key ok
		{
			pStr = pNode->Str;
			while(*pStr)//去掉换换行符
			{
				if(*pStr == '\n')
					*pStr = '\0';
				if(pStr - pNode->Str >= strlen(pNode->Str))
				{
					break;
				}
				pStr++;
			}
			if(value == NULL)
			{

				if((char *)strtok(pNode->Str,"=") !=NULL)
				{
					pStr = (char *)strtok(NULL,"=");
					if(pStr != NULL)
						sprintf(str,"%s",pStr);
					else
						sprintf(str,"");
				}
				//sscanf(pNode->Str,"%*[^=]=%s",(char *)str);	//过滤第一个遇到=号的字符串:%*[^=]=
			}
			else	
				sscanf(pNode->Str,"%*[^=]=%d",(unsigned int *)value);	//过滤第一个遇到=号的字符串:%*[^=]=

			//printf("Key:%s, pNode->Str:%s\n",Key,pNode->Str );
			//if(value == NULL)
			//	printf("Value:%s,len=%d\n",str,strlen(str));
			return Free_List(&ListHead);
		}
		if(strstr(pNode->Str,"["))
		{
			if(strstr(pNode->Str,"]"))
			{
				Free_List(&ListHead);
				printf("Find Key:%s Failed!\n",_Key);
				return OP_KEY_FIND_FAIL;
			}
		}
		pNode = pNode->Next;

	}
	Free_List(&ListHead);
	if(!find_header_flag)
	{
		printf("Find Group:%s Failed!\n",header);
		return OP_HEAD_FIND_FAIL;
	}
	return OP_SUCCEED;
}

int Save_Config_Key(const char * header,const char *Key,const char * Str,unsigned int const value)
{
	char head[50];
	List_Node * ListHead;
	Init_List_Head(&ListHead);
	if(ListHead == NULL)
		return OP_ERROR;	
//	DBG("ListHead=%p, ListHead->Next=%p\n",ListHead ,ListHead->Next);
	if(Read_File_To_List(CONFIG_PATH,&ListHead) < 0)
		return OP_ERROR;
	Display_List(ListHead);
	sprintf(head,"[%s]",header);
	List_Node * pNode = ListHead;
	List_Node * New_Node;
	int find_header_flag=0;
	char str[CONFIG_LINE_MAX];
	pNode=pNode->Next;
	while(strcmp(pNode->Str,"") != 0 )
	{		
		if(!find_header_flag)
		{
			if(strstr(pNode->Str,head))//find head
			{
				find_header_flag = 1;			
			}
			pNode = pNode->Next;
			continue;
		}
		if(strstr(pNode->Str,Key))//find key ok
		{	
			if(Str == NULL) 
				New_Node = Creat_Node(sprintf(str,"%s=%d\n",Key,value)); //一行
			else 
				New_Node = Creat_Node(sprintf(str,"%s=%s\n",Key,Str));//一行
			if(New_Node == NULL)
			{
				Free_List(&ListHead);
				return OP_ERROR;
			}
			strcpy(New_Node->Str,str);		
			Insert_Node(&(pNode->Prev),&New_Node);			
			Display_List(ListHead);
			Delete_Node(&pNode);
			//Display_List(ListHead);
			Save_List_To_File(CONFIG_PATH,&ListHead);	
			Free_List(&ListHead);
			return OP_SUCCEED;
		}
		if(strstr(pNode->Str,"["))
		{
			if(strstr(pNode->Str,"]")) //无此key值则在下一个小节名前增加该值
			{
				if(Str == NULL) 
					New_Node = Creat_Node(sprintf(str,"%s=%d\n",Key,value)); //一行
				else 
					New_Node = Creat_Node(sprintf(str,"%s=%s\n",Key,Str));//一行
				if(New_Node == NULL)
				{
					Free_List(&ListHead);
					return OP_ERROR;
				}
				strcpy(New_Node->Str,str);
				Insert_Node(&(pNode->Prev),&New_Node);			
				Save_List_To_File(CONFIG_PATH,&ListHead);
				Free_List(&ListHead);
				return OP_SUCCEED;
			}
		}
		pNode = pNode->Next;
	}	
	if(!find_header_flag)	//未找到小节名，增加一个
	{
		DBG("未找到小节名，增加一个!\n");
		New_Node = Creat_Node(sprintf(str,"%s\n",head)); //新增一行
		if(New_Node == NULL)
		{
			Free_List(&ListHead);
			return OP_ERROR;
		}
		strcpy(New_Node->Str,str);
		Insert_Tail(&ListHead,&New_Node);
		if(Str == NULL) 
			New_Node = Creat_Node(sprintf(str,"%s=%d\n",Key,value)); //一行
		else 
			New_Node = Creat_Node(sprintf(str,"%s=%s\n",Key,Str));//一行
		if(New_Node == NULL)
		{
			Free_List(&ListHead);
			return OP_ERROR;
		}
		strcpy(New_Node->Str,str);
		Insert_Tail(&ListHead,&New_Node);
		Save_List_To_File(CONFIG_PATH,&ListHead);
		Free_List(&ListHead);
	}else 					//找到小节名在文件最后一个
	{
		//DBG("未找到key，文件最后增加一个!\n");
		if(Str == NULL) 
			New_Node = Creat_Node(sprintf(str,"%s=%d\n",Key,value)); //一行
		else 
			New_Node = Creat_Node(sprintf(str,"%s=%s\n",Key,Str));//一行
		if(New_Node == NULL)
		{
			Free_List(&ListHead);
			return OP_ERROR;
		}
		strcpy(New_Node->Str,str);		
		Insert_Tail(&ListHead,&New_Node);
		Save_List_To_File(CONFIG_PATH,&ListHead);
		Free_List(&ListHead);
	}
	return OP_SUCCEED;
}

int Delete_Config_Key(char const* group,char const*Key)
{
	char head[50];
	List_Node * ListHead;
	Init_List_Head(&ListHead);
	if(ListHead == NULL)
		return OP_ERROR;	
//	DBG("ListHead=%p, ListHead->Next=%p\n",ListHead ,ListHead->Next);
	if(Read_File_To_List(CONFIG_PATH,&ListHead) < 0)
		return OP_ERROR;
	Display_List(ListHead);
	sprintf(head,"[%s]",group);
	List_Node * pNode = ListHead;
	List_Node * New_Node;
	int find_header_flag=0;
	char str[CONFIG_LINE_MAX];
	pNode=pNode->Next;
	while(strcmp(pNode->Str,"") != 0 )
	{
		if(!find_header_flag)
		{
			if(strstr(pNode->Str,head))//find head
			{
				find_header_flag = 1;			
			}
			pNode = pNode->Next;
			continue;
		}
		if(strstr(pNode->Str,Key))//find key ok
		{
			Delete_Node(&pNode);
			Save_List_To_File(CONFIG_PATH,&ListHead);
			Free_List(&ListHead);
			printf("Delete Key:%s Success!\n",Key);
			return OP_SUCCEED;
		}
		if(strstr(pNode->Str,"["))
		{
			if(strstr(pNode->Str,"]")) //无此key值则在下一个小节名前增加该值
			{
				printf("No find Key:%s\n",Key);
				Free_List(&ListHead);
				return OP_KEY_FIND_FAIL;
			}
		}
		pNode = pNode->Next;
	}
	if(!find_header_flag)	//未找到小节名，增加一个
	{
		printf("No find Group:%s\n",group);
		Free_List(&ListHead);
		return OP_HEAD_FIND_FAIL;
	}else 					//找到小节名在文件最后一个
	{
		printf("No find Key:%s\n",Key);
		Free_List(&ListHead);
		return OP_KEY_FIND_FAIL;
	}	
	return OP_SUCCEED;
}

int Delete_Config_Group(char const* group)
{
	char head[50];
	List_Node * ListHead;
	Init_List_Head(&ListHead);
	if(ListHead == NULL)
		return OP_ERROR;	
//	DBG("ListHead=%p, ListHead->Next=%p\n",ListHead ,ListHead->Next);
	if(Read_File_To_List(CONFIG_PATH,&ListHead) < 0)
		return OP_ERROR;
	Display_List(ListHead);
	sprintf(head,"[%s]",group);
	List_Node * pNode = ListHead;
	List_Node * New_Node;
	int find_header_flag=0;
	char str[CONFIG_LINE_MAX];
	pNode=pNode->Next;
	while(strcmp(pNode->Str,"") != 0 )
	{
		if(!find_header_flag)
		{
			if(strstr(pNode->Str,head))//find head
			{
				find_header_flag = 1;
				New_Node = pNode->Prev;
				Delete_Node(&pNode);
				pNode = New_Node;
			}
			pNode = pNode->Next;
			continue;
		}	
		if(strstr(pNode->Str,"["))
		{
			if(strstr(pNode->Str,"]")) //无此key值则在下一个小节名前增加该值
			{
				Save_List_To_File(CONFIG_PATH,&ListHead);
				Free_List(&ListHead);
				printf("Delete Group:%s Success!\n",group);
				return OP_SUCCEED;
			}
		}
		New_Node = pNode->Prev;
		Delete_Node(&pNode);
		pNode = New_Node;
		pNode = pNode->Next;
	}
	if(!find_header_flag)	//未找到小节名，增加一个
	{
		printf("No find Group:%s\n",group);
		Free_List(&ListHead);
		return OP_HEAD_FIND_FAIL;
	}
	Save_List_To_File(CONFIG_PATH,&ListHead);
	Free_List(&ListHead);
	printf("Delete Group:%s Success!\n",group);
	return OP_SUCCEED;
}

#if 0
int main(int argc, char const *argv[])
{
	char val[100];
	SAVE_CONFIG_KEY_STR("head1","key31","ABC9");
	SAVE_CONFIG_KEY_STR("head2","key32","ABC98");
	SAVE_CONFIG_KEY_STR("head3","key33","ABC987");
	SAVE_CONFIG_KEY_STR("head4","key34","ABC9876");
	SAVE_CONFIG_KEY_STR("head5","key35","ABC98765");
	SAVE_CONFIG_KEY_STR("head6","key36","ABC987654");
	int aa;
	READ_CONFIG_KEY_STR("head1","key39658",val);
	READ_CONFIG_KEY_INT("head9","VAL",&aa);
	Delete_Config_Key("head2","key39658");
	Delete_Config_Group("head2");
	printf("val:%s,aa=%d\n",val,aa );
	return 0;
}

#endif
