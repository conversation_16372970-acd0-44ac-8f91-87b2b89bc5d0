
/********************************************************************************
  * @file		*
  * <AUTHOR>
  * @version 	*
  * @date    	*
  * @brief   	*
  *          
  *          
  ******************************************************************************
  * @attention 
  * 保存播放记录的文件格式：//日期,时间,AUX,节目名称,音量,
  *
  *
  *
  * 
*******************************************************************************/

#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <stdio.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <stdlib.h>
#include <linux/types.h>
//
#include <string.h>
#include <signal.h>
#include <errno.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <net/if.h>
#include <pthread.h>
#include <dirent.h>

#include "record.h"
#include "debug.h"
#include "Auxdio_protocol.h"
#include "network_client.h"
#include "sysconf.h"


/*****************    本地常量声明    ******************/
static stRecord RecordQueueBuf[RECORD_WRITE_WAIT_QUEUE_MAX];
static u8  Pos=0;	
static u8 WaitWriteCount=0;

char * sourceNameBuf[5]={};
pthread_mutex_t pmWriteFile = PTHREAD_MUTEX_INITIALIZER;	//文件写入锁
static u8 sWrite_Log_Flag = 0;

/*****************    本地变量声明    ******************/


/*****************  外部函数和变量声明 *****************/
#define MY_SYSTEM(...) {char _bf[1024] = {0}; snprintf(_bf, sizeof(_bf)-1, __VA_ARGS__); system(_bf);}

/**
 * [IsWriteCheck 检测当前是否可写入]
 * @return [description]
 */
int IsWriteCheck()
{
	if(!WaitWriteCount)
	{
		return 1;
	}
	return 0;
}

/*

void My_System(const char *format, ...)
{
	char _bf[1024] = {0}; 
	snprintf(_bf, sizeof(_bf)-1, format);
	DBG("%s\n",_bf);
	system(_bf);	
}
*/
void Get_Local_date(char *buf,int len)
{
	time_t rawtime; 
	struct tm * timeinfo; 
	time ( &rawtime ); 
	timeinfo = localtime ( &rawtime ); 
	strftime( buf, len,"%Y-%m-%d",timeinfo );
//	DBG("Date:%s\n",buf);
}
void Get_Local_time(char *buf,int len)
{
	time_t rawtime; 
	time ( &rawtime ); 
	strftime( buf, len, "%X",localtime(&rawtime));
//	DBG("time:%s\n",buf);
}

int Find_Dir_File_Exist(const char * dir,const char *filename)
{
	struct dirent *entry;
	DIR *dpDir;

	dpDir =opendir(dir); //打开目录
	if(dpDir == NULL)
	{
		printf("opendir dir error!!!\n");
		return -1;
	}  
	while((entry = readdir(dpDir)) != NULL)
	{
		//过滤隐藏文件
		if(strcmp(".",entry->d_name)==0 || strcmp("..",entry->d_name)==0)   //比较字符串
			continue;
		if(strcmp(filename,entry->d_name)==0) //查找文件名
		{
			closedir(dpDir);
			return 1;
		}
	}
	closedir(dpDir);
	return 0;
}

/*********************************************************************
 * @function:  Del_Dir_File_By_Include_Str
 *
 * @brief   :	删除目录下包含指定字符串的文件名
 *
 * @arguments:  dir：目录 NameStr :需对比的字符串	
 *       		
 *
 * @return  : 返回删除的文件个数 错误返回-1
 *					 
 *********************************************************************/ 

int Del_Dir_File_By_Include_Str(const char * dir,const char *NameStr)
{
	struct dirent *entry;
	DIR *dpDir;
	int count=0;
	dpDir =opendir(dir); //打开目录
	if(dpDir == NULL)
	{
		printf("opendir dir error!!!\n");
		return -1;
	} 
	while((entry = readdir(dpDir)) != NULL)
	{
		//过滤隐藏文件
		if(strcmp(".",entry->d_name)==0 || strcmp("..",entry->d_name)==0)   //比较字符串
			continue;
		if(strstr(entry->d_name,NameStr) != NULL)//文件名包含有对比的字符串
		{
			printf("\nDelete file:%s%s\n",dir,entry->d_name);
			count++;
			MY_SYSTEM("rm %s%s",dir,entry->d_name);
		}
	}
	closedir(dpDir);
	return count;
}

/**
 * [Creat_Date_File_Name 自动创建文件名]
 * @param filename [description]
 */
void Creat_Date_File_Name(char *filename)
{
	char buf[FILE_NAME_MAX] ={0};
	Get_Local_date(buf,FILE_NAME_MAX);
	if(sWrite_Log_Flag)
	{
		sWrite_Log_Flag = 0;
		sprintf(filename,"%s%slog-%s.txt",RECORD_FILE_PATH,RECORD_FILE_PREFIX,buf);
	}else
	{
		sprintf(filename,"%s%s%s.txt",RECORD_FILE_PATH,RECORD_FILE_PREFIX,buf);
	}

}

/**
 * [WriteRecordInfo 向文件写入操作信息]
 * @param source      [description]
 * @param programName [description]
 * @param vol         [description]
 */
void WriteRecordInfo()
{
	FILE *fp;
	char path[FILE_NAME_MAX]={0};

	PTHREAD_LOCK(pmWriteFile);
	sWrite_Log_Flag = 0;
	Creat_Date_File_Name(path);
//	printf ( "/007The current date/time is: %s", asctime (timeinfo) ); 	
	fp = fopen(path,"at");
	if(fp == NULL)
	{		
		perror("open file");
		MY_SYSTEM("mkdir -p %s",RECORD_FILE_PATH);//判断 erron 创建目录
		fp = fopen(path,"at ");
		if(fp == NULL)
		{
			perror("open file");
			PTHREAD_UNLOCK(pmWriteFile);
			return ;
		}
	}
	if(Get_File_Size(path)> RECORD_FILE_SIZE_MAX)
	{
		printf("File:%s Over Size!\n",path);
		fclose(fp);
		PTHREAD_UNLOCK(pmWriteFile);
		return;
	}
	char buf[128] = {0};
	char date_tmp[30]={0};
	char time_tmp[30]={0};
	Get_Local_date(date_tmp,sizeof(date_tmp));
	Get_Local_time(time_tmp,sizeof(time_tmp));
	////日期,时间,AUX,节目名称,音量,
//	printf("date_tmp = %s\n", date_tmp);
	int len=0;
#if 0
	if(programName==NULL)
	{
		//len = sprintf(buf,"%s,%s,%s,,%d\n",date_tmp,time_tmp,PrioritySourceName[source],vol);

	}else
	{
		//len = sprintf(buf,"%s,%s,%s,%s,%d\n",date_tmp,time_tmp,PrioritySourceName[source],programName,vol);
	}
#endif

	if(Paging_status == PAGING_START)
	{
		len=sprintf(buf,"%s,%s,%s\r\n",date_tmp,time_tmp,"开启寻呼");
	}
	else
	{
		len=sprintf(buf,"%s,%s,%s\r\n",date_tmp,time_tmp,"关闭寻呼");
	}

	if(fwrite(buf, len,1, fp) < 0)
	{
		DBG("write %s Failed!\n",path);
		perror("write file");
	} 
	fclose(fp);
	PTHREAD_UNLOCK(pmWriteFile);
}



/**
 * [WriteRecordInfo 向文件写入操作信息]
 * @param source      [description]
 * @param programName [description]
 * @param vol         [description]
 */
void Systemlog(char * _log)
{
	FILE *fp=NULL;
	static unsigned int writeCount=0; //记录文件写入条数
	static char filePath[128]={0};
	char path[FILE_NAME_MAX]={0};

	PTHREAD_LOCK(pmWriteFile);
	sWrite_Log_Flag = 1;
	Creat_Date_File_Name(path);
#if 1
	if(writeCount==0||strcmp(path,filePath))	//首次读取最后一条记录计数
	{
		strcpy(filePath,path);
		char cmd[128]={0};
		char readBuf[128]={0};
		sprintf(cmd,"tail -n 1 %s|cut -d . -f1",path);
		printf("cmd>>%s\n", cmd);
		fp=popen(cmd,"r");
		if(fp==NULL)
		{
			writeCount = 0;
			perror("open tail cmd");
		}else
		{
			if(fgets(readBuf, sizeof(readBuf), fp) <= 0)
			{
				writeCount = 0;
				perror("fgets fp");
			}else
			{
				printf("last str:%s\n", readBuf);
				int i=0;
				for (i = 0; i < strlen(readBuf); ++i)
				{
					if(!isdigit(readBuf[i]) && readBuf[i]!= '\n' )
						break;
				}
				if(i == strlen(readBuf))	//所有字符为数字
				{
					writeCount =atoi(readBuf);
					if(writeCount<0)
					{
						printf("writeCount<0\n");
						writeCount = 0;
					}
				}else{
					printf("readBuf not digit!\n");
					writeCount = 0;
				}
				printf("最后一条记录计数:%d\n",writeCount);
			}
			pclose(fp);
		}
	}
#endif
//	printf ( "/007The current date/time is: %s", asctime (timeinfo) ); 	
	fp = fopen(path,"at");
	if(fp == NULL)
	{		
		perror("open file");
		MY_SYSTEM("mkdir -p %s",RECORD_FILE_PATH);//判断 erron 创建目录
		fp = fopen(path,"at ");
		if(fp == NULL)
		{
			perror("open file");
			PTHREAD_UNLOCK(pmWriteFile);
			return ;
		}
	}
	if(Get_File_Size(path)> RECORD_FILE_SIZE_MAX)
	{
		printf("File:%s Over Size!\n",path);
		fclose(fp);
		PTHREAD_UNLOCK(pmWriteFile);
		return;
	}
	char buf[1024]={0};
//	char date_tmp[30]={0};
	char time_tmp[30]={0};
//	Get_Local_date(date_tmp,sizeof(date_tmp));
	Get_Local_time(time_tmp,sizeof(time_tmp));
	int len = sprintf(buf,"%d.[%s] %s\r\n",++writeCount,time_tmp,_log);

	////日期,时间,AUX,节目名称,音量,
	if(fwrite(buf, len,1, fp) < 0)
	{
		DBG("write %s Failed!\n",path);
		perror("write file");
	}
	fclose(fp);
	PTHREAD_UNLOCK(pmWriteFile);
}





/*********************************************************************
 * @function 	*
 *
 * @brief   	*
 *				
 * @arguments  	*
 *
 * @return 		*
 *********************************************************************/
int Merge_File(const char *dest_file,const char *sour_file)
{
	FILE *dest_fp;
	FILE *sour_fp;
	DBG("Merging... File %s To %s !\n",sour_file,dest_file);
	dest_fp = fopen(dest_file,"at"); //打开合并后的文件
	if(dest_fp == NULL)
	{	
		DBG("open %s\n",dest_file);
		perror("open file");
		return ERROR;
	}
	sour_fp = fopen(sour_file,"rt"); //打开合并后的文件
	if(sour_fp == NULL)
	{
		DBG("open %s\n",sour_file);
		fclose(dest_fp);
		perror("open file");
		return ERROR;
	}
//	DBG("Merging... File %s To %s!\n",sour_file,dest_file);
	//打开文件后开始合并文
	char merge_file_buf[1024*1024]={0};//1M的空间
	char *merge_file_p = merge_file_buf;
	INT16U size=0;
	while(!feof(sour_fp))
	{
		if(size++ >= (1024*1024 - 128)) //超出文件读取大小则取完整一行
		{
			if( *merge_file_p == '\n')
				break;
		}
		*merge_file_p++=fgetc(sour_fp);
	}
	*merge_file_p = '\0';
//	fputs(merge_file_buf,dest_fp); 
	if(fwrite(merge_file_buf,size-1,1, dest_fp) < 0)
	{
		DBG("write %s Failed!\n",dest_file);
		perror("write file");
		fclose(dest_fp);
		fclose(sour_fp);
		return -1;
	}
	fclose(dest_fp);
	fclose(sour_fp);
	return SUCCEED;
}

// merge 合并
/*********************************************************************
 * @function 	*
 *
 * @brief   	* 合并目录下的多个txt文件
 *				
 * @arguments  	* 
 *
 * @return 		*
 *********************************************************************/

#if 0
int Merge_Dir_Mult_File(char *Merge_dir,int Mult_File_max)
{
	struct dirent *entry;
	DIR *dpDir;
	int merge_count=0;
	FILE *fp;
	FILE *Merge_fp;
	char tmp[FILE_NAME_MAX]={0};
	char file_tmp[FILE_NAME_MAX]={0};
	char date_tmp[30]={0};  

	dpDir =opendir(Merge_dir); //打开目录
	if(dpDir == NULL)
	{
		printf("opendir Merge_dir error!!!\n");
		return -1;
	}
	Get_Local_date(date_tmp,sizeof(date_tmp));
	sprintf(tmp,"%s%s%s.txt",Merge_dir,MERGE_FILE_PREFIX,date_tmp); //创建一个合并后的文件名 
	Del_Dir_File_By_Include_Str(Merge_dir,MERGE_FILE_PREFIX);	//删除旧文件

	while((entry = readdir(dpDir)) != NULL)
	{
		//过滤隐藏文件
		if(strcmp(".",entry->d_name)==0 || strcmp("..",entry->d_name)==0)   //比较字符串
			continue;
		if(strstr(entry->d_name,".txt")!=NULL || strstr(entry->d_name,".TXT")!=NULL) //获取文本文件
		{			
			if(strstr(entry->d_name,RECORD_FILE_PREFIX) == NULL)//为记录信息文件
				continue;
			DBG("Merge_dir... entry->d_type=%d entry->d_name=%s\n", entry->d_type, entry->d_name);
			sprintf( file_tmp,"%s%s",Merge_dir,entry->d_name);
			Merge_File(tmp,file_tmp); 	//合并一个文件
			if(++merge_count >= Mult_File_max)
			{
				break;
			}
		}else
		{
			DBG("No Match File Type:%s!\n",entry->d_name);
		}

	}
	closedir(dpDir);   //关闭目录
	return 1;
}
#else
int Merge_Dir_Mult_File(int Mult_File_max,char *Merged_name)
{
	FILE * fp;
	char buf[128]={0};
	char tmp[FILE_NAME_MAX]={0};
	char date_tmp[30]={0};  
	char *pbuf;
	int count=0;
	int ret=SUCCEED;
	int del_count=0;
	count = Get_Record_Count();	//得到当前目录的所有记录文件个数
	DBG("Finded %d file!\n",count);
	if(count > RECORD_DAY_MAX)
		Del_Record_Sort_File();

	sprintf(buf,"find %s|sort",RECORD_FILE_PATH);
	fp = popen(buf,"r");
	if (fp == NULL)
	{
		perror("find|sort:popen ERROR!");
		return ERROR;
	}
	Get_Local_date(date_tmp,sizeof(date_tmp));
	sprintf(tmp,"%s%s%s.txt",RECORD_FILE_PATH,MERGE_FILE_PREFIX,date_tmp); //创建一个合并后的文件名 
	Del_Dir_File_By_Include_Str(RECORD_FILE_PATH,MERGE_FILE_PREFIX);	//删除旧文件
	//删除最前的多除文件
	while(!feof(fp))
	{
		memset(buf,0,sizeof(buf));
		if(fgets(buf,sizeof(buf),fp) != NULL)
		{
			if(strstr(buf,RECORD_FILE_PREFIX) != NULL)//文件名包含有对比的字符串
			{				
				pbuf = buf;
				while(*pbuf != '\n')
				{
					*pbuf++;
				}
				*(pbuf) = '\0';
				DBG("Read File %s\n",buf);
				if(Merge_File(tmp,buf) >= 0)
				{					
					del_count++;
				}else
				{
					printf("\nMerge_File Failed.\n");
					ret = ERROR;
					break;
				}
				if(del_count >= RECORD_DAY_MAX)
				{
					ret = ERROR;
					break;
				}
			}
		}else
		{
			printf("\nfgets Failed!\n");
			ret = ERROR;
			break;
		}
	}
	DBG("Merged %d File!\n",del_count);
	strcpy(Merged_name,tmp);
	pclose(fp);
	return ret;
}
#endif



int GetRecordFileList(MyStr * list)
{
	struct dirent *entry;
	int Record_Count=0;
	int ret= -1;
	int pos=0;
	char path[128]={0};	
	struct dirent **namelist;
	int n;
	n = scandir(RECORD_FILE_PATH, &namelist, NULL, alphasort);
	if (n < 0)
		perror("scandir");
	else {
		while (n--) {
			//过滤隐藏文件
			if(strcmp(".",namelist[n]->d_name)==0 || strcmp("..",namelist[n]->d_name)==0)   //比较字符串
				continue;
			if(strstr(namelist[n]->d_name,RECORD_FILE_PREFIX)!=NULL) //查找文件名
			{
				Record_Count++;
				DBG("scandir file name:%s\n", namelist[n]->d_name);
				//strcpy((*list)[pos++],entry->d_name);	//拷贝文件名				
				strcpy(list->str,namelist[n]->d_name);
				list->len = strlen(namelist[n]->d_name);
				memset(path,0,128);
				sprintf(path,"%s%s",RECORD_FILE_PATH,namelist[n]->d_name);
				list->fileSize = Get_File_Size(path);
				list++;
				if(Record_Count>RECORD_DAY_MAX)
				{
					printf("\nFile List Over %d.\n",RECORD_DAY_MAX );
					break;
				}
			}

			free(namelist[n]);
		}
		free(namelist);
	}

#if 0
	DIR *dpDir;
	dpDir =opendir(RECORD_FILE_PATH); //打开目录
	if(dpDir == NULL)
	{
		printf("opendir %s error!!!\n",RECORD_FILE_PATH);
		return ret;
	}  
	while((entry = readdir(dpDir)) != NULL)
	{
		//过滤隐藏文件
		if(strcmp(".",entry->d_name)==0 || strcmp("..",entry->d_name)==0)   //比较字符串
			continue;
		if(strstr(entry->d_name,RECORD_FILE_PREFIX)!=NULL) //查找文件名
		{
			Record_Count++;
			printf("entry->d_name:%s\n", entry->d_name);
			//strcpy((*list)[pos++],entry->d_name);	//拷贝文件名
			
			strcpy(list->str,entry->d_name);
			list->len = strlen(entry->d_name);
			memset(path,0,128);
			sprintf(path,"%s%s",RECORD_FILE_PATH,entry->d_name);
			list->fileSize = Get_File_Size(path);
			list++;
			if(Record_Count>RECORD_DAY_MAX)
			{
				printf("\nFile List Over 7.\n" );
				break;
			}
		}
	}
	closedir(dpDir);
#endif
	return Record_Count;
}


int Get_Record_Count()
{
	struct dirent *entry;
	DIR *dpDir;
	int Record_Count=0;
	int ret= -1;
	dpDir =opendir(RECORD_FILE_PATH); //打开目录
	if(dpDir == NULL)
	{
		printf("opendir %s error!!!\n",RECORD_FILE_PATH);
		return ret;
	}  
	while((entry = readdir(dpDir)) != NULL)
	{
		//过滤隐藏文件
		if(strcmp(".",entry->d_name)==0 || strcmp("..",entry->d_name)==0)   //比较字符串
			continue;
		if(strstr(entry->d_name,RECORD_FILE_PREFIX)!=NULL) //查找文件名
		{
			Record_Count++;
		}
	}
	closedir(dpDir);
	return Record_Count;
}

/*
int Del_Record(char *dir)
{

	if(Get_Record_Count() >= RECORD_DAY_MAX)
	{
		DBG("Record File Over %d!\n",RECORD_DAY_MAX);
		ret = Del_Dir_File_By_Include_Str(dir,RECORD_FILE_PREFIX);	//删除目录下所有包含指定字符串的文件
	}else
	{
		DBG("Record File Normal!\n");
		ret = 1;
	}

	return ret;
}*/

/**
 * [Del_Record_Sort_File 删除设定天数外的文件]
 * @return [description]
 */
int Del_Record_Sort_File()
{
	FILE * fp;
	char buf[128]={0};
	char *pbuf;
	int count=0;
	int del_count=0;
	count = Get_Record_Count();	//得到当前目录的所有记录文件个数
	DBG("Finded %d file!\n",count);
	if(count > RECORD_DAY_MAX)
	{
		sprintf(buf,"find %s|sort",RECORD_FILE_PATH);
		fp = popen(buf,"r");
		if (fp == NULL)
		{
			perror("find|sort:popen ERROR!");
			return ERROR;
		}
		DBG("Delet...\n");
		//删除最前的多除文件
		while(!feof(fp))
		{
			memset(buf,0,sizeof(buf));
			if(fgets(buf,sizeof(buf),fp) != NULL)
			{				
				if(strstr(buf,RECORD_FILE_PREFIX) != NULL)//文件名包含有对比的字符串
				{
					pbuf = buf;
					while(*pbuf != '\n')
					{
						*pbuf++;
					}
					*(pbuf) = '\0';
					DBG("Read File %s\n",buf);
					MY_SYSTEM("rm -rf %s",buf);
					del_count++;
					if(del_count >= count - RECORD_DAY_MAX)
					{
						break;
					}
				}
			}else
			{	
				DBG("fgets Failed!\n");
				break;
			}
		}
		DBG("Del %d File!\n",del_count);
		pclose(fp);
	}
	return 1;
}



//以下通讯函数


void Out_array(INT8U *buf,INT16U len)
{
#ifdef DEBUG
	int i=0;
	printf("\nbuff:");
	for (i = 0; i <len ; ++i)
	{
		printf("%x ",buf[i]);
	}
	printf("\n");
#endif

}

char Current_Update_FilePath[128]={0};
char Current_Update_FileName[30]={0};
FILE * fpCurrent_Update_File=NULL;//当前正在打开上传的文件描述符
int Update_File_fseek=0;		//文件上传文件的偏移位置
unsigned long  Update_File_Size=0;
int  Update_File_Pkg_id = 0;		//当前上传包id
int  Update_File_Pkg_Total_id = 0;//总id

void CleanUpdateFileInfo()
{
	memset(Current_Update_FilePath,0,sizeof(Current_Update_FilePath));
	
	fpCurrent_Update_File=NULL;//当前正在打开上传的文件描述符
	Update_File_fseek=0;		//文件上传文件的偏移位置
	Update_File_Size=0;
	Update_File_Pkg_id = 0;
	Update_File_Pkg_Total_id=0;
}

/**
 * [SendRecordListToHost 主机获取设备记录文件列表]
 * @param pkg [description]
 */
void TcpSendRecordListToHost(u8 * pkg)
{
	MyStr File_List[RECORD_DAY_MAX]={0};
	char listBuf[RECORD_DAY_MAX][128]={0};
	int i = 0;
	for (; i < RECORD_DAY_MAX; ++i)
	{
		File_List[i].str=listBuf[i];
		File_List[i].len=0;
		File_List[i].fileSize = 0;
	}
	unsigned char sendBuf[1500]={0};//MAX_BUF_SIZE
	unsigned char DataBuf[129*RECORD_DAY_MAX + 1]={0};//文件名长度128 + 1其他数据
	unsigned char * pData=DataBuf;
	int Record_Count=0;
	int File_Total_ID=0;//文件总ID
	int client = pkg[4];
	Del_Record_Sort_File();//先删除多余日期文件.
	Record_Count = GetRecordFileList(File_List);
	if(Record_Count< 0)
	{
		printf("GetRecordFileList failed.\n");
		return ;
	}
	printf("\nRecord_Count = %d\n",Record_Count);
	*pData++ = Record_Count;
	DBG("File_List[0]:%s\n",File_List[0].str );
//	int i=0;
	for (i=0; i < Record_Count; ++i) 
	{

		*pData++ = File_List[i].len;
		strcpy(pData,File_List[i].str); //赋值文件名称
		pData += File_List[i].len;
		//加入文件大小
		*pData++ = (File_List[i].fileSize>>24)&0xFF;
		*pData++ = (File_List[i].fileSize>>16)&0xFF;
		*pData++ = (File_List[i].fileSize>>8)&0xFF;
		*pData++ = (File_List[i].fileSize)&0xFF;
		File_Total_ID = File_List[i].fileSize/1024;
		if(File_List[i].fileSize%1024)
			File_Total_ID+=1;
		*pData++ = (File_Total_ID>>8)&0xFF;
		*pData++ = (File_Total_ID)&0xFF;
		DBG("File_Total_ID=%d",File_Total_ID );
	}

	Network_Send_Compose_CMD(sendBuf,CMD_HOST_QUERY_RECORD_LIST,client,pData-DataBuf,DataBuf);
	// 发送数据
	UDP_SendData(Send_UNICAST, sendBuf,pData-DataBuf + NET_PACKAGE_MIN_SIZE,g_host_ip);
}


/**
 * [TcpSendRecordFileContent description]
 * @param pkg [description]
 */
void TcpSendRecordFileContent(u8 * pkg)
{
	unsigned char sendBuf[1500]={0};//MAX_BUF_SIZE
	unsigned char *pSendbuf=sendBuf;
	unsigned char HostQueryFileName[128]={0};
	unsigned char DataBuf[129*RECORD_DAY_MAX + 1]={0};//文件名长度128 + 1其他数据
	unsigned char *pData = NULL;
	int Datalen=0;
	int currentGetFileID=0;//主机获取当前文件的ID包号

	memset(sendBuf,0,sizeof(sendBuf));
	sendBuf[0]=CMD_SEND_RECORD_FILE_CONTENT>>8;		//命令字高位
	sendBuf[1]=CMD_SEND_RECORD_FILE_CONTENT;		//命令字低位
//	sendBuf[0]=0x0003>>8;	//命令字高位
//	sendBuf[1]=0x0003;		//命令字低位
	sendBuf[2]=0;			//包序号
	sendBuf[3]=0;			//保留

	sendBuf[4]=DEVICE_MODEL_PAGING_A;		//设备型号
	sendBuf[5]=0;			//包属性4bit&编码格式4bit

	pSendbuf = sendBuf+8;
	if(pkg == NULL)
	{
		return;
	}
	if( ((pkg[7]<<8|pkg[6])&0xFFFF) < 4)//接收到的数据包长度
	{
		return;
	}
	u8 * pPkgData = &pkg[8];

	strncpy(HostQueryFileName,&pkg[9],pkg[8]);
	DBG("HostQueryFileName:%s FileLen:%d\n",HostQueryFileName,*pPkgData);
	pPkgData++;
	pPkgData+=pkg[8]; //偏移文件名称长度
	currentGetFileID = ( (pPkgData[0]<<8 ) | pPkgData[1] ); //主机获取的id包号
	printf("currentGetFileID:%d,pPkgData[0]=%d,pPkgData[1]=%d\n",currentGetFileID,pPkgData[0],pPkgData[1]);

	//传输过程中重新请求了其他文件
	if(strcmp(HostQueryFileName,Current_Update_FileName)!=0 && fpCurrent_Update_File!=NULL) 
	{
		printf("文件:%s正在传输，主机请求了其他文件:%s\n",Current_Update_FileName,Current_Update_FileName );
		fclose(fpCurrent_Update_File);
		CleanUpdateFileInfo();
	}
	if(fpCurrent_Update_File==NULL)//首次传输
	{
		memset(Current_Update_FilePath,0,sizeof(Current_Update_FilePath));
		memset(Current_Update_FileName,0,sizeof(Current_Update_FileName));
		strncpy(Current_Update_FilePath,RECORD_FILE_PATH,strlen(RECORD_FILE_PATH));
		strncpy(Current_Update_FilePath+strlen(RECORD_FILE_PATH),HostQueryFileName,pkg[8]);
		strncpy(Current_Update_FileName,HostQueryFileName,strlen(HostQueryFileName));//保存文件名称
		
		fpCurrent_Update_File = fopen(Current_Update_FilePath,"rt");	//只读打开一个文本文件，只允许读数据
		if(fpCurrent_Update_File == NULL)
		{
			CleanUpdateFileInfo();
			perror("fopen:");
			return;
		}
		printf("\nFile:%s upload...\n",Current_Update_FilePath);
	}

	if(Update_File_Size <= 0 )	//获取要发送的文件大小
	{
		Update_File_Size = Get_File_Size(Current_Update_FilePath);
		DBG("Update_File_Size:%d\n",Update_File_Size);
		Update_File_Pkg_Total_id=Update_File_Size/SEND_PKG_SIZE;	//包总ID数
		if(Update_File_Size%SEND_PKG_SIZE)
		{
			Update_File_Pkg_Total_id+=1;
		}	
	}

	
	if(currentGetFileID <=0 || currentGetFileID > Update_File_Pkg_Total_id)//判断文件ID是否超过该文件最大包数
	{
		printf("文件ID超过该文件最大包数、默认发送完成.\n");
		fclose(fpCurrent_Update_File);
		CleanUpdateFileInfo();
		return;
	}


	//名称	
	*pSendbuf++ = strlen(Current_Update_FileName);//文件名称长度
	strcpy(pSendbuf,Current_Update_FileName);//文件名
	pSendbuf+=strlen(Current_Update_FileName);
	*pSendbuf++ = (currentGetFileID>>8)&0xff;
	*pSendbuf++ = currentGetFileID&0xff;


	fseek(fpCurrent_Update_File,(currentGetFileID-1)*SEND_PKG_SIZE, SEEK_SET);	//文件偏移
	Datalen = fread(pSendbuf+2,sizeof(char),SEND_PKG_SIZE,fpCurrent_Update_File);
	if(Datalen<0)
	{
		perror("fread\n");
		fclose(fpCurrent_Update_File);
		CleanUpdateFileInfo();
	}
	if(Datalen==0)//文件读取完成
	{
		printf("File:%s read Over.\n",Current_Update_FileName );
	//	Update_File_Pkg_id = Update_File_Pkg_Total_id;
	}

	*pSendbuf++ = (Datalen>>8)&0xff;	//包内容长度
	*pSendbuf++ = Datalen&0xff;
	DBG("当前文件包发送长度:%d\n",Datalen);
//	DBG("Datalen:%d,pSendbuf:%s\n",Datalen,pSendbuf);
	//unsigned char crc = Calculate_XorDat(, );
	pSendbuf += Datalen;//指针偏移至最后
	Datalen+=4 + strlen(Current_Update_FileName) +1;	//文件包id两个字节
	sendBuf[6]=Datalen>>8;
	sendBuf[7]=Datalen;
	*pSendbuf = Calculate_XorDat(&sendBuf[8], Datalen);	
	printf("Current_Update_FileName:%s\n",Current_Update_FileName );
//	fclose(fpCurrent_Update_File);
	// 发送数据

	UDP_SendData(Send_UNICAST, sendBuf,Datalen+NET_PACKAGE_MIN_SIZE,g_host_ip);
	if( currentGetFileID  == Update_File_Pkg_Total_id)
	{
		printf("文件发送完成...\n");
		fclose(fpCurrent_Update_File);
		CleanUpdateFileInfo();
	}
}


/**
 * [delete_record_data description]
 * @param none [description]
 */
void delete_record_data()
{
	char buf[64]={0};
	sprintf(buf,"rm %s -rf",RECORD_FILE_PATH);
	system(buf);
}

#if 0
//终端向主机发送记录文件内容
/**
 * [TcpSendRecordFileInfo 主机获取设备记录文件、并发送一次文件内容]
 * @param pkg [description]
 */
void TcpSendRecordFileInfo(u8 * pkg)
{
	if(fpCurrent_Update_File != NULL) //正在传输文件
	{
		printf("Updateing File host query file.\n");
		fclose(fpCurrent_Update_File);
		CleanUpdateFileInfo();
		return;
	}
	unsigned int DataLen = (pkg[6]<<8)|pkg[7];
	DBG("DataLen:%d\n",DataLen);

	memset(Current_Update_FilePath,0,sizeof(Current_Update_FilePath));
	memset(Current_Update_FileName,0,sizeof(Current_Update_FileName));
	strncpy(Current_Update_FilePath,RECORD_FILE_PATH,strlen(RECORD_FILE_PATH));
	strncpy(Current_Update_FilePath+strlen(RECORD_FILE_PATH),&pkg[8],DataLen);
	strncpy(Current_Update_FileName,&pkg[8],DataLen);//保存文件名称
	
	DBG("Current_Update_FilePath:%s\n",Current_Update_FilePath);
	Update_File_Size = Get_File_Size(Current_Update_FilePath);
	printf("\nGet_File_Size:%d\n",Update_File_Size);

	unsigned char sendBuf[100]={0};//MAX_BUF_SIZE
	unsigned char DataBuf[150 ]={0};//文件名长度128 + 1
	unsigned char * pData=DataBuf;

	*pData++ = strlen(Current_Update_FileName);//文件名长度
	strncpy(pData,Current_Update_FileName,DataLen);//发送文件名称
	pData += DataLen;

//	*pData++ = (Update_File_Size>>8)&0xff ;
//	*pData++ = Update_File_Size&0xff ;

	Update_File_Pkg_Total_id = Update_File_Size/SEND_PKG_SIZE;
	if(Update_File_Size%SEND_PKG_SIZE)	//有余数加一
	{
		Update_File_Pkg_Total_id++;
	}
	*pData++ = (Update_File_Pkg_Total_id>>8)&0xff ;//总包数
	*pData++ = Update_File_Pkg_Total_id&0xff ;

	Network_Send_Compose_CMD(sendBuf,CMD_SOST_QUERY_RECORD_FILE,pkg[4],pData-DataBuf,DataBuf);
	// 发送数据
	if(Update_File_Size >=0)
	{
	UDP_SendData(Send_UNICAST, sendBuf,pData-DataBuf + NET_PACKAGE_MIN_SIZE,g_host_ip);
	DBG("host_udp_send_data succeed.\n");
	TcpSendRecordFileContent(NULL);
}
	//if(Update_File_Size!= 0)
		//TCP_Server_Thread();
}
#endif

#if 0

int main(int argc, char const *argv[])
{
	
//	OS_Init_UDP_Record_M(); //初始化通讯线程
//	Out_Info_To_File(8,"/mnt/yaffs2/","FileName.mp3");
//	Del_Record_Sort_File();

	MyStr File_List[7]={0};

	char listBuf[123][128]={0};

	int i = 0;
	for (; i < RECORD_DAY_MAX; ++i)
	{
		File_List[i].str=listBuf[i];
		File_List[i].len=0;
	}
//	WriteRecordInfo(1,"FileName.mp3",12);
	DBG("Get_Record_Count=%d\n",Get_Record_Count());
	GetRecordFileList(File_List);
	DBG("File_List[0]:%s\n",File_List[0].str );

	char * fileName = "Record-2017-05-03.txt";
	char test[120]={0};
	int cpStrlen =  strlen(fileName);
	strncpy(test+8,fileName,strlen(fileName));
	test[6] = (cpStrlen>>8)&0xff;
	test[7] = cpStrlen&0xff;
	TcpSendRecordFileInfo(test);
	printf("TcpSendRecordFileInfo . \n");
//	TcpSendRecordFileContent(NULL);
	while(0)
	{
		sleep(1);
	}
	return 0;
}
#endif
