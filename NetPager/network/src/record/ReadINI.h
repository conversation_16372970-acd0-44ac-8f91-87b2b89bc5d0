

#ifndef __READINI_H__
#define __READINI_H__

#include <stdio.h>

#define OP_ERROR 			-1
#define OP_SUCCEED			1
#define OP_HEAD_FIND_FAIL	-2
#define OP_KEY_FIND_FAIL 	-3

typedef struct List_Node
{
	unsigned char Len;
	char * Str;
	struct List_Node * Next;
	struct List_Node * Prev;
}List_Node;

#define CONFIG_PATH 	"/mnt/yaffs2/config.ini"
#define CONFIG_LINE_MAX 		128
int _Read_Config_Key(const char * header,char const *_Key,unsigned int * value,char * str);
int Save_Config_Key(const char * header,const char *Key,const char * Str,unsigned int const value);

#define READ_CONFIG_KEY_STR(header,_Key,str) \
 	_Read_Config_Key(header,_Key,NULL,str)
#define READ_CONFIG_KEY_INT(header,_Key,value) \
 	_Read_Config_Key(header,_Key,value,NULL)

#define SAVE_CONFIG_KEY_STR(header,_Key,str) \
 	Save_Config_Key(header,_Key,str,0)
#define SAVE_CONFIG_KEY_INT(header,_Key,value) \
 	Save_Config_Key(header,_Key,NULL,value)




/** 寻找参数标识头
 *
 *  在文件中定位到参数的标识
 *  @param:  char * header 参数标识字符串
 *  @return: [int]  是否成功
 *  @note:   
 *  @see:    
 */ 
int find_header( FILE *fp,char * header);

// 把变量名转换为字符串输出
#define TOSTRING(name) #name 

// 写入参数标识头
#define WRITE_PARAM_HEADER(fp,Header) \
	fprintf(fp,"%s\n",Header)

#define FIND_PARAM_HEADER(fp,Header) \
	find_header(fp,Header)

// 保存参数宏,int参数
#define SAVE_PARAM_INT(fp,Key,Value) \
	fprintf(fp,"%s= %d\n",#Key,Value)

// 保存参数宏,字符串参数
#define SAVE_PARAM_STR(fp,Key,Value) \
	fprintf(fp,"%s= %s\n",#Key,Value)

// 读取参数宏,int参数
#define LOAD_PARAM_INT(fp,KeyAddr,ValueAddr) \
	fscanf(fp,"%s %d",KeyAddr,ValueAddr)

// 读取参数宏,字符串参数
#define LOAD_PARAM_STR(fp,KeyAddr,ValueAddr) \
	fscanf(fp,"%s %s",KeyAddr,ValueAddr)


#endif
