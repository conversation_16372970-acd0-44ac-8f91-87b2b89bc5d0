/******************************************************************
*           data type 
*
******************************************************************/
#ifndef __TYPE_DEFINE_H__
#define __TYPE_DEFINE_H__


/*************************************************
*             变量类型定义                       *
*************************************************/
typedef unsigned char BOOLEAN;
//typedef bool  BOOLEAN;				/* 布尔变量									*/
typedef unsigned char  INT8U;	 	/* 无符号8位整型变量                        */
typedef signed   char  INT8S;		/* 有符号8位整型变量                        */
typedef unsigned short INT16U;		/* 无符号16位整型变量                       */
typedef signed   short INT16S;		/* 有符号16位整型变量                       */
typedef unsigned long  INT32U; 		/* 无符号32位整型变量                       */
typedef signed   long  INT32S; 		/* 有符号32位整型变量                       */
typedef float          FP32;		/* 单精度浮点数（32位长度）					*/


//typedef bool  BOOLEAN;				/* 布尔变量									*/
typedef unsigned char  u8;	 	/* 无符号8位整型变量                        */
typedef signed   char  s8;		/* 有符号8位整型变量                        */
typedef unsigned short u16;		/* 无符号16位整型变量                       */
typedef signed   short s16;		/* 有符号16位整型变量                       */
typedef unsigned long  u32; 		/* 无符号32位整型变量                       */
typedef signed   long  s32; 		/* 有符号32位整型变量                       */
typedef float          f32;		/* 单精度浮点数（32位长度）					*/


#if 0

#define code 	const
#define idata  
/**************************
** 8bit mcu type 
***************************/
//typedef unsigned char 	uchar; /** no signed 8 bit integer variable**/
//typedef unsigned int 	uint ; // no signed 16 bit integer variable
//typedef bit bool 				//boolean variable
#define uchar		INT8U
#define uint 		INT16U
#define bool 		BOOLEAN


#define INT8U_IDT 	INT8U  //idata
#define INT16U_IDT 	INT16U //idata
#define INT8S_IDT 	INT8S  //idata
#define INT16S_IDT 	INT16S //idata
#define INT32U_IDT 	INT32U //idata
#define INT32S_IDT 	INT32S //idata


#define INT8U_XDT 	INT8U  //xdata
#define INT16U_XDT 	INT16U //xdata
#define INT8S_XDT 	INT8S  //xdata
#define INT16S_XDT 	INT16S //xdata
#define INT32U_XDT 	INT32U //xdata
#define INT32S_XDT 	INT32S //xdata

#if 
#define TRUE    1
#define FALSE   0
#endif


#define true    1
#define false   0



#define YES 	1
#define NO 		0

#define ON		1
#define OFF	    0

#define OK		0
#define ERROR	-1

#define OPEN 	1
#define CLOSE	0
#define RUN		1
#define STOP 	0  

#endif

#endif

