/**************************************************************************
  fileName:			Http_Client.c
  
  Author:			LiXiangKai

  Date:				2013-10-23

  Description:		HTTP客户端，只支持文件下载功能，不支持断点续传，不支持
					重定向；当超过30S服务器端无应答时，则认为连接已断开！

  修改日期：        2014.03.27 -- 调整升级固件扫描
**************************************************************************/

#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <sys/socket.h>
#include <stdio.h>
#include <errno.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <sys/select.h>
#include <signal.h>
#include <pthread.h>

#include "sysconf.h"
#include "http_client.h"
#include "system_command.h"
#include "MusicFile.h"
#include "md5.h"
/*********************************************************************
 * MACROS
 */

/*********************************************************************
 * CONSTANTS
 */

/*********************************************************************
 * TYPEDEFS
 */
 
 /********************************************************************
 * EXTERNAL VARIABLES
 */

/*********************************************************************
 * GLOBAL VARIABLES
 */
unsigned char g_download_path[512];      /*固件下载到本地的路径*/
unsigned char g_url_buf[512];            /*固件在服务器上的路径*/
unsigned char g_buf_send[4*1024];        /*发送数据暂存区*/
int g_update_flag = 0;                   /*0-没升级，1-正在升级*/
int g_update_type = 0;					 //0-app 1:bp1048
int g_download_progress = 0;             /*下载进度*/

unsigned char g_web_server_ip[16];
unsigned int g_web_server_port;

extern int g_xml_download_port;

char upgrade_file_md5_str[MD5_STR_LEN+1];
static unsigned char *firmware_download_buffer; //下载BP1048数据缓存


pthread_t bp1048_Pthread;
void start_bp1048_upgrade_pthread(void);
void Bp1048_upgrade_thread();
void clear_bp1048_upgrade();

/*********************************************************************
 * @fn      Http_Send
 *
 * @brief   向WEB服务器发送请求数据
 *
 * @param   sockfd - 套接字文件描述符
 *			send_buf - 需发送的数据
 *			len - 数据长度
 *			flags - 发送标志
 *
 * @return  none
 */
static void Http_Send(int sockfd, char *send_buf, int len, int flags)
{
	int ret = -1;
	int send_len = 0;

	while(send_len < len) // 可能包太大，需多发几次
	{
		ret = send(sockfd, send_buf+send_len, len-send_len, flags);
		if(ret < 0)
		{
			ret = send(sockfd, send_buf+send_len, len-send_len, flags);
			if (ret < 0)
			{
				perror("Http send error");
				pthread_exit(NULL);
			}
			else
			{
				send_len += ret;
			}
		}
		else
		{
			send_len += ret;
		}
	}
}

/*********************************************************************
 * @fn      Get_ContentLength
 *
 * @brief   获取请求文件的长度
 *
 * @param   rev_buf - WEB服务器的应答消息体缓存
 *
 * @return  content_length - 文件长度
 */
static unsigned long Get_ContentLength(char *rev_buf)
{
	char *p1 = NULL;
	char *p2 = NULL;
	unsigned long int content_length = 0;

	p1 = strstr(rev_buf, "Content-Length"); // 查找文件长度标识符
	if(p1 == NULL)
	{
		return -1;
	}
	else
	{
		p2 = p1 + strlen("Content-Length") + 2;
		content_length = atoi(p2); // 计算长度
	}
	
	return content_length;
}

/*********************************************************************
 * @fn      Http_Download_File
 *
 * @brief   文件下载
 *
 * @param   void
 *
 * @return  none
 */
void *Http_Download_File(void)
{
	g_update_flag=1;
	int rxlen;
	int sockfd;
	FILE *file;
	int ret = -1;
	int count = 0;
	fd_set readfd;
	int pkg_count = 0;
	int link_times = 0;
	struct timeval timeout;
	char *p0 = NULL, *p1 = NULL;
	unsigned long fileLength = 0;
	struct sockaddr_in Client_addr;
	unsigned char recv_buf[MAX_RECV_SIZE]; /*接收数据暂存区*/
	unsigned char checkbuf[16] = "HTTP/1.1 200 OK";
	unsigned char checkbuf2[16] = "HTTP/1.0 200 OK";
	
	Client_addr.sin_family = AF_INET;
    Client_addr.sin_addr.s_addr = inet_addr(g_web_server_ip);
    Client_addr.sin_port = htons(g_web_server_port);
	
	timeout.tv_sec = TIMEOUT;  // 获取数据超时时间设置
	timeout.tv_usec = 0;

	/*创建套接字，SERVER_IPV4，TCP*/
	sockfd = socket(AF_INET, SOCK_STREAM, 0);
	if (sockfd < 0)
	{
		perror("Http download socket create error");
		respond_pkg_update_status_code(UPDATE_FAIL);
		g_update_flag=0;

		clear_bp1048_upgrade();
		pthread_exit(NULL);
	}
	else
	{
		if(Paging_status != PAGING_START) printf("Http download socket create succeed!\n");
	}
	
	setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));	//设置超时时间

	/*连接WEB服务器*/
	if ((connect(sockfd, (struct sockaddr*)&Client_addr, sizeof(Client_addr))) < 0)
	{
		perror("Http download connect web server error");
		close(sockfd);
		respond_pkg_update_status_code(UPDATE_FAIL);
		g_update_flag=0;
		clear_bp1048_upgrade();
		pthread_exit(NULL);
	}
	else
	{
		if(Paging_status != PAGING_START) printf("Http download connect web server succeed!\n");
	}
	
	/*创建文件保存下载数据*/
	file = fopen(g_download_path, "w+b");
	if (file == NULL)
	{
		perror("Http download open file error");
		close(sockfd);
		respond_pkg_update_status_code(UPDATE_FAIL);
		g_update_flag=0;
		clear_bp1048_upgrade();
		pthread_exit(NULL);
	}
	else
	{
		if(Paging_status != PAGING_START) printf("Http download open file succeed!\n");
	}
	
	char download_path[512]={0};
	strcat(download_path,(char*)url_change(g_url_buf));
	Comb_Request_Package(download_path);                      // 请求消息体组包
	Http_Send(sockfd, g_buf_send, strlen(g_buf_send), 0); // 向服务器发送客户端请求
	
#if HTTP_DEBUG
	if(Paging_status != PAGING_START) printf("Http download request data: \n");
	if(Paging_status != PAGING_START) printf("%s", g_buf_send);
#endif
	
	timeout.tv_sec = TIMEOUT;  // 获取数据超时时间设置
	timeout.tv_usec = 0;
	
	FD_ZERO(&readfd);          // 清空读文件描述集合
	FD_SET(sockfd, &readfd);   // 注册套接字文件描述符

	g_download_progress = 0;    /*下载进度*/
	
	if(Paging_status != PAGING_START) printf("Http download request data1: \n");

	while(1)
	{
		timeout.tv_sec = TIMEOUT; // 获取数据超时时间设置
		timeout.tv_usec = 0;
		
		FD_ZERO(&readfd);         // 清空读文件描述集合
		FD_SET(sockfd, &readfd);  // 注册套接字文件描述符
		
		memset(recv_buf, 0x00, sizeof(recv_buf)); // 清空接收缓存
		ret = select(sockfd+1, &readfd, NULL, NULL, &timeout);
		switch(ret)
		{
			case -1 : // 调用出错
				perror("Http download call select error");
				remove_file(g_download_path);
				close(sockfd);
				fclose(file);
				respond_pkg_update_status_code(UPDATE_FAIL);
				g_update_flag=0;
				clear_bp1048_upgrade();
				pthread_exit(NULL);
				break;
			
			case 0 : // 超时
				if(Paging_status != PAGING_START) printf("Http download request timeout!!!\n");
				remove_file(g_download_path);
				close(sockfd);
				fclose(file);
				respond_pkg_update_status_code(CONNECT_TIME_OUT); // 连接服务器超时
				usleep(30000);                                    // 延时30ms
				respond_pkg_update_status_code(UPDATE_FAIL);      // 升级失败
				g_update_flag=0;
				clear_bp1048_upgrade();
				pthread_exit(NULL);
				break;
				
			default : // 有数据可读
				if (FD_ISSET(sockfd, &readfd))
				{
					link_times = 0;
					/*读取数据*/
					rxlen = recv(sockfd, recv_buf, MAX_RECV_SIZE, 0);
					if (rxlen < 0)
					{
						perror("Http download call recv error");
						close(sockfd);
						fclose(file);
						remove_file(g_download_path);
						g_update_flag=0;
						clear_bp1048_upgrade();
						pthread_exit(NULL);
					}
					else
					{
#if HTTP_DEBUG
						if(pkg_count == 0)
						{
							printf("Http download rcev length is: %d\n", rxlen);
							printf("%s\n", recv_buf);
						}
#endif
						pkg_count += 1; // 包接收计数器

						if (pkg_count == 1)
						{
							/*判断连接服务器是否成功*/
							if ((strncmp(recv_buf, checkbuf, 15)) == 0 || strncmp(recv_buf, checkbuf2, 15) == 0)
							{
								if(Paging_status != PAGING_START) printf("Start download data!\n");
							}
							else
							{
								if(Paging_status != PAGING_START) printf("Web server respond failed, pthread exit!\n");
								close(sockfd);
								fclose(file);
								remove_file(g_download_path);
								respond_pkg_update_status_code(UPDATE_FAIL); // 升级失败
								g_update_flag=0;
								clear_bp1048_upgrade();
								pthread_exit(NULL);
							}
							
							/*获取需下载文件的大小*/
							fileLength = Get_ContentLength(recv_buf);

							if(g_update_type == 1)
							{
								if(fileLength >= 1024*1024)		//BP1048 >=1M,error
								{
									if(Paging_status != PAGING_START) printf("Web server respond failed, pthread exit!\n");
									close(sockfd);
									fclose(file);
									remove_file(g_download_path);
									respond_pkg_update_status_code(UPDATE_FAIL); // 升级失败
									g_update_flag=0;
									clear_bp1048_upgrade();
									pthread_exit(NULL);
								}
								if(firmware_download_buffer == NULL)
								{
									int sector_num=0;
									if(fileLength%(BP1048_FIRMEARE_UPGRADE_ONCE_SIZE)==0)//计算有多少个包要发送
									{
										sector_num = fileLength/(BP1048_FIRMEARE_UPGRADE_ONCE_SIZE);
									}
									else
									{
										sector_num = fileLength/(BP1048_FIRMEARE_UPGRADE_ONCE_SIZE)+1;
									}
									firmware_download_buffer=malloc(sector_num*BP1048_FIRMEARE_UPGRADE_ONCE_SIZE);
								}
							}
							else
							{
								printf("Download file length is : %ld\n", fileLength);
							}
							

							if (fileLength <= 0)
							{
								if(Paging_status != PAGING_START) printf("Download file length incorrect!!!\n");
								close(sockfd);
								fclose(file);
								remove_file(g_download_path);
								respond_pkg_update_status_code(UPDATE_FAIL); // 升级失败
								g_update_flag=0;
								clear_bp1048_upgrade();
								pthread_exit(NULL);
							}
							
							/*查找有效数据的开始并写入文件保存*/
							p0 = strstr(recv_buf, "\r\n\r\n"); // 查找有效数据开始标识
							p1 = recv_buf;
							if (p0 == NULL)
							{
								if(Paging_status != PAGING_START) printf("NO DATA!\n");
								close(sockfd);
								fclose(file);
								remove_file(g_download_path);
								respond_pkg_update_status_code(UPDATE_FAIL); // 升级失败
								g_update_flag=0;
								clear_bp1048_upgrade();
								pthread_exit(NULL);
							}
							else
							{
								count = rxlen - (p0 - p1) - 4; // 计算第一次接收有效数据长度
#if HTTP_DEBUG
								if(Paging_status != PAGING_START) printf("The Count is : %d, p0 = %d, p1 = %d\n", count, p0, p1);
#endif
								fwrite(p0+4, sizeof(char), count, file); // 将数据写入文件，去掉\r\n\r\n
								fseek(file, 0, SEEK_END);                // 将文件指针移到末尾

								if(g_update_type == 1)
								{
									memcpy(firmware_download_buffer,p0+4,count);
								}
							}
						}
						else
						{
							if(g_update_type == 1)
							{
								memcpy(firmware_download_buffer+count,recv_buf,rxlen);	
							}
							/*循环写入数据*/
							count += rxlen;
							fwrite(recv_buf, sizeof(char), rxlen, file); // 写入数据
							fseek(file, 0, SEEK_END); // 将文件指针移动到末尾
						}
		

						if(g_update_type == 0)
						{
							if(g_download_progress!=(int)((count*100.0)/fileLength))
							{
								g_download_progress = (int)((count*100.0)/fileLength);
								if(g_download_progress %10 == 0)
									send_download_rate_of_progress((unsigned char)g_download_progress);
								//printf("%d%%\n",g_download_progress);    // 打印下载进度
							}
						}

						/*判断文件是否下载完成*/
						if (count == fileLength)
						{
							/*检查下载的文件是否完整*/
							if (fileLength == Get_File_Size(g_download_path))
							{
								if(g_update_type == 0)		//app
								{
									if(Paging_status != PAGING_START) printf("Update firmware Download Succeed!\n");
									respond_pkg_update_status_code(UPDATE_SUCCEED); // 升级成功
									usleep(100000);
									respond_pkg_update_status_code(UPDATE_SUCCEED);
									usleep(100000);
									/*下载完成重启模块设备*/
									char mv_buf[128]={0};
									sprintf(mv_buf,"mv %s %s",g_download_path,UPDATE_REAL_PATH);
									pox_system(mv_buf);

									Amp_Switch(0);	//关闭功放
									#if USE_ASM9260
									Bp1048_Send_Set_Reboot();
									usleep(50000);
									#endif

									//清除ZoneInfo、GroupInfo、UserInfo，避免从高版本回退低版本时启动异常
									char rm_buf[128]={0};
									sprintf(rm_buf,"rm %s",USERINFOFILE);
									pox_system(rm_buf);
									sprintf(rm_buf,"rm %s",GROUPINFOFILE);
									pox_system(rm_buf);
									sprintf(rm_buf,"rm %s",ZONEINFOFILE);
									pox_system(rm_buf);
									
									System_Reboot();
								}
								else
								{
									//BP1048 READY UPGRADE
									start_bp1048_upgrade_pthread();
								}
							}
							else
							{
								if(Paging_status != PAGING_START) printf("Download file failed!!!\n");
								remove_file(g_download_path);
								respond_pkg_update_status_code(UPDATE_FAIL); // 升级失败
							}

							clear_bp1048_upgrade();
							
							fclose(file);
							close(sockfd);
							g_update_flag=0;
							clear_bp1048_upgrade();
							pthread_exit(NULL);
						}
					}
				}
				break;
		}
	}

	clear_bp1048_upgrade();

	fclose(file);
	close(sockfd);
	g_update_flag=0;
	pthread_exit(NULL);
}

/*********************************************************************
 * @fn      Comb_Request_Package
 *
 * @brief   GET请求消息体
 *
 * @param   url - 文件在服务器存放的路径
 *
 * @return  none
 */
static void Comb_Request_Package(char *url)
{
	unsigned char port_temp[16];

	memset(g_buf_send, 0x00, sizeof(g_buf_send));
	memset(port_temp, 0x00, sizeof(port_temp));
	sprintf(g_buf_send, "GET %s", url);
	sprintf(port_temp, "%d", g_web_server_port);

	//HTTP/1.1\r\n 前面需要一个空格
	strcat(g_buf_send, " HTTP/1.1\r\n");
	strcat(g_buf_send, "Host: ");
	strcat(g_buf_send, g_web_server_ip);
	strcat(g_buf_send, ":");
	strcat(g_buf_send, port_temp);
	
	strcat(g_buf_send, "\r\nAccept: */*\r\n");
//	strcat(g_buf_send, "Connection: Keep-Alive\r\n");
	strcat(g_buf_send,"Connection: Close\r\n");
	strcat(g_buf_send, "\r\n");
}

/*********************************************************************
 * @fn      start_download_file_pthread
 *
 * @brief   启动文件下载线程
 *
 * @param   void
 *
 * @return  void
 */
void start_download_file_pthread(void)
{
	int ret = -1;

	pthread_t Load_File_Pthread;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	if(g_update_flag)
	{
		return;
	}
	ret = pthread_create(&Load_File_Pthread, &Pthread_Attr, (void *)Http_Download_File, NULL);
	if (ret < 0)
	{
		ret = pthread_create(&Load_File_Pthread, &Pthread_Attr, (void *)Http_Download_File, NULL);
		if (ret < 0)
		{
			respond_pkg_update_status_code(UPDATE_FAIL); // 升级失败
			perror("Http_Download_File phread create failed");
		}
		else
		{
			respond_pkg_update_status_code(START_DOWNLOAD); // 开始下载升级包
			if(Paging_status != PAGING_START) printf("Http_Download_File Pthread Create Seccess!\n");
		}
	}
	else
	{
		respond_pkg_update_status_code(START_DOWNLOAD); // 开始下载升级包
		if(Paging_status != PAGING_START) printf("Http_Download_File Pthread Create Seccess!\n");
	}
	pthread_attr_destroy(&Pthread_Attr);
}





void Bp1048_upgrade_thread()
{
	unsigned long firmware_len = Get_File_Size(g_download_path);
	int sector_num=0;
	if(!bp1048_upgrade_info.pkg_count)
	{
		if(firmware_len%(BP1048_FIRMEARE_UPGRADE_ONCE_SIZE)==0)//计算有多少个包要发送
		{
			sector_num = firmware_len/(BP1048_FIRMEARE_UPGRADE_ONCE_SIZE);
		}
		else
		{
			sector_num = firmware_len/(BP1048_FIRMEARE_UPGRADE_ONCE_SIZE)+1;
		}
		bp1048_upgrade_info.file_length = firmware_len;
		bp1048_upgrade_info.pkg_count= sector_num;
		printf("bp1048_upgrade_info.pkg_count=%d\r\n",bp1048_upgrade_info.pkg_count);
	}
	int error_count=0;
	int download_rate=0;
	while(1)
	{
		if( bp1048_upgrade_info.IsStart && !bp1048_upgrade_info.upgrade_valid)
		{
			error_count++;
			if(error_count>=200)
			{
				printf("bp1048 upgrade_timeout>=2s,return!\r\n");
				bp1048_upgrade_info.IsStart=0;
				respond_pkg_update_status_code(UPDATE_FAIL);
				pthread_exit(NULL);
			}
			usleep(10000);
			continue;
		}
		else
		{
			error_count=0;
		}
		//先发送准备包
		if(!bp1048_upgrade_info.IsStart)
		{
			bp1048_upgrade_info.upgrade_valid=0;
			bp1048_upgrade_info.IsStart=1;
			bp1048_upgrade_info.Upgrade_process=0;
			Bp1048_Send_Firmware(NULL);
		}
		else if (bp1048_upgrade_info.pkg_index< bp1048_upgrade_info.pkg_count )
		{
			bp1048_upgrade_info.upgrade_valid=0;
			bp1048_upgrade_info.pkg_index++;
			Bp1048_Send_Firmware(firmware_download_buffer+(bp1048_upgrade_info.pkg_index-1)*BP1048_FIRMEARE_UPGRADE_ONCE_SIZE);
			
			download_rate=(int)(100/(bp1048_upgrade_info.pkg_count*(1.0))*(bp1048_upgrade_info.pkg_index));
			if(	download_rate % 2 == 0)
			{
				send_download_rate_of_progress(download_rate);
			}
			else if(bp1048_upgrade_info.pkg_index == 1)
			{	
				download_rate=1;
				send_download_rate_of_progress(download_rate);
			}
		}
		else
		{
			bp1048_upgrade_info.upgrade_valid=0;
			if(	bp1048_upgrade_info.Upgrade_process == 2 )
			{
				Bp1048_Send_Firmware(NULL);
			}
			else if( bp1048_upgrade_info.Upgrade_process == 3 )
			{
				respond_pkg_update_status_code(UPDATE_SUCCEED);
				remove_file(g_download_path);
				System_Reboot();
			}
		}
		usleep(10000);
	}
}






/*********************************************************************
 * @fn      start_bp1048_upgrade_pthread
 *
 * @brief   启动BP1048升级线程
 *
 * @param   void
 *
 * @return  void
 */
void start_bp1048_upgrade_pthread(void)
{
	printf("start_bp1048_upgrade_pthread.\n");
	int ret = -1;
	ret = pthread_create(&bp1048_Pthread,NULL , (void *)Bp1048_upgrade_thread, NULL);
	pthread_join(bp1048_Pthread,NULL);
}


void clear_bp1048_upgrade()
{
	//清除BP1048升级信息
	if(g_update_type == 1)
	{
		if(firmware_download_buffer)
		{
			free(firmware_download_buffer);
			firmware_download_buffer=NULL;
		}
		memset(&bp1048_upgrade_info,0,sizeof(bp1048_upgrade_info));
	}
}