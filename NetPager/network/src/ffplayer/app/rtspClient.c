#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include <stdint.h>
#include <signal.h>
#include <pthread.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <sys/types.h>
#include <sys/time.h>
#include <sys/stat.h>
#include "gccRTSPClient.h"
#include "SaveZoneInfo.h"
#include "sysconf.h"

static struct sockaddr_in rtspTargetAddr;
int rtspTargetSocket=-1;
#define RTSP_TARGET_PORT 9000


static char isliveRtspStatus = RTSP_STATUS_CLOSE;

char myCameraRtspAddress[256]={0};

static unsigned int rtspPkgId=0;

//#define RTSP_ADDRESS "rtsp://admin:bydz87654321@************:554/cam/realmonitor?channel=1&subtype=0"
#define RTSP_ADDRESS "rtsp://***************:554/user=admin&password=&channel=1&stream=0.sdp?"

void printRtpPacketSequenceNumber(uint8_t* rtpPacket) {
    static uint16_t previousSequenceNumber = 0;

    // RTP包头位于RTP数据包的前12个字节
    // 序列号字段的偏移量为2个字节
    uint16_t sequenceNumber = (rtpPacket[2] << 8) | rtpPacket[3];
    //printf("RTP Packet Sequence Number: %u\n", sequenceNumber);

    if (previousSequenceNumber == 0) {
        previousSequenceNumber = sequenceNumber;
    } else {
        // 计算序号差值
        int16_t sequenceNumberDiff = sequenceNumber - previousSequenceNumber;

        if (sequenceNumberDiff > 0) {
            previousSequenceNumber = sequenceNumber;
        } else if (sequenceNumberDiff < 0) {
            printf("Error: RTP Packet Sequence Number Out of Order. Current: %u, Previous: %u\n", sequenceNumber, previousSequenceNumber);
            previousSequenceNumber = sequenceNumber;
        }
    }
}

void determine_payload_type(uint8_t* rtp_packet) {
    // 提取负载类型字段
    unsigned char payload_type = (rtp_packet[1] & 0x7F);
    // 检查RTP负载类型
    if (payload_type == 96) {
        printf("H.264...\n");
    } else if (payload_type == 98) {
        printf("H.265...\n");
    } else {
        printf("Unknown...\n");
    }
}

// 从sdp信息中提取视频编码字符函数
char* extract_video_codec_char_from_sdp(const char* sdp) {
    char* pch = NULL;
    char* token = NULL;
    const char* delim = "\n";

    // 逐行解析SDP
    pch = strtok((char*)sdp, delim);
    while (pch != NULL) {
        // 查找媒体行以及包含"video"的行
        if (strncmp(pch, "m=", 2) == 0 && strstr(pch, "video") != NULL) {
            // 提取第4个空格分隔的元素作为编码信息
            token = strtok(pch, " ");
            for (int i = 0; i < 3; i++) {
                token = strtok(NULL, " ");
            }
            break;
        }
        pch = strtok(NULL, delim);
    }
    
    if (token != NULL) {
        // 去除回车符或者换行符
        size_t len = strlen(token);
        if (len > 0 && (token[len - 1] == '\r' || token[len - 1] == '\n')) {
            token[len - 1] = '\0';
        }
        return token;
    }

    return NULL;
}


void rtspNetInit()
{
    if(rtspTargetSocket == -1)
    {
        // 设置目标socket
        rtspTargetSocket = socket(AF_INET, SOCK_DGRAM, 0);
        if (rtspTargetSocket < 0) {
            perror("Error creating socket");
            return;
        }
        // 设置目标地址
        rtspTargetAddr.sin_family = AF_INET;
        rtspTargetAddr.sin_port = htons(RTSP_TARGET_PORT);
        rtspTargetAddr.sin_addr.s_addr = inet_addr("127.0.0.1");
        printf("rtspNetInit succeed!\n");
    }
}

void  rtspStreamForwardLocalPort(unsigned char* packet,unsigned int packetSize)
{
    int sendlen=sendto(rtspTargetSocket, packet, packetSize, 0, (struct sockaddr*)&rtspTargetAddr, sizeof(rtspTargetAddr));
    //printf("senLen=%d\n",sendlen);
}

void rtpAuxHandlerFunc(void* clientData, unsigned char* packet,unsigned int packetSize)
{
    //printf("rtpAuxHandlerFunc:packetSize=%d\n",packetSize);
    if(Paging_status != CALLING_START)  //用于测试
    {
        printRtpPacketSequenceNumber(packet);
        // 转发数据到目标地址
        rtspStreamForwardLocalPort(packet,packetSize);
    }
    else
    {
        Send_Call_Video_Stream(packet,packetSize,rtspPkgId++);
    }
}
void rtcpAuxHandlerFunc(void* clientData, unsigned char* packet,unsigned int packetSize)
{
    printf("rtcpAuxHandlerFunc:packetSize=%d\n",packetSize);
}

void rtspStartPlay()
{
    printf("rtspStartPlay...\n");
    rtspPkgId=0;
    liveRtspPlay();
}



void ConfirmMyCameraRtspAddress()
{
    #if 0
    //判断触摸屏型号(1：GT9271-睛灵龙-旋转180度,2：GT911-杭州魔方-不需要旋转)
	static int touch_id=1;
	char product[8]={0};
    FILE *fp = fopen("/sys/devices/soc0/soc/1f223000.i2c0/i2c-0/0-005d/input/input0/id/product", "r");
    if (fp != NULL) {
		if (fgets(product, sizeof(product), fp) != NULL) {
			// 删除换行符
			product[strcspn(product, "\n")] = '\0';
			// 判断字符，并返回相应的值
			if (strcmp(product, "2437") == 0) {
				touch_id = 1;
			} else if (strcmp(product, "038f") == 0) {
				touch_id = 2;
			}
		}
		fclose(fp);
    }
    if(touch_id == 1)
    {
        sprintf(myCameraRtspAddress,"rtsp://admin:bydz87654321@************:554/cam/realmonitor?channel=1&subtype=0");
    }
    else
    {
        sprintf(myCameraRtspAddress,RTSP_ADDRESS);
    }
    #endif

    sprintf(myCameraRtspAddress,RTSP_ADDRESS);
}

void rtspStatusHandlerFunc(int status,const char *sdp)
{
    isliveRtspStatus=status;
    printf("RTSPHandlerFunc:status=%d\n",status);
    if(status == RTSP_STATUS_DESCRTIBE)
    {
        //printf("%s\n",sdp);
        char *codecNumChar=extract_video_codec_char_from_sdp(sdp);
        printf("sdp_codecNum=%s\n",codecNumChar);   //"96"代表H264,"98"代表H265
        int videoCodes=VIDEO_CODECS_UNKNOWN;
        if(codecNumChar != NULL)
        {
            if(strcmp(codecNumChar,"96") == 0)
            {
                videoCodes = VIDEO_CODECS_H264;
            }
            else if(strcmp(codecNumChar,"98") == 0)
            {
                videoCodes = VIDEO_CODECS_H265;
            }
            if( videoCodes == VIDEO_CODECS_UNKNOWN )
            {
                //toto退出
                m_stPager_Info.self_video_callStatus = VIDEO_CALL_STATUS_FREE;
            }
            else
            {
                Send_video_call_codecs_parm(videoCodes);
                //此处注意，可能由于本机获取RTSP参数较慢，所以会先收到对方的RTSP流并解析完成，此时本机状态可能已经变成了VIDEO_CALL_STATUS_ALL_READY
                //故此处应该判断当前本机状态
                #if USE_SSD202
                if(m_stPager_Info.self_video_callStatus<VIDEO_CALL_STATUS_RTSP_SEND_PARM)
                {
                    m_stPager_Info.self_video_callStatus = VIDEO_CALL_STATUS_RTSP_SEND_PARM;
                    Send_video_call_status_feedback(VIDEO_CALL_STATUS_RTSP_SEND_PARM);
                }
                #endif
            }
        }
        else
        {
            printf("sdp_codecNum=NULL.sdp=%s\n",sdp);
            m_stPager_Info.self_video_callStatus = VIDEO_CALL_STATUS_FREE;
        }

        #if USE_SSD202
        if(Paging_status != CALLING_START)  //用于测试，实际要收到对方的参数信息再打开
        {
            start_ffplayer_video_by_videoCodes(videoCodes);
            usleep(100000);
            rtspStartPlay();
        }
        #endif
    }
}

void rtspShutdown()
{
    if(isliveRtspStatus != RTSP_STATUS_CLOSE)
    {
        liveShutdownStream();
    }
    else
    {
        printf("rtspShutdown:RTSP is already shutdown!\n");
    }
}


static int rtspInitOK=0;
void startRTCPClient()
{
    //测试用
    ConfirmMyCameraRtspAddress();

    if(rtspInitOK == 0)
    {
        rtspNetInit();
        liveMediaInit();
        rtspInitOK=1;
    }
    liveSetAuxHandlerFunc(rtpAuxHandlerFunc,rtcpAuxHandlerFunc);
    liveSetStatusHandlerFunc(rtspStatusHandlerFunc);
    liveOpenURL("gccRTSPClient",myCameraRtspAddress,NULL,NULL);
    //liveOpenURL("gccRTSPClient","rtsp://admin:bydz87654321@************:554/cam/realmonitor?channel=1&subtype=0","admin","");
    liveDoEventLoop();
}


void startRTCPClient_thread(int IsSelfcall)
{
    create_video_call_cont(IsSelfcall);

    if(isliveRtspStatus != RTSP_STATUS_CLOSE)
    {
        printf("startRTCPClient_thread:already start!\n");
        return;
    }

	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)startRTCPClient, NULL);
	pthread_attr_destroy(&Pthread_Attr);
}