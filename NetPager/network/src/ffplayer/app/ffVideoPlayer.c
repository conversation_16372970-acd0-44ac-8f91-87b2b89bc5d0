#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include <stdint.h>
#include <signal.h>
#include <pthread.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <sys/types.h>
#include <sys/time.h>
#include <sys/stat.h>

#ifdef CHIP_IS_SS268
#include "ss268_panel.h"
#elif defined CHIP_IS_SS22X
#include "ss22x_panel.h"
#else
#include "lv_porting_sstar/sstar_disp.h"
#endif
#include "player.h"
#include "interface.h"

#include "SaveZoneInfo.h"
#include "const.h"

#define SDP_FILE_PATH   "/tmp/rtsp.sdp"

typedef void (*sighandler_t)(int);
sighandler_t signal(int signum, sighandler_t handler);

/*******************************************************************************************/
static int width, height;
static bool b_exit = false;

static bool isVideoPlayStart = false;

void signal_handler_fun(int signum) {
    printf("catch signal [%d]\n", signum);
    b_exit = true;
}

void ffplay_init()
{
    avformat_network_init();
}

//界面退出放在外部
void ffplay_video_exit ()
{
    if(isVideoPlayStart)
    {
        isVideoPlayStart=false;
        printf("ffplay_exit: OK!\n");
    }
    else
    {
        printf("ffplay_exit:error,already exit!\n");
    }
}


int getIsVideoPlayStart()
{
    return isVideoPlayStart || g_mmplayer;
}


static void * mm_player_thread(void *args)
{
    int ret;
    char *filename = (char *)args;

    bool isErrorExit=false; 
    while (!b_exit && isVideoPlayStart)
    {
        ret = mm_player_get_status();
        if (ret < 0)
        {
            printf("mmplayer has been closed!\n");
            continue;
        }

        if (ret & AV_PLAY_ERROR)
        {
            printf("AV_PLAY_ERROR!!!\n");
            mm_player_close();
            isErrorExit = true;
            break;
        }

        #if 0
        else if (ret & AV_PLAY_LOOP)
        {
        }
        else if ((ret & AV_PLAY_COMPLETE) == AV_PLAY_COMPLETE)
        {
            mm_player_close();
            ret = mm_player_open(filename, 0, 0, width, height);
            if (ret < 0)
            {
                b_exit = true;
            }
        }
        #endif

        av_usleep(50 * 1000);
    }

    if(!isErrorExit)
    {
        mm_player_close();
    }
    isVideoPlayStart=false;
    b_exit=false;

    if(isErrorExit)
    {
        //todo，如果是错误退出，需要通知UI，关闭页面,或者UI轮询检测（推荐）
    }

    return NULL;
}


void start_mm_player_thread(void *url)
{
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)mm_player_thread, url);
	pthread_attr_destroy(&Pthread_Attr);
	return;
}




void *ffplayer_video_thread(void *input_file)
{
    int ret, index = 1;
    int volumn = 0;
    bool mute = false, win_down = false;
    char cmd;
    double duration, position;
    char url[256]={0};
    bool disp_flag = false;
    char *filename = (char *)input_file;

    if(isVideoPlayStart)
    {
        printf("VideoPlay is already start!...\n");
        return NULL;
    }

    if (!input_file) {
        printf("invalid input format, please retey!\n");
        printf("such as : ./ssplayer filename\nor: ./ssplayer file1 file2\n");
        return NULL;
    }

    b_exit=false;
    isVideoPlayStart=true;

    //signal(SIGINT, signal_handler_fun);

    ssd20x_getpanel_wh(&width, &height);

    printf("try playing %s ...\n", input_file);

    //jms，屏幕旋转270度，只处理视频流
    mm_player_set_opts("video_rotate", "", AV_ROTATE_90);
    mm_player_set_opts("video_only", "", 1);
    mm_player_set_opts("video_ratio", "", AV_SCREEN_MODE);
    mm_player_set_opts("enable_scaler", "", 0);
    mm_player_set_opts("resolution", "8294400", 0);
    mm_player_set_opts("play_mode", "", AV_LOOP);
    mm_player_set_opts("audio_device", "", 0);

    ret = mm_player_open(input_file, 0, 0, width, height);
    if (ret < 0) {
        printf("mm_player_open failed!\n");
        goto exit;
    }
    //mm_player_getduration(&duration);

    memset(url, '\0', sizeof(url));
    strncpy(url, input_file, strlen(input_file));
    start_mm_player_thread(url);

    return 0;

exit:
    mm_player_close();
    isVideoPlayStart=false;
    b_exit=false;

    return 0;
}




void start_ffplayer_video_thread(void *url)
{
    if(getIsVideoPlayStart())
    {
        printf("getIsVideoPlayStart......\n");
        return;
    }
	int ret=0;
	pthread_t pid;
	pthread_attr_t Pthread_Attr;
	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);
	pthread_create(&pid, &Pthread_Attr, (void *)ffplayer_video_thread, url);
	pthread_attr_destroy(&Pthread_Attr);
}


void generate_sdp_file(int videoCodec) {
    FILE* file = fopen(SDP_FILE_PATH, "w");
    if (file == NULL) {
        printf("Failed to open output.sdp file.");
        return;
    }

    char* codecName;
    int payloadType;

    if (videoCodec == 1) {  // H264
        codecName = "H264";
        payloadType = 96;
    } else if (videoCodec == 2) {  // H265
        codecName = "H265";
        payloadType = 98;
    } else {
        printf("Invalid video codec.");
        fclose(file);
        return;
    }

    fprintf(file, "m=video 9000 RTP/AVP %d\n", payloadType);
    fprintf(file, "c=IN IP4 0.0.0.0\n");
    fprintf(file, "a=rtpmap:%d %s/90000\n", payloadType, codecName);
    fprintf(file, "a=framerate:20\n");

    fclose(file);

    printf("generate_sdp_file OK!\n");
}

void start_ffplayer_video_by_videoCodes(int videoCodecs)
{
    //组建SDP文件
    generate_sdp_file(videoCodecs);
    //启动
    start_ffplayer_video_thread(SDP_FILE_PATH);
}

#if USE_SSD202
int ready_recv_video_stream()
{
    if(m_stPager_Info.self_video_callStatus < VIDEO_CALL_STATUS_TRY_OPEN_RTSP)
    {
        //todo 退出
        return -1;
    }
    else
    {
        m_stPager_Info.self_video_callStatus = VIDEO_CALL_STATUS_ALL_READY;
        Send_video_call_status_feedback(m_stPager_Info.self_video_callStatus);
    }
    return 0;
}
#endif

typedef enum {
    PACKET_TYPE_RTP,
    PACKET_TYPE_RTCP,
    PACKET_TYPE_UNKNOWN
} PacketType;

PacketType getPacketType(uint8_t* packetData) {
    uint8_t payloadType = packetData[1] & 0x7F;
    printf("payloadType=%d\n",payloadType);
    // RTP包头的Payload Type范围是0-127
    if (payloadType >= 0 && payloadType <= 127) {
        return PACKET_TYPE_RTP;
    }

    // RTCP包头的Packet Type范围通常是200-204
    uint8_t rtcpType = packetData[1];
    printf("rtcpType=%d\n",rtcpType);
    if (rtcpType >= 200 && rtcpType <= 204) {
        return PACKET_TYPE_RTCP;
    }

    return PACKET_TYPE_UNKNOWN;
}