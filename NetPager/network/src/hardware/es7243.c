#if defined(USE_SSD202)

#include <stdint.h>
#include <stdio.h>
#include "i2c.h"
#include "const.h"
#include "es7243.h"

#define  I2S_DSP_MODE   0
#define  MCLK_DIV_FRE   256
#define  MCCLK_FREQ     12288000

/* ES7243 address*/
#define ES7243_ADDR                   0x10
#define ES7243_MCLK_SOURCE            FROM_CLOCK_DOUBLE_PIN                            /* In master mode, 0 : MCLK from pad    1 : MCLK from clock doubler */
#define FROM_PAD_PIN                  0
#define FROM_CLOCK_DOUBLE_PIN         1


/*
 * Clock coefficient structer
 */
struct _coeff_div {
    uint32_t mclk;            /* mclk frequency */
    uint32_t lrck;            /* lrck */
    uint8_t  ss_ds;
    uint8_t  adc_div;         /* adcclk divider */
    uint8_t  dll;             /* dll_bypass */
    uint8_t  doubler;         /* doubler enable */
    uint8_t  osr;             /* adc osr */
    uint8_t  mclk_src;        /* select mclk  source */
    uint32_t lrck_h;          /* The high 4 bits of lrck */
    uint32_t lrck_l;          /* The low 8 bits of lrck */
};


static es7243_input_mics_t mic_select = ES7243_INPUT_MIC1 | ES7243_INPUT_MIC2 | ES7243_INPUT_MIC3 | ES7243_INPUT_MIC4;         /* Number of microphones */

/* Codec hifi mclk clock divider coefficients
 *           MEMBER      REG
 *           mclk:       0x03
 *           lrck:       standard
 *           ss_ds:      --
 *           adc_div:    0x02
 *           dll:        0x06
 *           doubler:    0x02
 *           osr:        0x07
 *           mclk_src:   0x03
 *           lrckh:      0x04
 *           lrckl:      0x05
*/
static const struct _coeff_div coeff_div[] = {
    //mclk      lrck    ss_ds adc_div  dll  doubler osr  mclk_src  lrckh   lrckl
    /* 8k */
    {12288000,  8000 ,  0x00,  0x03,  0x01,  0x00,  0x20,  0x00,    0x06,  0x00},
    {16384000,  8000 ,  0x00,  0x04,  0x01,  0x00,  0x20,  0x00,    0x08,  0x00},
    {19200000,  8000 ,  0x00,  0x1e,  0x00,  0x01,  0x28,  0x00,    0x09,  0x60},
    {4096000,   8000 ,  0x00,  0x01,  0x01,  0x00,  0x20,  0x00,    0x02,  0x00},

    /* 11.025k */
    {11289600,  11025,  0x00,  0x02,  0x01,  0x00,  0x20,  0x00,    0x01,  0x00},

    /* 12k */
    {12288000,  12000,  0x00,  0x02,  0x01,  0x00,  0x20,  0x00,    0x04,  0x00},
    {19200000,  12000,  0x00,  0x14,  0x00,  0x01,  0x28,  0x00,    0x06,  0x40},

    /* 16k */
    {4096000,   16000,  0x00,  0x01,  0x01,  0x01,  0x20,  0x00,    0x01,  0x00},
    {19200000,  16000,  0x00,  0x0a,  0x00,  0x00,  0x1e,  0x00,    0x04,  0x80},
    {16384000,  16000,  0x00,  0x02,  0x01,  0x00,  0x20,  0x00,    0x04,  0x00},
    {12288000,  16000,  0x00,  0x03,  0x01,  0x01,  0x20,  0x00,    0x03,  0x00},

    /* 22.05k */
    {11289600,  22050,  0x00,  0x01,  0x01,  0x00,  0x20,  0x00,    0x02,  0x00},

    /* 24k */
    {12288000,  24000,  0x00,  0x01,  0x01,  0x00,  0x20,  0x00,    0x02,  0x00},
    {19200000,  24000,  0x00,  0x0a,  0x00,  0x01,  0x28,  0x00,    0x03,  0x20},

    /* 32k */
    {12288000,  32000,  0x00,  0x03,  0x00,  0x00,  0x20,  0x00,    0x01,  0x80},
    {16384000,  32000,  0x00,  0x01,  0x01,  0x00,  0x20,  0x00,    0x02,  0x00},
    {19200000,  32000,  0x00,  0x05,  0x00,  0x00,  0x1e,  0x00,    0x02,  0x58},

    /* 44.1k */
    {11289600,  44100,  0x00,  0x01,  0x01,  0x01,  0x20,  0x00,    0x01,  0x00},

    /* 48k */
    {12288000,  48000,  0x00,  0x01,  0x01,  0x01,  0x20,  0x00,    0x01,  0x00},
    {19200000,  48000,  0x00,  0x05,  0x00,  0x01,  0x28,  0x00,    0x01,  0x90},

    /* 64k */
    {16384000,  64000,  0x01,  0x01,  0x01,  0x00,  0x20,  0x00,    0x01,  0x00},
    {19200000,  64000,  0x00,  0x05,  0x00,  0x01,  0x1e,  0x00,    0x01,  0x2c},

    /* 88.2k */
    {11289600,  88200,  0x01,  0x01,  0x01,  0x01,  0x20,  0x00,    0x00,  0x80},

    /* 96k */
    {12288000,  96000,  0x01,  0x01,  0x01,  0x01,  0x20,  0x00,    0x00,  0x80},
    {19200000,  96000,  0x01,  0x05,  0x00,  0x01,  0x28,  0x00,    0x00,  0xc8},
};

static bool es7243_write_reg(uint8_t reg_addr, uint8_t data)
{
    if(i2c1_write(ES7243_ADDR, reg_addr,data))
    {
        return true;
    }
    else
    {
        printf("es7243_write_reg:%d error!\n",reg_addr);
        return false;
    }
}

int es7243_read_reg(uint8_t reg_addr)
{
    uint8_t data;
    if( i2c1_read(ES7243_ADDR, reg_addr, &data) )
    {
        return (int)data;
    }
    else
    {
        printf("es7243_read_reg:%d error!\n",reg_addr);
        return 0xff;
    }
}

static bool es7243_update_reg_bit(uint8_t reg_addr, uint8_t update_bits, uint8_t data)
{
    uint8_t regv;
    regv = es7243_read_reg(reg_addr);
    regv = (regv & (~update_bits)) | (update_bits & data);
    return es7243_write_reg(reg_addr, regv);
}

static int get_coeff(uint32_t mclk, uint32_t lrck)
{
    for (int i = 0; i < (sizeof(coeff_div) / sizeof(coeff_div[0])); i++) {
        if (coeff_div[i].lrck == lrck && coeff_div[i].mclk == mclk)
            return i;
    }
    return -1;
}

int8_t get_es7243_mclk_src(void)
{
    return ES7243_MCLK_SOURCE;
}


bool es7243_config_sample(es7243_audio_sampleRate_t sample)
{
    uint8_t regv;
    int coeff;
    int sample_fre = 0;
    int mclk_fre = 0;
    switch (sample) {
        case AUDIO_HAL_08K_SAMPLES:
            sample_fre = 8000;
            break;
        case AUDIO_HAL_11K_SAMPLES:
            sample_fre = 11025;
            break;
        case AUDIO_HAL_16K_SAMPLES:
            sample_fre = 16000;
            break;
        case AUDIO_HAL_22K_SAMPLES:
            sample_fre = 22050;
            break;
        case AUDIO_HAL_24K_SAMPLES:
            sample_fre = 24000;
            break;
        case AUDIO_HAL_32K_SAMPLES:
            sample_fre = 32000;
            break;
        case AUDIO_HAL_44K_SAMPLES:
            sample_fre = 44100;
            break;
        case AUDIO_HAL_48K_SAMPLES:
            sample_fre = 48000;
            break;
        default:
            printf("Unable to configure sample rate %dHz", sample_fre);
            break;
    }
    //mclk_fre = sample_fre * MCLK_DIV_FRE;
    mclk_fre = MCCLK_FREQ;
    coeff = get_coeff(mclk_fre, sample_fre);
    if (coeff < 0) {
        printf("Unable to configure sample rate %dHz with %dHz MCLK", sample_fre, mclk_fre);
        return false;
    }
    /* Set clock parammeters */
    if (coeff >= 0) {
        /* Set adc_div & doubler & dll */
        regv = es7243_read_reg(ES7243_MAINCLK_REG02) & 0x00;
        regv |= coeff_div[coeff].adc_div;
        regv |= coeff_div[coeff].doubler << 6;
        regv |= coeff_div[coeff].dll << 7;
        es7243_write_reg(ES7243_MAINCLK_REG02, regv);
        /* Set osr */
        regv = coeff_div[coeff].osr;
        es7243_write_reg(ES7243_OSR_REG07, regv);
        /* Set lrck */
        regv = coeff_div[coeff].lrck_h;
        es7243_write_reg(ES7243_LRCK_DIVH_REG04, regv);
        regv = coeff_div[coeff].lrck_l;
        es7243_write_reg(ES7243_LRCK_DIVL_REG05, regv);
    }
    return true;
}

bool es7243_mic_select(es7243_input_mics_t mic)
{
    mic_select = mic;
    if (mic_select & (ES7243_INPUT_MIC1 | ES7243_INPUT_MIC2 | ES7243_INPUT_MIC3 | ES7243_INPUT_MIC4)) {
        for (int i = 0; i < 4; i++) {
            es7243_update_reg_bit(ES7243_MIC1_GAIN_REG43 + i, 0x10, 0x00);
        }
        es7243_write_reg(ES7243_MIC12_POWER_REG4B, 0xff);
        es7243_write_reg(ES7243_MIC34_POWER_REG4C, 0xff);
        if (mic_select & ES7243_INPUT_MIC1) {
            printf("Enable ES7243_INPUT_MIC1");
            es7243_update_reg_bit(ES7243_CLOCK_OFF_REG01, 0x0b, 0x00);
            es7243_write_reg(ES7243_MIC12_POWER_REG4B, 0x00);
            es7243_update_reg_bit(ES7243_MIC1_GAIN_REG43, 0x10, 0x10);
        }
        if (mic_select & ES7243_INPUT_MIC2) {
            printf("Enable ES7243_INPUT_MIC2");
            es7243_update_reg_bit(ES7243_CLOCK_OFF_REG01, 0x0b, 0x00);
            es7243_write_reg(ES7243_MIC12_POWER_REG4B, 0x00);
            es7243_update_reg_bit(ES7243_MIC2_GAIN_REG44, 0x10, 0x10);
        }
        if (mic_select & ES7243_INPUT_MIC3) {
            printf("Enable ES7243_INPUT_MIC3");
            es7243_update_reg_bit(ES7243_CLOCK_OFF_REG01, 0x15, 0x00);
            es7243_write_reg(ES7243_MIC34_POWER_REG4C, 0x00);
            es7243_update_reg_bit(ES7243_MIC3_GAIN_REG45, 0x10, 0x10);
        }
        if (mic_select & ES7243_INPUT_MIC4) {
            printf("Enable ES7243_INPUT_MIC4");
            es7243_update_reg_bit(ES7243_CLOCK_OFF_REG01, 0x15, 0x00);
            es7243_write_reg(ES7243_MIC34_POWER_REG4C, 0x00);
            es7243_update_reg_bit(ES7243_MIC4_GAIN_REG46, 0x10, 0x10);
        }
    } else {
        printf("Microphone selection error");
        return false;
    }
    return true;
}


bool es7243_config_i2sMode(audio_hal_iface_i2sMode_t mode)
{
    uint8_t adc_iface = 0;
    adc_iface = es7243_read_reg(ES7243_SDP_INTERFACE1_REG11);
    adc_iface &= 0xfc;
    switch (mode) {
        case AUDIO_HAL_I2S_NORMAL:
            printf("ES7243 in I2S Format");
            adc_iface |= 0x00;
            break;
        case AUDIO_HAL_I2S_LEFT:
        case AUDIO_HAL_I2S_RIGHT:
            printf("ES7243 in LJ Format");
            adc_iface |= 0x01;
            break;
        case AUDIO_HAL_I2S_DSP:
            if (I2S_DSP_MODE) {
                printf("ES7243 in DSP-A Format");
                adc_iface |= 0x13;
            } else {
                printf("ES7243 in DSP-B Format");
                adc_iface |= 0x03;
            }
            break;
        default:
            adc_iface &= 0xfc;
            break;
    }
    es7243_write_reg(ES7243_SDP_INTERFACE1_REG11, adc_iface);

    //开启TDM模式
    es7243_write_reg(ES7243_SDP_INTERFACE2_REG12, 0x02);    // BIT1~BIT0:10,X1 LRCK TDM(I2S)

    return true;
}

bool es7243_set_bits(audio_hal_iface_bits_t bits)
{
    uint8_t adc_iface = 0;
    adc_iface = es7243_read_reg(ES7243_SDP_INTERFACE1_REG11);
    adc_iface &= 0x1f;
    switch (bits) {
        case AUDIO_HAL_BIT_LENGTH_16BITS:
            adc_iface |= 0x60;
            break;
        case AUDIO_HAL_BIT_LENGTH_24BITS:
            adc_iface |= 0x00;
            break;
        case AUDIO_HAL_BIT_LENGTH_32BITS:
            adc_iface |= 0x80;
            break;
        default:
            adc_iface |= 0x60;
            break;
    }
    es7243_write_reg(ES7243_SDP_INTERFACE1_REG11, adc_iface);
    return true;
}

bool es7243_adc_config_i2s(audio_hal_iface_i2sMode_t mode, es7243_audio_sampleRate_t sample, audio_hal_iface_bits_t bits)
{
    es7243_config_i2sMode(mode);
    es7243_config_sample(sample);
    es7243_set_bits(bits);
    return true;
}

bool es7243_start(uint8_t clock_reg_value)
{
    es7243_write_reg(ES7243_CLOCK_OFF_REG01, clock_reg_value);
    es7243_write_reg(ES7243_POWER_DOWN_REG06, 0x00);
    es7243_write_reg(ES7243_ANALOG_REG40, 0x43);
    es7243_write_reg(ES7243_MIC1_POWER_REG47, 0x00);
    es7243_write_reg(ES7243_MIC2_POWER_REG48, 0x00);
    es7243_write_reg(ES7243_MIC3_POWER_REG49, 0x00);
    es7243_write_reg(ES7243_MIC4_POWER_REG4A, 0x00);
    es7243_mic_select(mic_select);
    return true;
}

bool es7243_stop(void)
{
    es7243_write_reg(ES7243_MIC1_POWER_REG47, 0xff);
    es7243_write_reg(ES7243_MIC2_POWER_REG48, 0xff);
    es7243_write_reg(ES7243_MIC3_POWER_REG49, 0xff);
    es7243_write_reg(ES7243_MIC4_POWER_REG4A, 0xff);
    es7243_write_reg(ES7243_MIC12_POWER_REG4B,0xff);
    es7243_write_reg(ES7243_MIC34_POWER_REG4C, 0xff);
    es7243_write_reg(ES7243_ANALOG_REG40, 0xc0);
    es7243_write_reg(ES7243_CLOCK_OFF_REG01, 0x7f);
    es7243_write_reg(ES7243_POWER_DOWN_REG06, 0x07);
    return true;
}


bool es7243_adc_set_gain(es7243_gain_value_t gain1,es7243_gain_value_t gain2,es7243_gain_value_t gain3,es7243_gain_value_t gain4)
{
    #if 0
    uint32_t  max_gain_vaule = 14;
    if (gain < 0) {
        gain = 0;
    } else if (gain > max_gain_vaule) {
        gain = max_gain_vaule;
    }
    printf("SET: gain:%d", gain);
    #endif
    if (mic_select & ES7243_INPUT_MIC1) {
        es7243_update_reg_bit(ES7243_MIC1_GAIN_REG43, 0x0f, gain1);
    }
    if (mic_select & ES7243_INPUT_MIC2) {
        es7243_update_reg_bit(ES7243_MIC2_GAIN_REG44, 0x0f, gain2);
    }
    if (mic_select & ES7243_INPUT_MIC3) {
        es7243_update_reg_bit(ES7243_MIC3_GAIN_REG45, 0x0f, gain3);
    }
    if (mic_select & ES7243_INPUT_MIC4) {
       es7243_update_reg_bit(ES7243_MIC4_GAIN_REG46, 0x0f, gain4);
    }
    return true;
}

uint8_t es7243_adc_get_gain(void)
{
    int regv = 0;
    uint8_t gain_value;
    if (mic_select & ES7243_INPUT_MIC1) {
        regv = es7243_read_reg(ES7243_MIC1_GAIN_REG43);
    } else if (mic_select & ES7243_INPUT_MIC2) {
        regv = es7243_read_reg(ES7243_MIC2_GAIN_REG44);
    } else if (mic_select & ES7243_INPUT_MIC3) {
        regv = es7243_read_reg(ES7243_MIC3_GAIN_REG45);
    } else if (mic_select & ES7243_INPUT_MIC4) {
        regv = es7243_read_reg(ES7243_MIC4_GAIN_REG46);
    } else {
        printf("No MIC selected");
        return false;
    }

    gain_value = (regv & 0x0f);     /* Retain the last four bits for gain */
    printf("GET: gain_value:%d", gain_value);
    return gain_value;
}

void es7243_read_all(void)
{
    for (int i = 0; i <= 0x4E; i++) {
        uint8_t reg = es7243_read_reg(i);
        printf("REG:%02x, %02x\n", reg, i);
    }
}




bool es7243_adc_init(es7243_audio_sampleRate_t sample)
{
    if( !i2c1_init() )
        return false;
    es7243_write_reg(ES7243_RESET_REG00, 0xff);
    es7243_write_reg(ES7243_RESET_REG00, 0x41);
    es7243_write_reg(ES7243_CLOCK_OFF_REG01, 0x1f);
    es7243_write_reg(ES7243_TIME_CONTROL0_REG09, 0x30);      /* Set chip state cycle */
    es7243_write_reg(ES7243_TIME_CONTROL1_REG0A, 0x30);      /* Set power on state cycle */
    es7243_write_reg(ES7243_ADC12_HPF2_REG23, 0x2a);         /* Quick setup */
    es7243_write_reg(ES7243_ADC12_HPF1_REG22, 0x0a);
    es7243_write_reg(ES7243_ADC34_HPF2_REG20, 0x0a);
    es7243_write_reg(ES7243_ADC34_HPF1_REG21, 0x2a);
    /* Set master/slave audio interface */

#if 0   //master
        case AUDIO_HAL_MODE_MASTER:    /* MASTER MODE */
            ESP_LOGI(TAG, "ES7243 in Master mode");
            ret |= es7243_update_reg_bit(ES7243_MODE_CONFIG_REG08, 0x01, 0x01);
            /* Select clock source for internal mclk */
            switch (get_es7243_mclk_src()) {
                case FROM_PAD_PIN:
                    ret |= es7243_update_reg_bit(ES7243_MASTER_CLK_REG03, 0x80, 0x00);
                    break;
                case FROM_CLOCK_DOUBLE_PIN:
                    ret |= es7243_update_reg_bit(ES7243_MASTER_CLK_REG03, 0x80, 0x80);
                    break;
                default:
                    ret |= es7243_update_reg_bit(ES7243_MASTER_CLK_REG03, 0x80, 0x00);
                    break;
            }
            break;
#else  //slave
    es7243_update_reg_bit(ES7243_MODE_CONFIG_REG08, 0x01, 0x00);
#endif

    
    es7243_write_reg(ES7243_ANALOG_REG40, 0x43);               /* Select power off analog, vdda = 3.3V, close vx20ff, VMID select 5KΩ start */
    es7243_write_reg(ES7243_MIC12_BIAS_REG41, 0x70);           /* Select 2.87v */
    es7243_write_reg(ES7243_MIC34_BIAS_REG42, 0x70);           /* Select 2.87v */
    es7243_write_reg(ES7243_OSR_REG07, 0x20);
    es7243_write_reg(ES7243_MAINCLK_REG02, 0xc1);              /* Set the frequency division coefficient and use dll except clock doubler, and need to set 0xc1 to clear the state */
    //es7243_config_sample(AUDIO_HAL_48K_SAMPLES);
    es7243_adc_config_i2s(AUDIO_HAL_I2S_NORMAL,sample,AUDIO_HAL_BIT_LENGTH_16BITS);
    es7243_mic_select(mic_select);
    //四路分别对应line out loop,line in,mic1,mic2
    #if (IS_TRANSMITTER)
    es7243_adc_set_gain(GAIN_0DB,GAIN_0DB,GAIN_3DB,GAIN_3DB);
    #else   //常规的寻呼台MIC2一般接电脑MIC,灵敏度没有那么高，所以将其信号放大
    es7243_adc_set_gain(GAIN_0DB,GAIN_0DB,GAIN_3DB,GAIN_9DB);
    #endif
    printf("es7243_adc_init succeed!\n");
    return true;
}

bool es7243_adc_deinit()
{
    i2c1_deinit();
    return true;
}












unsigned char u8Es7243InitSetting[][2] = {
    {0x00, 0x01}, 
    {0x01, 0x0A},
    {0x02, 0x40},
    {0x03, 0x10},
    {0x04, 0x03},
    {0x05, 0x13},  
    {0x06, 0x00},   //[6:0,BCLK divider at master mode,Divide = DIV_BCLK + 1]
    {0x08, 0x43},
    {0x0B, 0x0C},

};

unsigned char u8Es7243DeinitSetting[][2] = {
    {0x06, 0x05},
    {0x05, 0x1B},
    {0x06, 0x18},
    {0x07, 0x3F},
    {0x08, 0x4B},
    {0x09, 0x9F},
};


int ES7243_Init(void)
{
    int s32Ret;
    unsigned char val = 0;
    unsigned char reg = 0;
    unsigned int u32Index = 0;

    if( !i2c1_init() )
        return false;

    val=es7243_read_reg(0xFD);
    
    printf("=============== 7243 Chip ID1 : 0x%x ======================.\n", val);

    for (u32Index = 0; u32Index < (sizeof(u8Es7243InitSetting) / sizeof(u8Es7243InitSetting[0])); u32Index++)
    {
        reg = u8Es7243InitSetting[u32Index][0];
        val = u8Es7243InitSetting[u32Index][1];

        s32Ret = es7243_write_reg(reg, val);
    }
    printf("================Init ES7243 success.======================\n");

    return 0;
}

int ES7243_Deinit(void)
{
    i2c1_deinit();
    return 0;
}



#endif