#if defined(USE_SSD202)

/*
 * @Author: <PERSON><PERSON>.<PERSON> 
 * @Date: 2022-06-23 14:09:55 
 * @Last Modified by: <PERSON><PERSON>.<PERSON>
 * @Last Modified time: 2022-06-23 14:09:55 
 */

#ifndef _ES7243_H
#define _ES7243_H

#define  ES7243_RESET_REG00                 0x00        /* Reset control */
#define  ES7243_CLOCK_OFF_REG01             0x01        /* Used to turn off the ADC clock */
#define  ES7243_MAINCLK_REG02               0x02        /* Set ADC clock frequency division */
#define  ES7243_MASTER_CLK_REG03            0x03        /* MCLK source $ SCLK division */
#define  ES7243_LRCK_DIVH_REG04             0x04        /* lrck_divh */
#define  ES7243_LRCK_DIVL_REG05             0x05        /* lrck_divl */
#define  ES7243_POWER_DOWN_REG06            0x06        /* power down */
#define  ES7243_OSR_REG07                   0x07
#define  ES7243_MODE_CONFIG_REG08           0x08        /* Set master/slave & channels */
#define  ES7243_TIME_CONTROL0_REG09         0x09        /* Set Chip intial state period*/
#define  ES7243_TIME_CONTROL1_REG0A         0x0A        /* Set Power up state period */
#define  ES7243_SDP_INTERFACE1_REG11        0x11        /* Set sample & fmt */
#define  ES7243_SDP_INTERFACE2_REG12        0x12        /* Pins state */
#define  ES7243_ADC_AUTOMUTE_REG13          0x13        /* Set mute */
#define  ES7243_ADC34_MUTERANGE_REG14       0x14        /* Set mute range */
#define  ES7243_ADC34_HPF2_REG20            0x20        /* HPF */
#define  ES7243_ADC34_HPF1_REG21            0x21
#define  ES7243_ADC12_HPF1_REG22            0x22
#define  ES7243_ADC12_HPF2_REG23            0x23
#define  ES7243_ANALOG_REG40                0x40        /* ANALOG Power */
#define  ES7243_MIC12_BIAS_REG41            0x41
#define  ES7243_MIC34_BIAS_REG42            0x42
#define  ES7243_MIC1_GAIN_REG43             0x43
#define  ES7243_MIC2_GAIN_REG44             0x44
#define  ES7243_MIC3_GAIN_REG45             0x45
#define  ES7243_MIC4_GAIN_REG46             0x46
#define  ES7243_MIC1_POWER_REG47            0x47
#define  ES7243_MIC2_POWER_REG48            0x48
#define  ES7243_MIC3_POWER_REG49            0x49
#define  ES7243_MIC4_POWER_REG4A            0x4A
#define  ES7243_MIC12_POWER_REG4B           0x4B        /* MICBias & ADC & PGA Power */
#define  ES7243_MIC34_POWER_REG4C           0x4C

typedef enum {
    ES7243_AD1_AD0_00 = 0x40,
    ES7243_AD1_AD0_01 = 0x42,
    ES7243_AD1_AD0_10 = 0x44,
    ES7243_AD1_AD0_11 = 0x46
} es7243_address_t;

typedef enum {
    ES7243_INPUT_MIC1 = 0x01,
    ES7243_INPUT_MIC2 = 0x02,
    ES7243_INPUT_MIC3 = 0x04,
    ES7243_INPUT_MIC4 = 0x08
} es7243_input_mics_t;

typedef enum gain_value{
	GAIN_0DB = 0,
	GAIN_3DB,
	GAIN_6DB,
	GAIN_9DB,
	GAIN_12DB,
	GAIN_15DB,
	GAIN_18DB,
	GAIN_21DB,
	GAIN_24DB,
	GAIN_27DB,
	GAIN_30DB,
	GAIN_33DB,
	GAIN_34_5DB,
	GAIN_36DB,
	GAIN_37_5DB,
} es7243_gain_value_t;


typedef enum {
	AUDIO_HAL_08K_SAMPLES = 0x01,
    AUDIO_HAL_11K_SAMPLES = 0x02,
    AUDIO_HAL_16K_SAMPLES = 0x03,
    AUDIO_HAL_22K_SAMPLES = 0x04,
	AUDIO_HAL_24K_SAMPLES = 0x05,
    AUDIO_HAL_32K_SAMPLES = 0x06,
    AUDIO_HAL_44K_SAMPLES = 0x07,
    AUDIO_HAL_48K_SAMPLES = 0x08,
}es7243_audio_sampleRate_t;

typedef enum {
	AUDIO_HAL_BIT_LENGTH_16BITS = 0x01,
    AUDIO_HAL_BIT_LENGTH_24BITS = 0x02,
    AUDIO_HAL_BIT_LENGTH_32BITS = 0x03,
}audio_hal_iface_bits_t;

typedef enum {
	AUDIO_HAL_I2S_NORMAL = 0x01,
    AUDIO_HAL_I2S_LEFT   = 0x02,
    AUDIO_HAL_I2S_RIGHT  = 0x03,
	AUDIO_HAL_I2S_DSP    = 0x04, 
}audio_hal_iface_i2sMode_t;

/*
 * @brief Initialize ES7243 ADC chip
 *
 * @param[in] codec_cfg:  configuration of ES7243
 *
 * @return
 *      - ESP_OK
 *      - ESP_FAIL
 */
bool es7243_adc_init(es7243_audio_sampleRate_t sample);

/**
 * @brief Deinitialize ES7243 ADC chip
 *
 * @return
 *     - ESP_OK
 *     - ESP_FAIL
 */
bool es7243_adc_deinit();

/**
 * @brief Configure ES7243 ADC mode and I2S interface
 *
 * @param[in] mode:  codec mode
 * @param[in] iface:  I2S config
 *
 * @return
 *     - ESP_FAIL Parameter error
 *     - ESP_OK   Success
 */
bool es7243_adc_config_i2s(audio_hal_iface_i2sMode_t mode, es7243_audio_sampleRate_t sample, audio_hal_iface_bits_t bits);


/**
 * @brief  Set gain (Note: the enabled microphone sets the same gain)
 *
 * @param[in] gain:  gain 
 * 
 *       gain        :   value
 *       GAIN_0DB    :   1
 *       GAIN_3DB    :   2
 *       GAIN_6DB    :   3
 *           ·
 *           ·
 *           ·
 *       GAIN_30DB   :   10
 *       GAIN_33DB   :   11
 *       GAIN_34_5DB :   12
 *       GAIN_36DB   :   13
 *       GAIN_37_5DB :   14
 *
 * @return
 *     - ESP_OK
 *     - ESP_FAIL
 */
bool es7243_adc_set_gain(es7243_gain_value_t gain1,es7243_gain_value_t gain2,es7243_gain_value_t gain3,es7243_gain_value_t gain4);

/**
 * @brief Get gain
 *
 * @return
 *     - ESP_OK
 *     - ESP_FAIL
 */
uint8_t es7243_adc_get_gain(void);

/**
 * @brief Select ES7243 mic
 *
 * @param[in] mic:  mics
 * 
 * @return
 *     - ESP_FAIL
 *     - ESP_OK
 */
bool es7243_mic_select(es7243_input_mics_t mic);

/**
 * @brief Read regs of ES7243
 *
 * @param[in] reg_addr:  reg_addr
 * 
 * @return
 *     - ESP_FAIL
 *     - ESP_OK
 */
int es7243_read_reg(uint8_t reg_addr);

/**
 * @brief Read all regs of ES7243
 */
void es7243_read_all(void);








int ES7243_Init(void);


#endif



#endif