#if defined(USE_SSD202)

#include <stdint.h>
#include <stdio.h>
#include "i2c.h"
#include "const.h"
#include "es7210_202.h"

#define  I2S_DSP_MODE   0
#define  MCLK_DIV_FRE   256
#define  MCCLK_FREQ     12288000

/* ES7210 address*/
#define ES7210_ADDR                   ES7210_AD1_AD0_00
#define ES7210_MCLK_SOURCE            FROM_CLOCK_DOUBLE_PIN                            /* In master mode, 0 : MCLK from pad    1 : MCLK from clock doubler */
#define FROM_PAD_PIN                  0
#define FROM_CLOCK_DOUBLE_PIN         1


/***************参数定义***************/
#define CHIPID				0x72100			//Read 0x3D/3E/3F == 0x72100X，判断IC型号是否为ES7210
#define STATEconfirm		0x0B			//状态机确认 回读STATEconfirm的寄存值是否为02，确认IC正常工作状态

#define NORMAL_I2S			0x00
#define NORMAL_LJ			0x01
#define NORMAL_DSPA			0x03
#define NORMAL_DSPB			0x23
#define TDM_1LRCK_DSPA		0x10
#define TDM_1LRCK_DSPB		0x11
#define TDM_1LRCK_I2S		0x12
#define TDM_1LRCK_LJ		0x13
#define TDM_NLRCK_DSPA		0x14
#define TDM_NLRCK_DSPB		0x15
#define TDM_NLRCK_I2S		0x16
#define TDM_NLRCK_LJ		0x17
#define Format_Len24		0x00
#define Format_Len20		0x01
#define Format_Len18		0x02
#define Format_Len16		0x03
#define Format_Len32		0x04

#define VDDA_3V3			0x00
#define VDDA_1V8			0x01
#define MCLK_PIN			0x00
#define SCLK_PIN			0x01
/***************参数定义***************/

/***************参数选择***************/
#define Device 				ES7210			//产品选择ES7210
#define CHANNELS_MAX		2				//TDM_NFS模式下配置,多少CH做级联,偶数,其他模式下默认2即可
#define MSMode_MasterSelOn	0				//产品主从模式选择:默认选择0为SlaveMode,打开为1选择MasterMode
#define Ratio 				32				//实际Ratio=MCLK/LRCK比率，需要和实际时钟比例匹配
#define Format 				NORMAL_I2S		//数据格式选择,需要和实际时序匹配
#define Format_Len			Format_Len16	//数据长度选择,需要和实际时序匹配
#define SCLK_DIV			1				//SCLK分频选择:(选择范围1~127),SCLK=MCLK/SCLK_DIV
#define SCLK_INV			0				//默认对齐方式为下降沿,1为上升沿对齐,需要和实际时序匹配
#define MCLK_SOURCE			SCLK_PIN		//是否硬件没接MCLK需要用SCLK当作MCLK
#define DoubleSpeed			1				//高采样率下配置DoubleSpeed才能工作(ES7210超过48K,ES7210L超过96K)

#define VDDA_VOLTAGE		VDDA_3V3		//模拟电压选择为3V3还是1V8,需要和实际硬件匹配
#define ADC_PGA_GAIN		0				//ADC模拟增益:(选择范围0~14),具体对应关系见相应DS说明
#define ADC_Volume			191				//ADC数字增益:(选择范围0~255),191:0DB,±0.5dB/Ste调试 
#define Dmic_Selon 			0				//DMIC选择:默认选择关闭0,打开为1
#define Dmic_GAIN 			0				//DMIC增益:(选择范围0~7),6dB/Step
/***************参数选择***************/


/*
 * Clock coefficient structer
 */
struct _coeff_div {
    uint32_t mclk;            /* mclk frequency */
    uint32_t lrck;            /* lrck */
    uint8_t  ss_ds;
    uint8_t  adc_div;         /* adcclk divider */
    uint8_t  dll;             /* dll_bypass */
    uint8_t  doubler;         /* doubler enable */
    uint8_t  osr;             /* adc osr */
    uint8_t  mclk_src;        /* select mclk  source */
    uint32_t lrck_h;          /* The high 4 bits of lrck */
    uint32_t lrck_l;          /* The low 8 bits of lrck */
};


static es7210_input_mics_t mic_select = ES7210_INPUT_MIC1 | ES7210_INPUT_MIC2 | ES7210_INPUT_MIC3 | ES7210_INPUT_MIC4;         /* Number of microphones */

/* Codec hifi mclk clock divider coefficients
 *           MEMBER      REG
 *           mclk:       0x03
 *           lrck:       standard
 *           ss_ds:      --
 *           adc_div:    0x02
 *           dll:        0x06
 *           doubler:    0x02
 *           osr:        0x07
 *           mclk_src:   0x03
 *           lrckh:      0x04
 *           lrckl:      0x05
*/
static const struct _coeff_div coeff_div[] = {
    //mclk      lrck    ss_ds adc_div  dll  doubler osr  mclk_src  lrckh   lrckl
    /* 8k */
    {12288000,  8000 ,  0x00,  0x03,  0x01,  0x00,  0x20,  0x00,    0x06,  0x00},
    {16384000,  8000 ,  0x00,  0x04,  0x01,  0x00,  0x20,  0x00,    0x08,  0x00},
    {19200000,  8000 ,  0x00,  0x1e,  0x00,  0x01,  0x28,  0x00,    0x09,  0x60},
    {4096000,   8000 ,  0x00,  0x01,  0x01,  0x00,  0x20,  0x00,    0x02,  0x00},

    /* 11.025k */
    {11289600,  11025,  0x00,  0x02,  0x01,  0x00,  0x20,  0x00,    0x01,  0x00},

    /* 12k */
    {12288000,  12000,  0x00,  0x02,  0x01,  0x00,  0x20,  0x00,    0x04,  0x00},
    {19200000,  12000,  0x00,  0x14,  0x00,  0x01,  0x28,  0x00,    0x06,  0x40},

    /* 16k */
    {4096000,   16000,  0x00,  0x01,  0x01,  0x01,  0x20,  0x00,    0x01,  0x00},
    {19200000,  16000,  0x00,  0x0a,  0x00,  0x00,  0x1e,  0x00,    0x04,  0x80},
    {16384000,  16000,  0x00,  0x02,  0x01,  0x00,  0x20,  0x00,    0x04,  0x00},
    {12288000,  16000,  0x00,  0x03,  0x01,  0x01,  0x20,  0x00,    0x03,  0x00},

    /* 22.05k */
    {11289600,  22050,  0x00,  0x01,  0x01,  0x00,  0x20,  0x00,    0x02,  0x00},

    /* 24k */
    {12288000,  24000,  0x00,  0x01,  0x01,  0x00,  0x20,  0x00,    0x02,  0x00},
    {19200000,  24000,  0x00,  0x0a,  0x00,  0x01,  0x28,  0x00,    0x03,  0x20},

    /* 32k */
    {12288000,  32000,  0x00,  0x03,  0x00,  0x00,  0x20,  0x00,    0x01,  0x80},
    {16384000,  32000,  0x00,  0x01,  0x01,  0x00,  0x20,  0x00,    0x02,  0x00},
    {19200000,  32000,  0x00,  0x05,  0x00,  0x00,  0x1e,  0x00,    0x02,  0x58},

    /* 44.1k */
    {11289600,  44100,  0x00,  0x01,  0x01,  0x01,  0x20,  0x00,    0x01,  0x00},

    /* 48k */
    {12288000,  48000,  0x00,  0x01,  0x01,  0x01,  0x20,  0x00,    0x01,  0x00},
    {19200000,  48000,  0x00,  0x05,  0x00,  0x01,  0x28,  0x00,    0x01,  0x90},

    /* 64k */
    {16384000,  64000,  0x01,  0x01,  0x01,  0x00,  0x20,  0x00,    0x01,  0x00},
    {19200000,  64000,  0x00,  0x05,  0x00,  0x01,  0x1e,  0x00,    0x01,  0x2c},

    /* 88.2k */
    {11289600,  88200,  0x01,  0x01,  0x01,  0x01,  0x20,  0x00,    0x00,  0x80},

    /* 96k */
    {12288000,  96000,  0x01,  0x01,  0x01,  0x01,  0x20,  0x00,    0x00,  0x80},
    {19200000,  96000,  0x01,  0x05,  0x00,  0x01,  0x28,  0x00,    0x00,  0xc8},
};

static bool es7210_write_reg(uint8_t reg_addr, uint8_t data)
{
    if(i2c1_write(ES7210_ADDR, reg_addr,data))
    {
        return true;
    }
    else
    {
        printf("es7210_write_reg:%d error!\n",reg_addr);
        return false;
    }
}

int es7210_read_reg(uint8_t reg_addr)
{
    uint8_t data;
    if( i2c1_read(ES7210_ADDR, reg_addr, &data) )
    {
        return (int)data;
    }
    else
    {
        printf("es7210_read_reg:%d error!\n",reg_addr);
        return 0xff;
    }
}

static bool es7210_update_reg_bit(uint8_t reg_addr, uint8_t update_bits, uint8_t data)
{
    uint8_t regv;
    regv = es7210_read_reg(reg_addr);
    regv = (regv & (~update_bits)) | (update_bits & data);
    return es7210_write_reg(reg_addr, regv);
}

static int get_coeff(uint32_t mclk, uint32_t lrck)
{
    for (int i = 0; i < (sizeof(coeff_div) / sizeof(coeff_div[0])); i++) {
        if (coeff_div[i].lrck == lrck && coeff_div[i].mclk == mclk)
            return i;
    }
    return -1;
}

int8_t get_es7210_mclk_src(void)
{
    return ES7210_MCLK_SOURCE;
}


bool es7210_config_sample(es7210_audio_sampleRate_t sample)
{
    uint8_t regv;
    int coeff;
    int sample_fre = 0;
    int mclk_fre = 0;
    switch (sample) {
        case AUDIO_HAL_08K_SAMPLES:
            sample_fre = 8000;
            break;
        case AUDIO_HAL_11K_SAMPLES:
            sample_fre = 11025;
            break;
        case AUDIO_HAL_16K_SAMPLES:
            sample_fre = 16000;
            break;
        case AUDIO_HAL_22K_SAMPLES:
            sample_fre = 22050;
            break;
        case AUDIO_HAL_24K_SAMPLES:
            sample_fre = 24000;
            break;
        case AUDIO_HAL_32K_SAMPLES:
            sample_fre = 32000;
            break;
        case AUDIO_HAL_44K_SAMPLES:
            sample_fre = 44100;
            break;
        case AUDIO_HAL_48K_SAMPLES:
            sample_fre = 48000;
            break;
        default:
            printf("Unable to configure sample rate %dHz", sample_fre);
            break;
    }
    //mclk_fre = sample_fre * MCLK_DIV_FRE;
    mclk_fre = MCCLK_FREQ;
    coeff = get_coeff(mclk_fre, sample_fre);
    if (coeff < 0) {
        printf("Unable to configure sample rate %dHz with %dHz MCLK", sample_fre, mclk_fre);
        return false;
    }
    /* Set clock parammeters */
    if (coeff >= 0) {
        /* Set adc_div & doubler & dll */
        regv = es7210_read_reg(ES7210_MAINCLK_REG02) & 0x00;
        regv |= coeff_div[coeff].adc_div;
        regv |= coeff_div[coeff].doubler << 6;
        regv |= coeff_div[coeff].dll << 7;
        es7210_write_reg(ES7210_MAINCLK_REG02, regv);
        /* Set osr */
        regv = coeff_div[coeff].osr;
        es7210_write_reg(ES7210_OSR_REG07, regv);
        /* Set lrck */
        regv = coeff_div[coeff].lrck_h;
        es7210_write_reg(ES7210_LRCK_DIVH_REG04, regv);
        regv = coeff_div[coeff].lrck_l;
        es7210_write_reg(ES7210_LRCK_DIVL_REG05, regv);
    }
    return true;
}

bool es7210_mic_select(es7210_input_mics_t mic)
{
    mic_select = mic;
    if (mic_select & (ES7210_INPUT_MIC1 | ES7210_INPUT_MIC2 | ES7210_INPUT_MIC3 | ES7210_INPUT_MIC4)) {
        for (int i = 0; i < 4; i++) {
            es7210_update_reg_bit(ES7210_MIC1_GAIN_REG43 + i, 0x10, 0x00);
        }
        es7210_write_reg(ES7210_MIC12_POWER_REG4B, 0xff);
        es7210_write_reg(ES7210_MIC34_POWER_REG4C, 0xff);
        if (mic_select & ES7210_INPUT_MIC1) {
            printf("Enable ES7210_INPUT_MIC1");
            es7210_update_reg_bit(ES7210_CLOCK_OFF_REG01, 0x0b, 0x00);
            es7210_write_reg(ES7210_MIC12_POWER_REG4B, 0x00);
            es7210_update_reg_bit(ES7210_MIC1_GAIN_REG43, 0x10, 0x10);
        }
        if (mic_select & ES7210_INPUT_MIC2) {
            printf("Enable ES7210_INPUT_MIC2");
            es7210_update_reg_bit(ES7210_CLOCK_OFF_REG01, 0x0b, 0x00);
            es7210_write_reg(ES7210_MIC12_POWER_REG4B, 0x00);
            es7210_update_reg_bit(ES7210_MIC2_GAIN_REG44, 0x10, 0x10);
        }
        if (mic_select & ES7210_INPUT_MIC3) {
            printf("Enable ES7210_INPUT_MIC3");
            es7210_update_reg_bit(ES7210_CLOCK_OFF_REG01, 0x15, 0x00);
            es7210_write_reg(ES7210_MIC34_POWER_REG4C, 0x00);
            es7210_update_reg_bit(ES7210_MIC3_GAIN_REG45, 0x10, 0x10);
        }
        if (mic_select & ES7210_INPUT_MIC4) {
            printf("Enable ES7210_INPUT_MIC4");
            es7210_update_reg_bit(ES7210_CLOCK_OFF_REG01, 0x15, 0x00);
            es7210_write_reg(ES7210_MIC34_POWER_REG4C, 0x00);
            es7210_update_reg_bit(ES7210_MIC4_GAIN_REG46, 0x10, 0x10);
        }
    } else {
        printf("Microphone selection error");
        return false;
    }
    return true;
}


bool es7210_config_i2sMode(audio_hal_iface_i2sMode_t mode)
{
    uint8_t adc_iface = 0;
    adc_iface = es7210_read_reg(ES7210_SDP_INTERFACE1_REG11);
    adc_iface &= 0xfc;
    switch (mode) {
        case AUDIO_HAL_I2S_NORMAL:
            printf("ES7210 in I2S Format");
            adc_iface |= 0x00;
            break;
        case AUDIO_HAL_I2S_LEFT:
        case AUDIO_HAL_I2S_RIGHT:
            printf("ES7210 in LJ Format");
            adc_iface |= 0x01;
            break;
        case AUDIO_HAL_I2S_DSP:
            if (I2S_DSP_MODE) {
                printf("ES7210 in DSP-A Format");
                adc_iface |= 0x13;
            } else {
                printf("ES7210 in DSP-B Format");
                adc_iface |= 0x03;
            }
            break;
        default:
            adc_iface &= 0xfc;
            break;
    }
    es7210_write_reg(ES7210_SDP_INTERFACE1_REG11, adc_iface);

    //开启TDM模式
    es7210_write_reg(ES7210_SDP_INTERFACE2_REG12, 0x02);    // BIT1~BIT0:10,X1 LRCK TDM(I2S)

    return true;
}

bool es7210_set_bits(audio_hal_iface_bits_t bits)
{
    uint8_t adc_iface = 0;
    adc_iface = es7210_read_reg(ES7210_SDP_INTERFACE1_REG11);
    adc_iface &= 0x1f;
    switch (bits) {
        case AUDIO_HAL_BIT_LENGTH_16BITS:
            adc_iface |= 0x60;
            break;
        case AUDIO_HAL_BIT_LENGTH_24BITS:
            adc_iface |= 0x00;
            break;
        case AUDIO_HAL_BIT_LENGTH_32BITS:
            adc_iface |= 0x80;
            break;
        default:
            adc_iface |= 0x60;
            break;
    }
    es7210_write_reg(ES7210_SDP_INTERFACE1_REG11, adc_iface);
    return true;
}

bool es7210_adc_config_i2s(audio_hal_iface_i2sMode_t mode, es7210_audio_sampleRate_t sample, audio_hal_iface_bits_t bits)
{
    es7210_config_i2sMode(mode);
    es7210_config_sample(sample);
    es7210_set_bits(bits);
    return true;
}

bool es7210_start(uint8_t clock_reg_value)
{
    es7210_write_reg(ES7210_CLOCK_OFF_REG01, clock_reg_value);
    es7210_write_reg(ES7210_POWER_DOWN_REG06, 0x00);
    es7210_write_reg(ES7210_ANALOG_REG40, 0x43);
    es7210_write_reg(ES7210_MIC1_POWER_REG47, 0x00);
    es7210_write_reg(ES7210_MIC2_POWER_REG48, 0x00);
    es7210_write_reg(ES7210_MIC3_POWER_REG49, 0x00);
    es7210_write_reg(ES7210_MIC4_POWER_REG4A, 0x00);
    es7210_mic_select(mic_select);
    return true;
}

bool es7210_stop(void)
{
    es7210_write_reg(ES7210_MIC1_POWER_REG47, 0xff);
    es7210_write_reg(ES7210_MIC2_POWER_REG48, 0xff);
    es7210_write_reg(ES7210_MIC3_POWER_REG49, 0xff);
    es7210_write_reg(ES7210_MIC4_POWER_REG4A, 0xff);
    es7210_write_reg(ES7210_MIC12_POWER_REG4B,0xff);
    es7210_write_reg(ES7210_MIC34_POWER_REG4C, 0xff);
    es7210_write_reg(ES7210_ANALOG_REG40, 0xc0);
    es7210_write_reg(ES7210_CLOCK_OFF_REG01, 0x7f);
    es7210_write_reg(ES7210_POWER_DOWN_REG06, 0x07);
    return true;
}


bool es7210_adc_set_gain(es7210_gain_value_t gain1,es7210_gain_value_t gain2,es7210_gain_value_t gain3,es7210_gain_value_t gain4)
{
    #if 0
    uint32_t  max_gain_vaule = 14;
    if (gain < 0) {
        gain = 0;
    } else if (gain > max_gain_vaule) {
        gain = max_gain_vaule;
    }
    printf("SET: gain:%d", gain);
    #endif
    if (mic_select & ES7210_INPUT_MIC1) {
        es7210_update_reg_bit(ES7210_MIC1_GAIN_REG43, 0x0f, gain1);
    }
    if (mic_select & ES7210_INPUT_MIC2) {
        es7210_update_reg_bit(ES7210_MIC2_GAIN_REG44, 0x0f, gain2);
    }
    if (mic_select & ES7210_INPUT_MIC3) {
        es7210_update_reg_bit(ES7210_MIC3_GAIN_REG45, 0x0f, gain3);
    }
    if (mic_select & ES7210_INPUT_MIC4) {
       es7210_update_reg_bit(ES7210_MIC4_GAIN_REG46, 0x0f, gain4);
    }
    return true;
}

uint8_t es7210_adc_get_gain(void)
{
    int regv = 0;
    uint8_t gain_value;
    if (mic_select & ES7210_INPUT_MIC1) {
        regv = es7210_read_reg(ES7210_MIC1_GAIN_REG43);
    } else if (mic_select & ES7210_INPUT_MIC2) {
        regv = es7210_read_reg(ES7210_MIC2_GAIN_REG44);
    } else if (mic_select & ES7210_INPUT_MIC3) {
        regv = es7210_read_reg(ES7210_MIC3_GAIN_REG45);
    } else if (mic_select & ES7210_INPUT_MIC4) {
        regv = es7210_read_reg(ES7210_MIC4_GAIN_REG46);
    } else {
        printf("No MIC selected");
        return false;
    }

    gain_value = (regv & 0x0f);     /* Retain the last four bits for gain */
    printf("GET: gain_value:%d", gain_value);
    return gain_value;
}

void es7210_read_all(void)
{
    for (int i = 0; i <= 0x4E; i++) {
        uint8_t reg = es7210_read_reg(i);
        printf("REG:%02x, %02x\n", reg, i);
    }
}

void es7210_read_state(void)
{
    uint8_t reg = es7210_read_reg(0x0B);
    printf("es7210_read_state:%02x\n", reg);
}




bool es7210_adc_init(es7210_audio_sampleRate_t sample)
{
    if( !i2c1_init() )
        return false;

#if 0
    es7210_write_reg(ES7210_RESET_REG00, 0xff);
    es7210_write_reg(ES7210_RESET_REG00, 0x41);
    es7210_write_reg(ES7210_CLOCK_OFF_REG01, 0x1f);
    es7210_write_reg(ES7210_TIME_CONTROL0_REG09, 0x30);      /* Set chip state cycle */
    es7210_write_reg(ES7210_TIME_CONTROL1_REG0A, 0x30);      /* Set power on state cycle */
    es7210_write_reg(ES7210_ADC12_HPF2_REG23, 0x2a);         /* Quick setup */
    es7210_write_reg(ES7210_ADC12_HPF1_REG22, 0x0a);
    es7210_write_reg(ES7210_ADC34_HPF2_REG20, 0x0a);
    es7210_write_reg(ES7210_ADC34_HPF1_REG21, 0x2a);
    /* Set master/slave audio interface */

#if 0   //master
        case AUDIO_HAL_MODE_MASTER:    /* MASTER MODE */
            ESP_LOGI(TAG, "ES7210 in Master mode");
            ret |= es7210_update_reg_bit(ES7210_MODE_CONFIG_REG08, 0x01, 0x01);
            /* Select clock source for internal mclk */
            switch (get_es7210_mclk_src()) {
                case FROM_PAD_PIN:
                    ret |= es7210_update_reg_bit(ES7210_MASTER_CLK_REG03, 0x80, 0x00);
                    break;
                case FROM_CLOCK_DOUBLE_PIN:
                    ret |= es7210_update_reg_bit(ES7210_MASTER_CLK_REG03, 0x80, 0x80);
                    break;
                default:
                    ret |= es7210_update_reg_bit(ES7210_MASTER_CLK_REG03, 0x80, 0x00);
                    break;
            }
            break;
#else  //slave
    es7210_update_reg_bit(ES7210_MODE_CONFIG_REG08, 0x01, 0x00);
#endif

    
    es7210_write_reg(ES7210_ANALOG_REG40, 0x43);               /* Select power off analog, vdda = 3.3V, close vx20ff, VMID select 5KΩ start */
    es7210_write_reg(ES7210_MIC12_BIAS_REG41, 0x70);           /* Select 2.87v */
    es7210_write_reg(ES7210_MIC34_BIAS_REG42, 0x70);           /* Select 2.87v */
    es7210_write_reg(ES7210_OSR_REG07, 0x20);
    es7210_write_reg(ES7210_MAINCLK_REG02, 0xc1);              /* Set the frequency division coefficient and use dll except clock doubler, and need to set 0xc1 to clear the state */
    //es7210_config_sample(AUDIO_HAL_48K_SAMPLES);
    es7210_adc_config_i2s(AUDIO_HAL_I2S_NORMAL,sample,AUDIO_HAL_BIT_LENGTH_16BITS);
    es7210_mic_select(mic_select);
    //四路分别对应line out loop,line in,mic1,mic2
    #if (IS_TRANSMITTER)
    es7210_adc_set_gain(GAIN_0DB,GAIN_0DB,GAIN_3DB,GAIN_3DB);
    #else   //常规的寻呼台MIC2一般接电脑MIC,灵敏度没有那么高，所以将其信号放大
    es7210_adc_set_gain(GAIN_0DB,GAIN_0DB,GAIN_3DB,GAIN_0DB);
    #endif

#endif

    es7210_write_reg(0x00,0xFF);	
	es7210_write_reg(0x00,0x32);	
	es7210_write_reg(0x09,0x20);	
	es7210_write_reg(0x0A,0x10);	
	es7210_write_reg(0x23,0x2A);	
	es7210_write_reg(0x22,0x0A);	
	es7210_write_reg(0x21,0x2A);	
	es7210_write_reg(0x20,0x0A);	
	
/***************模式选择***************/
	es7210_write_reg(0x08,(CHANNELS_MAX<<3) + (SCLK_INV<<3) + 0x04 + (DoubleSpeed<<1));
	if(Format == TDM_1LRCK_DSPA)
	{
		es7210_write_reg(0x11,(Format_Len<<5) + 0x03);
		es7210_write_reg(0x12,0x01);
	}
	if(Format == TDM_1LRCK_DSPB)
	{
		es7210_write_reg(0x11,(Format_Len<<5) + 0x23);
		es7210_write_reg(0x12,0x01);
	}	
	if(Format == TDM_1LRCK_I2S)
	{
		es7210_write_reg(0x11,(Format_Len<<5) + 0x00);//	
		es7210_write_reg(0x12,0x02);
	}
	if(Format == TDM_1LRCK_LJ)
	{
		es7210_write_reg(0x11,(Format_Len<<5) + 0x01);//	
		es7210_write_reg(0x12,0x02);
	}
	if(Format == TDM_NLRCK_DSPA)
	{
		es7210_write_reg(0x11,(Format_Len<<5) + 0x03);
		es7210_write_reg(0x12,0x03);//N TDMIN 后1P配置 没标志位
		es7210_write_reg(0x12,0x07);//N TDMIN 第1P配置 开启标志位识别		
	}
	if(Format == TDM_NLRCK_DSPB)
	{
		es7210_write_reg(0x11,(Format_Len<<5) + 0x23);	
		es7210_write_reg(0x12,0x03);//N TDMIN 后3P配置 没标志位
		es7210_write_reg(0x12,0x07);//N TDMIN 第1P配置 开启标志位识别		
	}
	if(Format == TDM_NLRCK_I2S)
	{
		es7210_write_reg(0x11,(Format_Len<<5) + 0x00);
		es7210_write_reg(0x12,0x03);//N TDMIN 后1P配置 没标志位
		es7210_write_reg(0x12,0x07);//N TDMIN 第1P配置 开启标志位识别		
	}
	if(Format == TDM_NLRCK_LJ)
	{
		es7210_write_reg(0x11,(Format_Len<<5) + 0x01);	
		es7210_write_reg(0x12,0x03);//N TDMIN 后3P配置 没标志位
		es7210_write_reg(0x12,0x07);//N TDMIN 第1P配置 开启标志位识别		
	}
	if(Format == NORMAL_I2S)
	{
		es7210_write_reg(0x11,(Format_Len<<5) + 0x00);	
		es7210_write_reg(0x12,0x00);	
	}
	if(Format == NORMAL_LJ)
	{
		es7210_write_reg(0x11,(Format_Len<<5) + 0x01);	
		es7210_write_reg(0x12,0x00);	
	}
	if(Format == NORMAL_DSPA)
	{
		es7210_write_reg(0x11,(Format_Len<<5) + 0x03);	
		es7210_write_reg(0x12,0x00);	
	}
	if(Format == NORMAL_DSPB)
	{
		es7210_write_reg(0x11,(Format_Len<<5) + 0x23);	
		es7210_write_reg(0x12,0x00);	
	}
/***************模式选择***************/
		
	es7210_write_reg(0x40,0xC3 - (VDDA_VOLTAGE<<5) + (0x0C*VDDA_VOLTAGE));
	
	es7210_write_reg(0x14,0x3C);
	es7210_write_reg(0x15,0x3C);

	es7210_write_reg(0x17,0x00);
	es7210_write_reg(0x41,0x70);
	es7210_write_reg(0x42,0x70);
	es7210_write_reg(0x47,0x08);	
	es7210_write_reg(0x48,0x08);	
	es7210_write_reg(0x49,0x08);	
	es7210_write_reg(0x4A,0x08);

	if(Dmic_Selon == 1)
	{
		es7210_write_reg(0x10,0xC0 + (Dmic_GAIN<<3) + Dmic_GAIN);	
	}	
	//ADC GAIN
	es7210_write_reg(0x43,0x10 + ADC_PGA_GAIN);	
	es7210_write_reg(0x44,0x10 + ADC_PGA_GAIN);	
	es7210_write_reg(0x45,0x10 + ADC_PGA_GAIN);	
	es7210_write_reg(0x46,0x10 + ADC_PGA_GAIN);	
	//ADC GAIN
	//ADC Volume
	es7210_write_reg(0x1B,ADC_Volume);	
	es7210_write_reg(0x1C,ADC_Volume);	
	es7210_write_reg(0x1D,ADC_Volume);	
	es7210_write_reg(0x1E,ADC_Volume);	
	//ADC Volume
	
//这里的Ratio是指的实际的Ratio
//只是在使用TDMIN-NFS的扩展TDMIN格式下：比如 12M288-48K 6CH,实际的LRCK为16K Ratio=12m288/16=768

	es7210_write_reg(0x03,SCLK_DIV);		//SCLK DIV
	es7210_write_reg(0x04,(Ratio>>8));		//LRCK DIV
	es7210_write_reg(0x05,(Ratio & 0xFF));	//LRCK DIV
	es7210_write_reg(0x06,0x00);

	
	if( Ratio == 3000 )//MCLK/LRCK=3000,24M-8K
	{	//48K下 必须使用DoubleSpeed=1 并且DVDD=3V3 不然无法正常工作
		es7210_write_reg(0x02,0x0F);//3000 *4/15=800	
		es7210_write_reg(0x07,0x32);//800				
	}
	if( Ratio == 2400 )//MCLK/LRCK=2400
	{
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x85);	}//2400 /5=480	
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x8A);	}//2400 /10=240
		es7210_write_reg(0x07,0x1E);//480
	}
	if( Ratio == 2048 )//MCLK/LRCK=2048
	{
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x84);	}
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x88);	}
		es7210_write_reg(0x07,0x20);	
	}
	if( Ratio == 1600 )//MCLK/LRCK=1600
	{	//48K下 必须使用DoubleSpeed=1 并且DVDD=3V3 不然无法正常工作
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x82);	}//1600/2=800
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x84);	}//1600/4=400
		es7210_write_reg(0x07,0x32);//OSR=800	
	}
	if (Ratio == 1500 )//MCLK/LRCK=1500
	{	//48K下 必须使用DoubleSpeed=1 并且DVDD=3V3 不然无法正常工作
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x4F);	}//1500 *8/15=800	
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x0F);	}//1500 *4/15=400
		es7210_write_reg(0x07,0x32);//800	
	}
	if( Ratio == 1200 )//MCLK/LRCK=1200
	{	
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0xC5);	}//1200 *2/5=480	
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x85);	}//1200 *1/5=240
		es7210_write_reg(0x07,0x1E);//480
	}
	if( Ratio == 1024 )//MCLK/LRCK=1024
	{
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x82);	}
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x84);	}
		es7210_write_reg(0x07,0x20);	
	}
	if( Ratio == 800 )//MCLK/LRCK=800
	{	//48K下 必须使用DoubleSpeed=1 并且DVDD=3V3 不然无法正常工作
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x81);	}//800/1=800
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x82);	}//800/2=400
		es7210_write_reg(0x07,0x32);//OSR=800	
	}
	if( Ratio == 768 )//MCLK/LRCK=768
	{
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0xC3);	}//768 *2/3=512	
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x83);	}//768 *1/3=256	
		es7210_write_reg(0x07,0x20);
	}
	if( Ratio == 600 )//MCLK/LRCK=600
	{		
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x05);	}//600 *4/5=480	
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0xC5);	}//600 *2/5=240
		es7210_write_reg(0x07,0x1E);//480		
	}
	if( Ratio == 512 )//MCLK/LRCK=512
	{	
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x81);	}//800/1=800
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x82);	}//800/2=400
		es7210_write_reg(0x07,0x20);	
	}
	if( Ratio == 500 )//MCLK/LRCK=500
	{	//48K下 必须使用DoubleSpeed=1 并且DVDD=3V3 不然无法正常工作
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x45);	}//500 *8/5=800
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x05);	}//500 *4/5=400
		es7210_write_reg(0x07,0x32);//OSR=800			
	}	
	if( Ratio == 400 )//MCLK/LRCK=400
	{	//48K下 必须使用DoubleSpeed=1 并且DVDD=3V3 不然无法正常工作
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0xC1);	}//400*2=800
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x81);	}//400*1=400
		es7210_write_reg(0x07,0x32);//OSR=800		
	}
	if( Ratio == 384 )//MCLK/LRCK=384
	{
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x03);	}//384 *4/3=512	
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0xC3);	}//384 *2/3=256	
		es7210_write_reg(0x07,0x20);
	}
	if( Ratio == 256 )//MCLK/LRCK=256
	{
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0xC1);	}//256*2=512
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x81);	}//256*1=256
		es7210_write_reg(0x07,0x20);
	}
	if( Ratio == 250 )//MCLK/LRCK=250
	{	//48K下 必须使用DoubleSpeed=1 并且DVDD=3V3 不然无法正常工作
		es7210_write_reg(0x02,0x45);//250 *8/5=400
		es7210_write_reg(0x07,0x32);//OSR=800			
	}
	if( Ratio == 192 )//MCLK/LRCK=192
	{
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x43);	}//192 *8/3=512	
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x03);	}//192 *4/3=256	
		es7210_write_reg(0x07,0x20);
	}
	if( Ratio == 128 )//MCLK/LRCK=128
	{
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x01);	}//128*4=512
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0xC1);	}//128*2=256
		es7210_write_reg(0x07,0x20);
	}		
	if( Ratio == 64 )//MCLK/LRCK=64
	{//8K采样率下频率太低注意DVDD接1V8 
		if( DoubleSpeed == 0 )	{	es7210_write_reg(0x02,0x41);	}//64*8=512
		if( DoubleSpeed == 1 )	{	es7210_write_reg(0x02,0x01);	}//64*4=256
		es7210_write_reg(0x07,0x20);
	}	
	if( Ratio == 32 )//MCLK/LRCK=32 PS:32FS下 DoubleSpeed必须配置1
	{//频率太低注意DVDD接1V8 
        printf("Ratio=32.......\n");
		es7210_write_reg(0x02,0x41);	
		es7210_write_reg(0x07,0x20);	
	}

	es7210_write_reg(0x4B,0x0F);	
	es7210_write_reg(0x4C,0x0F);	

	es7210_write_reg(0x01,0x20 + (0x20*MSMode_MasterSelOn));

	es7210_write_reg(0x00,0x71);	
	es7210_write_reg(0x00,0x41);
		
	es7210_write_reg(0x08,0x14 + (SCLK_INV<<3) + (DoubleSpeed<<1) + MSMode_MasterSelOn);	

    es7210_mic_select(mic_select);
    es7210_adc_set_gain(GAIN_0DB,GAIN_3DB,GAIN_3DB,GAIN_0DB);

    es7210_read_state();

    printf("es7210_adc_init succeed!\n");
    return true;
}

bool es7210_adc_deinit()
{
    i2c1_deinit();
    return true;
}


void es7210_reset(void)//后提供IIS时钟后需要额外配置
{
    printf("es7210_reset...\n");
	es7210_write_reg(0x00,0x71);	
	es7210_write_reg(0x00,0x01);	  
}







int ES7210_2AmicInit(void);
int ES7210_2AmicDeInit(void);
int ES7210_2DmicInit(void);
int ES7210_2DmicDeInit(void);
int ES7210_4AmicInit(void);
int ES7210_4AmicDeInit(void);
int ES7210_3AmicInit(void);
int ES7210_3AmicDeInit(void);


unsigned char u8Es7210_2AmicInitSetting[][2] = {
    {0x00, 0xFF},
    {0x00, 0x32},
    {0x09, 0x30},
    {0x0A, 0x30},
    {0x23, 0x2A},
    {0x22, 0x0A},
    {0x21, 0x2A},
    {0x20, 0x0A},
    {0x40, 0xC3},
    {0x41, 0x70},
    {0x42, 0x70},
    {0x43, 0x10},
    {0x44, 0x10},
    {0x45, 0x10},
    {0x46, 0x10},
    {0x47, 0x08},
    {0x48, 0x08},
    {0x49, 0x08},
    {0x4A, 0x08},
    {0x07, 0x20},
    {0x02, 0x41},
    {0x4B, 0x0F},
    {0x4C, 0x0F},
    {0x02, 0x41},
    {0x06, 0x00},
    {0x08, 0x12},
    {0x11, 0x60},
    {0x43, 0x1e},
    {0x44, 0x1e},
    {0x45, 0x10},
    {0x46, 0x10},
    {0x00, 0x71},
    {0x00, 0x41},
/*
    {0x1b, 0xff},
    {0x1c, 0xff},
    {0x1d, 0xbf},
    {0x1e, 0xbf},
*/
};

unsigned char u8Es7210_2DmicInitSetting[][2] = {
    {0x00, 0xFF},
    {0x00, 0x32},
    {0x09, 0x30},
    {0x0A, 0x30},
    {0x23, 0x2A},
    {0x22, 0x0A},
    {0x21, 0x2A},
    {0x20, 0x0A},
    {0x40, 0xC3},
    {0x41, 0x70},
    {0x42, 0x70},
    {0x43, 0x10},
    {0x44, 0x10},
    {0x45, 0x10},
    {0x46, 0x10},
    {0x47, 0x08},
    {0x48, 0x08},
    {0x49, 0x08},
    {0x4A, 0x08},
    {0x07, 0x20},
    {0x02, 0x41},
    {0x4B, 0x0F},
    {0x4C, 0x0F},
    {0x02, 0x41},
    {0x06, 0x00},
    {0x08, 0x12},
    {0x11, 0x60},
/*
    {0x43, 0x1e},
    {0x44, 0x1e},
    {0x45, 0x12},
    {0x46, 0x10},
*/
    {0x00, 0x71},
    {0x00, 0x41},
    {0x10, 0xc0},
/*
    {0x1b, 0xff},
    {0x1c, 0xff},
*/
    {0x1d, 0xff},
    {0x1e, 0xff},
};

unsigned char u8Es7210_4AmicInitSetting[][2] = {
    {0x00, 0xFF},
    {0x00, 0x32},
    {0x09, 0x30},
    {0x0A, 0x30},
    {0x23, 0x2A},
    {0x22, 0x0A},
    {0x21, 0x2A},
    {0x20, 0x0A},
    {0x40, 0xC3},
    {0x41, 0x70},
    {0x42, 0x70},
    {0x43, 0x10},
    {0x44, 0x10},
    {0x45, 0x10},
    {0x46, 0x10},
    {0x47, 0x08},
    {0x48, 0x08},
    {0x49, 0x08},
    {0x4A, 0x08},
    {0x07, 0x20},
    {0x02, 0x41},
    {0x4B, 0x0F},
    {0x4C, 0x0F},
    {0x02, 0x41},
    {0x06, 0x00},
    {0x08, 0x20},
    {0x11, 0x60},
    {0x12, 0x07},
    {0x43, 0x1e},
    {0x44, 0x1e},
    {0x45, 0x12},
    {0x46, 0x10},
    {0x00, 0x71},
    {0x00, 0x41},
/*
    {0x1b, 0xff},
    {0x1c, 0xff},
    {0x1d, 0xff},
    {0x1e, 0xff},
*/
};

unsigned char u8Es7210_3AmicInitSetting[][2] = {
    {0x00, 0xFF},
    {0x00, 0x32},
    {0x09, 0x30},
    {0x0A, 0x30},
    {0x23, 0x2A},
    {0x22, 0x0A},
    {0x21, 0x2A},
    {0x40, 0xC3},
    {0x41, 0x70},
    {0x42, 0x70},
    {0x43, 0x10},
    {0x44, 0x10},
    {0x45, 0x10},
    {0x46, 0x10},
    {0x47, 0x08},
    {0x48, 0x08},
    {0x49, 0x08},
    {0x4A, 0x08},
    {0x07, 0x20},
    {0x02, 0x41},
    {0x4B, 0x0F},
    {0x4C, 0x0F},
    {0x02, 0x41},
    {0x06, 0x00},
    {0x08, 0x12},
    {0x11, 0x60},
    //0x20, mic1 mic2
    //0x10, mic3 mic4
    //mic1和mic3短接，所以无论怎么切拿到的Chn0都是mic1的数据
    {0x12, 0x10},
    {0x43, 0x1e},
    {0x44, 0x1e},
    {0x45, 0x1e},
    {0x46, 0x1e},
    {0x00, 0x71},
    {0x00, 0x41},
};

unsigned char u8Es7210NormalDeinitSetting[][2] = {
    {0x04, 0x02},
    {0x04, 0x01},
    {0xf7, 0x30},
    {0xf9, 0x01},
    {0x16, 0xff},
    {0x17, 0x00},
    {0x01, 0x38},
    {0x20, 0x00},
    {0x21, 0x00},
    {0x00, 0x00},
    {0x00, 0x1e},
    {0x01, 0x30},
    {0x01, 0x00},
};

unsigned char u8Es7210_3AmicDeinitSetting[][2] = {
    {0x06, 0x00},
    {0x4B, 0xFF},
    {0x4C, 0xFF},
    {0x0B, 0xD0},
    {0x40, 0x80},
    {0x01, 0x7F},
    {0x06, 0x07},
};

int ES7210_WriteByte(unsigned char reg, unsigned char val)
{
    es7210_write_reg(reg, val);
}

int ES7210_ReadByte(unsigned char reg, unsigned char *val)
{
    *val=es7210_read_reg(reg);
}

int _ES7210_Init(unsigned char initSetting[][2] , unsigned char initSettingCount)
{
    int s32Ret;
    unsigned char reg = 0;
    unsigned int u32Index = 0;
    unsigned char val = 0;

    if( !i2c1_init() )
        return false;

    s32Ret = ES7210_ReadByte(0x3e, &val);
    if (0 == s32Ret)
    {
        printf("ES7210 ID0:%x.\n", val);
    }
    else
    {
        return s32Ret;
    }

    s32Ret = ES7210_ReadByte(0x3d, &val);
    if (0 == s32Ret)
    {
        printf("ES7210 ID1:%x.\n", val);
    }
    else
    {
        return s32Ret;
    }

    for (u32Index = 0; u32Index < initSettingCount; u32Index++)
    {
        reg = initSetting[u32Index][0];
        val = initSetting[u32Index][1];

        s32Ret = ES7210_WriteByte(reg, val);
        if (0 != s32Ret)
        {
            return s32Ret;
        }
    }

    printf("================Init ES7210 success.======================\n");

    return 0;
}

int _ES7210_Deinit(unsigned char deinitSetting[][2], unsigned char deinitSettingCount)
{
    printf("========================Suspend ES7210 success.===========================\n");
	return 0;
}

int ES7210_2AmicInit(void)
{
    return _ES7210_Init(u8Es7210_2AmicInitSetting, 
            sizeof(u8Es7210_2AmicInitSetting) / sizeof(u8Es7210_2AmicInitSetting[0]));
}

int ES7210_2AmicDeInit(void)
{
    return _ES7210_Deinit(u8Es7210NormalDeinitSetting,
            sizeof(u8Es7210NormalDeinitSetting) / sizeof(u8Es7210NormalDeinitSetting[0]));
}

int ES7210_2DmicInit(void)
{
    return _ES7210_Init(u8Es7210_2DmicInitSetting,
            sizeof(u8Es7210_2DmicInitSetting) / sizeof(u8Es7210_2DmicInitSetting[0]));
}

int ES7210_2DmicDeInit(void)
{
    return _ES7210_Deinit(u8Es7210NormalDeinitSetting,
            sizeof(u8Es7210NormalDeinitSetting) / sizeof(u8Es7210NormalDeinitSetting[0]));
}

int ES7210_4AmicInit(void)
{
    return _ES7210_Init(u8Es7210_4AmicInitSetting,
            sizeof(u8Es7210_4AmicInitSetting) / sizeof(u8Es7210_4AmicInitSetting[0]));
}

int ES7210_4AmicDeInit(void)
{
    return _ES7210_Deinit(u8Es7210NormalDeinitSetting,
            sizeof(u8Es7210NormalDeinitSetting) / sizeof(u8Es7210NormalDeinitSetting[0]));
}

int ES7210_3AmicInit(void)
{
    return _ES7210_Init(u8Es7210_3AmicInitSetting,
            sizeof(u8Es7210_3AmicInitSetting) / sizeof(u8Es7210_3AmicInitSetting[0]));
}

int ES7210_3AmicDeInit(void)
{
    return _ES7210_Deinit(u8Es7210_3AmicDeinitSetting,
            sizeof(u8Es7210_3AmicDeinitSetting) / sizeof(u8Es7210NormalDeinitSetting[0]));
}





#endif