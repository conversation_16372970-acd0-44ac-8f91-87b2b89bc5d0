/**************************************************************************
  Filename:			sysconf.c
  
  Author:			lixiangkai
  
  Create Date:      2013-10-31
  
  Description:		系统设备配置
**************************************************************************/
#include <stdio.h>
#include <fcntl.h>
#include <unistd.h>
#include <stdlib.h>
#include <linux/soundcard.h>
#include <linux/watchdog.h>
#include <strings.h>
#include <pthread.h>
#include "sysconf.h"
#include <signal.h>
#include <dirent.h>
#include <fnmatch.h>
#include <stdbool.h>
#include <sys/stat.h>

typedef void (*sighandler_t)(int);

/*********************************************************************
 * @fn      IsFileExist
 *
 * @brief   判断文件是否存在
 *
 * @param   *file_path  - 需删除文件的绝对路径
 *
 * @return  成功-0 失败-1
 */
bool IsFileExist(const char *file_path)
{
	/*
	mode	Description
	F_OK	测试文件是否存在
	R_OK	测试文件是否有读权限
	W_OK	测试文件是否有写权限
	X_OK	测试文件是否有执行权限
	*/
	if( access(file_path, F_OK) == 0 )
		return true;
	return false;
}

/*********************************************************************
 * @fn      get_file_size
 *
 * @brief   获取指定文件的大小
 *
 * @param   path - 文件存放的绝对路径
 *
 * @return  filesize - 文件大小
 */
unsigned long Get_File_Size(const char *path)
{  
    unsigned long int filesize = 0;
    struct stat statbuff;
	
    if(stat(path, &statbuff) < 0)
	{
        return filesize;
    }
	else
	{
        filesize = statbuff.st_size;
		if(Paging_status != PAGING_START) printf("filesize = %ld\n", filesize);
    }
	
    return filesize;
}

bool WildcardFileSearch(const char* pattern,const char* dirPath,char *dstFileName)
{
	struct dirent *entry;
	DIR *dir;
	int ret;

	dir = opendir(dirPath);
	if(dir != NULL)
	{
		while((entry = readdir(dir)) != NULL)
		{
			ret = fnmatch(pattern,entry->d_name,FNM_PATHNAME | FNM_PERIOD);
			if(ret == 0)
			{
				sprintf(dstFileName,entry->d_name);
				return true;
			}
			else
			{
				continue;
			}
		}
	}

	closedir(dir);
	return false;
}

/*********************************************************************
 * @fn      i2s_init
 *
 * @brief   I2S初始化
 *
 * @param   rate - 采样率
 *          afmt - 数据格式
 *
 * @return  ERROR - 失败
 *          SUCCEED - 成功
 */
int i2s_init(int rate, int afmt)
{
	int fd;
	int ret = -1;
	
	/*打开音频设备*/
	fd = open(DSP, O_RDWR);
	if(fd < 0)
	{
		if(Paging_status != PAGING_START) printf("i2s_init open error!!!\n");
		return ERROR;
	}
	
	/*初始化I2S采样率*/
    ret = ioctl(fd, SNDCTL_DSP_SPEED, &rate);
	if (ret < 0)
	{
		if(Paging_status != PAGING_START) printf("Init i2s failed!\n");
		close(fd);
		return ERROR;
	}
	
	/*初始化I2S输出数据格式*/
	ret = ioctl(fd, SNDCTL_DSP_SETFMT, &afmt);   /*support 16/24/32*/
	if (ret < 0)
	{
		if(Paging_status != PAGING_START) printf("Init i2s failed!\n");
		close(fd);
		return ERROR;
	}
	
	close(fd);
	return SUCCEED;
}

extern int lvglMutex_status;			//lvgl界面互斥锁状态，1表示已加锁
/*********************************************************************
 * @fn      pthread_dog
 *
 * @brief   回调函数 喂狗
 *
 * @param
 *
 * @return
 */
void *pthread_dog()
{
	int fdw;
	int ret = -1;
	int i=0;
	//打开看门狗
	fdw = open(WATDOG_DEV, O_RDWR);
	if(fdw < 0)
	{
		if(Paging_status != PAGING_START) printf("Can not open /dev/watchdog!\n");
		pthread_exit(NULL);
	}

	int timeout = WDT_TIMEOUT;
	ret = ioctl(fdw, WDIOC_SETTIMEOUT, &timeout); //此处不能直接用timeout，必须要用变量中转注意。
	if (ret < 0)
	{
		if(Paging_status != PAGING_START) printf("Feed watchdog failed!\n");
		close(fdw);
		pthread_exit(NULL);
	}
	else
	{
		if(Paging_status != PAGING_START) printf("feed watchdog %d\n", i++);
	}

	int sleep_cnt=0;
	int temp_lvglMutex=lvglMutex_status;
	int temp_lvgl_Nochange_cnt=0;
	/*循环喂狗*/
	while(1)
	{
		sleep_cnt++;

		if(temp_lvglMutex != lvglMutex_status)
		{
			temp_lvglMutex=lvglMutex_status;
			temp_lvgl_Nochange_cnt=0;
		}
		else
		{
			if( lvglMutex_status == 1)
			{
				temp_lvgl_Nochange_cnt++;
			}
			else
			{
				temp_lvgl_Nochange_cnt=0;
			}
			if(temp_lvgl_Nochange_cnt >= 200)		//连续锁定10s
			{
				temp_lvgl_Nochange_cnt=0;
				printf("Error:lvgl_Nochange_cnt>10s,reboot\n");
				System_Reboot();
			}
		}
		
		if(sleep_cnt>=100)		//5s喂一次狗
		{
			sleep_cnt=0;
			#if USE_ASM9260
			write(fdw, "a", 1); //喂狗
			#elif defined(USE_SSD212) || defined(USE_SSD202)
			ioctl(fdw, WDIOC_KEEPALIVE, 0);
			#endif
			if(Paging_status != PAGING_START) printf("feed watchdog %d,temp_lvgl_Nochange_cnt=%d\n", i++,temp_lvgl_Nochange_cnt);
		}
		usleep(50000);
	}
}

/*********************************************************************
 * @fn      Init_watdog
 *
 * @brief   喂狗
 *
 * @param
 *
 * @return  ERROR - 失败
 *          SUCCEED - 成功
 */
int Init_watdog()
{
	#if CLOSE_WATCHDOG
	return SUCCEED;
	#endif 
	int ret;
	pthread_t Dog_pthread;
	pthread_attr_t Pthread_Attr;

	pthread_attr_init(&Pthread_Attr);
	pthread_attr_setdetachstate(&Pthread_Attr, PTHREAD_CREATE_DETACHED);

	ret = pthread_create(&Dog_pthread, &Pthread_Attr, (void *)pthread_dog, NULL);
	if (ret == -1)
	{
		if(Paging_status != PAGING_START) printf("Pthread Creat Failure!!!\n");
		return ERROR;
	}
	else
	{
		if(Paging_status != PAGING_START) printf("Pthread Creat Seccess!\n");
	}

	pthread_attr_destroy(&Pthread_Attr);
	return SUCCEED;
}









#if 1
/*********************************************************************
 * @fn      Exec_System_CMD
 *
 * @brief   执行系统命令 （SYSTEM函数C语言实现源码）
 *
 * @param   cmdstring:命令参数
 *
 * @return  失败返回 -1
 */
int Exec_System_CMD(const char * cmdstring)
{
    pid_t pid;
    int status=0;
    if(cmdstring == NULL)
    {
        return (1);
    }
    if((pid = fork())<0)	//fork一个子进程
    {
        status = -1;	//fork失败返回-1
    }
    else if(pid == 0)	//子进程(调用EXEC函数执行)
    {
        #if _DEBUG_TP_
        if(Paging_status != PAGING_START) printf("cmdstring=%s\n", cmdstring);
        #endif
        execl("/bin/sh", "sh", "-c", cmdstring, (char *)0);
        exit (127);	//exec执行失败返回127,注意exec只在失败时才返回现在的进程，成功的话现在的进程就不存在了
    }
    else	//父进程(调用WAIT去等待子进程结束）
    {
		if ( waitpid (pid, &status, 0) != pid)
			status = -1;
    }
    return status;		//如果waitpid成功，则返回子进程的返回状态
}



/*********************************************************************
 * @fn      Popen_CMD
 *
 * @brief   popen命令
 *
 * @param   m_cmd:命令参数
 *
 * @return  失败返回 -1
 */
int Popen_CMD(const char *m_cmd)
{
	FILE *popen_fp =NULL;
	char msg_buf[1024]={0};
	popen_fp = popen(m_cmd, "r");
	if (popen_fp == NULL)
	{
		perror("Popen_CMD popen error:");
		return ERROR;
	}
	else
	{
		if(Paging_status != PAGING_START) printf("Popen_CMD m_cmd:%s\n", m_cmd);
		fgets(msg_buf, sizeof(msg_buf), popen_fp);
		if(Paging_status != PAGING_START) printf("popen msg:%s\n", msg_buf);
		fclose(popen_fp);
		return SUCCEED;
	}
}

/*********************************************************************
 * @fn      pox_system
 *
 * @brief   重新封装system函数，以免出现错误
 *
 * @param   *cmd_line - 操作对象
 *
 * @return  ret
 */
int pox_system(const char *cmd_line)
{
	int ret = 0;

	sighandler_t old_handler;
	old_handler = signal(SIGCHLD, SIG_DFL);
	ret = system(cmd_line);
	signal(SIGCHLD, old_handler);

	if((ret == 127) || (ret < 0))
	{
		return ERROR;
	}
	else
	{
		return SUCCEED;
	}
}



void System_Reboot()
{
	#ifdef USE_PC_SIMULATOR
	return;
	#endif
	printf("System_Reboot...\n");
	Amp_Switch(0);	//关闭功放
	#if defined(USE_SSD212) || defined(USE_SSD202)
	Signal_OutPut_switch(0);	//关闭运放
	#endif
	
	pox_system("sync");
	pox_system("sync");
	pox_system("sync");

	pox_system("reboot");
}


#endif

