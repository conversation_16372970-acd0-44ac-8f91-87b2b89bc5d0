/*
*---------------------------------------------------------------
*                        Lvgl Img Tool                          
*                                                               
* 注:使用UTF8编码                                                 
* 注:本字体文件由Lvgl Img Tool V0.1 生成                           
* 作者:阿里(qq:617622104)                                         
*---------------------------------------------------------------
*/


#include "lvgl/lvgl.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif


const LV_ATTRIBUTE_MEM_ALIGN uint8_t pic_singlePlay_map[] = {
#if LV_COLOR_DEPTH == 1 || LV_COLOR_DEPTH == 8
//Pixel format: Alpha 8 bit, Red: 3 bit, Green: 3 bit, Blue: 2 bit
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x02, 0xff, 0x07, 0xff, 0x13, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x13, 0xff, 0x07, 0xff, 0x02, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x01, 0xff, 0x17, 0xff, 0x5b, 0xff, 0x8e, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x8e, 0xff, 0x5b, 0xff, 0x17, 0xff, 0x02, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x01, 0xff, 0x06, 0xff, 0x5b, 0xff, 0xe8, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xe8, 0xff, 0x5b, 0xff, 0x07, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x02, 0xff, 0x11, 0xff, 0x90, 0xff, 0xfd, 0xff, 0xdb, 0xff, 0xad, 0xff, 0xa6, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa6, 0xff, 0xad, 0xff, 0xdb, 0xff, 0xfd, 0xff, 0x90, 0xff, 0x13, 
  0xff, 0x01, 0xff, 0x02, 0xff, 0x03, 0xff, 0x02, 0xff, 0x04, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xa9, 0xff, 0x2c, 0xff, 0x1b, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x19, 0xff, 0x1b, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1b, 0xff, 0x19, 0xff, 0x18, 0xff, 0x18, 0xff, 0x18, 0xff, 0x1b, 0xff, 0x2c, 0xff, 0xa9, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x04, 0xff, 0x0d, 0xff, 0x15, 0xff, 0x0d, 0xff, 0x06, 0xff, 0x18, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xa1, 0xff, 0x1a, 0xff, 0x07, 0xff, 0x04, 0xff, 0x04, 0xff, 0x04, 0xff, 0x06, 0xff, 0x16, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x16, 0xff, 0x06, 0xff, 0x04, 0xff, 0x04, 0xff, 0x04, 0xff, 0x07, 0xff, 0x1a, 0xff, 0xa1, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x0f, 0xff, 0x61, 0xff, 0x9a, 0xff, 0x61, 0xff, 0x11, 0xff, 0x19, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x07, 0xff, 0x93, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x93, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x0d, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x07, 0xff, 0x9d, 0xff, 0xaa, 0xff, 0xd1, 0xff, 0xf9, 0xff, 0xf2, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x16, 0xff, 0x23, 0xff, 0x8c, 0xff, 0xf4, 0xff, 0xf2, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x10, 0xff, 0x81, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x0d, 0xff, 0x80, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x0d, 0xff, 0x80, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x0d, 0xff, 0x80, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x0d, 0xff, 0x80, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x0d, 0xff, 0x80, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x0d, 0xff, 0x80, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x0d, 0xff, 0x80, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x07, 0xff, 0x52, 0xff, 0x9d, 0xff, 0x9d, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x17, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xa1, 0xff, 0x19, 0xff, 0x06, 0xff, 0x03, 0xff, 0x03, 0xff, 0x03, 0xff, 0x03, 0xff, 0x03, 0xff, 0x06, 0xff, 0x0f, 0xff, 0x19, 0xff, 0x19, 0xff, 0x06, 0xff, 0x03, 0xff, 0x03, 0xff, 0x03, 0xff, 0x06, 0xff, 0x19, 0xff, 0xa1, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xa8, 0xff, 0x29, 0xff, 0x18, 0xff, 0x15, 0xff, 0x15, 0xff, 0x15, 0xff, 0x15, 0xff, 0x15, 0xff, 0x16, 0xff, 0x17, 0xff, 0x18, 0xff, 0x18, 0xff, 0x16, 0xff, 0x15, 0xff, 0x15, 0xff, 0x15, 0xff, 0x18, 0xff, 0x29, 0xff, 0xa8, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x1a, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x19, 0xff, 0x14, 0xff, 0x90, 0xff, 0xfd, 0xff, 0xd8, 0xff, 0xa4, 0xff, 0x9b, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9b, 0xff, 0xa4, 0xff, 0xd8, 0xff, 0xfd, 0xff, 0x90, 0xff, 0x13, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x18, 0xff, 0x09, 0xff, 0x5b, 0xff, 0xe8, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xe8, 0xff, 0x5b, 0xff, 0x07, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x05, 0xff, 0x17, 0xff, 0x5b, 0xff, 0x91, 0xff, 0xa3, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa3, 0xff, 0x91, 0xff, 0x5b, 0xff, 0x17, 0xff, 0x02, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xa1, 0xff, 0x19, 0xff, 0x06, 0xff, 0x04, 0xff, 0x09, 0xff, 0x15, 0xff, 0x1b, 0xff, 0x1b, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1a, 0xff, 0x19, 0xff, 0x18, 0xff, 0x12, 0xff, 0x06, 0xff, 0x01, 0x00, 0x00, 
  0xff, 0x1a, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xa8, 0xff, 0x29, 0xff, 0x18, 0xff, 0x16, 0xff, 0x16, 0xff, 0x18, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x19, 0xff, 0x10, 0xff, 0x06, 0xff, 0x04, 0xff, 0x02, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0x13, 0xff, 0x90, 0xff, 0xfd, 0xff, 0xd8, 0xff, 0xa4, 0xff, 0x9b, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x9a, 0xff, 0x61, 0xff, 0x0d, 0xff, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0x07, 0xff, 0x5b, 0xff, 0xe8, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0x17, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0x02, 0xff, 0x17, 0xff, 0x5b, 0xff, 0x91, 0xff, 0xa3, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0xa4, 0xff, 0x67, 0xff, 0x0e, 0xff, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x02, 0xff, 0x07, 0xff, 0x14, 0xff, 0x1b, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x1c, 0xff, 0x10, 0xff, 0x04, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
//Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x02, 0xff, 0xff, 0x07, 0xff, 0xff, 0x13, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x13, 0xff, 0xff, 0x07, 0xff, 0xff, 0x02, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x01, 0xff, 0xff, 0x17, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x8e, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x8e, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x17, 0xff, 0xff, 0x02, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x01, 0xff, 0xff, 0x06, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xe8, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xe8, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x07, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x02, 0xff, 0xff, 0x11, 0xff, 0xff, 0x90, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xdb, 0xff, 0xff, 0xad, 0xff, 0xff, 0xa6, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa6, 0xff, 0xff, 0xad, 0xff, 0xff, 0xdb, 0xff, 0xff, 0xfd, 0xff, 0xff, 0x90, 0xff, 0xff, 0x13, 
  0xff, 0xff, 0x01, 0xff, 0xff, 0x02, 0xff, 0xff, 0x03, 0xff, 0xff, 0x02, 0xff, 0xff, 0x04, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa9, 0xff, 0xff, 0x2c, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x19, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x19, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x2c, 0xff, 0xff, 0xa9, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x04, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x15, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x06, 0xff, 0xff, 0x18, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x07, 0xff, 0xff, 0x04, 0xff, 0xff, 0x04, 0xff, 0xff, 0x04, 0xff, 0xff, 0x06, 0xff, 0xff, 0x16, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x16, 0xff, 0xff, 0x06, 0xff, 0xff, 0x04, 0xff, 0xff, 0x04, 0xff, 0xff, 0x04, 0xff, 0xff, 0x07, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xa1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x0f, 0xff, 0xff, 0x61, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x61, 0xff, 0xff, 0x11, 0xff, 0xff, 0x19, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x07, 0xff, 0xff, 0x93, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x93, 0xff, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x07, 0xff, 0xff, 0x9d, 0xff, 0xff, 0xaa, 0xff, 0xff, 0xd1, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x16, 0xff, 0xff, 0x23, 0xff, 0xff, 0x8c, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x10, 0xff, 0xff, 0x81, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x07, 0xff, 0xff, 0x52, 0xff, 0xff, 0x9d, 0xff, 0xff, 0x9d, 0xff, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0xff, 0xff, 0x19, 0xff, 0xff, 0x06, 0xff, 0xff, 0x03, 0xff, 0xff, 0x03, 0xff, 0xff, 0x03, 0xff, 0xff, 0x03, 0xff, 0xff, 0x03, 0xff, 0xff, 0x06, 0xff, 0xff, 0x0f, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x06, 0xff, 0xff, 0x03, 0xff, 0xff, 0x03, 0xff, 0xff, 0x03, 0xff, 0xff, 0x06, 0xff, 0xff, 0x19, 0xff, 0xff, 0xa1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8, 0xff, 0xff, 0x29, 0xff, 0xff, 0x18, 0xff, 0xff, 0x15, 0xff, 0xff, 0x15, 0xff, 0xff, 0x15, 0xff, 0xff, 0x15, 0xff, 0xff, 0x15, 0xff, 0xff, 0x16, 0xff, 0xff, 0x17, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x16, 0xff, 0xff, 0x15, 0xff, 0xff, 0x15, 0xff, 0xff, 0x15, 0xff, 0xff, 0x18, 0xff, 0xff, 0x29, 0xff, 0xff, 0xa8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x19, 0xff, 0xff, 0x14, 0xff, 0xff, 0x90, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xd8, 0xff, 0xff, 0xa4, 0xff, 0xff, 0x9b, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9b, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xd8, 0xff, 0xff, 0xfd, 0xff, 0xff, 0x90, 0xff, 0xff, 0x13, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x18, 0xff, 0xff, 0x09, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xe8, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xe8, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x07, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x05, 0xff, 0xff, 0x17, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x91, 0xff, 0xff, 0xa3, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa3, 0xff, 0xff, 0x91, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x17, 0xff, 0xff, 0x02, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0xff, 0xff, 0x19, 0xff, 0xff, 0x06, 0xff, 0xff, 0x04, 0xff, 0xff, 0x09, 0xff, 0xff, 0x15, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x19, 0xff, 0xff, 0x18, 0xff, 0xff, 0x12, 0xff, 0xff, 0x06, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8, 0xff, 0xff, 0x29, 0xff, 0xff, 0x18, 0xff, 0xff, 0x16, 0xff, 0xff, 0x16, 0xff, 0xff, 0x18, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x10, 0xff, 0xff, 0x06, 0xff, 0xff, 0x04, 0xff, 0xff, 0x02, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0x13, 0xff, 0xff, 0x90, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xd8, 0xff, 0xff, 0xa4, 0xff, 0xff, 0x9b, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x61, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0x07, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xe8, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0x02, 0xff, 0xff, 0x17, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x91, 0xff, 0xff, 0xa3, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0x67, 0xff, 0xff, 0x0e, 0xff, 0xff, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0xff, 0xff, 0x02, 0xff, 0xff, 0x07, 0xff, 0xff, 0x14, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x10, 0xff, 0xff, 0x04, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
//Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x02, 0xff, 0xff, 0x07, 0xff, 0xff, 0x13, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x13, 0xff, 0xff, 0x07, 0xff, 0xff, 0x02, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x01, 0xff, 0xff, 0x17, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x8e, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x8e, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x17, 0xff, 0xff, 0x02, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x01, 0xff, 0xff, 0x06, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xe8, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xe8, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x07, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x02, 0xff, 0xff, 0x11, 0xff, 0xff, 0x90, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xdb, 0xff, 0xff, 0xad, 0xff, 0xff, 0xa6, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa6, 0xff, 0xff, 0xad, 0xff, 0xff, 0xdb, 0xff, 0xff, 0xfd, 0xff, 0xff, 0x90, 0xff, 0xff, 0x13, 
  0xff, 0xff, 0x01, 0xff, 0xff, 0x02, 0xff, 0xff, 0x03, 0xff, 0xff, 0x02, 0xff, 0xff, 0x04, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa9, 0xff, 0xff, 0x2c, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x19, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x19, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x2c, 0xff, 0xff, 0xa9, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x04, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x15, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x06, 0xff, 0xff, 0x18, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x07, 0xff, 0xff, 0x04, 0xff, 0xff, 0x04, 0xff, 0xff, 0x04, 0xff, 0xff, 0x06, 0xff, 0xff, 0x16, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x16, 0xff, 0xff, 0x06, 0xff, 0xff, 0x04, 0xff, 0xff, 0x04, 0xff, 0xff, 0x04, 0xff, 0xff, 0x07, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xa1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x0f, 0xff, 0xff, 0x61, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x61, 0xff, 0xff, 0x11, 0xff, 0xff, 0x19, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x07, 0xff, 0xff, 0x93, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x93, 0xff, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x07, 0xff, 0xff, 0x9d, 0xff, 0xff, 0xaa, 0xff, 0xff, 0xd1, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x16, 0xff, 0xff, 0x23, 0xff, 0xff, 0x8c, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x10, 0xff, 0xff, 0x81, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x80, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf2, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x07, 0xff, 0xff, 0x52, 0xff, 0xff, 0x9d, 0xff, 0xff, 0x9d, 0xff, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0xff, 0x17, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0xff, 0xff, 0x19, 0xff, 0xff, 0x06, 0xff, 0xff, 0x03, 0xff, 0xff, 0x03, 0xff, 0xff, 0x03, 0xff, 0xff, 0x03, 0xff, 0xff, 0x03, 0xff, 0xff, 0x06, 0xff, 0xff, 0x0f, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x06, 0xff, 0xff, 0x03, 0xff, 0xff, 0x03, 0xff, 0xff, 0x03, 0xff, 0xff, 0x06, 0xff, 0xff, 0x19, 0xff, 0xff, 0xa1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8, 0xff, 0xff, 0x29, 0xff, 0xff, 0x18, 0xff, 0xff, 0x15, 0xff, 0xff, 0x15, 0xff, 0xff, 0x15, 0xff, 0xff, 0x15, 0xff, 0xff, 0x15, 0xff, 0xff, 0x16, 0xff, 0xff, 0x17, 0xff, 0xff, 0x18, 0xff, 0xff, 0x18, 0xff, 0xff, 0x16, 0xff, 0xff, 0x15, 0xff, 0xff, 0x15, 0xff, 0xff, 0x15, 0xff, 0xff, 0x18, 0xff, 0xff, 0x29, 0xff, 0xff, 0xa8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x19, 0xff, 0xff, 0x14, 0xff, 0xff, 0x90, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xd8, 0xff, 0xff, 0xa4, 0xff, 0xff, 0x9b, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9b, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xd8, 0xff, 0xff, 0xfd, 0xff, 0xff, 0x90, 0xff, 0xff, 0x13, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x18, 0xff, 0xff, 0x09, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xe8, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xe8, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x07, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x05, 0xff, 0xff, 0x17, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x91, 0xff, 0xff, 0xa3, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa3, 0xff, 0xff, 0x91, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x17, 0xff, 0xff, 0x02, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0xff, 0xff, 0x19, 0xff, 0xff, 0x06, 0xff, 0xff, 0x04, 0xff, 0xff, 0x09, 0xff, 0xff, 0x15, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x19, 0xff, 0xff, 0x18, 0xff, 0xff, 0x12, 0xff, 0xff, 0x06, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0x1a, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8, 0xff, 0xff, 0x29, 0xff, 0xff, 0x18, 0xff, 0xff, 0x16, 0xff, 0xff, 0x16, 0xff, 0xff, 0x18, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x19, 0xff, 0xff, 0x10, 0xff, 0xff, 0x06, 0xff, 0xff, 0x04, 0xff, 0xff, 0x02, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0x13, 0xff, 0xff, 0x90, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xd8, 0xff, 0xff, 0xa4, 0xff, 0xff, 0x9b, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x9a, 0xff, 0xff, 0x61, 0xff, 0xff, 0x0d, 0xff, 0xff, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0x07, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xe8, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0x17, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0x02, 0xff, 0xff, 0x17, 0xff, 0xff, 0x5b, 0xff, 0xff, 0x91, 0xff, 0xff, 0xa3, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xa4, 0xff, 0xff, 0x67, 0xff, 0xff, 0x0e, 0xff, 0xff, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0xff, 0xff, 0x02, 0xff, 0xff, 0x07, 0xff, 0xff, 0x14, 0xff, 0xff, 0x1b, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x10, 0xff, 0xff, 0x04, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 32
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x02, 0xff, 0xff, 0xff, 0x07, 0xff, 0xff, 0xff, 0x13, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x13, 0xff, 0xff, 0xff, 0x07, 0xff, 0xff, 0xff, 0x02, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x01, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff, 0x8e, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x8e, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x02, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x01, 0xff, 0xff, 0xff, 0x06, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff, 0xe8, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xe8, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff, 0x07, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x02, 0xff, 0xff, 0xff, 0x11, 0xff, 0xff, 0xff, 0x90, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xdb, 0xff, 0xff, 0xff, 0xad, 0xff, 0xff, 0xff, 0xa6, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa6, 0xff, 0xff, 0xff, 0xad, 0xff, 0xff, 0xff, 0xdb, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0x90, 0xff, 0xff, 0xff, 0x13, 
  0xff, 0xff, 0xff, 0x01, 0xff, 0xff, 0xff, 0x02, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x02, 0xff, 0xff, 0xff, 0x04, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa9, 0xff, 0xff, 0xff, 0x2c, 0xff, 0xff, 0xff, 0x1b, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x1b, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1b, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x1b, 0xff, 0xff, 0xff, 0x2c, 0xff, 0xff, 0xff, 0xa9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x04, 0xff, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xff, 0x15, 0xff, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xff, 0x06, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x07, 0xff, 0xff, 0xff, 0x04, 0xff, 0xff, 0xff, 0x04, 0xff, 0xff, 0xff, 0x04, 0xff, 0xff, 0xff, 0x06, 0xff, 0xff, 0xff, 0x16, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x16, 0xff, 0xff, 0xff, 0x06, 0xff, 0xff, 0xff, 0x04, 0xff, 0xff, 0xff, 0x04, 0xff, 0xff, 0xff, 0x04, 0xff, 0xff, 0xff, 0x07, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0xa1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x0f, 0xff, 0xff, 0xff, 0x61, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x61, 0xff, 0xff, 0xff, 0x11, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x07, 0xff, 0xff, 0xff, 0x93, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x93, 0xff, 0xff, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x07, 0xff, 0xff, 0xff, 0x9d, 0xff, 0xff, 0xff, 0xaa, 0xff, 0xff, 0xff, 0xd1, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x16, 0xff, 0xff, 0xff, 0x23, 0xff, 0xff, 0xff, 0x8c, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x10, 0xff, 0xff, 0xff, 0x81, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x07, 0xff, 0xff, 0xff, 0x52, 0xff, 0xff, 0xff, 0x9d, 0xff, 0xff, 0xff, 0x9d, 0xff, 0xff, 0xff, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x06, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x06, 0xff, 0xff, 0xff, 0x0f, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x06, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x03, 0xff, 0xff, 0xff, 0x06, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0xa1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8, 0xff, 0xff, 0xff, 0x29, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x15, 0xff, 0xff, 0xff, 0x15, 0xff, 0xff, 0xff, 0x15, 0xff, 0xff, 0xff, 0x15, 0xff, 0xff, 0xff, 0x15, 0xff, 0xff, 0xff, 0x16, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x16, 0xff, 0xff, 0xff, 0x15, 0xff, 0xff, 0xff, 0x15, 0xff, 0xff, 0xff, 0x15, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x29, 0xff, 0xff, 0xff, 0xa8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x1a, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x14, 0xff, 0xff, 0xff, 0x90, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xd8, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0x9b, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9b, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xd8, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0x90, 0xff, 0xff, 0xff, 0x13, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x09, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff, 0xe8, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xe8, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff, 0x07, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x05, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff, 0x91, 0xff, 0xff, 0xff, 0xa3, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa3, 0xff, 0xff, 0xff, 0x91, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x02, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x06, 0xff, 0xff, 0xff, 0x04, 0xff, 0xff, 0xff, 0x09, 0xff, 0xff, 0xff, 0x15, 0xff, 0xff, 0xff, 0x1b, 0xff, 0xff, 0xff, 0x1b, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x12, 0xff, 0xff, 0xff, 0x06, 0xff, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0xff, 0x1a, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa8, 0xff, 0xff, 0xff, 0x29, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x16, 0xff, 0xff, 0xff, 0x16, 0xff, 0xff, 0xff, 0x18, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x19, 0xff, 0xff, 0xff, 0x10, 0xff, 0xff, 0xff, 0x06, 0xff, 0xff, 0xff, 0x04, 0xff, 0xff, 0xff, 0x02, 0xff, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0xff, 0x13, 0xff, 0xff, 0xff, 0x90, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xd8, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0x9b, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x9a, 0xff, 0xff, 0xff, 0x61, 0xff, 0xff, 0xff, 0x0d, 0xff, 0xff, 0xff, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0xff, 0x07, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff, 0xe8, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0xff, 0x02, 0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0x5b, 0xff, 0xff, 0xff, 0x91, 0xff, 0xff, 0xff, 0xa3, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0xa4, 0xff, 0xff, 0xff, 0x67, 0xff, 0xff, 0xff, 0x0e, 0xff, 0xff, 0xff, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x02, 0xff, 0xff, 0xff, 0x07, 0xff, 0xff, 0xff, 0x14, 0xff, 0xff, 0xff, 0x1b, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0x10, 0xff, 0xff, 0xff, 0x04, 0xff, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
};

const lv_img_dsc_t pic_singlePlay = {
    .header.always_zero = 0,
    .header.w = 30,
    .header.h = 30,
    .data_size = 900 * LV_IMG_PX_SIZE_ALPHA_BYTE,
    .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
    .data = pic_singlePlay_map,
};

//end of file