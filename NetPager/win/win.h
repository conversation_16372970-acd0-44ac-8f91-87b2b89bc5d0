/*
 * win.h
 *
 *  Created on: Aug 27, 2020
 *      Author: king
 */

#ifndef NETPAGER_WIN_WIN_H_
#define NETPAGER_WIN_WIN_H_
#include "lvgl/lvgl.h"
#include "doubleLink.h"
#include "pthread.h"


enum {
	WIN_LOGIN   =  0x01,
	WIN_CONTROL  =  0x02,
	WIN_MUSICLIST  =  0x03,
	WIN_UDISK  	   =  0x04,
	WIN_SETTINGS_MAIN   =  0x05
};


void InitWinStack();
int GetCurrentWin();
void SetCurrentWin(int win);
void DeleteWin(int win);
int IsValidWin(int win);


extern pthread_mutex_t lvglMutex;	//LVGL界面锁

extern lv_style_t style_font_20,style_font_15,style_font_24,style_font_26,style_font_30;
extern lv_font_t font20,font15,font24,font26,font30;

extern int control_win_current_page;	//控制界面当前页码
extern unsigned char control_page_type;	//控制界面显示类型(分区/分组)
lv_obj_t *control_all_cont;				//控制界面总成


extern int lvglMutex_status;			//lvgl界面互斥锁状态，1表示已加锁


#endif /* NETPAGER_WIN_WIN_H_ */
