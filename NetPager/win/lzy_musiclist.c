/*
 * musiclist.c
 *
 *  Created on: Aug 31, 2020
 *      Author: king
 */

#include <stdio.h>
#include "win.h"
#include "theme.h"
#include "sysconf.h"
#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
#include "MusicFile.h"
#include "MusicFile_Alter.h"

lv_obj_t *musiclist_main_cont,*musiclist_all_count;			//空间界面主体部分
lv_obj_t *dir_list,*songlist;

lv_obj_t *dir_btn[MUSICNETDIRMAX],*song_btn[MUSICNETLISTSONGMAX];

LV_IMG_DECLARE(png_folder);

extern int g_PlayMode;
extern long int g_host_ip;

extern lv_obj_t *group_enter_btn;//进入扩展按钮

lv_obj_t *msg_confirm_delete;	//确认删除对话框

static char readyDelListID[128]={0};	//准备删除歌曲的播放列表ID
static char readyDelSongName[128]={0};	//准备删除的歌曲名

lv_obj_t *storage_label_info;			//标签栏显示存储空间信息

static void dir_list_btn_event_cb(lv_obj_t * obj, lv_event_t e);

static int Is_Dir_Match_User_PlayList(int DirIndex)
{
	if( DirIndex<serverMusicList.DirNum )
	{
		//列表用户名为空（代表不支持）或者列表用户名是admin或者列表用户名与当前登录用户相等,或者当前账户拥有管理播放列表的权限
		if(    strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) == 0 || strlen(serverMusicList.SongDir[DirIndex].UserName) == 0 \
		   ||  strcmp(serverMusicList.SongDir[DirIndex].UserName,SUPER_USER_NAME) == 0 \
		   ||  strcmp(serverMusicList.SongDir[DirIndex].UserName,m_stUser_Info.CurrentUserName) == 0 \
		   ||  (m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].LimitCode & USER_LIMITS_PLAYLIST) )
		{
			return 1;
		}
	}
	return 0;
}

static int get_user_dir_real_song_cnt(int DirIndex)
{
	int songCnt=0;
	int i=0;
	if( DirIndex>=serverMusicList.DirNum )
	{
		return 0;
	}
	if(strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) == 0 ||
		m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].LimitCode & USER_LIMITS_PLAYLIST)
	{
		return serverMusicList.SongDir[DirIndex].nFileNum;
	}
	for( i=0;i<serverMusicList.SongDir[DirIndex].nFileNum;i++)
	{
		if( strcmp(serverMusicList.SongDir[DirIndex].musicfile[i].UserName,SUPER_USER_NAME) == 0	||
			strcmp(serverMusicList.SongDir[DirIndex].musicfile[i].UserName,m_stUser_Info.CurrentUserName) == 0)
		{
			songCnt++;
		}
	}
	return songCnt;
}

static int Is_Song_Match_User_PlayList(int DirIndex,int SongIndex)
{
	if( DirIndex>=serverMusicList.DirNum || SongIndex>=serverMusicList.SongDir[DirIndex].nFileNum)
	{
		return 0;
	}
	if(strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) == 0 ||
		m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].LimitCode & USER_LIMITS_PLAYLIST)
	{
		return 1;
	}
	if( strcmp(serverMusicList.SongDir[DirIndex].musicfile[SongIndex].UserName,SUPER_USER_NAME) == 0	||
			strcmp(serverMusicList.SongDir[DirIndex].musicfile[SongIndex].UserName,m_stUser_Info.CurrentUserName) == 0)
	{
		return 1;
	}
	
	return 0;
}

static int get_user_real_dir_index(int index)
{
	int realDirIndex=-1;
	int i=0;
	int valid_cnt=-1;
	for( i=0;i<serverMusicList.DirNum;i++)
	{
		if(Is_Dir_Match_User_PlayList(i))
		{
			valid_cnt++;
			if(valid_cnt == index)
			{
				realDirIndex=i;
				break;
			}
		}
	}
	printf("get_user_real_dir_index=%d\n",realDirIndex);
	return realDirIndex;
}

void MusicList_Win_PlayMode_change(int playMode,int IsSelfcall)
{
	if(GetCurrentWin() != WIN_MUSICLIST)
		return;
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}

	lv_obj_t *btn=lv_obj_get_child(lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PLAYMODE].obj, NULL);
	lv_imgbtn_set_src(btn, LV_BTN_STATE_RELEASED, img_PlayMode[playMode-1]);
	lv_imgbtn_set_src(btn, LV_BTN_STATE_PRESSED, img_PlayMode[playMode-1]);
	lv_label_set_text(lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PLAYMODE].label,text_PlayMode[playMode-1]);

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}


void musiclist_labelInfo_update(int IsSelfcall)
{
	if(GetCurrentWin() != WIN_MUSICLIST)
			return;
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}
	char labelInfo[256]={0};
	int storageCapacity=songUploadInfo.storageCapacity/1024/1024;
	float storageSpaceUsed=songUploadInfo.storageSpaceUsed/1024.0/1024.0;
	float storageSpaceRemaining=songUploadInfo.storageSpaceRemaining/1024.0/1024.0;
	printf("storageCapacity=%lld\n",songUploadInfo.storageCapacity);
	if(language == CHINESE)
	{
		sprintf(labelInfo,"[ %s: %dMB, %s: %.1fMB, %s: %.1fMB ]",language_musiclist_storage_cacacity_text,storageCapacity,
		language_musiclist_storage_used_text,storageSpaceUsed,
		language_musiclist_storage_remaining_text,storageSpaceRemaining);
	}
	else if(language == ZHTW)
	{
		sprintf(labelInfo,"[ %s: %dMB, %s: %.1fMB, %s: %.1fMB ]",language_musiclist_storage_cacacity_text,storageCapacity,
		language_musiclist_storage_used_text,storageSpaceUsed,
		language_musiclist_storage_remaining_text,storageSpaceRemaining);
	}
	else if(language == ENGLISH)
	{
		sprintf(labelInfo,"[ %s: %dMB, %s: %.1fMB, %s: %.1fMB ]",language_musiclist_storage_cacacity_text,storageCapacity,
		language_musiclist_storage_used_text,storageSpaceUsed,
		language_musiclist_storage_remaining_text,storageSpaceRemaining);
	}
	
	lv_label_set_text_fmt(storage_label_info, labelInfo);

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}


void early_clean_muiscList(int IsSelfcall)
{
	if(GetCurrentWin() != WIN_MUSICLIST)
		return;

	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}

	//关闭确认删除对话框
	if(msg_confirm_delete!=NULL)
	{
		lv_msgbox_start_auto_close(msg_confirm_delete, 0);
		msg_confirm_delete=NULL;
	}
	
	//刷新列表
	lv_list_clean(songlist);
	//取消列表选中状态
	lv_obj_t * temp_dir_btn = lv_list_get_btn_selected(dir_list);
	if(temp_dir_btn != NULL)
	{
		int dir_index=lv_list_get_btn_index(dir_list,temp_dir_btn);
		if(dir_index != -1)
		{
			lv_obj_set_state(dir_btn[dir_index], LV_STATE_DEFAULT);
		}
	}

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}

//刷新musiclist
void musiclist_refresh_list(int IsSelfcall)
{
	if(GetCurrentWin() != WIN_MUSICLIST)
		return;
	int i=0;

	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}

	//关闭确认删除对话框
	if(msg_confirm_delete!=NULL)
	{
		lv_msgbox_start_auto_close(msg_confirm_delete, 0);
		msg_confirm_delete=NULL;
	}
	
	//刷新列表
	lv_list_clean(dir_list);
	lv_list_clean(songlist);
	for(i = 0; i<serverMusicList.DirNum; i++) {
		if( Is_Dir_Match_User_PlayList(i) )
		{
			dir_btn[i] = lv_list_add_btn(dir_list, &png_folder, serverMusicList.SongDir[i].cDirName);
			//lv_btn_set_checkable(btn, true); 开启后btn可以选中和取消,因列表项选中后不允许取消,故不开启
			lv_obj_set_style_local_pad_top(dir_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
			lv_obj_set_style_local_pad_bottom(dir_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
			lv_obj_set_event_cb(dir_btn[i], dir_list_btn_event_cb);
		}
    }

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}



void musiclist_back_to_control_win(int IsSelfcall)
{
	lv_obj_del(musiclist_all_count);
	DeleteWin(WIN_MUSICLIST);
	//清空dir_btn,song_btn数组
	memset(dir_btn,0,sizeof(dir_btn));
	memset(song_btn,0,sizeof(song_btn));
	control_win_update(control_page_type,IsSelfcall);
}


static void musiclist_test_event_cb(lv_obj_t * obj, lv_event_t e)
{
	lv_obj_t * btn = lv_list_get_btn_selected(dir_list);
	if(btn)
	{
		int index=lv_list_get_btn_index(dir_list,btn);
    	printf("btn_selected=%d,index=%d\n",btn,index);
	}
}

//选择歌曲播放
static void song_list_btn_event_cb(lv_obj_t * obj, lv_event_t e)
{
	int i;
    if(e == LV_EVENT_CLICKED)
    {
    	lv_obj_set_state(obj, LV_STATE_CHECKED);
    	lv_obj_t * btn = lv_list_get_btn_selected(songlist);
    	if(!btn)
    		return;
      	int song_index=lv_list_get_btn_index(songlist,btn);

      	btn = lv_list_get_btn_selected(dir_list);
    	if(!btn)
    		return;
      	int dir_index=lv_list_get_btn_index(dir_list,btn);
		dir_index=get_user_real_dir_index(dir_index);
		if(dir_index == -1)
			return;
		printf("song_index=%d,realDirIndex=%d\n",song_index,dir_index);
      	//龙之音V1版本，选择歌曲默认不播放
		//musiclist_play(dir_index,song_index);
    }
}

//选择目录
static void dir_list_btn_event_cb(lv_obj_t * obj, lv_event_t e)
{
	int i;
    if(e == LV_EVENT_CLICKED)
    {
    	lv_obj_set_state(obj, LV_STATE_CHECKED);
    	lv_obj_t * btn = lv_list_get_btn_selected(dir_list);
    	if(!btn)
    		return;
      	int dir_index=lv_list_get_btn_index(dir_list,btn);
		printf("des_index=%d\n",dir_index);
		dir_index=get_user_real_dir_index(dir_index);
		if(dir_index == -1)
			return;
    	lv_list_clean(songlist);
		int realSongCnt=0;
        for(i = 0; i<serverMusicList.SongDir[dir_index].nFileNum; i++) {
        	if(!Is_Song_Match_User_PlayList(dir_index,i))
				continue;
			char songName[256]={0};
        	sprintf(songName,"%d. %s",realSongCnt+1,serverMusicList.SongDir[dir_index].musicfile[i].cName);
        	song_btn[realSongCnt] = lv_list_add_btn(songlist, NULL, songName);
        	lv_obj_set_event_cb(song_btn[realSongCnt], song_list_btn_event_cb);
        	lv_obj_set_style_local_pad_top(song_btn[realSongCnt], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
        	lv_obj_set_style_local_pad_bottom(song_btn[realSongCnt], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
			lv_obj_t *btn_label=lv_list_get_btn_label(song_btn[realSongCnt]);
			if(btn_label)
				lv_label_set_long_mode(btn_label,LV_LABEL_LONG_DOT);
			realSongCnt++;
        }
    }
}

static void msg_box_event(lv_obj_t * btnm, lv_event_t e)
{
	if(e == LV_EVENT_DELETE) {//由close产生的事件

		//lv_obj_del_async(lv_obj_get_parent(msg));//删除父窗口则会删除父窗口和其所有子窗口
		//printf("delete\n");
	} else	if(e == LV_EVENT_VALUE_CHANGED) {
		const int *ex_data=lv_event_get_data();
		//printf("lv_event_get_data=%d",*ex_data);
		lv_msgbox_start_auto_close(btnm, 0);
		if(btnm == msg_confirm_delete)
		{
			if(*ex_data == 1)	//确定
			{
				lv_obj_del(btnm);
				//请求删除歌曲
				Send_host_request_delete_song(1,readyDelListID,readyDelSongName);
			}
		}
		msg_confirm_delete=NULL;
	}
}

static void round_btn_cb(lv_obj_t * obj, lv_event_t e)
{
	if(e == LV_EVENT_PRESSED || e == LV_EVENT_RELEASED || e == LV_EVENT_PRESS_LOST)
	{
		lv_obj_t *btn=lv_obj_get_child(obj, NULL);
		if(btn)
			lv_obj_set_state(btn, lv_obj_get_state(obj, LV_OBJ_PART_MAIN));
	}

    if(e == LV_EVENT_RELEASED)
	{
		int dir_index=-1,song_index=-1;
		lv_obj_t * temp_dir_btn = lv_list_get_btn_selected(dir_list);
		lv_obj_t * temp_song_btn = lv_list_get_btn_selected(songlist);
		if(temp_dir_btn != NULL)
		{
			dir_index=lv_list_get_btn_index(dir_list,temp_dir_btn);
			dir_index=get_user_real_dir_index(dir_index);
			if(dir_index == -1)
				return;
		}
		if(temp_song_btn != NULL )
		{
			song_index=lv_list_get_btn_index(songlist,temp_song_btn);
		}
		printf("dir_index=%d,song_index=%d\n",dir_index,song_index);
		if(obj == lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PREPLAY].obj || lv_obj_get_parent(obj) == lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PREPLAY].obj)
		{
			if(dir_index >=0)
			{	
				int temp_song_index=song_index;
				//song_index=song_index-1>=0?song_index-1:serverMusicList.SongDir[dir_index].nFileNum-1;
				int realSongCnt=get_user_dir_real_song_cnt(dir_index);
				song_index=song_index-1>=0?song_index-1:realSongCnt-1;
				if(dir_index >=0 && song_index>=0 && dir_index<serverMusicList.DirNum && song_index<realSongCnt)
				{
					//取消原来btn的选中状态
					if(temp_song_index>=0)
					{
						lv_obj_set_state(song_btn[temp_song_index], LV_STATE_DEFAULT);
					}
					//一定要foucs新的btn，否则lv_list_get_btn_selected还是原来的btn
					lv_list_focus_btn(songlist,song_btn[song_index]);
					//设置新的btn为选中状态
					lv_obj_set_state(song_btn[song_index], LV_STATE_CHECKED);
					musiclist_play(dir_index,song_index);
				}
			}
			else
			{
				//提示用户选择歌曲列表
				static const char * btns[] = {"OK", ""};
				lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
				lv_msgbox_add_btns(msg, btns);
				lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
				lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
				lv_msgbox_set_text(msg,language_musiclist_selectList_text);
			}
			
		}
		else if(obj == lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PLAY].obj || lv_obj_get_parent(obj) == lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PLAY].obj)
		{
			int realSongCnt=get_user_dir_real_song_cnt(dir_index);
			if(dir_index >=0 && song_index>=0 && dir_index<serverMusicList.DirNum && song_index<realSongCnt)
				musiclist_play(dir_index,song_index);
			else
			{
				//提示用户选择歌曲列表
				static const char * btns[] = {"OK", ""};
				lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
				lv_msgbox_add_btns(msg, btns);
				lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
				lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
				lv_msgbox_set_text(msg,language_musiclist_selectSong_text);
			}
			
		}
		else if(obj == lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_STOP].obj || lv_obj_get_parent(obj) == lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_STOP].obj)
		{
			control_terminal_idle();
		}
		else if(obj == lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_NEXTPLAY].obj || lv_obj_get_parent(obj) == lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_NEXTPLAY].obj)
		{
			if(dir_index >=0)
			{
				int temp_song_index=song_index;
				
				//song_index=song_index+1>=serverMusicList.SongDir[dir_index].nFileNum?0:song_index+1;
				int realSongCnt=get_user_dir_real_song_cnt(dir_index);
				song_index=song_index+1>=realSongCnt?0:song_index+1;
				if(dir_index >=0 && song_index>=0 && dir_index<serverMusicList.DirNum && song_index<realSongCnt)
				{
					if(temp_song_index>=0)
					{
						lv_obj_set_state(song_btn[temp_song_index], LV_STATE_DEFAULT);
					}
					lv_list_focus_btn(songlist,song_btn[song_index]);
					lv_obj_set_state(song_btn[song_index], LV_STATE_CHECKED);
					musiclist_play(dir_index,song_index);
				}
			}
			else
			{
				//提示用户选择歌曲列表
				static const char * btns[] = {"OK", ""};
				lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
				lv_msgbox_add_btns(msg, btns);
				lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
				lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
				lv_msgbox_set_text(msg,language_musiclist_selectList_text);
			}
			
		}
		else if(obj == lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PLAYMODE].obj || lv_obj_get_parent(obj) == lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PLAYMODE].obj)
		{
			g_PlayMode=g_PlayMode+1>5?1:g_PlayMode+1;
			save_sysconf("MusicPlay","PlayMode");
			host_change_playMode(g_host_ip);
			MusicList_Win_PlayMode_change(g_PlayMode,1);
		}
		else if(obj == lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_DELETE].obj || lv_obj_get_parent(obj) == lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_DELETE].obj)
		{
			if(dir_index >=0 && song_index>=0 && dir_index<serverMusicList.DirNum && song_index<serverMusicList.SongDir[dir_index].nFileNum)
			{
				//先记录歌曲列表ID和歌曲名
				sprintf(readyDelListID,serverMusicList.SongDir[dir_index].id);
				sprintf(readyDelSongName,serverMusicList.SongDir[dir_index].musicfile[song_index].cName);

				//确认上传对话框
				static char * btns[] = {"Cancel", "OK",""};
				btns[0] = language_messagebox_cancel_text;
				btns[1] = language_messagebox_confirm_text;
				msg_confirm_delete = lv_msgbox_create(lv_scr_act(), NULL);
				lv_msgbox_add_btns(msg_confirm_delete, btns);
				lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg_confirm_delete);
				//lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_add_style(msg_confirm_delete, LV_OBJ_PART_MAIN, &style_font_20);
				lv_obj_set_event_cb(msg_confirm_delete, msg_box_event);
				char confirm_upload_text[256]={0};
				sprintf(confirm_upload_text,"%s \"%s\" ?",language_musiclist_confirm_delete_text,serverMusicList.SongDir[dir_index].musicfile[song_index].cName);
				lv_msgbox_set_text(msg_confirm_delete,confirm_upload_text);
			}
			else
			{
				//提示用户选择歌曲列表
				static const char * btns[] = {"OK", ""};
				lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
				lv_msgbox_add_btns(msg, btns);
				lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
				lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
				lv_msgbox_set_text(msg,language_musiclist_selectSong_text);
			}
		}
	}
}




void musiclist_win_start(void)
{
	int i=0;
	int num=0;
	for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
	{
		if(m_stZone_Info.zoneInfo[i].g_zone_isSelect)
		{
			num++;
			break;
		}
	}
	if( num == 0)
	{
		static const char * btns[] = {"OK", ""};
		lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
		lv_msgbox_add_btns(msg, btns);
		lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
		lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
		lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);

		lv_msgbox_set_text(msg,language_control_message_selectOnlineZone_text);
		return;
	}

	musiclist_all_count = lv_cont_create(control_all_cont, NULL);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_y(musiclist_all_count,48);
		lv_obj_set_size(musiclist_all_count,LV_HOR_RES_MAX+4,LV_VER_RES_MAX-48+4);		//此处为了解决默认情况下屏幕留边的问题
	}
	else
	{
		lv_obj_set_y(musiclist_all_count,48);
		lv_obj_set_size(musiclist_all_count,LV_HOR_RES_MAX+4,LV_VER_RES_MAX-48+4);		//此处为了解决默认情况下屏幕留边的问题
	}
	lv_obj_set_style_local_pad_all(musiclist_all_count, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_margin_all(musiclist_all_count, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_radius(musiclist_all_count, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	musiclist_main_cont = lv_cont_create(musiclist_all_count, NULL);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_size(musiclist_main_cont,LV_HOR_RES_MAX+4,516-48);
	}
	else
	{
		lv_obj_set_size(musiclist_main_cont,LV_HOR_RES_MAX+4,410-48);
	}
	lv_obj_set_y(musiclist_main_cont,0);
	lv_obj_set_style_local_pad_all(musiclist_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_margin_all(musiclist_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_radius(musiclist_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_bg_color(musiclist_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(250,250,250));
	lv_obj_set_style_local_border_width(musiclist_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 1);
	//lv_obj_set_click(control_main_cont, false);
	lv_obj_add_protect(musiclist_main_cont, LV_PROTECT_CLICK_FOCUS);

	//创建列表
	dir_list = lv_list_create(musiclist_main_cont, NULL);
    //lv_list_set_scroll_propagation(dir_list, true);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_size(dir_list, 350, 376);
		lv_obj_set_pos(dir_list, 75, 60);
	}
	else
	{
		lv_obj_set_size(dir_list, 300, 284);
		lv_obj_set_pos(dir_list, 55, 46);
	}
    
    lv_obj_add_style(dir_list, LV_OBJ_PART_MAIN, &style_font_20);
    for(i = 0; i<serverMusicList.DirNum; i++) {
		if( Is_Dir_Match_User_PlayList(i) )
		{
			dir_btn[i] = lv_list_add_btn(dir_list, &png_folder, serverMusicList.SongDir[i].cDirName);
			//lv_btn_set_checkable(btn, true); 开启后btn可以选中和取消,因列表项选中后不允许取消,故不开启
			lv_obj_set_style_local_pad_top(dir_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
			lv_obj_set_style_local_pad_bottom(dir_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
			lv_obj_set_event_cb(dir_btn[i], dir_list_btn_event_cb);
		}
    }
    //lv_obj_set_event_cb(musiclist_main_cont, musiclist_test_event_cb);



	//创建列表
    songlist = lv_list_create(musiclist_main_cont, NULL);
    //lv_list_set_scroll_propagation(songlist, true);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_size(songlist, 400, 376);
		lv_obj_set_pos(songlist, 540, 60);
	}
	else
	{
		lv_obj_set_size(songlist, 350, 284);
		lv_obj_set_pos(songlist, 400, 46);
	}
    lv_obj_add_style(songlist, LV_OBJ_PART_MAIN, &style_font_20);


    lv_obj_t  *label= lv_label_create(musiclist_main_cont, NULL);
    lv_obj_align(label, dir_list, LV_ALIGN_OUT_TOP_MID,-15,-28);
    lv_label_set_text(label, language_musiclist_list_text);
    lv_obj_add_style(label, LV_OBJ_PART_MAIN, &style_font_26);
    lv_obj_set_auto_realign(label, true);
    lv_obj_set_style_local_text_color(label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(28,69,94));


    label= lv_label_create(musiclist_main_cont, NULL);
    lv_obj_align(label, songlist, LV_ALIGN_OUT_TOP_MID,-15,-28);
    lv_label_set_text(label, language_musiclist_song_text);
    lv_obj_add_style(label, LV_OBJ_PART_MAIN, &style_font_26);
    lv_obj_set_auto_realign(label, true);
    lv_obj_set_style_local_text_color(label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(28,69,94));


	//创建标签栏,显示
    storage_label_info=lv_label_create(musiclist_main_cont, NULL);
	lv_obj_align(storage_label_info, musiclist_main_cont, LV_ALIGN_IN_BOTTOM_MID,8,-4);
	lv_label_set_align(storage_label_info, LV_LABEL_ALIGN_CENTER);
	lv_obj_set_auto_realign(storage_label_info,true);
	lv_obj_add_style(storage_label_info, LV_OBJ_PART_MAIN, &style_font_20);
	lv_obj_set_style_local_text_color(storage_label_info, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
	//管理员登录，不显示存储信息标签栏
	if(strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) == 0)
	{
		lv_obj_set_hidden(storage_label_info,true);
	}


	//创建底部控制栏
	lv_obj_t *musiclist_bottom_box = lv_obj_create(musiclist_all_count, NULL);
	//lv_obj_set_pos(control_zone_cont, 0, 0);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_y(musiclist_bottom_box,516-48);
		lv_obj_set_size(musiclist_bottom_box, LV_HOR_RES_MAX, LV_VER_RES_MAX-516);
	}
	else
	{
		lv_obj_set_y(musiclist_bottom_box,410-48);
		lv_obj_set_size(musiclist_bottom_box, LV_HOR_RES_MAX, LV_VER_RES_MAX-410);
	}
	lv_obj_set_style_local_pad_all(musiclist_bottom_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_margin_all(musiclist_bottom_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_radius(musiclist_bottom_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(musiclist_bottom_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);



	//底部控制按钮
	for(i=0;i<LZY_SERVER_MUSICLIST_MAX_MUSICLIST_NUM;i++)
	{
		lzy_server_musiclist_button_list[i].obj=lv_obj_create(musiclist_bottom_box, NULL);
	   // lv_obj_align(test_area, control_bottom_box, LV_ALIGN_IN_TOP_MID,0,40);
	    lv_obj_set_style_local_bg_color(lzy_server_musiclist_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
	    lv_obj_set_style_local_radius(lzy_server_musiclist_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_RADIUS_CIRCLE);
		if(IS_DISP_RES_1024)
		{
	    	lv_obj_set_size(lzy_server_musiclist_button_list[i].obj, 48, 48);
	    	lv_obj_set_pos(lzy_server_musiclist_button_list[i].obj, 90+i*160, 8);
		}
		else
		{
			lv_obj_set_size(lzy_server_musiclist_button_list[i].obj, 42, 42);
	    	lv_obj_set_pos(lzy_server_musiclist_button_list[i].obj, 60+i*130, 5);
		}
	    lv_obj_set_style_local_border_width(lzy_server_musiclist_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	    lv_obj_set_style_local_pad_all(lzy_server_musiclist_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	    lv_obj_set_style_local_margin_all(lzy_server_musiclist_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	    lv_obj_t *imgbtn=lv_imgbtn_create(lzy_server_musiclist_button_list[i].obj, NULL);
		lv_obj_align(imgbtn, lzy_server_musiclist_button_list[i].obj, LV_ALIGN_CENTER,50,5);
		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_RELEASED, &lzy_server_musiclist_button_list[i].pic);
		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_PRESSED, &lzy_server_musiclist_button_list[i].pic);

		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_CHECKED_RELEASED, &lzy_server_musiclist_button_list[i].pic);
		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_CHECKED_PRESSED, &lzy_server_musiclist_button_list[i].pic);

		lv_obj_set_style_local_image_recolor_opa(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_OPA_COVER);
		lv_obj_set_style_local_image_recolor(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_COLOR_BLACK);

		lv_obj_set_style_local_image_recolor_opa(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_CHECKED, LV_OPA_COVER);
		lv_obj_set_style_local_image_recolor(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_CHECKED, LV_COLOR_YELLOW);


		//创建label显示按钮标签
		lzy_server_musiclist_button_list[i].label=lv_label_create(musiclist_all_count, NULL);
		lv_obj_align(lzy_server_musiclist_button_list[i].label, lzy_server_musiclist_button_list[i].obj, LV_ALIGN_OUT_BOTTOM_MID,0,1);
		lv_label_set_align(lzy_server_musiclist_button_list[i].label, LV_LABEL_ALIGN_CENTER);
		lv_label_set_text(lzy_server_musiclist_button_list[i].label, lzy_server_musiclist_button_list[i].nameCN);
		lv_obj_set_auto_realign(lzy_server_musiclist_button_list[i].label,true);
		lv_obj_set_width(lzy_server_musiclist_button_list[i].label, lv_obj_get_width_grid(lzy_server_musiclist_button_list[i].obj, 1, 1));
		lv_obj_add_style(lzy_server_musiclist_button_list[i].label, LV_OBJ_PART_MAIN, &style_font_15);

	    lv_obj_set_event_cb(lzy_server_musiclist_button_list[i].obj, round_btn_cb);
	    lv_obj_set_event_cb(imgbtn, round_btn_cb);
	}


	 SetCurrentWin(WIN_MUSICLIST);

	 MusicList_Win_PlayMode_change(g_PlayMode,1);

	//隐藏分组扩展按钮
	lv_obj_set_hidden(group_enter_btn,true);

	musiclist_labelInfo_update(1);
}


#endif