/*
 * theme.h
 *
 *  Created on: Sep 22, 2020
 *      Author: king
 */

#ifndef NETPAGER_WIN_THEME_H_
#define NETPAGER_WIN_THEME_H_

#include "lvgl/lvgl.h"

lv_color_t get_theme_color();

LV_IMG_DECLARE(pic_zoneGroup);
LV_IMG_DECLARE(pic_selectAll);
LV_IMG_DECLARE(pic_cancel);
LV_IMG_DECLARE(pic_settings);
LV_IMG_DECLARE(pic_paging);
LV_IMG_DECLARE(pic_call);
LV_IMG_DECLARE(pic_headphones);
LV_IMG_DECLARE(pic_music);
LV_IMG_DECLARE(pic_udisk);
LV_IMG_DECLARE(pic_control_play);
LV_IMG_DECLARE(pic_control_pause);
LV_IMG_DECLARE(pic_stop);
LV_IMG_DECLARE(pic_volume);

//MUSICLIST
LV_IMG_DECLARE(pic_play);
LV_IMG_DECLARE(pic_preplay);
LV_IMG_DECLARE(pic_nextplay);
LV_IMG_DECLARE(pic_singlePlay);
LV_IMG_DECLARE(pic_singleRepeat);
LV_IMG_DECLARE(pic_sequencyPlay);
LV_IMG_DECLARE(pic_listLoop);
LV_IMG_DECLARE(pic_shufflePlay);

//LZY serverMusicList、udiskMusicList
LV_IMG_DECLARE(pic_upload);
LV_IMG_DECLARE(pic_delete);


enum
{
	CONTROL_BUTTON_ZONEGROUP,
	CONTROL_BUTTON_SELECTALL,
	//CONTROL_BUTTON_CANCEL,
	CONTROL_BUTTON_PAGING,
	CONTROL_BUTTON_CALL,
	CONTROL_BUTTON_LISTEN,
	CONTROL_BUTTON_MUSIC,
	CONTROL_BUTTON_UDISK,
	CONTROL_BUTTON_STOP,
	CONTROL_BUTTON_VOLUME,
	CONTROL_BUTTON_SETTINGS,
	MAX_CONTROL_NUM
};


enum
{
	MUSICLIST_BUTTON_PLAYMODE,
	MUSICLIST_BUTTON_PREPLAY,
	MUSICLIST_BUTTON_PLAY,
	MUSICLIST_BUTTON_STOP,
	MUSICLIST_BUTTON_NEXTPLAY,
	MAX_MUSICLIST_NUM
};


enum
{
	LZY_SERVER_MUSICLIST_BUTTON_DELETE,
	LZY_SERVER_MUSICLIST_BUTTON_PLAYMODE,
	LZY_SERVER_MUSICLIST_BUTTON_PREPLAY,
	LZY_SERVER_MUSICLIST_BUTTON_PLAY,
	LZY_SERVER_MUSICLIST_BUTTON_STOP,
	LZY_SERVER_MUSICLIST_BUTTON_NEXTPLAY,
	LZY_SERVER_MUSICLIST_MAX_MUSICLIST_NUM
};

enum
{
	LZY_UDISK_MUSICLIST_BUTTON_UPLOAD,
	LZY_UDISK_MUSICLIST_BUTTON_PLAYMODE,
	LZY_UDISK_MUSICLIST_BUTTON_PREPLAY,
	LZY_UDISK_MUSICLIST_BUTTON_PLAY,
	LZY_UDISK_MUSICLIST_BUTTON_STOP,
	LZY_UDISK_MUSICLIST_BUTTON_NEXTPLAY,
	LZY_UDISK_MUSICLIST_MAX_MUSICLIST_NUM
};



typedef struct
{
	lv_obj_t *obj;
	lv_img_dsc_t pic;
	lv_obj_t *label;
	char nameCN[32];
	char nameEN[32];
}st_control_button_list;

extern st_control_button_list control_button_list[MAX_CONTROL_NUM];
extern st_control_button_list musiclist_button_list[MAX_MUSICLIST_NUM];

extern st_control_button_list lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_MAX_MUSICLIST_NUM];
extern st_control_button_list lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_MAX_MUSICLIST_NUM];

extern const lv_img_dsc_t *img_PlayMode[5];
extern char text_PlayMode[5][16];

extern const lv_img_dsc_t *img_Udisk_PlayMode[5];
extern char text_Udisk_PlayMode[5][16];

#endif /* NETPAGER_WIN_THEME_H_ */
