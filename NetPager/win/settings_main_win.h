/*
 * control.h
 *
 *  Created on: Aug 25, 2020
 *      Author: king
 */

#ifndef SETTINGS_MAIN_WIN_H_
#define SETTINGS_MAIN_WIN_H_

#include "lvgl/lvgl.h"
#include "const.h"

enum
{
    SIDER_BTN_SYSINFO,
    #if 0
    SIDER_BTN_ACCOUNT,
    #endif
    SIDER_BTN_NETWORK,
    #if(APP_TYPE != APP_JUSBE_MIXER)
    SIDER_BTN_MIC,
    SIDER_BTN_LISTEN,
        #if SUPPORT_AUTO_TRIGGER
        SIDER_BTN_TRIGGER,
        #endif
    #endif
    SIDER_BTN_BELL,
    SIDER_BTN_BACKLIGHT,
    SIDER_BTN_OTHER,

    MAX_SIDEBAR_BTN_NUM
};


#define DEFAULT_SETTINGS_ITEM  SIDER_BTN_SYSINFO      //默认选中的设置项


extern lv_obj_t *screen_settings;				        //设置界面screen

extern lv_obj_t *setting_sidebar_btn[MAX_SIDEBAR_BTN_NUM];       //侧边栏按钮
extern int settings_current_item;                       //当前选择的item项
extern lv_obj_t *setting_right_area[MAX_SIDEBAR_BTN_NUM];         //右侧显示区域

/****Start**********设备信息******************************/
extern lv_obj_t * systemInfo_ip;
extern lv_obj_t * systemInfo_serverOnline;
extern lv_obj_t * systemInfo_networkMode;
extern lv_obj_t * systemInfo_SerialNum;
/****End**********设备信息*****************************/


void enter_systemInfo_win();
void enter_backlight_win();

#endif