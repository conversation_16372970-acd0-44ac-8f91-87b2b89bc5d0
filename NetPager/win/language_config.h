#ifndef LANGUAGE_CONFIG_H_
#define LANGUAGE_CONFIG_H_

#include "language_text.h"

extern char *language_playsource_offline_text;
extern char *language_playsource_idle_text;
extern char *language_playsource_localPlay_text;
extern char *language_playsource_netPlay_text;
extern char *language_playsource_timing_text;
extern char *language_playsource_audioCollector_text;
extern char *language_playsource_intercom_text;
extern char *language_playsource_paging_text;
extern char *language_playsource_alarm_text;
extern char *language_playsource_100V_text;

extern char *language_playmode_singlePlay_text;
extern char *language_playmode_singleLoop_text;
extern char *language_playmode_sequencyPlay_text;
extern char *language_playmode_listLoop_text;
extern char *language_playmode_shuffle_text;

extern char *language_musiclist_root_text;
extern char *language_musiclist_preSong_text;
extern char *language_musiclist_play_text;
extern char *language_musiclist_stop_text;
extern char *language_musiclist_pause_text;
extern char *language_musiclist_nextSong_text;
extern char *language_musiclist_list_text;
extern char *language_musiclist_song_text;
extern char *language_musiclist_upload_text;
extern char *language_musiclist_delete_text;
extern char *language_musiclist_selectList_text;
extern char *language_musiclist_selectSong_text;
extern char *language_musiclist_insertUdisk_text;
extern char *language_musiclist_udiskLoading_text;

extern char *language_musiclist_song_upload_text;
extern char *language_musiclist_song_delete_success_text;
extern char *language_musiclist_song_delete_failed_text;
extern char *language_musiclist_notSupported_upload_song_text;
extern char *language_musiclist_insufficient_storage_text;
extern char *language_musiclist_uploaded_text;
extern char *language_musiclist_confirm_delete_text;
extern char *language_musiclist_confirm_upload_head_text;
extern char *language_musiclist_confirm_upload_tail_text;
extern char *language_musiclist_upload_transferred_text;
extern char *language_musiclist_upload_status_ready_text;
extern char *language_musiclist_upload_status_uploading_text;
extern char *language_musiclist_upload_status_succeed_text;
extern char *language_musiclist_upload_status_failed_song_invaild_text;
extern char *language_musiclist_upload_status_failed_storage_text;
extern char *language_musiclist_upload_status_failed_text;
extern char *language_musiclist_upload_close_text;
extern char *language_musiclist_upload_stop_text;
extern char *language_musiclist_storage_cacacity_text;
extern char *language_musiclist_storage_used_text;
extern char *language_musiclist_storage_remaining_text;

extern char *language_login_title_text;
extern char *language_login_userName_text;
extern char *language_login_password_text;
extern char *language_login_remember_text;
extern char *language_login_button_text;
extern char *language_login_tips_password_error_text;
extern char *language_login_tips_dsp_error_text;
extern char *language_login_tips_init_network_error_text;
extern char *language_login_tips_connect_server_error_text;
extern char *language_login_tips_getUserInfo_error_text;
extern char *language_login_tips_no_user_error_text;

extern char *language_control_button_controlType_text;
extern char *language_control_button_zoneOrGroup_text;
extern char *language_control_button_selectAll_text;
extern char *language_control_button_paging_text;
extern char *language_control_button_transmit_text;
extern char *language_control_button_intercom_text;
extern char *language_control_button_listen_text;
extern char *language_control_button_NetMusic_text;
extern char *language_control_button_udisk_text;
extern char *language_control_button_stop_text;
extern char *language_control_button_volume_text;
extern char *language_control_button_settings_text;

extern char *language_control_button_stop_zone_text;
extern char *language_control_button_stop_usb_text;
extern char *language_control_button_intercom_volume_text;
extern char *language_control_zone_cnt_text;
extern char *language_control_online_zone_cnt_text;

extern char *language_control_zoneInfo_zone_cnt_text;
extern char *language_control_zoneInfo_online_zone_cnt_text;
extern char *language_control_zoneInfo_selected_zone_cnt_text;
extern char *language_control_groupInfo_group_cnt_text;
extern char *language_control_groupInfo_selected_group_cnt_text;
extern char *language_control_pagerInfo_pager_cnt_text;
extern char *language_control_pagerInfo_online_pager_cnt_text;
extern char *language_control_pagerInfo_selected_pager_cnt_text;

extern char *language_control_type_zone_text;
extern char *language_control_type_group_text;
extern char *language_control_type_pager_text;

extern  char *language_messagebox_confirm_text;
extern  char *language_messagebox_cancel_text;

extern char *language_control_message_cancelAlarm_text;
extern char *language_control_message_stopPaging_text;
extern char *language_control_message_stopIntercom_text;
extern char *language_control_message_logout_text;
extern char *language_control_message_selectOnlineZone_text;
extern char *language_control_message_selectGroupNoOnlineZone_text;
extern char *language_control_message_talkAfterRing_text;
extern char *language_control_message_selectOneIntercomDevice_text;
extern char *language_control_message_selectZoneNoIntercom_text;
extern char *language_control_message_selectPager_text;
extern char *language_control_message_selectOnePagerToTalk_text;
extern char *language_control_message_selectPagerNoIntercom_text;
extern char *language_control_message_calledBusy_text;
extern char *language_control_message_resetZone_text;

extern char *language_control_call_calling_text;
extern char *language_control_call_wait_answer_text;
extern char *language_control_call_wait_reply_text;
extern char *language_control_call_hangUp_text;
extern char *language_control_call_answer_text;
extern char *language_control_call_hangedUp_text;
extern char *language_control_call_no_answer_text;
extern char *language_control_call_codecs_not_support_text;
extern char *language_control_call_busy_text;
extern char *language_control_call_refused_text;
extern char *language_control_input_settings_password_text;

extern char *language_settings_title_text;
extern char *language_settings_tab_deviceInfo_text;
extern char *language_settings_tab_network_text;
extern char *language_settings_tab_mic_text;
extern char *language_settings_tab_output_text;
extern char *language_settings_tab_listen_text;
extern char *language_settings_tab_trigger_text;
extern char *language_settings_tab_ring_text;
extern char *language_settings_tab_display_text;
extern char *language_settings_tab_other_text;

extern char *language_settings_device_version_text;
extern char *language_settings_device_networkMode_text;
extern char *language_settings_device_connectStatus_text;
extern char *language_settings_device_connected_text;
extern char *language_settings_device_disconnected_text;
extern char *language_settings_device_serialNumber_text;
extern char *language_settings_device_netCableNotInsert_text;
extern char *language_settings_device_netCableNotInsertAndUse4G_text;

extern char *language_settings_network_ip_text;
extern char *language_settings_network_subnetMask_text;
extern char *language_settings_network_gateway_text;
extern char *language_settings_network_settings_text;
extern char *language_settings_network_settingsOK_text;
extern char *language_settings_network_inputCorrectIP_text;
extern char *language_settings_network_inputCorrectSubNetMask_text;
extern char *language_settings_network_inputCorrectGateWay_text;

extern char *language_settings_mic_mixAux_text;
extern char *language_settings_mic_sensitivity_text;

extern char *language_settings_output_listenVol_text;
extern char *language_settings_output_intercomVol_text;
extern char *language_settings_output_ringVol_text;

extern char *language_settings_ring_pagingRing_text;

extern char *language_settings_display_brightness_text;
extern char *language_settings_display_automatic_screenOff_text;
extern char *language_settings_display_minute_text;
extern char *language_settings_display_disable_text;
extern char *language_settings_display_language_text;

extern char *language_settings_other_zone_paging_volume_text;
extern char *language_settings_other_paging_timeout_text;
extern char *language_settings_other_reset_zones_text;
extern char *language_settings_other_password_access_text;
extern char *language_settings_other_logout_text;

void Language_Config(void);

#endif