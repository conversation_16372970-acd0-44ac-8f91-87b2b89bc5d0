#include "language_text.h"


char *language_playsource_offline_CN          = "离线";
char *language_playsource_idle_CN             = "空闲";
char *language_playsource_localPlay_CN        = "本地播放";
char *language_playsource_netPlay_CN          = "网络点播";
char *language_playsource_timing_CN           = "定时";
char *language_playsource_audioCollector_CN   = "音频采集";
char *language_playsource_intercom_CN         = "对讲";
char *language_playsource_paging_CN           = "寻呼";
char *language_playsource_alarm_CN            = "消防警报";
char *language_playsource_100V_CN             = "100V";
char *language_playsource_offline_TW          = "離線";
char *language_playsource_idle_TW             = "空閑";
char *language_playsource_localPlay_TW        = "本地播放";
char *language_playsource_netPlay_TW          = "網絡點播";
char *language_playsource_timing_TW           = "定時";
char *language_playsource_audioCollector_TW   = "音頻采集";
char *language_playsource_intercom_TW         = "對講";
char *language_playsource_paging_TW           = "尋呼";
char *language_playsource_alarm_TW            = "消防警報";
char *language_playsource_100V_TW             = "100V";
char *language_playsource_offline_EN          = "offline";
char *language_playsource_idle_EN             = "idle";
char *language_playsource_localPlay_EN        = "localPlay";
char *language_playsource_netPlay_EN          = "netPlay";
char *language_playsource_timing_EN           = "timing";
char *language_playsource_audioCollector_EN   = "audioCollector";
char *language_playsource_intercom_EN         = "intercom";
char *language_playsource_paging_EN           = "paging";
char *language_playsource_alarm_EN            = "alarm";
char *language_playsource_100V_EN             = "100V";


char *language_playmode_singlePlay_CN         = "单曲播放";
char *language_playmode_singleLoop_CN         = "单曲循环";
char *language_playmode_sequencyPlay_CN       = "顺序播放";
char *language_playmode_listLoop_CN           = "列表循环";
char *language_playmode_shuffle_CN            = "随机播放";
char *language_playmode_singlePlay_TW         = "單曲播放";
char *language_playmode_singleLoop_TW         = "單曲循環";
char *language_playmode_sequencyPlay_TW       = "順序播放";
char *language_playmode_listLoop_TW           = "列表循環";
char *language_playmode_shuffle_TW            = "隨機播放";
char *language_playmode_singlePlay_EN         = "Single play";
char *language_playmode_singleLoop_EN         = "Single repeat";
char *language_playmode_sequencyPlay_EN       = "Order play";
char *language_playmode_listLoop_EN           = "List repeat";
char *language_playmode_shuffle_EN            = "Shuffle";

char *language_musiclist_root_CN                 = "根目录";
char *language_musiclist_preSong_CN              = "上一曲";
char *language_musiclist_play_CN                 = "播放";
char *language_musiclist_stop_CN                 = "停止";
char *language_musiclist_pause_CN                = "暂停";
char *language_musiclist_nextSong_CN             = "下一曲";
char *language_musiclist_list_CN                 = "列表";
char *language_musiclist_song_CN                 = "歌曲";
char *language_musiclist_upload_CN               = "上传";
char *language_musiclist_delete_CN               = "删除";
char *language_musiclist_selectList_CN           = "请先选择列表!";
char *language_musiclist_selectSong_CN           = "请选择歌曲!";
char *language_musiclist_insertUdisk_CN          = "请插入U盘!";
char *language_musiclist_udiskLoading_CN         = "U盘加载中,请稍候!";
char *language_musiclist_root_TW                 = "根目錄";
char *language_musiclist_preSong_TW              = "上一曲";
char *language_musiclist_play_TW                 = "播放";
char *language_musiclist_stop_TW                 = "停止";
char *language_musiclist_pause_TW                = "暫停";
char *language_musiclist_nextSong_TW             = "下一曲";
char *language_musiclist_list_TW                 = "列表";
char *language_musiclist_song_TW                 = "歌曲";
char *language_musiclist_upload_TW               = "上傳";
char *language_musiclist_delete_TW               = "刪除";
char *language_musiclist_selectList_TW           = "請先選擇列表!";
char *language_musiclist_selectSong_TW           = "請選擇歌曲!";
char *language_musiclist_insertUdisk_TW          = "請插入U盤!";
char *language_musiclist_udiskLoading_TW         = "U盤加載中,請稍候!";
char *language_musiclist_root_EN                 = "root";
char *language_musiclist_preSong_EN              = "Previous";
char *language_musiclist_play_EN                 = "Play";
char *language_musiclist_stop_EN                 = "Stop";
char *language_musiclist_pause_EN                = "Pause";
char *language_musiclist_nextSong_EN             = "Next";
char *language_musiclist_list_EN                 = "LIST";
char *language_musiclist_song_EN                 = "SONG";
char *language_musiclist_upload_EN               = "Upload";
char *language_musiclist_delete_EN               = "Delete";
char *language_musiclist_selectList_EN           = "Please select a list!";
char *language_musiclist_selectSong_EN           = "Please select a song!";
char *language_musiclist_insertUdisk_EN          = "please insert the udisk!";
char *language_musiclist_udiskLoading_EN         = "udisk loading,please wait!";



char *language_musiclist_song_upload_CN                            = "歌曲上传";
char *language_musiclist_song_delete_success_CN                    = "删除成功";               
char *language_musiclist_song_delete_failed_CN                     = "删除失败";
char *language_musiclist_notSupported_upload_song_CN               = "不支持歌曲上传";
char *language_musiclist_insufficient_storage_CN                   = "存储空间不足,无法上传";
char *language_musiclist_uploaded_CN                               = "歌曲已上传";
char *language_musiclist_confirm_delete_CN                         = "确认删除歌曲";
char *language_musiclist_confirm_upload_head_CN                    = "确认将歌曲";
char *language_musiclist_confirm_upload_tail_CN                    = "上传至服务器";
char *language_musiclist_upload_transferred_CN                     = "已传输";
char *language_musiclist_upload_status_ready_CN                    = "状态：准备上传";
char *language_musiclist_upload_status_uploading_CN                = "状态：正在上传中";
char *language_musiclist_upload_status_succeed_CN                  = "状态：上传成功";
char *language_musiclist_upload_status_failed_song_invaild_CN      = "状态：上传失败(歌曲异常)";
char *language_musiclist_upload_status_failed_storage_CN           = "状态：上传失败(存储空间不足)";
char *language_musiclist_upload_status_failed_CN                   = "状态：上传失败";
char *language_musiclist_upload_close_CN                           = "关闭";
char *language_musiclist_upload_stop_CN                            = "停止"; 
char *language_musiclist_storage_cacacity_CN                       = "总存储空间";  
char *language_musiclist_storage_used_CN                           = "已用空间"; 
char *language_musiclist_storage_remaining_CN                      = "剩余空间";  
char *language_musiclist_song_upload_TW                            = "歌曲上傳";
char *language_musiclist_song_delete_success_TW                    = "刪除成功";               
char *language_musiclist_song_delete_failed_TW                     = "刪除失敗";
char *language_musiclist_notSupported_upload_song_TW               = "不支持歌曲上傳";
char *language_musiclist_insufficient_storage_TW                   = "存儲空間不足,無法上傳";
char *language_musiclist_uploaded_TW                               = "歌曲已上傳";
char *language_musiclist_confirm_delete_TW                         = "確認刪除歌曲";
char *language_musiclist_confirm_upload_head_TW                    = "確認將歌曲";
char *language_musiclist_confirm_upload_tail_TW                    = "上傳至服務器";
char *language_musiclist_upload_transferred_TW                     = "已傳輸";
char *language_musiclist_upload_status_ready_TW                    = "狀態：準備上傳";
char *language_musiclist_upload_status_uploading_TW                = "狀態：正在上傳中";
char *language_musiclist_upload_status_succeed_TW                  = "狀態：上傳成功";
char *language_musiclist_upload_status_failed_song_invaild_TW      = "狀態：上傳失敗(歌曲異常)";
char *language_musiclist_upload_status_failed_storage_TW           = "狀態：上傳失敗(存儲空間不足)";
char *language_musiclist_upload_status_failed_TW                   = "狀態：上傳失敗";
char *language_musiclist_upload_close_TW                           = "關閉";
char *language_musiclist_upload_stop_TW                            = "停止"; 
char *language_musiclist_storage_cacacity_TW                       = "總存儲空間";  
char *language_musiclist_storage_used_TW                           = "已用空間"; 
char *language_musiclist_storage_remaining_TW                      = "剩余空間";  
char *language_musiclist_song_upload_EN = "Song Upload";
char *language_musiclist_song_delete_success_EN = "Delete Successful";
char *language_musiclist_song_delete_failed_EN = "Delete Failed";
char *language_musiclist_notSupported_upload_song_EN = "Song Upload Not Supported";
char *language_musiclist_insufficient_storage_EN = "Insufficient Storage. Cannot Upload";
char *language_musiclist_uploaded_EN             = "Song uploaded";
char *language_musiclist_confirm_delete_EN = "Confirm deleting the song:";
char *language_musiclist_confirm_upload_head_EN = "Confirm Upload Song:";
char *language_musiclist_confirm_upload_tail_EN = "to Server";
char *language_musiclist_upload_transferred_EN = "Transferred";
char *language_musiclist_upload_status_ready_EN = "Status: Ready to Upload";
char *language_musiclist_upload_status_uploading_EN = "Status: Uploading";
char *language_musiclist_upload_status_succeed_EN = "Status: Upload Successful";
char *language_musiclist_upload_status_failed_song_invaild_EN = "Status: Upload Failed (Invalid Song)";
char *language_musiclist_upload_status_failed_storage_EN = "Status: Upload Failed (Insufficient Storage)";
char *language_musiclist_upload_status_failed_EN = "Status: Upload Failed";
char *language_musiclist_upload_close_EN                           = "Close";
char *language_musiclist_upload_stop_EN                            = "Stop"; 
char *language_musiclist_storage_cacacity_EN                       = "Total storage space";  
char *language_musiclist_storage_used_EN                           = "Used space"; 
char *language_musiclist_storage_remaining_EN                      = "Remaining space";


char *language_login_title_CN               = "用户登录";
char *language_login_userName_CN            = "账号";
char *language_login_password_CN            = "密码";
char *language_login_remember_CN            = "记住密码";
char *language_login_button_CN              = "登录";
char *language_login_tips_password_error_CN   = "密码错误";
char *language_login_tips_dsp_error_CN      = "DSP初始化失败，请重试";
char *language_login_tips_init_network_error_CN   = "初始化网络中，请稍候";
char *language_login_tips_connect_server_error_CN   = "正在连接服务器，请稍候";
char *language_login_tips_getUserInfo_error_CN   = "获取用户信息中，请稍候";
char *language_login_tips_no_user_error_CN   = "用户不存在";
char *language_login_title_TW               = "用戶登錄";
char *language_login_userName_TW            = "賬號";
char *language_login_password_TW            = "密碼";
char *language_login_remember_TW            = "記住密碼";
char *language_login_button_TW              = "登錄";
char *language_login_tips_password_error_TW   = "密碼錯誤";
char *language_login_tips_dsp_error_TW      = "DSP初始化失敗，請重試";
char *language_login_tips_init_network_error_TW   = "初始化網絡中，請稍候";
char *language_login_tips_connect_server_error_TW   = "正在連接服務器，請稍候";
char *language_login_tips_getUserInfo_error_TW   = "獲取用戶信息中，請稍候";
char *language_login_tips_no_user_error_TW   = "用戶不存在";
char *language_login_title_EN               = "USER LOGIN";
char *language_login_userName_EN            = "UserName";
char *language_login_password_EN            = "Password";
char *language_login_remember_EN            = "remember password";
char *language_login_button_EN              = "Login";
char *language_login_tips_password_error_EN   = "Password error";
char *language_login_tips_dsp_error_EN      = "DSP initialization failed, please try again";
char *language_login_tips_init_network_error_EN   = "Initializing network, please wait";
char *language_login_tips_connect_server_error_EN   = "Please wait while connecting to the server";
char *language_login_tips_getUserInfo_error_EN   = "Getting user information, please wait";
char *language_login_tips_no_user_error_EN   = "User does not exist";

char *language_control_button_controlType_CN        = "控制类别";
char *language_control_button_zoneOrGroup_CN        = "分区/分组";
char *language_control_button_selectAll_CN          = "全选/取消";
char *language_control_button_paging_CN             = "寻呼";
char *language_control_button_transmit_CN           = "发射";
char *language_control_button_intercom_CN           = "对讲";
char *language_control_button_listen_CN             = "本地监听";
char *language_control_button_NetMusic_CN           = "网络音乐";
char *language_control_button_udisk_CN              = "UDISK";
char *language_control_button_stop_CN               = "停止";
char *language_control_button_volume_CN             = "音量";
char *language_control_button_settings_CN           = "设置";
char *language_control_button_controlType_TW        = "控製類別";
char *language_control_button_zoneOrGroup_TW        = "分區/分組";
char *language_control_button_selectAll_TW          = "全選/取消";
char *language_control_button_paging_TW             = "尋呼";
char *language_control_button_transmit_TW           = "發射";
char *language_control_button_intercom_TW           = "對講";
char *language_control_button_listen_TW             = "本地監聽";
char *language_control_button_NetMusic_TW           = "網絡音樂";
char *language_control_button_udisk_TW              = "UDISK";
char *language_control_button_stop_TW               = "停止";
char *language_control_button_volume_TW             = "音量";
char *language_control_button_settings_TW           = "設置";
char *language_control_button_controlType_EN        = "Control Type";
char *language_control_button_zoneOrGroup_EN        = "ZoneOrGroup";
char *language_control_button_selectAll_EN          = "Check All";
char *language_control_button_paging_EN             = "Paging";
char *language_control_button_transmit_EN           = "Transmit";
char *language_control_button_intercom_EN           = "Intercom";
char *language_control_button_listen_EN             = "Listen";
char *language_control_button_NetMusic_EN           = "Music";
char *language_control_button_udisk_EN              = "UDISK";
char *language_control_button_stop_EN               = "Stop";
char *language_control_button_volume_EN             = " Volume ";
char *language_control_button_settings_EN           = "Settings";

char *language_control_button_stop_zone_CN          = "停止分区";
char *language_control_button_stop_usb_CN           = "停止USB";
char *language_control_button_intercom_volume_CN    = "对讲音量";
char *language_control_zone_cnt_CN                  = "分区总数";
char *language_control_online_zone_cnt_CN           = "在线数量";
char *language_control_button_stop_zone_TW          = "停止分區";
char *language_control_button_stop_usb_TW           = "停止USB";
char *language_control_button_intercom_volume_TW    = "對講音量";
char *language_control_zone_cnt_TW                  = "分區總數";
char *language_control_online_zone_cnt_TW           = "在線數量";
char *language_control_button_stop_zone_EN          = "Stop Zone";
char *language_control_button_stop_usb_EN           = "Stop Udisk";
char *language_control_button_intercom_volume_EN    = "intercom volume";
char *language_control_zone_cnt_EN                  = "Total Zones";
char *language_control_online_zone_cnt_EN           = "Online Zones";

char *language_control_zoneInfo_zone_cnt_CN             = "分区总数";
char *language_control_zoneInfo_online_zone_cnt_CN      = "在线分区数量";
char *language_control_zoneInfo_selected_zone_cnt_CN    = "已选择分区数量";
char *language_control_groupInfo_group_cnt_CN           = "分组总数";
char *language_control_groupInfo_selected_group_cnt_CN  = "选择分组数量";
char *language_control_pagerInfo_pager_cnt_CN           = "寻呼台总数";
char *language_control_pagerInfo_online_pager_cnt_CN    = "在线数量";
char *language_control_pagerInfo_selected_pager_cnt_CN  = "已选择数量";
char *language_control_zoneInfo_zone_cnt_TW             = "分區總數";
char *language_control_zoneInfo_online_zone_cnt_TW      = "在線分區數量";
char *language_control_zoneInfo_selected_zone_cnt_TW    = "已選擇分區數量";
char *language_control_groupInfo_group_cnt_TW           = "分組總數";
char *language_control_groupInfo_selected_group_cnt_TW  = "選擇分組數量";
char *language_control_pagerInfo_pager_cnt_TW           = "尋呼臺總數";
char *language_control_pagerInfo_online_pager_cnt_TW    = "在線數量";
char *language_control_pagerInfo_selected_pager_cnt_TW  = "已選擇數量";
char *language_control_zoneInfo_zone_cnt_EN             = "total zones";
char *language_control_zoneInfo_online_zone_cnt_EN      = "online zones";
char *language_control_zoneInfo_selected_zone_cnt_EN    = "selected zones";
char *language_control_groupInfo_group_cnt_EN           = "total groups";
char *language_control_groupInfo_selected_group_cnt_EN  = "selected groups";
char *language_control_pagerInfo_pager_cnt_EN           = "total pagers";
char *language_control_pagerInfo_online_pager_cnt_EN    = "online pagers";
char *language_control_pagerInfo_selected_pager_cnt_EN  = "selected pagers";

char *language_control_type_zone_CN                     = "分区";
char *language_control_type_group_CN                    = "分组";
char *language_control_type_pager_CN                    = "寻呼台";
char *language_control_type_zone_TW                     = "分區";
char *language_control_type_group_TW                    = "分組";
char *language_control_type_pager_TW                    = "尋呼臺";
char *language_control_type_zone_EN                     = "Zone";
char *language_control_type_group_EN                    = "Group";
char *language_control_type_pager_EN                    = "Pager";

char *language_messagebox_confirm_CN                    = "确定";
char *language_messagebox_cancel_CN                     = "取消";
char *language_messagebox_confirm_TW                    = "確定";
char *language_messagebox_cancel_TW                     = "取消";
char *language_messagebox_confirm_EN                    = "OK";
char *language_messagebox_cancel_EN                     = "Cancel";

char *language_control_message_cancelAlarm_CN           = "请先解除消防警报!";
char *language_control_message_cancelAlarm_TW           = "請先解除消防警報!";
char *language_control_message_cancelAlarm_EN           = "Please turn off the alarm!";
char *language_control_message_stopPaging_CN            = "请先停止寻呼!";
char *language_control_message_stopPaging_TW            = "請先停止尋呼!";
char *language_control_message_stopPaging_EN            = "Please stop paging!";
char *language_control_message_stopIntercom_CN          = "请先停止对讲!";
char *language_control_message_stopIntercom_TW          = "請先停止對講!";
char *language_control_message_stopIntercom_EN          = "Please stop talking!";
char *language_control_message_logout_CN                = "确定退出当前账户?";
char *language_control_message_logout_TW                = "確定退出當前賬戶?";
char *language_control_message_logout_EN                = "Are you sure you want to logout?";
char *language_control_message_selectOnlineZone_CN      = "请选择在线分区!";
char *language_control_message_selectOnlineZone_TW      = "請選擇在線分區!";
char *language_control_message_selectOnlineZone_EN      = "Please select online Zones!";
char *language_control_message_selectGroupNoOnlineZone_CN      = "所选分组没有分区在线!";
char *language_control_message_selectGroupNoOnlineZone_TW      = "所選分組沒有分區在線!";
char *language_control_message_selectGroupNoOnlineZone_EN      = "Selected Group has no Online Zones!";
char *language_control_message_talkAfterRing_CN      = "寻呼钟声播报中，请在本对话框消失后开始讲话!";
char *language_control_message_talkAfterRing_TW      = "尋呼鐘聲播報中，請在本對話框消失後開始講話!";
char *language_control_message_talkAfterRing_EN      = "During the paging ringtone announcement, please start talking after this dialog box disappears!";
char *language_control_message_selectOneIntercomDevice_CN      = "请选择一个对讲终端!";
char *language_control_message_selectOneIntercomDevice_TW      = "請選擇一個對講終端!";
char *language_control_message_selectOneIntercomDevice_EN      = "Please select a device supported intercom!";
char *language_control_message_selectZoneNoIntercom_CN      = "该分区不支持对讲!";
char *language_control_message_selectZoneNoIntercom_TW      = "該分區不支持對講!";
char *language_control_message_selectZoneNoIntercom_EN      = "The device does not support intercom!";
char *language_control_message_selectPager_CN           = "请选择寻呼台!";
char *language_control_message_selectPager_TW           = "請選擇尋呼臺!";
char *language_control_message_selectPager_EN           = "Please select a Pager!";
char *language_control_message_selectOnePagerToTalk_CN           = "只能选择一个寻呼台进行对讲!";
char *language_control_message_selectOnePagerToTalk_TW           = "只能選擇一個尋呼臺進行對講!";
char *language_control_message_selectOnePagerToTalk_EN           = "Only one pager can be selected for intercom!";
char *language_control_message_selectPagerNoIntercom_CN           = "该寻呼台不支持对讲!";
char *language_control_message_selectPagerNoIntercom_TW           = "該尋呼臺不支持對講!";
char *language_control_message_selectPagerNoIntercom_EN           = "The pager does not support intercom!";
char *language_control_message_calledBusy_CN           = "对方忙,请稍后再拨!";
char *language_control_message_calledBusy_TW           = "對方忙,請稍後再撥!";
char *language_control_message_calledBusy_EN           = "The device you called was busy,Please try again later";
char *language_control_message_resetZone_CN           = "重置成功,系统将重新搜索分区!";
char *language_control_message_resetZone_TW           = "重置成功,系統將重新搜索分區!!";
char *language_control_message_resetZone_EN           = "reset successful,the zones will re-search soon!";


char *language_control_call_calling_CN                  = "通话中";
char *language_control_call_wait_answer_CN              = "等待接听";
char *language_control_call_wait_reply_CN               = "等待应答";
char *language_control_call_hangUp_CN                   = "挂断";
char *language_control_call_answer_CN                   = "接听";
char *language_control_call_hangedUp_CN                 = "已挂断";
char *language_control_call_no_answer_CN                = "对方无人接听";
char *language_control_call_codecs_not_support_CN              = "不支持的语音编码";
char *language_control_call_busy_CN                     = "对方繁忙";
char *language_control_call_refused_CN                  = "对方已拒绝";
char *language_control_input_settings_password_CN       = "请输入用户密码";
char *language_control_call_calling_TW                  = "通話中";
char *language_control_call_wait_answer_TW              = "等待接聽";
char *language_control_call_wait_reply_TW               = "等待應答";
char *language_control_call_hangUp_TW                   = "掛斷";
char *language_control_call_answer_TW                   = "接聽";
char *language_control_call_hangedUp_TW                 = "已掛斷";
char *language_control_call_no_answer_TW                = "對方無人接聽";
char *language_control_call_codecs_not_support_TW       = "不支持的語音編碼";
char *language_control_call_busy_TW                     = "對方繁忙";
char *language_control_call_refused_TW                  = "對方已拒絕";
char *language_control_input_settings_password_TW       = "請輸入用戶密碼";
char *language_control_call_calling_EN                  = "Connected";
char *language_control_call_wait_answer_EN              = "Waiting for answer";
char *language_control_call_wait_reply_EN               = "Waiting for reply";
char *language_control_call_hangUp_EN                   = "Hang up";
char *language_control_call_answer_EN                   = "Answer";
char *language_control_call_hangedUp_EN                 = "Hanged up";
char *language_control_call_no_answer_EN                = "No answer";
char *language_control_call_codecs_not_support_EN              = "Unsupported audio codec";
char *language_control_call_busy_EN                     = "Caller is Busy";
char *language_control_call_refused_EN                  = "Call declined";
char *language_control_input_settings_password_EN       = "Please enter your password";

char *language_settings_title_CN                        = "系统设置";
char *language_settings_tab_deviceInfo_CN               = "设备信息";
char *language_settings_tab_network_CN                  = "网络设置";
char *language_settings_tab_mic_CN                      = "话筒设置";
char *language_settings_tab_output_CN                   = "输出设置";
char *language_settings_tab_listen_CN                   = "监听设置";
char *language_settings_tab_trigger_CN                  = "触发设置";
char *language_settings_tab_ring_CN                     = "钟声设置";
char *language_settings_tab_display_CN                  = "显示设置";
char *language_settings_tab_other_CN                    = "其他设置";
char *language_settings_title_TW                        = "系統設置";
char *language_settings_tab_deviceInfo_TW               = "設備信息";
char *language_settings_tab_network_TW                  = "網絡設置";
char *language_settings_tab_mic_TW                      = "話筒設置";
char *language_settings_tab_output_TW                   = "輸出設置";
char *language_settings_tab_listen_TW                   = "監聽設置";
char *language_settings_tab_trigger_TW                  = "觸發設置";
char *language_settings_tab_ring_TW                     = "鐘聲設置";
char *language_settings_tab_display_TW                  = "顯示設置";
char *language_settings_tab_other_TW                    = "其他設置";
char *language_settings_title_EN                        = "System settings";
char *language_settings_tab_deviceInfo_EN               = "Device";
char *language_settings_tab_network_EN                  = "Network";
char *language_settings_tab_mic_EN                      = "Mic";
char *language_settings_tab_output_EN                   = "Output";
char *language_settings_tab_listen_EN                   = "Listen";
char *language_settings_tab_trigger_EN                  = "Trigger";
char *language_settings_tab_ring_EN                     = "Ring";
char *language_settings_tab_display_EN                  = "Display";
char *language_settings_tab_other_EN                    = "Other";

char *language_settings_device_version_CN               = "版本号";
char *language_settings_device_networkMode_CN           = "网络模式";
char *language_settings_device_connectStatus_CN         = "服务器连接状态";
char *language_settings_device_connected_CN             = "已连接";
char *language_settings_device_disconnected_CN          = "未连接";
char *language_settings_device_serialNumber_CN          = "序列号";
char *language_settings_device_netCableNotInsert_CN     = "网线未插入";
char *language_settings_device_netCableNotInsertAndUse4G_CN = "网线未插入，启用4G网络";
char *language_settings_device_version_TW               = "版本號";
char *language_settings_device_networkMode_TW           = "網絡模式";
char *language_settings_device_connectStatus_TW         = "服務器連接狀態";
char *language_settings_device_connected_TW             = "已連接";
char *language_settings_device_disconnected_TW          = "未連接";
char *language_settings_device_serialNumber_TW          = "序列號";
char *language_settings_device_netCableNotInsert_TW     = "網線未插入";
char *language_settings_device_netCableNotInsertAndUse4G_TW = "網線未插入，啟用4G網絡";
char *language_settings_device_version_EN               = "Version";
char *language_settings_device_networkMode_EN           = "Network Mode";
char *language_settings_device_connectStatus_EN         = "Connection status";
char *language_settings_device_connected_EN             = "connected";
char *language_settings_device_disconnected_EN          = "disconnected";
char *language_settings_device_serialNumber_EN          = "Serial number";
char *language_settings_device_netCableNotInsert_EN     = "Network cable is not inserted";
char *language_settings_device_netCableNotInsertAndUse4G_EN = "Network cable is not inserted,using 4G network";

char *language_settings_network_ip_CN                   = "IP地址";
char *language_settings_network_subnetMask_CN           = "子网掩码";
char *language_settings_network_gateway_CN              = "网关";
char *language_settings_network_settings_CN             = "设置";
char *language_settings_network_settingsOK_CN               = "设置成功，系统即将重启!";
char *language_settings_network_inputCorrectIP_CN           = "请输入正确的IP地址!";
char *language_settings_network_inputCorrectSubNetMask_CN   = "请输入正确的子网掩码!";
char *language_settings_network_inputCorrectGateWay_CN      = "请输入正确的网关!";
char *language_settings_network_ip_TW                   = "IP地址";
char *language_settings_network_subnetMask_TW           = "子網掩碼";
char *language_settings_network_gateway_TW              = "網關";
char *language_settings_network_settings_TW             = "設置";
char *language_settings_network_settingsOK_TW               = "設置成功，系統即將重啟!";
char *language_settings_network_inputCorrectIP_TW           = "請輸入正確的IP地址!";
char *language_settings_network_inputCorrectSubNetMask_TW   = "請輸入正確的子網掩碼!";
char *language_settings_network_inputCorrectGateWay_TW      = "請輸入正確的網關!";
char *language_settings_network_ip_EN                   = "IP address";
char *language_settings_network_subnetMask_EN           = "SubNet Mask";
char *language_settings_network_gateway_EN              = "GateWay";
char *language_settings_network_settings_EN             = "Confirm";
char *language_settings_network_settingsOK_EN               = "configuration is successful and the system will reboot soon!";
char *language_settings_network_inputCorrectIP_EN           = "Please input correct ip address!";
char *language_settings_network_inputCorrectSubNetMask_EN   = "Please input correct subNet Mask!";
char *language_settings_network_inputCorrectGateWay_EN      = "Please input correct gateway!";


char *language_settings_mic_mixAux_CN                   = "混合线路输入";
char *language_settings_mic_sensitivity_CN              = "麦克风灵敏度";
char *language_settings_mic_mixAux_TW                   = "混合線路輸入";
char *language_settings_mic_sensitivity_TW              = "麥克風靈敏度";
char *language_settings_mic_mixAux_EN                   = "AUX input mixing";
char *language_settings_mic_sensitivity_EN              = "Microphone sensitivity";

char *language_settings_output_listenVol_CN                = "监听音量";
char *language_settings_output_intercomVol_CN              = "对讲音量";
char *language_settings_output_ringVol_CN                  = "铃声音量";
char *language_settings_output_listenVol_TW                = "監聽音量";
char *language_settings_output_intercomVol_TW              = "對講音量";
char *language_settings_output_ringVol_TW                  = "鈴聲音量";
char *language_settings_output_listenVol_EN                = "Listen volume";
char *language_settings_output_intercomVol_EN              = "Intercom volume";
char *language_settings_output_ringVol_EN                  = "Ring volume";

char *language_settings_ring_pagingRing_CN                 = "寻呼钟声";
char *language_settings_ring_pagingRing_TW                 = "尋呼鐘聲";
char *language_settings_ring_pagingRing_EN                 = "Paging ring";

char *language_settings_display_brightness_CN              = "屏幕亮度";
char *language_settings_display_automatic_screenOff_CN     = "自动熄屏";
char *language_settings_display_minute_CN                  = "分钟";
char *language_settings_display_disable_CN                 = "禁止";
char *language_settings_display_language_CN                = "语言";
char *language_settings_display_brightness_TW              = "屏幕亮度";
char *language_settings_display_automatic_screenOff_TW     = "自動熄屏";
char *language_settings_display_minute_TW                  = "分鐘";
char *language_settings_display_disable_TW                 = "禁止";
char *language_settings_display_language_TW                = "語言";
char *language_settings_display_brightness_EN              = "Screen brightness";
char *language_settings_display_automatic_screenOff_EN     = "Automatic screen off";
char *language_settings_display_minute_EN                  = " minutes";
char *language_settings_display_disable_EN                 = "disable";
char *language_settings_display_language_EN                = "Language";

char *language_settings_other_zone_paging_volume_CN        = "分区寻呼默认音量";
char *language_settings_other_paging_timeout_CN            = "寻呼信号超时关闭";
char *language_settings_other_reset_zones_CN               = "重置分区";
char *language_settings_other_password_access_CN           = "设置页面密码访问";
char *language_settings_other_logout_CN                    = "退出登录";
char *language_settings_other_zone_paging_volume_TW        = "分區尋呼默認音量";
char *language_settings_other_paging_timeout_TW            = "尋呼信號超時關閉";
char *language_settings_other_reset_zones_TW               = "重置分區";
char *language_settings_other_password_access_TW           = "設置頁面密碼訪問";
char *language_settings_other_logout_TW                    = "退出登錄";
char *language_settings_other_zone_paging_volume_EN        = "Default volume on paging";
char *language_settings_other_paging_timeout_EN            = "Signal timeout on paging";
char *language_settings_other_reset_zones_EN               = "Re-search";
char *language_settings_other_password_access_EN           = "Password Access Settings";
char *language_settings_other_logout_EN                    = "Log out";