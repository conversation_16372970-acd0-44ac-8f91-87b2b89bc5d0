#include "win.h"
#include "const.h"
#include "language_config.h"

extern int language;

char *language_playsource_offline_text;
char *language_playsource_idle_text;
char *language_playsource_localPlay_text;
char *language_playsource_netPlay_text;
char *language_playsource_timing_text;
char *language_playsource_audioCollector_text;
char *language_playsource_intercom_text;
char *language_playsource_paging_text;
char *language_playsource_alarm_text;
char *language_playsource_100V_text;

char *language_playmode_singlePlay_text;
char *language_playmode_singleLoop_text;
char *language_playmode_sequencyPlay_text;
char *language_playmode_listLoop_text;
char *language_playmode_shuffle_text;

char *language_musiclist_root_text;
char *language_musiclist_preSong_text;
char *language_musiclist_play_text;
char *language_musiclist_stop_text;
char *language_musiclist_pause_text;
char *language_musiclist_nextSong_text;
char *language_musiclist_upload_text;
char *language_musiclist_delete_text;
char *language_musiclist_list_text;
char *language_musiclist_song_text;
char *language_musiclist_selectList_text;
char *language_musiclist_selectSong_text;
char *language_musiclist_insertUdisk_text;
char *language_musiclist_udiskLoading_text;

char *language_musiclist_song_upload_text;
char *language_musiclist_song_delete_success_text;
char *language_musiclist_song_delete_failed_text;
char *language_musiclist_notSupported_upload_song_text;
char *language_musiclist_insufficient_storage_text;
char *language_musiclist_uploaded_text;
char *language_musiclist_confirm_delete_text;
char *language_musiclist_confirm_upload_head_text;
char *language_musiclist_confirm_upload_tail_text;
char *language_musiclist_upload_transferred_text;
char *language_musiclist_upload_status_ready_text;
char *language_musiclist_upload_status_uploading_text;
char *language_musiclist_upload_status_succeed_text;
char *language_musiclist_upload_status_failed_song_invaild_text;
char *language_musiclist_upload_status_failed_storage_text;
char *language_musiclist_upload_status_failed_text;
char *language_musiclist_upload_close_text;
char *language_musiclist_upload_stop_text;
char *language_musiclist_storage_cacacity_text;
char *language_musiclist_storage_used_text;
char *language_musiclist_storage_remaining_text;

char *language_login_title_text;
char *language_login_userName_text;
char *language_login_password_text;
char *language_login_remember_text;
char *language_login_button_text;
char *language_login_tips_password_error_text;
char *language_login_tips_dsp_error_text;
char *language_login_tips_init_network_error_text;
char *language_login_tips_connect_server_error_text;
char *language_login_tips_getUserInfo_error_text;
char *language_login_tips_no_user_error_text;

char *language_control_button_controlType_text;
char *language_control_button_zoneOrGroup_text;
char *language_control_button_selectAll_text;
char *language_control_button_paging_text;
char *language_control_button_transmit_text;
char *language_control_button_intercom_text;
char *language_control_button_listen_text;
char *language_control_button_NetMusic_text;
char *language_control_button_udisk_text;
char *language_control_button_stop_text;
char *language_control_button_volume_text;
char *language_control_button_settings_text;

char *language_control_button_stop_zone_text;
char *language_control_button_stop_usb_text;
char *language_control_button_intercom_volume_text;
char *language_control_zone_cnt_text;
char *language_control_online_zone_cnt_text;

char *language_control_zoneInfo_zone_cnt_text;
char *language_control_zoneInfo_online_zone_cnt_text;
char *language_control_zoneInfo_selected_zone_cnt_text;
char *language_control_groupInfo_group_cnt_text;
char *language_control_groupInfo_selected_group_cnt_text;
char *language_control_pagerInfo_pager_cnt_text;
char *language_control_pagerInfo_online_pager_cnt_text;
char *language_control_pagerInfo_selected_pager_cnt_text;

char *language_control_type_zone_text;
char *language_control_type_group_text;
char *language_control_type_pager_text;

char *language_messagebox_confirm_text;
char *language_messagebox_cancel_text;

char *language_control_message_cancelAlarm_text;
char *language_control_message_stopPaging_text;
char *language_control_message_stopIntercom_text;
char *language_control_message_logout_text;
char *language_control_message_selectOnlineZone_text;
char *language_control_message_selectGroupNoOnlineZone_text;
char *language_control_message_talkAfterRing_text;
char *language_control_message_selectOneIntercomDevice_text;
char *language_control_message_selectZoneNoIntercom_text;
char *language_control_message_selectPager_text;
char *language_control_message_selectOnePagerToTalk_text;
char *language_control_message_selectPagerNoIntercom_text;
char *language_control_message_calledBusy_text;
char *language_control_message_resetZone_text;


char *language_control_call_calling_text;
char *language_control_call_wait_answer_text;
char *language_control_call_wait_reply_text;
char *language_control_call_hangUp_text;
char *language_control_call_answer_text;
char *language_control_call_hangedUp_text;
char *language_control_call_no_answer_text;
char *language_control_call_codecs_not_support_text;
char *language_control_call_busy_text;
char *language_control_call_refused_text;
char *language_control_input_settings_password_text;


char *language_settings_title_text;
char *language_settings_tab_deviceInfo_text;
char *language_settings_tab_network_text;
char *language_settings_tab_mic_text;
char *language_settings_tab_output_text;
char *language_settings_tab_listen_text;
char *language_settings_tab_trigger_text;
char *language_settings_tab_ring_text;
char *language_settings_tab_display_text;
char *language_settings_tab_other_text;

char *language_settings_device_version_text;
char *language_settings_device_networkMode_text;
char *language_settings_device_connectStatus_text;
char *language_settings_device_connected_text;
char *language_settings_device_disconnected_text;
char *language_settings_device_serialNumber_text;
char *language_settings_device_netCableNotInsert_text;
char *language_settings_device_netCableNotInsertAndUse4G_text;

char *language_settings_network_ip_text;
char *language_settings_network_subnetMask_text;
char *language_settings_network_gateway_text;
char *language_settings_network_settings_text;
char *language_settings_network_settingsOK_text;
char *language_settings_network_inputCorrectIP_text;
char *language_settings_network_inputCorrectSubNetMask_text;
char *language_settings_network_inputCorrectGateWay_text;

char *language_settings_mic_mixAux_text;
char *language_settings_mic_sensitivity_text;

char *language_settings_output_listenVol_text;
char *language_settings_output_intercomVol_text;
char *language_settings_output_ringVol_text;

char *language_settings_ring_pagingRing_text;

char *language_settings_display_brightness_text;
char *language_settings_display_automatic_screenOff_text;
char *language_settings_display_minute_text;
char *language_settings_display_disable_text;
char *language_settings_display_language_text;

char *language_settings_other_zone_paging_volume_text;
char *language_settings_other_paging_timeout_text;
char *language_settings_other_reset_zones_text;
char *language_settings_other_password_access_text;
char *language_settings_other_logout_text;

static void Language_Config_playsource_Chinese(void)
{
	language_playsource_offline_text	        =  language_playsource_offline_CN;
	language_playsource_idle_text               =  language_playsource_idle_CN;
	language_playsource_localPlay_text          =  language_playsource_localPlay_CN;
    language_playsource_netPlay_text            =  language_playsource_netPlay_CN;
	language_playsource_timing_text             =  language_playsource_timing_CN;
    language_playsource_audioCollector_text	    =  language_playsource_audioCollector_CN;
	language_playsource_intercom_text           =  language_playsource_intercom_CN;
	language_playsource_paging_text             =  language_playsource_paging_CN;
    language_playsource_alarm_text              =  language_playsource_alarm_CN;
	language_playsource_100V_text               =  language_playsource_100V_CN;
}                                                             
static void Language_Config_playsource_English(void)
{
	language_playsource_offline_text	        =  language_playsource_offline_EN;
	language_playsource_idle_text               =  language_playsource_idle_EN;
	language_playsource_localPlay_text          =  language_playsource_localPlay_EN;
    language_playsource_netPlay_text            =  language_playsource_netPlay_EN;
	language_playsource_timing_text             =  language_playsource_timing_EN;
    language_playsource_audioCollector_text	    =  language_playsource_audioCollector_EN;
	language_playsource_intercom_text           =  language_playsource_intercom_EN;
	language_playsource_paging_text             =  language_playsource_paging_EN;
    language_playsource_alarm_text              =  language_playsource_alarm_EN;
	language_playsource_100V_text               =  language_playsource_100V_EN;
}
static void Language_Config_playsource_ZHTW(void)
{
	language_playsource_offline_text	        =  language_playsource_offline_TW;
	language_playsource_idle_text               =  language_playsource_idle_TW;
	language_playsource_localPlay_text          =  language_playsource_localPlay_TW;
    language_playsource_netPlay_text            =  language_playsource_netPlay_TW;
	language_playsource_timing_text             =  language_playsource_timing_TW;
    language_playsource_audioCollector_text	    =  language_playsource_audioCollector_TW;
	language_playsource_intercom_text           =  language_playsource_intercom_TW;
	language_playsource_paging_text             =  language_playsource_paging_TW;
    language_playsource_alarm_text              =  language_playsource_alarm_TW;
	language_playsource_100V_text               =  language_playsource_100V_TW;
}



static void Language_Config_musicList_Chinese(void)
{
    language_playmode_singlePlay_text     =     language_playmode_singlePlay_CN;
    language_playmode_singleLoop_text     =     language_playmode_singleLoop_CN;
    language_playmode_sequencyPlay_text   =     language_playmode_sequencyPlay_CN;
    language_playmode_listLoop_text       =     language_playmode_listLoop_CN;
    language_playmode_shuffle_text        =     language_playmode_shuffle_CN;

    language_musiclist_root_text          =     language_musiclist_root_CN;
    language_musiclist_preSong_text       =     language_musiclist_preSong_CN;
    language_musiclist_play_text          =     language_musiclist_play_CN;
    language_musiclist_stop_text          =     language_musiclist_stop_CN;
    language_musiclist_pause_text         =     language_musiclist_pause_CN;
    language_musiclist_nextSong_text      =     language_musiclist_nextSong_CN;
    language_musiclist_upload_text        =     language_musiclist_upload_CN;
    language_musiclist_delete_text        =     language_musiclist_delete_CN;

    language_musiclist_list_text          =     language_musiclist_list_CN;  
    language_musiclist_song_text          =     language_musiclist_song_CN;  
    language_musiclist_selectList_text    =     language_musiclist_selectList_CN;  
    language_musiclist_selectSong_text    =     language_musiclist_selectSong_CN;  
    language_musiclist_insertUdisk_text   =     language_musiclist_insertUdisk_CN;
    language_musiclist_udiskLoading_text  =     language_musiclist_udiskLoading_CN;


    language_musiclist_song_upload_text                         =      language_musiclist_song_upload_CN;  
    language_musiclist_song_delete_success_text                 =      language_musiclist_song_delete_success_CN; 
    language_musiclist_song_delete_failed_text                  =      language_musiclist_song_delete_failed_CN; 
    language_musiclist_notSupported_upload_song_text            =      language_musiclist_notSupported_upload_song_CN; 
    language_musiclist_insufficient_storage_text                =      language_musiclist_insufficient_storage_CN;
    language_musiclist_uploaded_text                            =      language_musiclist_uploaded_CN; 
    language_musiclist_confirm_delete_text                      =      language_musiclist_confirm_delete_CN;
    language_musiclist_confirm_upload_head_text                 =      language_musiclist_confirm_upload_head_CN; 
    language_musiclist_confirm_upload_tail_text                 =      language_musiclist_confirm_upload_tail_CN; 
    language_musiclist_upload_transferred_text                  =      language_musiclist_upload_transferred_CN; 
    language_musiclist_upload_status_ready_text                 =      language_musiclist_upload_status_ready_CN; 
    language_musiclist_upload_status_uploading_text             =      language_musiclist_upload_status_uploading_CN;
    language_musiclist_upload_status_succeed_text               =      language_musiclist_upload_status_succeed_CN; 
    language_musiclist_upload_status_failed_song_invaild_text   =      language_musiclist_upload_status_failed_song_invaild_CN; 
    language_musiclist_upload_status_failed_storage_text        =      language_musiclist_upload_status_failed_storage_CN; 
    language_musiclist_upload_status_failed_text                =      language_musiclist_upload_status_failed_CN;
    language_musiclist_upload_close_text                        =      language_musiclist_upload_close_CN;
    language_musiclist_upload_stop_text                         =      language_musiclist_upload_stop_CN;
    language_musiclist_storage_cacacity_text                    =      language_musiclist_storage_cacacity_CN;        
    language_musiclist_storage_used_text                        =      language_musiclist_storage_used_CN; 
    language_musiclist_storage_remaining_text                   =      language_musiclist_storage_remaining_CN; 
}
static void Language_Config_musicList_English(void)
{
    language_playmode_singlePlay_text     =     language_playmode_singlePlay_EN;
    language_playmode_singleLoop_text     =     language_playmode_singleLoop_EN;
    language_playmode_sequencyPlay_text   =     language_playmode_sequencyPlay_EN;
    language_playmode_listLoop_text       =     language_playmode_listLoop_EN;
    language_playmode_shuffle_text        =     language_playmode_shuffle_EN;

    language_musiclist_root_text          =     language_musiclist_root_EN;
    language_musiclist_preSong_text       =     language_musiclist_preSong_EN;
    language_musiclist_play_text          =     language_musiclist_play_EN;
    language_musiclist_stop_text          =     language_musiclist_stop_EN;
    language_musiclist_pause_text         =     language_musiclist_pause_EN;
    language_musiclist_nextSong_text      =     language_musiclist_nextSong_EN;
    language_musiclist_upload_text        =     language_musiclist_upload_EN;
    language_musiclist_delete_text        =     language_musiclist_delete_EN;

    language_musiclist_list_text          =     language_musiclist_list_EN;  
    language_musiclist_song_text          =     language_musiclist_song_EN;  
    language_musiclist_selectList_text    =     language_musiclist_selectList_EN;  
    language_musiclist_selectSong_text    =     language_musiclist_selectSong_EN;  
    language_musiclist_insertUdisk_text   =     language_musiclist_insertUdisk_EN;
    language_musiclist_udiskLoading_text  =     language_musiclist_udiskLoading_EN;


    language_musiclist_song_upload_text                         =      language_musiclist_song_upload_EN;  
    language_musiclist_song_delete_success_text                 =      language_musiclist_song_delete_success_EN; 
    language_musiclist_song_delete_failed_text                  =      language_musiclist_song_delete_failed_EN; 
    language_musiclist_notSupported_upload_song_text            =      language_musiclist_notSupported_upload_song_EN; 
    language_musiclist_insufficient_storage_text                =      language_musiclist_insufficient_storage_EN;
    language_musiclist_uploaded_text                            =      language_musiclist_uploaded_EN;  
    language_musiclist_confirm_delete_text                      =      language_musiclist_confirm_delete_EN;
    language_musiclist_confirm_upload_head_text                 =      language_musiclist_confirm_upload_head_EN; 
    language_musiclist_confirm_upload_tail_text                 =      language_musiclist_confirm_upload_tail_EN; 
    language_musiclist_upload_transferred_text                  =      language_musiclist_upload_transferred_EN; 
    language_musiclist_upload_status_ready_text                 =      language_musiclist_upload_status_ready_EN; 
    language_musiclist_upload_status_uploading_text             =      language_musiclist_upload_status_uploading_EN;
    language_musiclist_upload_status_succeed_text               =      language_musiclist_upload_status_succeed_EN; 
    language_musiclist_upload_status_failed_song_invaild_text   =      language_musiclist_upload_status_failed_song_invaild_EN; 
    language_musiclist_upload_status_failed_storage_text        =      language_musiclist_upload_status_failed_storage_EN; 
    language_musiclist_upload_status_failed_text                =      language_musiclist_upload_status_failed_EN;
    language_musiclist_upload_close_text                        =      language_musiclist_upload_close_EN;
    language_musiclist_upload_stop_text                         =      language_musiclist_upload_stop_EN;
    language_musiclist_storage_cacacity_text                    =      language_musiclist_storage_cacacity_EN;        
    language_musiclist_storage_used_text                        =      language_musiclist_storage_used_EN; 
    language_musiclist_storage_remaining_text                   =      language_musiclist_storage_remaining_EN; 
}
static void Language_Config_musicList_ZHTW(void)
{
    language_playmode_singlePlay_text     =     language_playmode_singlePlay_TW;
    language_playmode_singleLoop_text     =     language_playmode_singleLoop_TW;
    language_playmode_sequencyPlay_text   =     language_playmode_sequencyPlay_TW;
    language_playmode_listLoop_text       =     language_playmode_listLoop_TW;
    language_playmode_shuffle_text        =     language_playmode_shuffle_TW;

    language_musiclist_root_text          =     language_musiclist_root_TW;
    language_musiclist_preSong_text       =     language_musiclist_preSong_TW;
    language_musiclist_play_text          =     language_musiclist_play_TW;
    language_musiclist_stop_text          =     language_musiclist_stop_TW;
    language_musiclist_pause_text         =     language_musiclist_pause_TW;
    language_musiclist_nextSong_text      =     language_musiclist_nextSong_TW;
    language_musiclist_upload_text        =     language_musiclist_upload_TW;
    language_musiclist_delete_text        =     language_musiclist_delete_TW;

    language_musiclist_list_text          =     language_musiclist_list_TW;  
    language_musiclist_song_text          =     language_musiclist_song_TW;  
    language_musiclist_selectList_text    =     language_musiclist_selectList_TW;  
    language_musiclist_selectSong_text    =     language_musiclist_selectSong_TW;  
    language_musiclist_insertUdisk_text   =     language_musiclist_insertUdisk_TW;
    language_musiclist_udiskLoading_text  =     language_musiclist_udiskLoading_TW;


    language_musiclist_song_upload_text                         =      language_musiclist_song_upload_TW;  
    language_musiclist_song_delete_success_text                 =      language_musiclist_song_delete_success_TW; 
    language_musiclist_song_delete_failed_text                  =      language_musiclist_song_delete_failed_TW; 
    language_musiclist_notSupported_upload_song_text            =      language_musiclist_notSupported_upload_song_TW; 
    language_musiclist_insufficient_storage_text                =      language_musiclist_insufficient_storage_TW;
    language_musiclist_uploaded_text                            =      language_musiclist_uploaded_TW;  
    language_musiclist_confirm_delete_text                      =      language_musiclist_confirm_delete_TW;
    language_musiclist_confirm_upload_head_text                 =      language_musiclist_confirm_upload_head_TW; 
    language_musiclist_confirm_upload_tail_text                 =      language_musiclist_confirm_upload_tail_TW; 
    language_musiclist_upload_transferred_text                  =      language_musiclist_upload_transferred_TW; 
    language_musiclist_upload_status_ready_text                 =      language_musiclist_upload_status_ready_TW; 
    language_musiclist_upload_status_uploading_text             =      language_musiclist_upload_status_uploading_TW;
    language_musiclist_upload_status_succeed_text               =      language_musiclist_upload_status_succeed_TW; 
    language_musiclist_upload_status_failed_song_invaild_text   =      language_musiclist_upload_status_failed_song_invaild_TW; 
    language_musiclist_upload_status_failed_storage_text        =      language_musiclist_upload_status_failed_storage_TW; 
    language_musiclist_upload_status_failed_text                =      language_musiclist_upload_status_failed_TW; 
    language_musiclist_upload_close_text                        =      language_musiclist_upload_close_TW;
    language_musiclist_upload_stop_text                         =      language_musiclist_upload_stop_TW;
    language_musiclist_storage_cacacity_text                    =      language_musiclist_storage_cacacity_TW;        
    language_musiclist_storage_used_text                        =      language_musiclist_storage_used_TW; 
    language_musiclist_storage_remaining_text                   =      language_musiclist_storage_remaining_TW;
}


static void Language_Config_login_Chinese(void)
{
	language_login_title_text	     =  language_login_title_CN;
	language_login_userName_text     =  language_login_userName_CN;
	language_login_password_text     =  language_login_password_CN;
    language_login_remember_text     =  language_login_remember_CN;
	language_login_button_text       =  language_login_button_CN;
    language_login_tips_password_error_text =  language_login_tips_password_error_CN;
    language_login_tips_dsp_error_text  =  language_login_tips_dsp_error_CN;
    language_login_tips_init_network_error_text =  language_login_tips_init_network_error_CN;
    language_login_tips_connect_server_error_text =  language_login_tips_connect_server_error_CN;
    language_login_tips_getUserInfo_error_text =  language_login_tips_getUserInfo_error_CN;
    language_login_tips_no_user_error_text =  language_login_tips_no_user_error_CN;
}                                                             
static void Language_Config_login_English(void)
{
	language_login_title_text	     =  language_login_title_EN;
	language_login_userName_text     =  language_login_userName_EN;
	language_login_password_text     =  language_login_password_EN;
    language_login_remember_text     =  language_login_remember_EN;
	language_login_button_text       =  language_login_button_EN;
    language_login_tips_password_error_text =  language_login_tips_password_error_EN;
    language_login_tips_dsp_error_text  =  language_login_tips_dsp_error_EN;
    language_login_tips_init_network_error_text =  language_login_tips_init_network_error_EN;
    language_login_tips_connect_server_error_text =  language_login_tips_connect_server_error_EN;
    language_login_tips_getUserInfo_error_text =  language_login_tips_getUserInfo_error_EN;
    language_login_tips_no_user_error_text =  language_login_tips_no_user_error_EN;
}
static void Language_Config_login_ZHTW(void)
{
	language_login_title_text	     =  language_login_title_TW;
	language_login_userName_text     =  language_login_userName_TW;
	language_login_password_text     =  language_login_password_TW;
    language_login_remember_text     =  language_login_remember_TW;
	language_login_button_text       =  language_login_button_TW;
    language_login_tips_password_error_text =  language_login_tips_password_error_TW;
    language_login_tips_dsp_error_text  =  language_login_tips_dsp_error_TW;
    language_login_tips_init_network_error_text =  language_login_tips_init_network_error_TW;
    language_login_tips_connect_server_error_text =  language_login_tips_connect_server_error_TW;
    language_login_tips_getUserInfo_error_text =  language_login_tips_getUserInfo_error_TW;
    language_login_tips_no_user_error_text =  language_login_tips_no_user_error_TW;
}                                                


static void Language_Config_control_Chinese(void)
{
	language_control_button_controlType_text	    =  language_control_button_controlType_CN;
	language_control_button_zoneOrGroup_text        =  language_control_button_zoneOrGroup_CN;
	language_control_button_selectAll_text          =  language_control_button_selectAll_CN;
    language_control_button_paging_text             =  language_control_button_paging_CN;
    language_control_button_transmit_text           =  language_control_button_transmit_CN;
	language_control_button_intercom_text           =  language_control_button_intercom_CN;
    language_control_button_listen_text	            =  language_control_button_listen_CN;
	language_control_button_NetMusic_text           =  language_control_button_NetMusic_CN;
	language_control_button_udisk_text              =  language_control_button_udisk_CN;
    language_control_button_stop_text               =  language_control_button_stop_CN;
	language_control_button_volume_text             =  language_control_button_volume_CN;
    language_control_button_settings_text           =  language_control_button_settings_CN;

    language_control_button_stop_zone_text          =  language_control_button_stop_zone_CN;
    language_control_button_stop_usb_text           =  language_control_button_stop_usb_CN;
    language_control_button_intercom_volume_text    =  language_control_button_intercom_volume_CN;
    language_control_zone_cnt_text                  =  language_control_zone_cnt_CN;
    language_control_online_zone_cnt_text           =  language_control_online_zone_cnt_CN;

    language_control_zoneInfo_zone_cnt_text         =  language_control_zoneInfo_zone_cnt_CN;
    language_control_zoneInfo_online_zone_cnt_text  =  language_control_zoneInfo_online_zone_cnt_CN;
    language_control_zoneInfo_selected_zone_cnt_text=  language_control_zoneInfo_selected_zone_cnt_CN;
    language_control_groupInfo_group_cnt_text       =  language_control_groupInfo_group_cnt_CN;
    language_control_groupInfo_selected_group_cnt_text =  language_control_groupInfo_selected_group_cnt_CN;
    language_control_pagerInfo_pager_cnt_text       =  language_control_pagerInfo_pager_cnt_CN;
    language_control_pagerInfo_online_pager_cnt_text=  language_control_pagerInfo_online_pager_cnt_CN;
    language_control_pagerInfo_selected_pager_cnt_text=  language_control_pagerInfo_selected_pager_cnt_CN;

    language_control_type_zone_text                 =  language_control_type_zone_CN;
    language_control_type_group_text                =  language_control_type_group_CN;
    language_control_type_pager_text                =  language_control_type_pager_CN;

    language_messagebox_confirm_text                =  language_messagebox_confirm_CN;
    language_messagebox_cancel_text                 =  language_messagebox_cancel_CN;

    language_control_message_cancelAlarm_text       =  language_control_message_cancelAlarm_CN;
    language_control_message_stopPaging_text        =  language_control_message_stopPaging_CN;
    language_control_message_stopIntercom_text      =  language_control_message_stopIntercom_CN;
    language_control_message_logout_text            =  language_control_message_logout_CN;
    language_control_message_selectOnlineZone_text  =  language_control_message_selectOnlineZone_CN;
    language_control_message_selectGroupNoOnlineZone_text = language_control_message_selectGroupNoOnlineZone_CN;
    language_control_message_talkAfterRing_text      = language_control_message_talkAfterRing_CN;
    language_control_message_selectOneIntercomDevice_text    = language_control_message_selectOneIntercomDevice_CN;
    language_control_message_selectZoneNoIntercom_text   = language_control_message_selectZoneNoIntercom_CN;
    language_control_message_selectPager_text    = language_control_message_selectPager_CN;
    language_control_message_selectOnePagerToTalk_text   = language_control_message_selectOnePagerToTalk_CN;
    language_control_message_selectPagerNoIntercom_text  = language_control_message_selectPagerNoIntercom_CN;
    language_control_message_calledBusy_text     = language_control_message_calledBusy_CN;
    language_control_message_resetZone_text      = language_control_message_resetZone_CN;

    language_control_call_calling_text          =       language_control_call_calling_CN;
    language_control_call_wait_answer_text      =       language_control_call_wait_answer_CN;
    language_control_call_wait_reply_text       =       language_control_call_wait_reply_CN;
    language_control_call_hangUp_text           =       language_control_call_hangUp_CN;
    language_control_call_answer_text           =       language_control_call_answer_CN;
    language_control_call_hangedUp_text         =       language_control_call_hangedUp_CN;
    language_control_call_no_answer_text        =       language_control_call_no_answer_CN;
    language_control_call_codecs_not_support_text      =       language_control_call_codecs_not_support_CN;
    language_control_call_busy_text             =       language_control_call_busy_CN;
    language_control_call_refused_text          =       language_control_call_refused_CN;
    language_control_input_settings_password_text =     language_control_input_settings_password_CN;
}                                                             
static void Language_Config_control_English(void)
{
	language_control_button_controlType_text	    =  language_control_button_controlType_EN;
	language_control_button_zoneOrGroup_text        =  language_control_button_zoneOrGroup_EN;
	language_control_button_selectAll_text          =  language_control_button_selectAll_EN;
    language_control_button_paging_text             =  language_control_button_paging_EN;
    language_control_button_transmit_text           =  language_control_button_transmit_EN;
	language_control_button_intercom_text           =  language_control_button_intercom_EN;
    language_control_button_listen_text	            =  language_control_button_listen_EN;
	language_control_button_NetMusic_text           =  language_control_button_NetMusic_EN;
	language_control_button_udisk_text              =  language_control_button_udisk_EN;
    language_control_button_stop_text               =  language_control_button_stop_EN;
	language_control_button_volume_text             =  language_control_button_volume_EN;
    language_control_button_settings_text           =  language_control_button_settings_EN;

    language_control_button_stop_zone_text          =  language_control_button_stop_zone_EN;
    language_control_button_stop_usb_text           =  language_control_button_stop_usb_EN;
    language_control_button_intercom_volume_text    =  language_control_button_intercom_volume_EN;
    language_control_zone_cnt_text                  =  language_control_zone_cnt_EN;
    language_control_online_zone_cnt_text           =  language_control_online_zone_cnt_EN;

    language_control_zoneInfo_zone_cnt_text         =  language_control_zoneInfo_zone_cnt_EN;
    language_control_zoneInfo_online_zone_cnt_text  =  language_control_zoneInfo_online_zone_cnt_EN;
    language_control_zoneInfo_selected_zone_cnt_text=  language_control_zoneInfo_selected_zone_cnt_EN;
    language_control_groupInfo_group_cnt_text       =  language_control_groupInfo_group_cnt_EN;
    language_control_groupInfo_selected_group_cnt_text =  language_control_groupInfo_selected_group_cnt_EN;
    language_control_pagerInfo_pager_cnt_text       =  language_control_pagerInfo_pager_cnt_EN;
    language_control_pagerInfo_online_pager_cnt_text=  language_control_pagerInfo_online_pager_cnt_EN;
    language_control_pagerInfo_selected_pager_cnt_text=  language_control_pagerInfo_selected_pager_cnt_EN;

    language_control_type_zone_text                 =  language_control_type_zone_EN;
    language_control_type_group_text                =  language_control_type_group_EN;
    language_control_type_pager_text                =  language_control_type_pager_EN;

    language_messagebox_confirm_text                =  language_messagebox_confirm_EN;
    language_messagebox_cancel_text                 =  language_messagebox_cancel_EN;

    language_control_message_cancelAlarm_text       =  language_control_message_cancelAlarm_EN;
    language_control_message_stopPaging_text        =  language_control_message_stopPaging_EN;
    language_control_message_stopIntercom_text      =  language_control_message_stopIntercom_EN;
    language_control_message_logout_text            =  language_control_message_logout_EN;
    language_control_message_selectOnlineZone_text  =  language_control_message_selectOnlineZone_EN;
    language_control_message_selectGroupNoOnlineZone_text = language_control_message_selectGroupNoOnlineZone_EN;
    language_control_message_talkAfterRing_text      = language_control_message_talkAfterRing_EN;
    language_control_message_selectOneIntercomDevice_text    = language_control_message_selectOneIntercomDevice_EN;
    language_control_message_selectZoneNoIntercom_text   = language_control_message_selectZoneNoIntercom_EN;
    language_control_message_selectPager_text    = language_control_message_selectPager_EN;
    language_control_message_selectOnePagerToTalk_text   = language_control_message_selectOnePagerToTalk_EN;
    language_control_message_selectPagerNoIntercom_text  = language_control_message_selectPagerNoIntercom_EN;
    language_control_message_calledBusy_text     = language_control_message_calledBusy_EN;
    language_control_message_resetZone_text      = language_control_message_resetZone_EN;

    language_control_call_calling_text          =       language_control_call_calling_EN;
    language_control_call_wait_answer_text      =       language_control_call_wait_answer_EN;
    language_control_call_wait_reply_text       =       language_control_call_wait_reply_EN;
    language_control_call_hangUp_text           =       language_control_call_hangUp_EN;
    language_control_call_answer_text           =       language_control_call_answer_EN;
    language_control_call_hangedUp_text         =       language_control_call_hangedUp_EN;
    language_control_call_no_answer_text        =       language_control_call_no_answer_EN;
    language_control_call_codecs_not_support_text      =       language_control_call_codecs_not_support_EN;
    language_control_call_busy_text             =       language_control_call_busy_EN;
    language_control_call_refused_text          =       language_control_call_refused_EN;
    language_control_input_settings_password_text =     language_control_input_settings_password_EN;
}
static void Language_Config_control_ZHTW(void)
{
	language_control_button_controlType_text	    =  language_control_button_controlType_TW;
	language_control_button_zoneOrGroup_text        =  language_control_button_zoneOrGroup_TW;
	language_control_button_selectAll_text          =  language_control_button_selectAll_TW;
    language_control_button_paging_text             =  language_control_button_paging_TW;
    language_control_button_transmit_text           =  language_control_button_transmit_TW;
	language_control_button_intercom_text           =  language_control_button_intercom_TW;
    language_control_button_listen_text	            =  language_control_button_listen_TW;
	language_control_button_NetMusic_text           =  language_control_button_NetMusic_TW;
	language_control_button_udisk_text              =  language_control_button_udisk_TW;
    language_control_button_stop_text               =  language_control_button_stop_TW;
	language_control_button_volume_text             =  language_control_button_volume_TW;
    language_control_button_settings_text           =  language_control_button_settings_TW;

    language_control_button_stop_zone_text          =  language_control_button_stop_zone_TW;
    language_control_button_stop_usb_text           =  language_control_button_stop_usb_TW;
    language_control_button_intercom_volume_text    =  language_control_button_intercom_volume_TW;
    language_control_zone_cnt_text                  =  language_control_zone_cnt_TW;
    language_control_online_zone_cnt_text           =  language_control_online_zone_cnt_TW;

    language_control_zoneInfo_zone_cnt_text         =  language_control_zoneInfo_zone_cnt_TW;
    language_control_zoneInfo_online_zone_cnt_text  =  language_control_zoneInfo_online_zone_cnt_TW;
    language_control_zoneInfo_selected_zone_cnt_text=  language_control_zoneInfo_selected_zone_cnt_TW;
    language_control_groupInfo_group_cnt_text       =  language_control_groupInfo_group_cnt_TW;
    language_control_groupInfo_selected_group_cnt_text =  language_control_groupInfo_selected_group_cnt_TW;
    language_control_pagerInfo_pager_cnt_text       =  language_control_pagerInfo_pager_cnt_TW;
    language_control_pagerInfo_online_pager_cnt_text=  language_control_pagerInfo_online_pager_cnt_TW;
    language_control_pagerInfo_selected_pager_cnt_text=  language_control_pagerInfo_selected_pager_cnt_TW;

    language_control_type_zone_text                 =  language_control_type_zone_TW;
    language_control_type_group_text                =  language_control_type_group_TW;
    language_control_type_pager_text                =  language_control_type_pager_TW;

    language_messagebox_confirm_text                =  language_messagebox_confirm_TW;
    language_messagebox_cancel_text                 =  language_messagebox_cancel_TW;

    language_control_message_cancelAlarm_text       =  language_control_message_cancelAlarm_TW;
    language_control_message_stopPaging_text        =  language_control_message_stopPaging_TW;
    language_control_message_stopIntercom_text      =  language_control_message_stopIntercom_TW;
    language_control_message_logout_text            =  language_control_message_logout_TW;
    language_control_message_selectOnlineZone_text  =  language_control_message_selectOnlineZone_TW;
    language_control_message_selectGroupNoOnlineZone_text = language_control_message_selectGroupNoOnlineZone_TW;
    language_control_message_talkAfterRing_text      = language_control_message_talkAfterRing_TW;
    language_control_message_selectOneIntercomDevice_text    = language_control_message_selectOneIntercomDevice_TW;
    language_control_message_selectZoneNoIntercom_text   = language_control_message_selectZoneNoIntercom_TW;
    language_control_message_selectPager_text    = language_control_message_selectPager_TW;
    language_control_message_selectOnePagerToTalk_text   = language_control_message_selectOnePagerToTalk_TW;
    language_control_message_selectPagerNoIntercom_text  = language_control_message_selectPagerNoIntercom_TW;
    language_control_message_calledBusy_text     = language_control_message_calledBusy_TW;
    language_control_message_resetZone_text      = language_control_message_resetZone_TW;

    language_control_call_calling_text          =       language_control_call_calling_TW;
    language_control_call_wait_answer_text      =       language_control_call_wait_answer_TW;
    language_control_call_wait_reply_text       =       language_control_call_wait_reply_TW;
    language_control_call_hangUp_text           =       language_control_call_hangUp_TW;
    language_control_call_answer_text           =       language_control_call_answer_TW;
    language_control_call_hangedUp_text         =       language_control_call_hangedUp_TW;
    language_control_call_no_answer_text        =       language_control_call_no_answer_TW;
    language_control_call_codecs_not_support_text      =       language_control_call_codecs_not_support_TW;
    language_control_call_busy_text             =       language_control_call_busy_TW;
    language_control_call_refused_text          =       language_control_call_refused_TW;
    language_control_input_settings_password_text =     language_control_input_settings_password_TW;
}


static void Language_Config_settings_Chinese(void)
{
    language_settings_title_text            =   language_settings_title_CN;
    language_settings_tab_deviceInfo_text   =   language_settings_tab_deviceInfo_CN;
    language_settings_tab_network_text      =   language_settings_tab_network_CN;
    language_settings_tab_mic_text          =   language_settings_tab_mic_CN;
    language_settings_tab_output_text       =   language_settings_tab_output_CN;
    language_settings_tab_listen_text       =   language_settings_tab_listen_CN;
    language_settings_tab_trigger_text      =   language_settings_tab_trigger_CN;
    language_settings_tab_ring_text         =   language_settings_tab_ring_CN;
    language_settings_tab_display_text      =   language_settings_tab_display_CN;
    language_settings_tab_other_text        =   language_settings_tab_other_CN;

    language_settings_device_version_text       =   language_settings_device_version_CN;
    language_settings_device_networkMode_text   =   language_settings_device_networkMode_CN;
    language_settings_device_connectStatus_text =   language_settings_device_connectStatus_CN;
    language_settings_device_connected_text     =   language_settings_device_connected_CN;
    language_settings_device_disconnected_text  =   language_settings_device_disconnected_CN;
    language_settings_device_serialNumber_text  =   language_settings_device_serialNumber_CN;

    language_settings_device_netCableNotInsert_text = language_settings_device_netCableNotInsert_CN;
    language_settings_device_netCableNotInsertAndUse4G_text = language_settings_device_netCableNotInsertAndUse4G_CN;

    language_settings_network_ip_text           =   language_settings_network_ip_CN;
    language_settings_network_subnetMask_text   =   language_settings_network_subnetMask_CN;
    language_settings_network_gateway_text      =   language_settings_network_gateway_CN;
    language_settings_network_settings_text     =   language_settings_network_settings_CN;
    language_settings_network_settingsOK_text             = language_settings_network_settingsOK_CN;
    language_settings_network_inputCorrectIP_text         = language_settings_network_inputCorrectIP_CN;
    language_settings_network_inputCorrectSubNetMask_text = language_settings_network_inputCorrectSubNetMask_CN;
    language_settings_network_inputCorrectGateWay_text    = language_settings_network_inputCorrectGateWay_CN;

    language_settings_mic_mixAux_text           =   language_settings_mic_mixAux_CN;
    language_settings_mic_sensitivity_text      =   language_settings_mic_sensitivity_CN;

    language_settings_output_listenVol_text     =   language_settings_output_listenVol_CN;
    language_settings_output_intercomVol_text   =   language_settings_output_intercomVol_CN;
    language_settings_output_ringVol_text       =   language_settings_output_ringVol_CN;

    language_settings_ring_pagingRing_text      =   language_settings_ring_pagingRing_CN;

    language_settings_display_brightness_text   =   language_settings_display_brightness_CN;
    language_settings_display_automatic_screenOff_text    = language_settings_display_automatic_screenOff_CN;
    language_settings_display_minute_text       =       language_settings_display_minute_CN;
    language_settings_display_disable_text      =   language_settings_display_disable_CN;
    language_settings_display_language_text     =   language_settings_display_language_CN;

    language_settings_other_zone_paging_volume_text = language_settings_other_zone_paging_volume_CN;
    language_settings_other_paging_timeout_text = language_settings_other_paging_timeout_CN;
    language_settings_other_reset_zones_text = language_settings_other_reset_zones_CN;
    language_settings_other_password_access_text = language_settings_other_password_access_CN;
    language_settings_other_logout_text = language_settings_other_logout_CN;
}
static void Language_Config_settings_English(void)
{
    language_settings_title_text            =   language_settings_title_EN;
    language_settings_tab_deviceInfo_text   =   language_settings_tab_deviceInfo_EN;
    language_settings_tab_network_text      =   language_settings_tab_network_EN;
    language_settings_tab_mic_text          =   language_settings_tab_mic_EN;
    language_settings_tab_output_text       =   language_settings_tab_output_EN;
    language_settings_tab_listen_text       =   language_settings_tab_listen_EN;
    language_settings_tab_trigger_text      =   language_settings_tab_trigger_EN;
    language_settings_tab_ring_text         =   language_settings_tab_ring_EN;
    language_settings_tab_display_text      =   language_settings_tab_display_EN;
    language_settings_tab_other_text        =   language_settings_tab_other_EN;

    language_settings_device_version_text       =   language_settings_device_version_EN;
    language_settings_device_networkMode_text   =   language_settings_device_networkMode_EN;
    language_settings_device_connectStatus_text =   language_settings_device_connectStatus_EN;
    language_settings_device_connected_text     =   language_settings_device_connected_EN;
    language_settings_device_disconnected_text  =   language_settings_device_disconnected_EN;
    language_settings_device_serialNumber_text  =   language_settings_device_serialNumber_EN;

    language_settings_device_netCableNotInsert_text = language_settings_device_netCableNotInsert_EN;
    language_settings_device_netCableNotInsertAndUse4G_text = language_settings_device_netCableNotInsertAndUse4G_EN;

    language_settings_network_ip_text           =   language_settings_network_ip_EN;
    language_settings_network_subnetMask_text   =   language_settings_network_subnetMask_EN;
    language_settings_network_gateway_text      =   language_settings_network_gateway_EN;
    language_settings_network_settings_text     =   language_settings_network_settings_EN;
    language_settings_network_settingsOK_text             = language_settings_network_settingsOK_EN;
    language_settings_network_inputCorrectIP_text         = language_settings_network_inputCorrectIP_EN;
    language_settings_network_inputCorrectSubNetMask_text = language_settings_network_inputCorrectSubNetMask_EN;
    language_settings_network_inputCorrectGateWay_text    = language_settings_network_inputCorrectGateWay_EN;

    language_settings_mic_mixAux_text           =   language_settings_mic_mixAux_EN;
    language_settings_mic_sensitivity_text      =   language_settings_mic_sensitivity_EN;

    language_settings_output_listenVol_text     =   language_settings_output_listenVol_EN;
    language_settings_output_intercomVol_text   =   language_settings_output_intercomVol_EN;
    language_settings_output_ringVol_text       =   language_settings_output_ringVol_EN;

    language_settings_ring_pagingRing_text      =   language_settings_ring_pagingRing_EN;

    language_settings_display_brightness_text   =   language_settings_display_brightness_EN;
    language_settings_display_automatic_screenOff_text    = language_settings_display_automatic_screenOff_EN;
    language_settings_display_minute_text       =       language_settings_display_minute_EN;
    language_settings_display_disable_text      =   language_settings_display_disable_EN;
    language_settings_display_language_text     =   language_settings_display_language_EN;

    language_settings_other_zone_paging_volume_text = language_settings_other_zone_paging_volume_EN;
    language_settings_other_paging_timeout_text = language_settings_other_paging_timeout_EN;
    language_settings_other_reset_zones_text = language_settings_other_reset_zones_EN;
    language_settings_other_password_access_text = language_settings_other_password_access_EN;
    language_settings_other_logout_text = language_settings_other_logout_EN;
}
static void Language_Config_settings_ZHTW(void)
{
    language_settings_title_text            =   language_settings_title_TW;
    language_settings_tab_deviceInfo_text   =   language_settings_tab_deviceInfo_TW;
    language_settings_tab_network_text      =   language_settings_tab_network_TW;
    language_settings_tab_mic_text          =   language_settings_tab_mic_TW;
    language_settings_tab_output_text       =   language_settings_tab_output_TW;
    language_settings_tab_listen_text       =   language_settings_tab_listen_TW;
    language_settings_tab_trigger_text      =   language_settings_tab_trigger_TW;
    language_settings_tab_ring_text         =   language_settings_tab_ring_TW;
    language_settings_tab_display_text      =   language_settings_tab_display_TW;
    language_settings_tab_other_text        =   language_settings_tab_other_TW;

    language_settings_device_version_text       =   language_settings_device_version_TW;
    language_settings_device_networkMode_text   =   language_settings_device_networkMode_TW;
    language_settings_device_connectStatus_text =   language_settings_device_connectStatus_TW;
    language_settings_device_connected_text     =   language_settings_device_connected_TW;
    language_settings_device_disconnected_text  =   language_settings_device_disconnected_TW;
    language_settings_device_serialNumber_text  =   language_settings_device_serialNumber_TW;

    language_settings_device_netCableNotInsert_text = language_settings_device_netCableNotInsert_TW;
    language_settings_device_netCableNotInsertAndUse4G_text = language_settings_device_netCableNotInsertAndUse4G_TW;

    language_settings_network_ip_text           =   language_settings_network_ip_TW;
    language_settings_network_subnetMask_text   =   language_settings_network_subnetMask_TW;
    language_settings_network_gateway_text      =   language_settings_network_gateway_TW;
    language_settings_network_settings_text     =   language_settings_network_settings_TW;
    language_settings_network_settingsOK_text             = language_settings_network_settingsOK_TW;
    language_settings_network_inputCorrectIP_text         = language_settings_network_inputCorrectIP_TW;
    language_settings_network_inputCorrectSubNetMask_text = language_settings_network_inputCorrectSubNetMask_TW;
    language_settings_network_inputCorrectGateWay_text    = language_settings_network_inputCorrectGateWay_TW;

    language_settings_mic_mixAux_text           =   language_settings_mic_mixAux_TW;
    language_settings_mic_sensitivity_text      =   language_settings_mic_sensitivity_TW;

    language_settings_output_listenVol_text     =   language_settings_output_listenVol_TW;
    language_settings_output_intercomVol_text   =   language_settings_output_intercomVol_TW;
    language_settings_output_ringVol_text       =   language_settings_output_ringVol_TW;

    language_settings_ring_pagingRing_text      =   language_settings_ring_pagingRing_TW;

    language_settings_display_brightness_text   =   language_settings_display_brightness_TW;
    language_settings_display_automatic_screenOff_text    = language_settings_display_automatic_screenOff_TW;
    language_settings_display_minute_text       =       language_settings_display_minute_TW;
    language_settings_display_disable_text      =   language_settings_display_disable_TW;
    language_settings_display_language_text     =   language_settings_display_language_TW;

    language_settings_other_zone_paging_volume_text = language_settings_other_zone_paging_volume_TW;
    language_settings_other_paging_timeout_text = language_settings_other_paging_timeout_TW;
    language_settings_other_reset_zones_text = language_settings_other_reset_zones_TW;
    language_settings_other_password_access_text = language_settings_other_password_access_TW;
    language_settings_other_logout_text = language_settings_other_logout_TW;
}


void Language_Config(void)
{
    if(language == CHINESE)
    {
        Language_Config_playsource_Chinese();
        Language_Config_login_Chinese();
        Language_Config_control_Chinese();
        Language_Config_musicList_Chinese();
        init_control_button();
        init_musiclist_button();
        Language_Config_settings_Chinese();
        Init_settingsWin_display();

        if(GetCurrentWin() == WIN_SETTINGS_MAIN)
        {
            settings_win_language_change();
        }
    }
    else if(language == ZHTW)
    {
        Language_Config_playsource_ZHTW();
        Language_Config_login_ZHTW();
        Language_Config_control_ZHTW();
        Language_Config_musicList_ZHTW();
        init_control_button();
        init_musiclist_button();
        Language_Config_settings_ZHTW();
        Init_settingsWin_display();

        if(GetCurrentWin() == WIN_SETTINGS_MAIN)
        {
            settings_win_language_change();
        }
    }
    else if(language == ENGLISH)
    {
        Language_Config_playsource_English();
        Language_Config_login_English();
        Language_Config_control_English();
        Language_Config_musicList_English();
        init_control_button();
        init_musiclist_button();
        Language_Config_settings_English();
        Init_settingsWin_display();

        if(GetCurrentWin() == WIN_SETTINGS_MAIN)
        {
            settings_win_language_change();
        }
    }
}