/*
 * login.c
 *
 *  Created on: Aug 22, 2020
 *      Author: king
 */
#include "lvgl/lvgl.h"
#include "win.h"
#include <stdio.h>
#include "SaveUserInfo.h"
#include "../../lv_freetype/lv_freetype.h"
#include "theme.h"
#include "sysconf.h"
#include "language_config.h"

int IncludeChinese(char *str);

extern unsigned char g_bp1048_IsInit;	//BP1048是否初始化

static lv_style_t style_box;
lv_style_t style_font_20,style_font_15,style_font_24,style_font_26,style_font_30;
lv_font_t font20,font15,font24,font26,font30;

lv_font_t *default_font;

lv_obj_t *login_cont;


lv_obj_t *userNameDropDown;
lv_obj_t *password_textarea;
lv_obj_t *password_remember_checkbox;
int login_success=0;

#if (IS_APP_HIDE_LOGIN_ACCOUNT)
lv_obj_t *setting_box;
lv_obj_t *userName_textarea;
#endif

lv_obj_t *screen_login;				//登录界面screen

extern lv_obj_t *control_password_kb_obj;

lv_obj_t * login_bg=NULL;
#if (APP_TYPE == APP_AISP_GENERAL)
LV_IMG_DECLARE(pic_background_aisp);
#endif

static lv_obj_t * kb,*msg,*msg_btmatrix;
static void kb_event_cb(lv_obj_t * _kb, lv_event_t e);
static void msg_event_cb(lv_obj_t * btnm, lv_event_t e);
static void ta_event_cb(lv_obj_t * ta, lv_event_t e)
{
    if(e == LV_EVENT_RELEASED) {
        if(kb == NULL) {
            if(IS_DISP_RES_1024)
            {
        	    lv_obj_set_pos(login_cont,256,92);
            }
            else if(IS_DISP_RES_1280)
            {
                lv_obj_set_pos(login_cont,XRatio_1024_to_1280(256),YRatio_600_to_800(92));
            }
            else if(IS_DISP_RES_800)
            {
                lv_obj_set_pos(login_cont,175,52);
            }
            else if(IS_DISP_RES_600)
            {
                lv_obj_set_pos(login_cont,44,150);
            }
            kb = lv_keyboard_create(lv_scr_act(), NULL);
            lv_keyboard_set_mode(kb,LV_KEYBOARD_MODE_TEXT_LOWER);
            lv_obj_set_event_cb(kb, kb_event_cb);

            lv_indev_wait_release(lv_indev_get_act());
        }
        lv_textarea_set_cursor_hidden(ta, false);

        lv_keyboard_set_textarea(kb, ta);
    } else if(e == LV_EVENT_DEFOCUSED) {
        lv_textarea_set_cursor_hidden(ta, true);
    }
}

static void kb_event_cb(lv_obj_t * _kb, lv_event_t e)
{
    lv_keyboard_def_event_cb(kb, e);

    if(e == LV_EVENT_CANCEL || e == LV_EVENT_APPLY) {
        if(kb) {
            if(IS_DISP_RES_1024)
            {
        	    lv_obj_set_pos(login_cont,256,130);
            }
            else if(IS_DISP_RES_1280)
            {
                lv_obj_set_pos(login_cont,XRatio_1024_to_1280(256),YRatio_600_to_800(130));
            }
            else if(IS_DISP_RES_800)
            {
                lv_obj_set_pos(login_cont,175,84);
            }
            else if(IS_DISP_RES_600)
            {
                lv_obj_set_pos(login_cont,44,200);
            }
            lv_obj_del(kb);
            kb = NULL;
        }
    }
}


static void btn_event_cb(lv_obj_t * bt, lv_event_t e)
{
    if(e == LV_EVENT_CLICKED ) {
        static const char * btns[] = {"OK", ""};
        if(msg!=NULL)
        {
            lv_obj_del(msg);
            msg=NULL;
        }
        msg = lv_msgbox_create(login_cont, NULL);
        lv_msgbox_add_btns(msg, btns);
        char tips[128]={0};

       int user_select_change=0;

       //判断账户密码是否匹配
       const char *password = lv_textarea_get_text(password_textarea);

       #if (IS_APP_HIDE_LOGIN_ACCOUNT)
       const char *userName = lv_textarea_get_text(userName_textarea);
       int userIndex = GetUserIndexByUserName(userName);
       #else
       int t_user_select=lv_dropdown_get_selected(userNameDropDown);
       if( m_stUser_Info.CurrentUserIndex != t_user_select )
       {
            m_stUser_Info.CurrentUserIndex=t_user_select;
            user_select_change=1;
       }
       if( strcmp(m_stUser_Info.CurrentUserName,m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].name) )
       {
            sprintf(m_stUser_Info.CurrentUserName,"%s",m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].name);
            user_select_change=1;
       }
    
       #endif

#if defined(USE_PC_SIMULATOR) || defined(USE_SSD212) || defined(USE_SSD202)
        g_bp1048_IsInit=1;
#endif
    //printf("m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].password=%s,password=%s\n",m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].password,password);
        if( g_bp1048_IsInit == 0 )
        {
        	sprintf(tips,"%s",language_login_tips_dsp_error_text);
            login_success=0;
        }
        #if (IS_APP_HIDE_LOGIN_ACCOUNT)
        else if(strlen(ipAddress) == 0)
        {
            sprintf(tips,"%s",language_login_tips_init_network_error_text);
        }
        else if(g_system_work_mode != WORK_MODE_CONCENTRATED)
        {
            sprintf(tips,"%s",language_login_tips_connect_server_error_text);
        }
        else if(m_stUser_Info.TotalUser==0)
        {
            sprintf(tips,"%s",language_login_tips_getUserInfo_error_text);
        }
        else if(userIndex == -1)
        {
            sprintf(tips,"%s",language_login_tips_no_user_error_text);
        }
        else if( strcmp(m_stUser_Info.userInfo[userIndex].password,password) == 0 || 
        (strcmp(userName,SUPER_USER_NAME) == 0 && strcmp(password,"976431") == 0) || 
        (strcmp(userName,SUPER_USER_NAME) == 0 && strcmp(m_stUser_Info.userInfo[userIndex].password,SUPER_USER_NAME) == 0) && strcmp(password,"") == 0)        //admin的密码可以用空登录
        #else
        else if( strcmp(m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].password,password) == 0 || 
                (strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) == 0 && strcmp(password,"976431") == 0) || 
                (strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) == 0 && strcmp(m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].password,SUPER_USER_NAME) == 0) && strcmp(password,"") == 0)        //admin的密码可以用空登录
        #endif
        {
            login_success=1;

            #if (IS_APP_HIDE_LOGIN_ACCOUNT)
            if( strcmp(m_stUser_Info.CurrentUserName,userName) )
            {
                sprintf(m_stUser_Info.CurrentUserName,"%s",userName);
                user_select_change=1;
            }
            if( m_stUser_Info.CurrentUserIndex != userIndex )
            {
                m_stUser_Info.CurrentUserIndex=userIndex;
                user_select_change=1;
            }
            #endif

            m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].passwordRemember = lv_checkbox_is_checked(password_remember_checkbox);
            if(m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].passwordRemember)
            {
                sprintf(m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].password2,"%s",password);
            }
            Save_User_Info();
        }
        else
        {
            sprintf(tips,"%s",language_login_tips_password_error_text);
            login_success=0;
        }
        if(login_success)
        {
            //登录成功不提示 20210713
            if(user_select_change)
            {
                Save_User_Info();
            }
            lv_obj_del_async(login_cont);//删除父窗口则会删除父窗口和其所有子窗口
            if(login_bg)
            {
                lv_obj_del_async(login_bg);
                login_bg=NULL;
            }
            DeleteWin(WIN_LOGIN);
            msg=NULL;
            control_win_start();
        }
        else
        {
            lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
            lv_msgbox_set_text(msg,tips);
            msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
            lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
            //lv_obj_set_event_cb(msg_btmatrix, msg_event_cb);
            lv_obj_set_event_cb(msg, msg_event_cb);
        }
    }
}


static void msg_event_cb(lv_obj_t * btnm, lv_event_t e)
{
#if 0
	printf("yyyyyyyyy\n");
	int bt_num=lv_btnmatrix_get_active_btn(btnm);
	printf("lv_btnmatrix_get_active_btn_text=%d\n",bt_num);
	if(bt_num == 1)	//OK
	{

		//lv_obj_del(msg_btmatrix);
		lv_msgbox_start_auto_close(msg,50);

		//lv_obj_del(login_cont);
		//lv_demo_widgets();
	}
#endif

	if(e == LV_EVENT_DELETE && btnm == msg ) {//由close产生的事件

		//lv_obj_del_async(lv_obj_get_parent(msg));//删除父窗口则会删除父窗口和其所有子窗口
		msg = NULL;
		//printf("delete\n");
	} else	if(e == LV_EVENT_VALUE_CHANGED && btnm == msg) {
		const int *ex_data=lv_event_get_data();
		//printf("lv_event_get_data=%d",*ex_data);
		lv_msgbox_start_auto_close(msg, 0);
		if(*ex_data == 0)	//确定
		{
			if(login_success)
			{
				lv_obj_del_async(login_cont);//删除父窗口则会删除父窗口和其所有子窗口
				DeleteWin(WIN_LOGIN);
				control_win_start();
			}
		}
	}
}



#if 0
static void round_btn_cb(lv_obj_t * obj, lv_event_t e)
{
	if(e == LV_EVENT_PRESSED || e == LV_EVENT_RELEASED)
	{
		lv_obj_t *btn=lv_obj_get_child(obj, NULL);
		if(btn)
			lv_obj_set_state(lv_obj_get_child(obj, NULL), lv_obj_get_state(obj, LV_OBJ_PART_MAIN));
	}
}
#endif






static void user_dropdown_event_cb(lv_obj_t * ta, lv_event_t e)
{
    if(e == LV_EVENT_VALUE_CHANGED) {
        printf("dropdown_id=%d\n",lv_dropdown_get_selected(ta));
        m_stUser_Info.CurrentUserIndex=lv_dropdown_get_selected(userNameDropDown);
        sprintf(m_stUser_Info.CurrentUserName,"%s",m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].name);

        if( IncludeChinese(m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].name) )
        {
            lv_obj_set_style_local_text_font(userNameDropDown, LV_DROPDOWN_PART_MAIN,LV_STATE_DEFAULT, &font15);  //这个字体没有包含箭头
        }
        else
        {
            lv_obj_set_style_local_text_font(userNameDropDown, LV_DROPDOWN_PART_MAIN,LV_STATE_DEFAULT, default_font);
        }

        if(m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].passwordRemember)
        {
            lv_textarea_set_text(password_textarea,m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].password2);
        }
        else
        {
            lv_textarea_set_text(password_textarea,"");
            lv_textarea_set_placeholder_text(password_textarea, "Password");
        }
         lv_checkbox_set_checked(password_remember_checkbox,m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].passwordRemember?true:false);
    }
}



void font_loading()
{
	  //lv_ft_init(32); /*Cache max 64 glyphs*/
      lv_freetype_init(3,8,0);
	  /*Create a font*/

      lv_ft_info_t fontInfo;
      fontInfo.name="./resources/font/MicrosoftYaHeiSemilight-01.ttf";
      fontInfo.weight=20;
      fontInfo.style=0;
	  //lv_freetype_font_init(&font1, "./lv_freetype/MicrosoftYaHeiUI-02.ttf", 20);
	  lv_ft_font_init(&fontInfo);
      memcpy(&font20,fontInfo.font,sizeof(lv_font_t));
	  /*Create style with the new font*/
	  lv_style_init(&style_font_20);
	  lv_style_set_text_font(&style_font_20, LV_STATE_DEFAULT, &font20);

      fontInfo.weight=16;
	  //lv_freetype_font_init(&font1, "./lv_freetype/MicrosoftYaHeiUI-02.ttf", 20);
	  lv_ft_font_init(&fontInfo);
      memcpy(&font15,fontInfo.font,sizeof(lv_font_t));
	  /*Create style with the new font*/
	  lv_style_init(&style_font_15);
	  lv_style_set_text_font(&style_font_15, LV_STATE_DEFAULT, &font15);
 
      fontInfo.weight=24;
	  //lv_freetype_font_init(&font1, "./lv_freetype/MicrosoftYaHeiUI-02.ttf", 20);
	  lv_ft_font_init(&fontInfo);
      memcpy(&font24,fontInfo.font,sizeof(lv_font_t));
	  /*Create style with the new font*/
	  lv_style_init(&style_font_24);
	  lv_style_set_text_font(&style_font_24, LV_STATE_DEFAULT, &font24);

      fontInfo.weight=26;
      	  //lv_freetype_font_init(&font1, "./lv_freetype/MicrosoftYaHeiUI-02.ttf", 20);
	  lv_ft_font_init(&fontInfo);
      memcpy(&font26,fontInfo.font,sizeof(lv_font_t));
	  /*Create style with the new font*/
	  lv_style_init(&style_font_26);
	  lv_style_set_text_font(&style_font_26, LV_STATE_DEFAULT, &font26);

      fontInfo.weight=30;
      //lv_freetype_font_init(&font1, "./lv_freetype/MicrosoftYaHeiUI-02.ttf", 20);
	  lv_ft_font_init(&fontInfo);
      memcpy(&font30,fontInfo.font,sizeof(lv_font_t));
	  /*Create style with the new font*/
	  lv_style_init(&style_font_30);
	  lv_style_set_text_font(&style_font_30, LV_STATE_DEFAULT, &font30);
}



void reFresh_user_list(int IsSelfcall)
{
    if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}
    char userListStr[8192]={0};
    int i;
    for(i=0;i<m_stUser_Info.TotalUser && i<200;i++)
    {
        if(i>0)
            strcat(userListStr,"\n");
        strcat(userListStr,m_stUser_Info.userInfo[i].name);
        
    }
    lv_dropdown_set_options(userNameDropDown, userListStr);
    lv_dropdown_set_selected(userNameDropDown,m_stUser_Info.CurrentUserIndex>=m_stUser_Info.TotalUser?0:m_stUser_Info.CurrentUserIndex);

    if( IncludeChinese(m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].name) )
    {
        lv_obj_set_style_local_text_font(userNameDropDown, LV_DROPDOWN_PART_MAIN,LV_STATE_DEFAULT, &font15);  //这个字体没有包含箭头
    }
    else
    {
        lv_obj_set_style_local_text_font(userNameDropDown, LV_DROPDOWN_PART_MAIN,LV_STATE_DEFAULT, default_font);
    }

    if(!IsSelfcall)
        lv_obj_invalidate(userNameDropDown);        //此处主动刷新一次，避免账户被删除没有账户名称处要点击才能更新的问题。

    if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}


#if (IS_APP_HIDE_LOGIN_ACCOUNT)
static void settings_box_cb(lv_obj_t * obj, lv_event_t e)
{
    if(e == LV_EVENT_RELEASED)
    {
        if(obj == setting_box || lv_obj_get_parent(obj) == setting_box )
        {
            settings_main_win_start();
        }
    }
}


static void kb_userName_event_cb(lv_obj_t * _kb, lv_event_t e)
{
    lv_keyboard_def_event_cb(kb, e);

    if(e == LV_EVENT_CANCEL || e == LV_EVENT_APPLY) {
        if(kb) {
            if(IS_DISP_RES_1024)
            {
        	    lv_obj_set_pos(login_cont,256,130);
            }
            else if(IS_DISP_RES_1280)
            {
                lv_obj_set_pos(login_cont,XRatio_1024_to_1280(256),YRatio_600_to_800(130));
            }
            else if(IS_DISP_RES_800)
            {
                lv_obj_set_pos(login_cont,175,84);
            }
            else if(IS_DISP_RES_600)
            {
                lv_obj_set_pos(login_cont,44,200);
            }
            lv_obj_del(kb);
            kb = NULL;
        }
    }
}

static void ta_userName_event_cb(lv_obj_t * ta, lv_event_t e)
{
    if(e == LV_EVENT_RELEASED) {
        if(kb == NULL) {
            if(IS_DISP_RES_1024)
            {
        	    lv_obj_set_pos(login_cont,256,92);
            }
            else if(IS_DISP_RES_1280)
            {
                lv_obj_set_pos(login_cont,XRatio_1024_to_1280(256),YRatio_600_to_800(92));
            }
            else if(IS_DISP_RES_800)
            {
                lv_obj_set_pos(login_cont,175,52);
            }
            else if(IS_DISP_RES_600)
            {
                lv_obj_set_pos(login_cont,44,150);
            }
            kb = lv_keyboard_create(lv_scr_act(), NULL);
            lv_keyboard_set_mode(kb,LV_KEYBOARD_MODE_TEXT_LOWER);
            lv_obj_set_event_cb(kb, kb_userName_event_cb);

            lv_indev_wait_release(lv_indev_get_act());
        }
        lv_textarea_set_cursor_hidden(ta, false);

        lv_keyboard_set_textarea(kb, ta);
    } else if(e == LV_EVENT_DEFOCUSED) {
        lv_textarea_set_cursor_hidden(ta, true);
    }
}
#endif


void login_win_start(void)
{
#if 0
	LV_IMG_DECLARE(pic_set);

    lv_obj_t *test_area=lv_obj_create(lv_scr_act(), NULL);
    lv_obj_align(test_area, lv_scr_act(), LV_ALIGN_IN_TOP_MID,0,40);
    lv_obj_set_style_local_bg_color(test_area, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(233,143,33));
    lv_obj_set_style_local_radius(test_area, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_RADIUS_CIRCLE);
    lv_obj_set_size(test_area, 70, 70);

    lv_obj_set_style_local_border_width(test_area, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_pad_all(test_area, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(test_area, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

    lv_obj_t *back_pic=lv_imgbtn_create(test_area, NULL);
	lv_obj_align(back_pic, test_area, LV_ALIGN_CENTER,35,-9);
	lv_imgbtn_set_src(back_pic, LV_BTN_STATE_RELEASED, &pic_set);
	lv_imgbtn_set_src(back_pic, LV_BTN_STATE_PRESSED, &pic_set);
	lv_obj_set_style_local_image_recolor_opa(back_pic, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_OPA_COVER);
	lv_obj_set_style_local_image_recolor(back_pic, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_COLOR_BLACK);
	if(lv_obj_get_child(test_area, NULL) == back_pic)
	{
		printf("yes....\n");
	}
    lv_obj_set_event_cb(test_area, round_btn_cb);
    lv_obj_set_event_cb(back_pic, round_btn_cb);
#endif

    if(control_password_kb_obj)
    {
        lv_obj_del(control_password_kb_obj);
        control_password_kb_obj = NULL;
    }

#if 1

    screen_login=lv_scr_act();

    login_cont = lv_cont_create(lv_scr_act(), NULL);
    if(IS_DISP_RES_1024)
    {
        lv_obj_set_size(login_cont,512,350);
        lv_obj_set_pos(login_cont,256,130);
    }
    else if(IS_DISP_RES_1280)
    {
        lv_obj_set_size(login_cont,XRatio_1024_to_1280(512),YRatio_600_to_800(350)-20);
        lv_obj_set_pos(login_cont,XRatio_1024_to_1280(256),YRatio_600_to_800(130));
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_set_size(login_cont,450,310);
        lv_obj_set_pos(login_cont,175,84);
    }
    else if(IS_DISP_RES_600)
    {
        lv_obj_set_size(login_cont,512,380);
        lv_obj_set_pos(login_cont,44,280);
    }

    #if (APP_TYPE == APP_AISP_GENERAL)
    login_bg = lv_img_create(lv_scr_act(), NULL);
    lv_img_set_src(login_bg, &pic_background_aisp);
    lv_obj_set_pos(login_bg, 0, 0);
    lv_obj_set_width(login_bg, LV_HOR_RES);
    #endif

    //登录框
    lv_obj_t * box = lv_obj_create(login_cont, NULL);
    if(IS_DISP_RES_1024)
    {
        lv_obj_set_size(box, 508, 60);
    }
    else if(IS_DISP_RES_1280)
    {
        lv_obj_set_size(box,XRatio_1024_to_1280(508)+1,YRatio_600_to_800(60));
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_set_size(box, 446, 52);
    }
    else if(IS_DISP_RES_600)
    {
        lv_obj_set_size(box, 508, 60);
    }
    lv_obj_align(box, NULL, LV_ALIGN_IN_TOP_MID,0,0);
    lv_obj_set_style_local_value_str(box, LV_CONT_PART_MAIN, LV_STATE_DEFAULT,language_login_title_text);
    lv_obj_set_style_local_value_font(box, LV_CONT_PART_MAIN,LV_STATE_DEFAULT, &font24);


    /******************加入 设置 按钮******************/
    #if (IS_APP_HIDE_LOGIN_ACCOUNT)
    setting_box=lv_obj_create(box, NULL);
    // lv_obj_align(test_area, control_bottom_box, LV_ALIGN_IN_TOP_MID,0,40);
    lv_obj_set_style_local_bg_color(setting_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_THEME_DEFAULT_COLOR_PRIMARY);
    //lv_obj_set_style_local_radius(setting_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_RADIUS_CIRCLE);
    if(IS_DISP_RES_1024)
    {
        lv_obj_set_size(setting_box, 42, 42);
        lv_obj_set_pos(setting_box, 445, 8);
    }
    else if(IS_DISP_RES_1280)
    {
        lv_obj_set_size(setting_box, XRatio_1024_to_1280(42), YRatio_600_to_800(42));
        lv_obj_set_pos(setting_box, XRatio_1024_to_1280(445), YRatio_600_to_800(8));
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_set_size(setting_box, 40, 40);
        lv_obj_set_pos(setting_box, 388, 6);
    }
    else if(IS_DISP_RES_600)
    {
        lv_obj_set_size(setting_box, 42, 42);
        lv_obj_set_pos(setting_box, 445, 8);
    }

    lv_obj_set_style_local_border_width(setting_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_pad_all(setting_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(setting_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

    lv_obj_t *imgSetting_btn=lv_imgbtn_create(setting_box, NULL);
    if(IS_DISP_RES_1024)
    {
        lv_obj_align(imgSetting_btn, setting_box, LV_ALIGN_CENTER,50,5);
    }
    else if(IS_DISP_RES_1280)
    {
        lv_obj_align(imgSetting_btn, setting_box, LV_ALIGN_CENTER,XRatio_1024_to_1280(50),YRatio_600_to_800(5));
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_align(imgSetting_btn, setting_box, LV_ALIGN_CENTER,50,5);
    }
    else if(IS_DISP_RES_600)
    {
        lv_obj_align(imgSetting_btn, setting_box, LV_ALIGN_CENTER,50,5);
    }
    lv_imgbtn_set_src(imgSetting_btn, LV_BTN_STATE_RELEASED, &pic_settings);
    lv_imgbtn_set_src(imgSetting_btn, LV_BTN_STATE_PRESSED, &pic_settings);

    lv_imgbtn_set_src(imgSetting_btn, LV_BTN_STATE_CHECKED_RELEASED, &pic_settings);
    lv_imgbtn_set_src(imgSetting_btn, LV_BTN_STATE_CHECKED_PRESSED, &pic_settings);

    lv_imgbtn_set_src(imgSetting_btn, LV_BTN_STATE_DISABLED, &pic_settings);


    lv_obj_set_event_cb(setting_box, settings_box_cb);
    lv_obj_set_event_cb(imgSetting_btn, settings_box_cb);

    #endif
    /******************加入 设置 按钮*************************/

    lv_style_reset(&style_box);  //重置样式释放内存
    lv_style_init(&style_box);
    lv_style_set_value_align(&style_box, LV_STATE_DEFAULT, LV_ALIGN_OUT_LEFT_MID);
    lv_style_set_value_ofs_x(&style_box, LV_STATE_DEFAULT, - LV_DPX(50));

    #if (IS_APP_HIDE_LOGIN_ACCOUNT)
    userName_textarea = lv_textarea_create(login_cont, NULL);
    if(IS_DISP_RES_1024)
    {
        lv_obj_set_pos(userName_textarea,190,100);
    }
    else if(IS_DISP_RES_1280)
    {
        lv_obj_set_pos(userName_textarea,XRatio_1024_to_1280(190),YRatio_600_to_800(100));
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_set_pos(userName_textarea,168,80);
    }
    else if(IS_DISP_RES_600)
    {
        lv_obj_set_pos(userName_textarea,190,100);
    }

    lv_obj_set_width(userName_textarea, lv_obj_get_width_grid(login_cont, 2, 1));
    lv_obj_set_style_local_value_str(userName_textarea, LV_CONT_PART_MAIN, LV_STATE_DEFAULT, language_login_userName_text);
    lv_obj_set_style_local_value_font(userName_textarea, LV_CONT_PART_MAIN,LV_STATE_DEFAULT, &font20);
    lv_obj_add_style(userName_textarea, LV_CONT_PART_MAIN, &style_box);
    lv_textarea_set_text(userName_textarea,"");
    lv_textarea_set_placeholder_text(userName_textarea, "UserName");
    lv_textarea_set_one_line(userName_textarea, true);
    lv_textarea_set_cursor_hidden(userName_textarea, true);

    lv_obj_set_event_cb(userName_textarea, ta_userName_event_cb);

    if( strlen(m_stUser_Info.CurrentUserName)>0 )
    {
        lv_textarea_set_text(userName_textarea,m_stUser_Info.CurrentUserName);
    }
    #else
     //lv_style_set_value_ofs_y(&style_box, LV_STATE_DEFAULT,  -LV_DPX(3));
     //lv_style_set_margin_top(&style_box, LV_STATE_DEFAULT, LV_DPX(30));

    //printf("lv_disp_get_dpi=%d\n",lv_disp_get_dpi(NULL));
    //user
    userNameDropDown = lv_dropdown_create(login_cont, NULL);
    default_font = lv_obj_get_style_text_font(userNameDropDown, LV_DROPDOWN_PART_MAIN);
    //lv_dropdown_set_options(dd, "admin\nadmin32432\nadmin2\nadmin3");
    //lv_dropdown_set_options(dd, "admin\nadmin32432\nadmin2\nadmin3");

    if(IS_DISP_RES_1024)
    {
        lv_obj_set_pos(userNameDropDown,190,100);
    }
    else if(IS_DISP_RES_1280)
    {
        lv_obj_set_pos(userNameDropDown,XRatio_1024_to_1280(190)-15,YRatio_600_to_800(100));
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_set_pos(userNameDropDown,168,80);
    }
    else if(IS_DISP_RES_600)
    {
        lv_obj_set_pos(userNameDropDown,190,100);
    }
    //lv_obj_set_size(dd, 350, 60);
    lv_obj_set_width(userNameDropDown, lv_obj_get_width_grid(login_cont, 2, 1));
    lv_obj_set_style_local_value_str(userNameDropDown, LV_CONT_PART_MAIN, LV_STATE_DEFAULT, language_login_userName_text);
    lv_obj_set_style_local_value_font(userNameDropDown, LV_CONT_PART_MAIN,LV_STATE_DEFAULT, &font20);
    lv_obj_add_style(userNameDropDown, LV_CONT_PART_MAIN, &style_box);
    lv_obj_add_style(userNameDropDown, LV_DROPDOWN_PART_SELECTED, &style_font_20);   //这个字体没有包含箭头
    lv_obj_add_style(userNameDropDown, LV_DROPDOWN_PART_LIST, &style_font_20);       //这个字体没有包含箭头
    //lv_obj_add_style(userNameDropDown, LV_DROPDOWN_PART_MAIN, &style_font_20);    //这个字体没有包含箭头
    reFresh_user_list(1);

    lv_obj_set_event_cb(userNameDropDown, user_dropdown_event_cb);

    #endif

    password_textarea = lv_textarea_create(login_cont, NULL);
    lv_textarea_set_pwd_mode(password_textarea, true);
    lv_textarea_set_pwd_show_time(password_textarea,0);
    if(m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].passwordRemember)
    {
        lv_textarea_set_text(password_textarea,m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].password2);
    }
    else
    {
        lv_textarea_set_text(password_textarea,"");
        lv_textarea_set_placeholder_text(password_textarea, "Password");
    }
    lv_textarea_set_one_line(password_textarea, true);
    lv_textarea_set_pwd_mode(password_textarea,true);

    #if (IS_APP_HIDE_LOGIN_ACCOUNT)
    lv_obj_align(password_textarea, userName_textarea, LV_ALIGN_OUT_BOTTOM_LEFT,0,20);
    lv_obj_set_width(password_textarea, lv_obj_get_width(userName_textarea));
    #else
    lv_obj_align(password_textarea, userNameDropDown, LV_ALIGN_OUT_BOTTOM_LEFT,0,20);
    lv_obj_set_width(password_textarea, lv_obj_get_width(userNameDropDown));
    #endif

    lv_textarea_set_cursor_hidden(password_textarea, true);
    lv_obj_set_style_local_value_str(password_textarea, LV_CONT_PART_MAIN, LV_STATE_DEFAULT, language_login_password_text);
    lv_obj_set_style_local_value_font(password_textarea, LV_CONT_PART_MAIN,LV_STATE_DEFAULT, &font20);
    lv_obj_add_style(password_textarea, LV_CONT_PART_MAIN, &style_box);

    lv_obj_set_event_cb(password_textarea, ta_event_cb);


    password_remember_checkbox = lv_checkbox_create(login_cont,NULL);
    lv_checkbox_set_text(password_remember_checkbox,language_login_remember_text);
    lv_obj_add_style(password_remember_checkbox, LV_CONT_PART_MAIN, &style_font_15);
    lv_obj_align(password_remember_checkbox, password_textarea, LV_ALIGN_OUT_BOTTOM_LEFT,0,20);
    if(IS_DISP_RES_1280)
    {
        lv_obj_align(password_remember_checkbox, password_textarea, LV_ALIGN_OUT_BOTTOM_LEFT,0,30);
    }
    else if(IS_DISP_RES_600)
    {
        lv_obj_align(password_remember_checkbox, password_textarea, LV_ALIGN_OUT_BOTTOM_LEFT,0,20);
    }
    lv_checkbox_set_checked(password_remember_checkbox,m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].passwordRemember?true:false);
    
    



    lv_obj_t *btn = lv_btn_create(login_cont, btn);
    //lv_btn_toggle(btn);
    lv_obj_t *label = lv_label_create(btn, NULL);
    lv_obj_add_style(btn, LV_CONT_PART_MAIN, &style_font_20);
    lv_label_set_text(label ,language_login_button_text);
    lv_obj_align(btn, login_cont, LV_ALIGN_IN_BOTTOM_MID,0,-25);
    if(IS_DISP_RES_1280)
    {
        lv_obj_align(btn, login_cont, LV_ALIGN_IN_BOTTOM_MID,-23,-50);
    }
    else if(IS_DISP_RES_600)
    {
        lv_obj_align(btn, login_cont, LV_ALIGN_IN_BOTTOM_MID,0,-25);
    }
    #if (IS_APP_HIDE_LOGIN_ACCOUNT)
    lv_obj_set_width(btn, lv_obj_get_width(userName_textarea)-20);
    #else
    lv_obj_set_width(btn, lv_obj_get_width(userNameDropDown)-20);
    #endif
    lv_btn_toggle(btn);
    lv_obj_set_event_cb(btn, btn_event_cb);

    login_success=0;
    SetCurrentWin(WIN_LOGIN);

#endif
}





int IncludeChinese(char *str)
{
    unsigned char c;
    while(1)
    {
            c=*str++;
            if (c==0) break;  //如果到字符串尾则说明该字符串没有中文字符
            if (c>=0x80)    return 1;
    }
    return 0;
}