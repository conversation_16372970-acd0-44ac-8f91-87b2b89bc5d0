/*
 * stMusicList.c
 *
 *  Created on: Aug 31, 2020
 *      Author: king
 */
#include <stdio.h>
#include "win.h"
#include "theme.h"
#include "sysconf.h"
#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
#include "MusicFile.h"
#include "MusicFile_Alter.h"
#include "udiskPlay.h".
#include "../mediaPlayer/ffplayer.h"

#define stUsbInfo 	udiskInfo
#define stMusicList stUsbInfo.musicList

lv_obj_t *udisk_musiclist_main_cont,*udisk_musiclist_all_count;			//空间界面主体部分
lv_obj_t *udisk_dir_list,*udisk_songlist;

lv_obj_t *udisk_dir_btn[MUSICUSBDIRMAX],*udisk_song_btn[MUSICUSBFILEMAX];

extern lv_obj_t *group_enter_btn;//进入扩展按钮

lv_obj_t *udisk_msg_confirm_upload;	//确认上传对话框

LV_IMG_DECLARE(png_folder);


lv_obj_t *uploadSong_main_cont;			//上传主容器
lv_obj_t *uploadSong_progress_bar;		//上传进度条
lv_obj_t *uploadSong_label_status;		//上传状态
lv_obj_t *uploadSong_label_progress;		//上传进度
lv_obj_t *uploadSong_bt_stop;			//上传停止/关闭按钮


void udisk_current_playId_change(int IsSelfcall,int new_playId);
void create_uploadSong_cont(int IsSelfcall);
void ready_close_uploadSong_dialog(int IsSelfcall,int Isdelay,int delayTime);

static void udisk_Win_PlayMode_change(int playMode,int IsSelfcall)
{
	if(GetCurrentWin() != WIN_UDISK)
		return;
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}

	lv_obj_t *btn=lv_obj_get_child(lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAYMODE].obj, NULL);
	lv_imgbtn_set_src(btn, LV_BTN_STATE_RELEASED, img_Udisk_PlayMode[playMode-1]);
	lv_imgbtn_set_src(btn, LV_BTN_STATE_PRESSED, img_Udisk_PlayMode[playMode-1]);
	lv_label_set_text(lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAYMODE].label,text_Udisk_PlayMode[playMode-1]);

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}



void udisk_back_to_control_win(int IsSelfcall)
{
	//如果是正在上传状态，先通知服务器停止，再关闭
	if(songUploadInfo.uploadStatus == UPLOAD_STATUS_START)
	{
		//todo 通知服务器，服务器收到后删除临时歌曲文件	可以不做此操作，因为songUpload.c上传线程失败后已经处理了
		ready_close_uploadSong_dialog(1,0,0);
	}
	else	//其他情况，直接关闭即可
	{
		ready_close_uploadSong_dialog(1,0,0);
	}

	lv_obj_del(udisk_musiclist_all_count);
	DeleteWin(WIN_UDISK);
	//清空udisk_dir_btn,udisk_song_btn数组
	memset(udisk_dir_btn,0,sizeof(udisk_dir_btn));
	memset(udisk_song_btn,0,sizeof(udisk_song_btn));
	control_win_update(control_page_type,IsSelfcall);
	refresh_usbPlay(IsSelfcall);
	//如果不是在图形线程调用，那么需要刷新一次窗体，否则可能图形异常。注意！！！
}


//选择歌曲播放
static void song_list_btn_event_cb(lv_obj_t * obj, lv_event_t e)
{
	int i;
    if(e == LV_EVENT_CLICKED)
    {
    	lv_obj_set_state(obj, LV_STATE_CHECKED);
    	lv_obj_t * btn = lv_list_get_btn_selected(udisk_songlist);
    	if(!btn)
    		return;
      	int song_index=lv_list_get_btn_index(udisk_songlist,btn);

      	btn = lv_list_get_btn_selected(udisk_dir_list);
    	if(!btn)
    		return;
      	int dir_index=lv_list_get_btn_index(udisk_dir_list,btn);
		if(dir_index == -1)
			return;
		printf("song_index=%d,realDirIndex=%d\n",song_index,dir_index);
		#if 0
		#if USE_ASM9260
      	BP1048_Send_Set_Usb_Play_File(stMusicList.SongDir[dir_index].musicfile[song_index].realId);
		#else if USE_SSD212
		udisk_play(dir_index,song_index);
		#endif
		#endif
		//20230616 龙之音V1版本选择歌曲不自动播放
		//start_udiskPlay(1,dir_index,song_index);
    }
}

//选择目录
static void udisk_dir_list_btn_event_cb(lv_obj_t * obj, lv_event_t e)
{
	int i;
    if(e == LV_EVENT_CLICKED)
    {
    	lv_obj_set_state(obj, LV_STATE_CHECKED);
    	lv_obj_t * btn = lv_list_get_btn_selected(udisk_dir_list);
    	if(!btn)
    		return;
      	int dir_index=lv_list_get_btn_index(udisk_dir_list,btn);
		printf("des_index=%d\n",dir_index);
		if(dir_index == -1)
			return;
    	lv_list_clean(udisk_songlist);
		memset(udisk_song_btn,0,sizeof(udisk_song_btn));
        for(i = 0; i<stMusicList.SongDir[dir_index].nFileNum; i++) {
        	char songName[256]={0};
        	sprintf(songName,"%d. %s",i+1,stMusicList.SongDir[dir_index].musicfile[i].cName);
        	udisk_song_btn[i] = lv_list_add_btn(udisk_songlist, NULL, songName);
        	lv_obj_set_event_cb(udisk_song_btn[i], song_list_btn_event_cb);
        	lv_obj_set_style_local_pad_top(udisk_song_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
        	lv_obj_set_style_local_pad_bottom(udisk_song_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
			lv_obj_t *btn_label=lv_list_get_btn_label(udisk_song_btn[i]);
			if(btn_label)
				lv_label_set_long_mode(btn_label,LV_LABEL_LONG_DOT);
        }
		if(stUsbInfo.current_playFileId!=0)
		{
			int dir_index_new = udisk_get_DirId_pos(stUsbInfo.current_playFileId);
			if(dir_index_new == dir_index)
			{
				int song_index_new = udisk_get_fileId_pos(stUsbInfo.current_playFileId);
				//一定要foucs新的btn，否则lv_list_get_btn_selected还是原来的btn
				//聚焦在新的上
				if(udisk_song_btn[song_index_new])
				{
					lv_list_focus_btn(udisk_songlist,udisk_song_btn[song_index_new]);
					//设置新的btn为选中状态
					lv_obj_set_state(udisk_song_btn[song_index_new], LV_STATE_CHECKED);
				}
			}
		}
    }
}


static void udisk_msg_box_event(lv_obj_t * btnm, lv_event_t e)
{
	if(e == LV_EVENT_DELETE) {//由close产生的事件

		//lv_obj_del_async(lv_obj_get_parent(msg));//删除父窗口则会删除父窗口和其所有子窗口
		//printf("delete\n");
	} else	if(e == LV_EVENT_VALUE_CHANGED) {
		const int *ex_data=lv_event_get_data();
		//printf("lv_event_get_data=%d",*ex_data);
		lv_msgbox_start_auto_close(btnm, 0);
		if(btnm == udisk_msg_confirm_upload)
		{
			if(*ex_data == 1)	//确定
			{
				lv_obj_del(btnm);
				//请求上传歌曲
				Send_host_request_upload_song(songUploadInfo.uploadSongName,songUploadInfo.uploadListID,songUploadInfo.uploadSongSize,songUploadInfo.uploadSongCompressSize);
			}
		}
	}
}


static void round_btn_cb(lv_obj_t * obj, lv_event_t e)
{
	if(e == LV_EVENT_PRESSED || e == LV_EVENT_RELEASED || e == LV_EVENT_PRESS_LOST)
	{
		lv_obj_t *btn=lv_obj_get_child(obj, NULL);
		if(btn)
			lv_obj_set_state(btn, lv_obj_get_state(obj, LV_OBJ_PART_MAIN));
	}

    if(e == LV_EVENT_RELEASED)
	{
		if(uploadSong_main_cont)
		{
			return;
		}

		int dir_index=-1,song_index=-1;
		lv_obj_t * temp_udisk_dir_btn = lv_list_get_btn_selected(udisk_dir_list);
		lv_obj_t * temp_udisk_song_btn = lv_list_get_btn_selected(udisk_songlist);
		if(temp_udisk_dir_btn != NULL)
		{
			dir_index=lv_list_get_btn_index(udisk_dir_list,temp_udisk_dir_btn);
			if(dir_index == -1)
				return;
		}
		if(temp_udisk_song_btn != NULL )
		{
			song_index=lv_list_get_btn_index(udisk_songlist,temp_udisk_song_btn);
		}
		printf("dir_index=%d,song_index=%d\n",dir_index,song_index);
		if(obj == lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PREPLAY].obj || lv_obj_get_parent(obj) == lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PREPLAY].obj)
		{
			if(dir_index >=0)
			{	
				int temp_song_index=song_index;
				song_index=song_index-1>=0?song_index-1:stMusicList.SongDir[dir_index].nFileNum-1;
				if(dir_index >=0 && song_index>=0 && dir_index<stMusicList.DirNum && song_index<stMusicList.SongDir[dir_index].nFileNum)
				{
					//取消原来btn的选中状态
					if(temp_song_index>=0)
					{
						lv_obj_set_state(udisk_song_btn[temp_song_index], LV_STATE_DEFAULT);
					}
					//一定要foucs新的btn，否则lv_list_get_btn_selected还是原来的btn
					lv_list_focus_btn(udisk_songlist,udisk_song_btn[song_index]);
					//设置新的btn为选中状态
					lv_obj_set_state(udisk_song_btn[song_index], LV_STATE_CHECKED);
					#if 0
					#if USE_ASM9260
					BP1048_Send_Set_Usb_Play_File(stMusicList.SongDir[dir_index].musicfile[song_index].realId);
					#endif
					#endif
					start_udiskPlay(1,dir_index,song_index);
				}
			}
			else
			{
				//提示用户选择歌曲列表
				static const char * btns[] = {"OK", ""};
				lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
				lv_msgbox_add_btns(msg, btns);
				lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
				lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
				lv_msgbox_set_text(msg,language_musiclist_selectList_text);
			}
			
		}
		else if(obj == lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAY].obj || lv_obj_get_parent(obj) == lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAY].obj)
		{
			printf("stUsbInfo.playStatus=%d\n",stUsbInfo.playStatus);
			if(stUsbInfo.playStatus == BP1048_MUSIC_STATUS_STOP)
			{
				if(dir_index >=0 && song_index>=0 && dir_index<stMusicList.DirNum && song_index<stMusicList.SongDir[dir_index].nFileNum)
				{
					#if 0
					#if USE_ASM9260
					BP1048_Send_Set_Usb_Play_File(stMusicList.SongDir[dir_index].musicfile[song_index].realId);
					#endif
					#endif
					start_udiskPlay(1,dir_index,song_index);
				}
				else
				{
					//提示用户选择歌曲列表
					static const char * btns[] = {"OK", ""};
					lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
					lv_msgbox_add_btns(msg, btns);
					lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
					lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
					lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
					lv_msgbox_set_text(msg,language_musiclist_selectSong_text);
				}
			}
			else
			{
				pauseOrResume_udiskPlay(1);
			}
		}
		else if(obj == lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_STOP].obj || lv_obj_get_parent(obj) == lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_STOP].obj)
		{
			//停止播放
			#if 0
			#if USE_ASM9260
			Bp1048_Send_Set_Usb_PlayStatus(BP1048_MUSIC_STATUS_STOP);
			#elif USE_SSD212
			udisk_stop();
			#endif
			#endif
			stop_udiskPlay(1);
		}
		else if(obj == lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_NEXTPLAY].obj || lv_obj_get_parent(obj) == lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_NEXTPLAY].obj)
		{
			if(dir_index >=0)
			{
				int temp_song_index=song_index;
				song_index=song_index+1>=stMusicList.SongDir[dir_index].nFileNum?0:song_index+1;
				if(dir_index >=0 && song_index>=0 && dir_index<stMusicList.DirNum && song_index<stMusicList.SongDir[dir_index].nFileNum)
				{
					if(temp_song_index>=0)
					{
						lv_obj_set_state(udisk_song_btn[temp_song_index], LV_STATE_DEFAULT);
					}
					lv_list_focus_btn(udisk_songlist,udisk_song_btn[song_index]);
					lv_obj_set_state(udisk_song_btn[song_index], LV_STATE_CHECKED);
					#if 0
					#if USE_ASM9260
					BP1048_Send_Set_Usb_Play_File(stMusicList.SongDir[dir_index].musicfile[song_index].realId);
					#endif
					#endif
					start_udiskPlay(1,dir_index,song_index);
				}
			}
			else
			{
				//提示用户选择歌曲列表
				static const char * btns[] = {"OK", ""};
				lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
				lv_msgbox_add_btns(msg, btns);
				lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
				lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
				lv_msgbox_set_text(msg,language_musiclist_selectList_text);
			}
			
		}
		else if(obj == lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAYMODE].obj || lv_obj_get_parent(obj) == lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAYMODE].obj)
		{
			if(stUsbInfo.playMode == BP1048_UDISK_PLAY_MODE_SINGLE_LOOP)
			{
				stUsbInfo.playMode = BP1048_UDISK_PLAY_MODE_LIST_LOOP;
			}
			else if(stUsbInfo.playMode == BP1048_UDISK_PLAY_MODE_LIST_LOOP)
			{
				stUsbInfo.playMode = BP1048_UDISK_PLAY_MODE_SINGLE_LOOP;
			}
			save_sysconf("MusicPlay","Udisk_PlayMode");
			udisk_Win_PlayMode_change(stUsbInfo.playMode,1);
			BP1048_Send_Set_Usb_Play_Mode(stUsbInfo.playMode);
		}
		else if(obj == lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_UPLOAD].obj || lv_obj_get_parent(obj) == lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_UPLOAD].obj)
		{
			if(dir_index >=0 && song_index>=0 && dir_index<stMusicList.DirNum && song_index<stMusicList.SongDir[dir_index].nFileNum)
			{
				//预上传，发送指令给服务器
				//判断有没有获取到剩余空间,如果没有，提示用户不支持上传
				#if 1
				if(songUploadInfo.storageCapacity == 0)
				{
					//提示用户选择歌曲列表
					static const char * btns[] = {"OK", ""};
					lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
					lv_msgbox_add_btns(msg, btns);
					lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
					lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
					lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
					lv_msgbox_set_text(msg,language_musiclist_notSupported_upload_song_text);
				}else
				#endif
				{
					//组合成播放路径
					char filePath[256]={0};
					sprintf(filePath,"%s/%s",udiskInfo.musicList.SongDir[dir_index].cDirName,udiskInfo.musicList.SongDir[dir_index].musicfile[song_index].cName);

					//预判该首歌曲的存储空间,使用ffplay获取
					unsigned long fileSize=Get_File_Size(filePath);
					int bitRate=ffmpeg_get_media_song_bitRate(filePath);
					unsigned long compress_fileSize=fileSize;
					if(bitRate>songUploadInfo.compress_bitrate)
					{
						double div=bitRate*1.0/songUploadInfo.compress_bitrate;
						compress_fileSize=fileSize/div;
					}
					printf("fileSize=%ld,bitRate=%d,compress_fileSize=%ld\n",fileSize,bitRate,compress_fileSize);

					//判断空间是否足够？
					if(songUploadInfo.storageSpaceRemaining<compress_fileSize)
					{
						//提示用户空间不足
						static const char * btns[] = {"OK", ""};
						lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
						lv_msgbox_add_btns(msg, btns);
						lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
						lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
						lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
						lv_msgbox_set_text(msg,language_musiclist_insufficient_storage_text);
						return;
					}
					
					//存储相关信息
					sprintf(songUploadInfo.uploadSongName,udiskInfo.musicList.SongDir[dir_index].musicfile[song_index].cName);
					sprintf(songUploadInfo.uploadListID,"");
					sprintf(songUploadInfo.uploadSongPath,"%s/%s",udiskInfo.musicList.SongDir[dir_index].cDirName,udiskInfo.musicList.SongDir[dir_index].musicfile[song_index].cName);
					songUploadInfo.uploadSongSize=fileSize;
					songUploadInfo.uploadSongCompressSize=compress_fileSize;
					
					//确认上传对话框
					static char * btns[] = {"Cancel", "OK",""};
					btns[0] = language_messagebox_cancel_text;
					btns[1] = language_messagebox_confirm_text;
					udisk_msg_confirm_upload = lv_msgbox_create(lv_scr_act(), NULL);
					lv_msgbox_add_btns(udisk_msg_confirm_upload, btns);
					lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(udisk_msg_confirm_upload);
					//lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
					lv_obj_add_style(udisk_msg_confirm_upload, LV_OBJ_PART_MAIN, &style_font_20);
					lv_obj_set_event_cb(udisk_msg_confirm_upload, udisk_msg_box_event);
					char confirm_upload_text[256]={0};
					sprintf(confirm_upload_text,"%s \"%s\" %s?",language_musiclist_confirm_upload_head_text,udiskInfo.musicList.SongDir[dir_index].musicfile[song_index].cName,
							language_musiclist_confirm_upload_tail_text);
					lv_msgbox_set_text(udisk_msg_confirm_upload,confirm_upload_text);
				}
			}
			else
			{
				//提示用户选择歌曲列表
				static const char * btns[] = {"OK", ""};
				lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
				lv_msgbox_add_btns(msg, btns);
				lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
				lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
				lv_msgbox_set_text(msg,language_musiclist_selectSong_text);
			}
		}
	}
}


static void uploadSongStop_event_cb(lv_obj_t * obj, lv_event_t e)
{
	if(e == LV_EVENT_PRESSED)
	{

	}
	else if(e == LV_EVENT_RELEASED)
	{
		//如果是正在上传状态，先通知服务器停止，再关闭
		if(songUploadInfo.uploadStatus == UPLOAD_STATUS_START)
		{
			//todo 通知服务器，服务器收到后删除临时歌曲文件
			ready_close_uploadSong_dialog(1,0,0);
		}
		else	//其他情况，直接关闭即可
		{
			ready_close_uploadSong_dialog(1,0,0);
		}

		songUploadInfo.uploadStatus = UPLOAD_STATUS_FREE;       //空闲状态
    	songUploadInfo.uploadProgress = 0;        //上传进度归0
	}
}


void close_uploadSong_dialog_task(lv_task_t *param) {

	if(uploadSong_main_cont)
	{
		lv_obj_del(uploadSong_main_cont);
		uploadSong_main_cont=NULL;
		printf("close uploadSong dialog ok...\n");

		songUploadInfo.uploadStatus = UPLOAD_STATUS_FREE;       //空闲状态
    	songUploadInfo.uploadProgress = 0;        //上传进度归0
	}
	lv_task_del(param);
}

void ready_close_uploadSong_dialog(int IsSelfcall,int Isdelay,int delayTime)
{
	printf("ready_close_uploadSong_dialog...\n");
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}
	if(uploadSong_main_cont)
	{
		if(Isdelay)
		{
			lv_task_create(close_uploadSong_dialog_task, delayTime, LV_TASK_PRIO_MID, NULL);
		}
		else
		{
			lv_obj_del(uploadSong_main_cont);
			uploadSong_main_cont=NULL;

			songUploadInfo.uploadStatus = UPLOAD_STATUS_FREE;       //空闲状态
    		songUploadInfo.uploadProgress = 0;        //上传进度归0
		}
	}
	else
	{
		printf("uploadSong_main_cont has been closed...\n");
	}

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}


//刷新上传状态
void set_uploadSong_status_and_progress(int status,int progress,int IsSelfcall)
{
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}

	if(!uploadSong_main_cont)
	{
		goto EXIT;
	}

	switch(status)
	{
		case UPLOAD_STATUS_FREE:
		{
			lv_label_set_text(uploadSong_label_status, language_musiclist_upload_status_ready_text);
		}
		break;
		case UPLOAD_STATUS_START:
		{
			lv_label_set_text(uploadSong_label_status, language_musiclist_upload_status_uploading_text);
			
		}
		break;
		case UPLOAD_STATUS_SUCCEED:
		{
			lv_label_set_text(uploadSong_label_status, language_musiclist_upload_status_succeed_text);
		}
		break;
		case UPLOAD_STATUS_FAILED_FORMAT:
		{
			lv_label_set_text(uploadSong_label_status, language_musiclist_upload_status_failed_song_invaild_text);
		}
		break;
		case UPLOAD_STATUS_FAILED_NO_SPACE:
		{
			lv_label_set_text(uploadSong_label_status, language_musiclist_upload_status_failed_storage_text);
		}
		break;
		
		case UPLOAD_STATUS_FAILED_NORMAL:
		{
			lv_label_set_text(uploadSong_label_status, language_musiclist_upload_status_failed_text);
		}
		break;
	}

	//如果正在上传，同时显示上传进度
	if(status == UPLOAD_STATUS_START)
	{
		char progressText[32]={0};
		sprintf(progressText,"%s：%d%%",language_musiclist_upload_transferred_text,progress);
		lv_label_set_text(uploadSong_label_progress, progressText);
	}

	//如果不是空闲或者正在上传状态，按钮显示关闭而不是停止
	if(!(status == UPLOAD_STATUS_FREE || status == UPLOAD_STATUS_START))
	{
		lv_obj_set_style_local_value_str(uploadSong_bt_stop, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT,language_musiclist_upload_close_text);
	}

EXIT:
	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}


void create_uploadSong_cont(int IsSelfcall)
{
	if(uploadSong_main_cont)
		return;

	printf("create_uploadSong_cont\n");
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}
	uploadSong_main_cont =lv_cont_create(lv_scr_act(), NULL);
	lv_obj_set_size(uploadSong_main_cont,450,270);
	lv_obj_align(uploadSong_main_cont,NULL,LV_ALIGN_CENTER,0,0);
	lv_obj_set_style_local_border_color(uploadSong_main_cont,LV_OBJ_PART_MAIN,LV_STATE_DEFAULT,get_theme_color());


	lv_obj_t *uploadSong_head_box = lv_obj_create(uploadSong_main_cont, NULL);
	lv_obj_set_size(uploadSong_head_box, 450, 50);
	lv_obj_align(uploadSong_head_box, uploadSong_main_cont, LV_ALIGN_IN_TOP_MID,0,0);
	lv_obj_set_style_local_pad_all(uploadSong_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(uploadSong_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    //lv_obj_set_style_local_radius(uploadSong_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(uploadSong_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_bg_color(uploadSong_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());

	lv_obj_t *label_head=lv_label_create(uploadSong_head_box, NULL);
	lv_obj_align(label_head, uploadSong_head_box, LV_ALIGN_IN_TOP_MID,0,5);
	lv_label_set_align(label_head, LV_LABEL_ALIGN_CENTER);
	

	lv_label_set_text(label_head, language_musiclist_song_upload_text);
	
	lv_obj_set_auto_realign(label_head,true);
	lv_obj_set_width(label_head, lv_obj_get_width_grid(uploadSong_head_box, 2, 1));

	lv_obj_set_style_local_text_font(label_head, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font26);

	uploadSong_label_status=lv_label_create(uploadSong_main_cont, NULL);
	lv_obj_align(uploadSong_label_status, uploadSong_main_cont, LV_ALIGN_IN_TOP_LEFT,120,60);
	lv_label_set_align(uploadSong_label_status, LV_LABEL_ALIGN_CENTER);
	//lv_label_set_text(uploadSong_label_status, language_musiclist_upload_status_uploading_text);
	lv_obj_set_auto_realign(uploadSong_label_status,true);
	lv_obj_set_width(uploadSong_label_status, lv_obj_get_width_grid(uploadSong_main_cont, 2, 1));
	lv_obj_set_style_local_text_font(uploadSong_label_status, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font26);

	uploadSong_label_progress=lv_label_create(uploadSong_main_cont, NULL);
	lv_obj_align(uploadSong_label_progress, uploadSong_main_cont, LV_ALIGN_IN_TOP_LEFT,120,120);
	lv_label_set_align(uploadSong_label_progress, LV_LABEL_ALIGN_CENTER);
	//lv_label_set_text(uploadSong_label_progress, language_musiclist_upload_transferred_text);
	lv_obj_set_auto_realign(uploadSong_label_progress,true);
	lv_obj_set_width(uploadSong_label_progress, lv_obj_get_width_grid(uploadSong_main_cont, 2, 1));
	lv_obj_set_style_local_text_font(uploadSong_label_progress, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font26);
	
	uploadSong_bt_stop=lv_btn_create(uploadSong_main_cont,NULL);
	lv_obj_align(uploadSong_bt_stop,uploadSong_main_cont,LV_ALIGN_IN_BOTTOM_MID,0,-30);
	lv_obj_set_style_local_value_str(uploadSong_bt_stop, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT,language_musiclist_upload_stop_text);
	lv_obj_set_style_local_value_font(uploadSong_bt_stop, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font26);
	lv_obj_set_style_local_border_color(uploadSong_bt_stop,LV_OBJ_PART_MAIN,LV_STATE_DEFAULT,get_theme_color());
	lv_obj_set_style_local_bg_color(uploadSong_bt_stop, LV_OBJ_PART_MAIN, LV_STATE_CHECKED, get_theme_color());

	lv_obj_set_event_cb(uploadSong_bt_stop, uploadSongStop_event_cb);
	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
#if 0
	//此处需要立刻刷新一遍状态，否则自动应答的设备状态变化太快，没有更新
	set_call_status_show(m_stPager_Info.other_callStatus,IsSelfcall);
#endif
}



void udisk_win_start(void)
{
	int i=0;

	if( !stUsbInfo.IsPlug )
	{
		static const char * btns[] = {"OK", ""};
		lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
		lv_msgbox_add_btns(msg, btns);
		lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
		lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
		lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);

		lv_msgbox_set_text(msg,language_musiclist_insertUdisk_text);
		return;
	}
	#if USE_ASM9260
	if( !stMusicList.ready_flag )
	{
		static const char * btns[] = {"OK", ""};
		lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
		lv_msgbox_add_btns(msg, btns);
		lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
		lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
		lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);

		lv_msgbox_set_text(msg,language_musiclist_udiskLoading_text);
		return;
	}
	#endif

	udisk_musiclist_all_count = lv_cont_create(control_all_cont, NULL);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_y(udisk_musiclist_all_count,48);
		lv_obj_set_size(udisk_musiclist_all_count,LV_HOR_RES_MAX+4,LV_VER_RES_MAX-48+4);		//此处为了解决默认情况下屏幕留边的问题
	}
	else
	{
		lv_obj_set_y(udisk_musiclist_all_count,48);
		lv_obj_set_size(udisk_musiclist_all_count,LV_HOR_RES_MAX+4,LV_VER_RES_MAX-48+4);		//此处为了解决默认情况下屏幕留边的问题
	}
	lv_obj_set_style_local_pad_all(udisk_musiclist_all_count, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_margin_all(udisk_musiclist_all_count, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_radius(udisk_musiclist_all_count, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	udisk_musiclist_main_cont = lv_cont_create(udisk_musiclist_all_count, NULL);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_size(udisk_musiclist_main_cont,LV_HOR_RES_MAX+4,516-48);
	}
	else
	{
		lv_obj_set_size(udisk_musiclist_main_cont,LV_HOR_RES_MAX+4,410-48);
	}
	lv_obj_set_y(udisk_musiclist_main_cont,0);
	lv_obj_set_style_local_pad_all(udisk_musiclist_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_margin_all(udisk_musiclist_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_radius(udisk_musiclist_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_bg_color(udisk_musiclist_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(250,250,250));
	lv_obj_set_style_local_border_width(udisk_musiclist_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 1);
	//lv_obj_set_click(control_main_cont, false);
	lv_obj_add_protect(udisk_musiclist_main_cont, LV_PROTECT_CLICK_FOCUS);

	//创建列表
	udisk_dir_list = lv_list_create(udisk_musiclist_main_cont, NULL);
    //lv_list_set_scroll_propagation(udisk_dir_list, true);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_size(udisk_dir_list, 350, 380);
		lv_obj_set_pos(udisk_dir_list, 75, 60);
	}
	else
	{
		lv_obj_set_size(udisk_dir_list, 300, 304);
		lv_obj_set_pos(udisk_dir_list, 55, 46);
	}
    
    lv_obj_add_style(udisk_dir_list, LV_OBJ_PART_MAIN, &style_font_20);
    for(i = 0; i<stMusicList.DirNum; i++) {
		udisk_dir_btn[i] = lv_list_add_btn(udisk_dir_list, &png_folder, Get_DirName_From_Path(stMusicList.SongDir[i].cDirName));
		//lv_btn_set_checkable(btn, true); 开启后btn可以选中和取消,因列表项选中后不允许取消,故不开启
		lv_obj_set_style_local_pad_top(udisk_dir_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
		lv_obj_set_style_local_pad_bottom(udisk_dir_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
		lv_obj_set_event_cb(udisk_dir_btn[i], udisk_dir_list_btn_event_cb);
    }



	//创建列表
    udisk_songlist = lv_list_create(udisk_musiclist_main_cont, NULL);
    //lv_list_set_scroll_propagation(udisk_songlist, true);
    if(IS_DISP_RES_1024)
	{
		lv_obj_set_size(udisk_songlist, 400, 380);
		lv_obj_set_pos(udisk_songlist, 540, 60);
	}
	else
	{
		lv_obj_set_size(udisk_songlist, 350, 304);
		lv_obj_set_pos(udisk_songlist, 400, 46);
	}
    lv_obj_add_style(udisk_songlist, LV_OBJ_PART_MAIN, &style_font_20);


    lv_obj_t  *label= lv_label_create(udisk_musiclist_main_cont, NULL);
    lv_obj_align(label, udisk_dir_list, LV_ALIGN_OUT_TOP_MID,-15,-28);
    lv_label_set_text(label, language_musiclist_list_text);
    lv_obj_add_style(label, LV_OBJ_PART_MAIN, &style_font_26);
    lv_obj_set_auto_realign(label, true);
    lv_obj_set_style_local_text_color(label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(28,69,94));


    label= lv_label_create(udisk_musiclist_main_cont, NULL);
    lv_obj_align(label, udisk_songlist, LV_ALIGN_OUT_TOP_MID,-15,-28);
    lv_label_set_text(label, language_musiclist_song_text);
    lv_obj_add_style(label, LV_OBJ_PART_MAIN, &style_font_26);
    lv_obj_set_auto_realign(label, true);
    lv_obj_set_style_local_text_color(label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(28,69,94));



	//创建底部控制栏
	lv_obj_t *musiclist_bottom_box = lv_obj_create(udisk_musiclist_all_count, NULL);
	//lv_obj_set_pos(control_zone_cont, 0, 0);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_y(musiclist_bottom_box,516-48);
		lv_obj_set_size(musiclist_bottom_box, LV_HOR_RES_MAX, LV_VER_RES_MAX-516);
	}
	else
	{
		lv_obj_set_y(musiclist_bottom_box,410-48);
		lv_obj_set_size(musiclist_bottom_box, LV_HOR_RES_MAX, LV_VER_RES_MAX-410);
	}
	lv_obj_set_style_local_pad_all(musiclist_bottom_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_margin_all(musiclist_bottom_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_radius(musiclist_bottom_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(musiclist_bottom_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	//底部控制按钮
	for(i=0;i<LZY_UDISK_MUSICLIST_MAX_MUSICLIST_NUM;i++)
	{
		lzy_udisk_musiclist_button_list[i].obj=lv_obj_create(musiclist_bottom_box, NULL);
	   // lv_obj_align(test_area, control_bottom_box, LV_ALIGN_IN_TOP_MID,0,40);
	    lv_obj_set_style_local_bg_color(lzy_udisk_musiclist_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
	    lv_obj_set_style_local_radius(lzy_udisk_musiclist_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_RADIUS_CIRCLE);
		if(IS_DISP_RES_1024)
		{
	    	lv_obj_set_size(lzy_udisk_musiclist_button_list[i].obj, 48, 48);
	    	lv_obj_set_pos(lzy_udisk_musiclist_button_list[i].obj, 90+i*160, 8);
		}
		else
		{
			lv_obj_set_size(lzy_udisk_musiclist_button_list[i].obj, 42, 42);
	    	lv_obj_set_pos(lzy_udisk_musiclist_button_list[i].obj, 60+i*130, 5);
		}

	    lv_obj_set_style_local_border_width(lzy_udisk_musiclist_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	    lv_obj_set_style_local_pad_all(lzy_udisk_musiclist_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	    lv_obj_set_style_local_margin_all(lzy_udisk_musiclist_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	    lv_obj_t *imgbtn=lv_imgbtn_create(lzy_udisk_musiclist_button_list[i].obj, NULL);
		lv_obj_align(imgbtn, lzy_udisk_musiclist_button_list[i].obj, LV_ALIGN_CENTER,50,5);
		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_RELEASED, &lzy_udisk_musiclist_button_list[i].pic);
		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_PRESSED, &lzy_udisk_musiclist_button_list[i].pic);

		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_CHECKED_RELEASED, &lzy_udisk_musiclist_button_list[i].pic);
		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_CHECKED_PRESSED, &lzy_udisk_musiclist_button_list[i].pic);

		if(i == LZY_UDISK_MUSICLIST_BUTTON_PLAY)
		{
			lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_CHECKED_RELEASED, &pic_control_pause);
			lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_CHECKED_PRESSED, &pic_control_pause);
		}

		lv_obj_set_style_local_image_recolor_opa(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_OPA_COVER);
		lv_obj_set_style_local_image_recolor(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_COLOR_BLACK);

		lv_obj_set_style_local_image_recolor_opa(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_CHECKED, LV_OPA_COVER);
		lv_obj_set_style_local_image_recolor(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_CHECKED, LV_COLOR_YELLOW);



		//创建label显示按钮标签
		lzy_udisk_musiclist_button_list[i].label=lv_label_create(udisk_musiclist_all_count, NULL);
		lv_obj_align(lzy_udisk_musiclist_button_list[i].label, lzy_udisk_musiclist_button_list[i].obj, LV_ALIGN_OUT_BOTTOM_MID,0,1);
		lv_label_set_align(lzy_udisk_musiclist_button_list[i].label, LV_LABEL_ALIGN_CENTER);
		lv_label_set_text(lzy_udisk_musiclist_button_list[i].label, lzy_udisk_musiclist_button_list[i].nameCN);
		lv_obj_set_auto_realign(lzy_udisk_musiclist_button_list[i].label,true);
		lv_obj_set_width(lzy_udisk_musiclist_button_list[i].label, lv_obj_get_width_grid(lzy_udisk_musiclist_button_list[i].obj, 1, 1));
		lv_obj_add_style(lzy_udisk_musiclist_button_list[i].label, LV_OBJ_PART_MAIN, &style_font_15);


	    lv_obj_set_event_cb(lzy_udisk_musiclist_button_list[i].obj, round_btn_cb);
	    lv_obj_set_event_cb(imgbtn, round_btn_cb);
	}


	 SetCurrentWin(WIN_UDISK);
	 udisk_current_playId_change(1,stUsbInfo.current_playFileId);
	 udisk_Win_PlayMode_change(stUsbInfo.playMode,1);
	 refresh_usbPlay(1);

	//隐藏分组扩展按钮
	lv_obj_set_hidden(group_enter_btn,true);
}


int udisk_get_fileId_pos(int fileId)
{
	int index = -1;
	int i=0;
	for(i=0;i<stMusicList.DirNum;i++)
	{
		if( fileId >= stMusicList.SongDir[i].FileStartIndex && fileId <= stMusicList.SongDir[i].FileStartIndex + stMusicList.SongDir[i].nFileNum -1 )
		{
			index = fileId-stMusicList.SongDir[i].FileStartIndex;
			break;
		}
	}
	return index;
}

int udisk_get_DirId_pos(int fileId)
{
	int index = -1;
	int i=0;
	for(i=0;i<stMusicList.DirNum;i++)
	{
		if( fileId >= stMusicList.SongDir[i].FileStartIndex && fileId <= stMusicList.SongDir[i].FileStartIndex + stMusicList.SongDir[i].nFileNum -1 )
		{
			index = i;
			break;
		}
	}
	return index;
}

void udisk_current_playId_change(int IsSelfcall,int new_playId)
{
	printf("udisk_current_playId_change:%d\n",new_playId);
	if(new_playId == 0)
		return;
	if( GetCurrentWin()!=WIN_UDISK )
	{
		stUsbInfo.current_playFileId = new_playId;
		return;
	}

	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}

	//取消原来btn的选中状态
	int dir_index=-1,dir_index_new=-1,song_index=-1,song_index_new=-1;
	lv_obj_t * temp_udisk_dir_btn = lv_list_get_btn_selected(udisk_dir_list);
	lv_obj_t * temp_udisk_song_btn = lv_list_get_btn_selected(udisk_songlist);

	dir_index_new = udisk_get_DirId_pos(new_playId);
	if(temp_udisk_dir_btn != NULL)
	{
		dir_index=lv_list_get_btn_index(udisk_dir_list,temp_udisk_dir_btn);
		printf("old_dir_index=%d,new_dir_index=%d\n",dir_index,dir_index_new);
		if(dir_index != dir_index_new && dir_index!=-1)
		{
			lv_obj_set_state(udisk_dir_btn[dir_index], LV_STATE_DEFAULT);
		}
	}

	if(dir_index_new!=-1 && dir_index != dir_index_new)
	{
		//聚焦在新的上
		lv_list_focus_btn(udisk_dir_list,udisk_dir_btn[dir_index_new]);
		//设置新的btn为选中状态
		lv_obj_set_state(udisk_dir_btn[dir_index_new], LV_STATE_CHECKED);
	}
	stUsbInfo.current_playFileId = new_playId;
	if(dir_index_new!=-1 && dir_index != dir_index_new)
	{
		udisk_dir_list_btn_event_cb(udisk_dir_btn[dir_index_new],LV_EVENT_CLICKED);
	}
	else
	{
		song_index_new = udisk_get_fileId_pos(new_playId);
		if(temp_udisk_song_btn != NULL )
		{
			song_index=lv_list_get_btn_index(udisk_songlist,temp_udisk_song_btn);
			if(song_index != song_index_new && song_index!=-1)
			{
				lv_obj_set_state(udisk_song_btn[song_index], LV_STATE_DEFAULT);
			}
		}
		//一定要foucs新的btn，否则lv_list_get_btn_selected还是原来的btn
		//聚焦在新的上
		if(song_index_new !=-1 && udisk_song_btn[song_index_new])
		{
			lv_list_focus_btn(udisk_songlist,udisk_song_btn[song_index_new]);
			//设置新的btn为选中状态
			lv_obj_set_state(udisk_song_btn[song_index_new], LV_STATE_CHECKED);
		}
	}

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}

}

#endif