/*
 * zone.c
 *
 *  Created on: Aug 24, 2020
 *      Author: king
 */

#include <stdio.h>
#include "control.h"
#include "theme.h"
#include "win.h"
#include "settings_main_win.h"
#include "com_bp1048.h"
#include "SaveUserInfo.h"


lv_obj_t *screen_settings;				//设置界面screen
lv_obj_t *settings_back_btn;	        //返回

lv_obj_t *settings_all_cont;            //设置界面总成
lv_obj_t *settings_head_label;			//顶栏时间标签

lv_obj_t *settings_sidebar;            //左侧边栏
lv_obj_t *settings_right_area_common;    //右侧显示区域(共同)


lv_obj_t *setting_sidebar_btn[MAX_SIDEBAR_BTN_NUM]={0};       //侧边栏按钮
int settings_current_item=DEFAULT_SETTINGS_ITEM;  //当前选择的item项
lv_obj_t *setting_right_area[MAX_SIDEBAR_BTN_NUM]={0};         //右侧显示区域

#if(APP_TYPE == APP_JUSBE_MIXER)
char settings_btn_nameCN[MAX_SIDEBAR_BTN_NUM][20]={"设备信息","网络设置","钟声设置","显示设置","其他设置"};
#else
#if SUPPORT_AUTO_TRIGGER
char settings_btn_nameCN[MAX_SIDEBAR_BTN_NUM][20]={"设备信息","网络设置","话筒设置","监听设置","触发设置","钟声设置","显示设置","其他设置"};
#else
char settings_btn_nameCN[MAX_SIDEBAR_BTN_NUM][20]={"设备信息","网络设置","话筒设置","输出设置","钟声设置","显示设置","其他设置"};
#endif
#endif


/****Start**********设备信息******************************/
lv_obj_t * systemInfo_ip;
lv_obj_t * systemInfo_serverOnline;
lv_obj_t * systemInfo_networkMode;
lv_obj_t * systemInfo_SerialNum;

void enter_systemInfo_win();
/****End**********设备信息*****************************/



/****Start**********账户设置******************************/
int account_user_selected=0;    //从1开始
lv_obj_t *page_account_list;  //账户列表页面
lv_obj_t *account_table;     //账户表单
lv_obj_t *account_add_btn;     //新增账户按钮
lv_obj_t *account_del_btn;     //账户删除按钮
lv_obj_t *page_account_modify;  //账户编辑页面
lv_obj_t *acoount_modify_box;   //账户编辑提示框
lv_obj_t *account_userName_ta;  //用户名文本框
lv_obj_t *account_passord_ta;  //密码文本框
lv_obj_t *account_admin_cb;     //管理员权限checkbox
lv_obj_t *account_save_btn;     //账户编辑保存按钮
/****End**********账户设置*****************************/


/****Start*********网络设置******************************/
lv_obj_t *network_type_label;   //DHCP or static
lv_obj_t *network_type_switch;   //DHCP or static

lv_obj_t *network_ip_label;       //IP
lv_obj_t *network_subnet_label;   //subnet
lv_obj_t *network_gateway_label;   //gateway

lv_obj_t *network_ip_textarea;       //IP
lv_obj_t *network_subnet_textarea;   //subnet
lv_obj_t *network_gateway_textarea;   //gateway

lv_obj_t *network_save_btn;         //save btn

int network_subnet_label_posy,network_gateway_label_posy,network_subnet_textarea_posy,network_gateway_textarea_posy;

int temp_g_IP_Assign;   //临时IP获取方式

/****End**********网络设置*****************************/

void enter_network_win();
/****Start*********话筒设置******************************/

lv_obj_t *mic_sensitivity_label;    //麦克风灵敏度标签显示

void enter_mic_win();
/****End**********话筒设置*****************************/

/****Start*********输出设置******************************/
void enter_listen_win();
lv_obj_t *listen_volume_label;
#if ENABLE_CALL_FUNCTION
lv_obj_t *call_volume_label,*ring_volume_label;
#endif
/****End**********输出设置*****************************/


#if SUPPORT_AUTO_TRIGGER
/****Start*********触发设置******************************/

lv_obj_t *trigger_group_label;    //触发分组标签
lv_obj_t *trigger_group_list;    //触发分组列表
lv_obj_t *trigger_group_list_item[100];   //触发分组列表内item
lv_obj_t *trigger_group_save_button;    //触发保存按钮
lv_obj_t *trigger_group_reset_button;   //触发重置按钮


void enter_trigger_win();
/****End**********触发设置*****************************/
#endif

/****Start*********钟声设置******************************/


void enter_bell_win();
/****End**********钟声设置*****************************/


/****Start*********屏幕亮度******************************/
lv_obj_t *language_DropDown;

void enter_backlight_win();
/****End**********屏幕亮度*****************************/


/****Start*********其他设置******************************/

lv_obj_t *signal_timeout_label;    //信号超时标签
lv_obj_t *paging_zone_volume_label;    //分区寻呼默认音量标签

void enter_other_win();
/****End**********其他设置*****************************/


extern lv_obj_t *screen_login;				//登录界面screen
extern char g_host_tcp_addr_domain[64];
extern char g_host_tcp_addr_domain2[64];
extern char g_host_tcp_prase_ipAddress[16];		//解析后的主机IP

int enter_settings_language=0;      //记录进入设置界面时的语言

void Init_settingsWin_display()
{
    #if(APP_TYPE == APP_JUSBE_MIXER)
    //char settings_btn_nameCN[MAX_SIDEBAR_BTN_NUM][20]={"设备信息","网络设置","钟声设置","显示设置","其他设置"};
    sprintf(settings_btn_nameCN[0],language_settings_tab_deviceInfo_text);
    sprintf(settings_btn_nameCN[1],language_settings_tab_network_text);
    sprintf(settings_btn_nameCN[2],language_settings_tab_ring_text);
    sprintf(settings_btn_nameCN[3],language_settings_tab_display_text);
    sprintf(settings_btn_nameCN[4],language_settings_tab_other_text);
    #else
    #if SUPPORT_AUTO_TRIGGER
    //char settings_btn_nameCN[MAX_SIDEBAR_BTN_NUM][20]={"设备信息","网络设置","话筒设置","监听设置","触发设置","钟声设置","显示设置","其他设置"};
    sprintf(settings_btn_nameCN[0],language_settings_tab_deviceInfo_text);
    sprintf(settings_btn_nameCN[1],language_settings_tab_network_text);
    sprintf(settings_btn_nameCN[2],language_settings_tab_mic_text);
    sprintf(settings_btn_nameCN[3],language_settings_tab_listen_text);
    sprintf(settings_btn_nameCN[4],language_settings_tab_trigger_text);
    sprintf(settings_btn_nameCN[5],language_settings_tab_ring_text);
    sprintf(settings_btn_nameCN[6],language_settings_tab_display_text);
    sprintf(settings_btn_nameCN[7],language_settings_tab_other_text);
    #else
    //char settings_btn_nameCN[MAX_SIDEBAR_BTN_NUM][20]={"设备信息","网络设置","话筒设置","输出设置","钟声设置","显示设置","其他设置"};
    sprintf(settings_btn_nameCN[0],language_settings_tab_deviceInfo_text);
    sprintf(settings_btn_nameCN[1],language_settings_tab_network_text);
    sprintf(settings_btn_nameCN[2],language_settings_tab_mic_text);
    sprintf(settings_btn_nameCN[3],language_settings_tab_output_text);
    sprintf(settings_btn_nameCN[4],language_settings_tab_ring_text);
    sprintf(settings_btn_nameCN[5],language_settings_tab_display_text);
    sprintf(settings_btn_nameCN[6],language_settings_tab_other_text);
    #endif
    #endif
}

static void settings_back_cb(lv_obj_t * obj, lv_event_t e)
{
    if(e == LV_EVENT_CLICKED)
    {
        #if ENABLE_CALL_FUNCTION
        //如果存在对讲对话框，不允许退出
        if(call_main_cont)
        {
            return;
        }
        #endif

        int i;
        for(i=0;i<MAX_SIDEBAR_BTN_NUM;i++)
        {
            setting_right_area[i]=NULL;

            if(i == SIDER_BTN_SYSINFO)
            {
                systemInfo_ip=NULL;
                systemInfo_serverOnline=NULL;
                systemInfo_networkMode=NULL;
                systemInfo_SerialNum=NULL;
            }
        }
        settings_current_item=DEFAULT_SETTINGS_ITEM;

        DeleteWin(WIN_SETTINGS_MAIN);

        if(IsValidWin(WIN_CONTROL))
        {
            lv_scr_load(screen_control);
            lv_obj_del(screen_settings);
            //如果语言改变了，需要刷新一遍底部按钮文字
            if(enter_settings_language != language)
            {
                for(i=0;i<MAX_CONTROL_NUM;i++)
                {
                    #if(APP_TYPE == APP_JUSBE_MIXER)
                    {
                        if( i == CONTROL_BUTTON_LISTEN || i == CONTROL_BUTTON_UDISK )
                        {
                            continue;
                        }
                    }
                    #endif
                    if(g_bp1048_info.firmware_type == BP1048_FW_NORMAL_TYPE)
                    {
                        if( i == CONTROL_BUTTON_UDISK )
                        {
                            continue;
                        }
                    }
                    if(!g_isSupportCall)
                    {
                        if( i == CONTROL_BUTTON_CALL )
                        {
                            continue;
                        }
                    }
                    if(control_button_list[i].label)
                    {
                        lv_label_set_text(control_button_list[i].label, control_button_list[i].nameCN);
                    }
                }
            }
            control_win_update(control_page_type,1);
        }
        else if(IsValidWin(WIN_LOGIN))
        {
            lv_scr_load(screen_login);
            lv_obj_del(screen_settings);
        }
    }
}


static void settings_sidebar_item_cb(lv_obj_t * obj, lv_event_t e)
{
    if(e == LV_EVENT_CLICKED)
    {
        #if DISBALE_NETWORK_SETTING_AND_FIXED_STATIC
        if(obj == setting_sidebar_btn[SIDER_BTN_NETWORK])
        {
            return;
        }
        #endif

        if(obj == setting_sidebar_btn[settings_current_item])
            return;
        #if 0
        if(obj == setting_sidebar_btn[SIDER_BTN_ACCOUNT] && m_stUser_Info.CurrentUserIndex!=0)
        {
            static const char * btns[] = {"OK", ""};
            lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
            lv_msgbox_add_btns(msg, btns);
            lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
		    lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
            lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_26);
            char tips[128]={0};
            lv_msgbox_set_text(msg,"请登录管理员账户操作");
            return;
        }
        #endif

        //先恢复原来的item选择状态
        lv_obj_set_state(setting_sidebar_btn[settings_current_item], LV_STATE_DEFAULT);
        //删除原来的右侧显示区域
        if(setting_right_area[settings_current_item])
        {
            //printf("settings_current_item=%d\n",settings_current_item);

            
            lv_obj_del(setting_right_area[settings_current_item]);
            setting_right_area[settings_current_item]=NULL;

            if(settings_current_item == SIDER_BTN_SYSINFO)
            {
                systemInfo_ip=NULL;
                systemInfo_serverOnline=NULL;
                systemInfo_networkMode=NULL;
                systemInfo_SerialNum=NULL;
            }
        }
        if(obj == setting_sidebar_btn[SIDER_BTN_SYSINFO])
        {
            settings_current_item=SIDER_BTN_SYSINFO;
            enter_systemInfo_win();
        }
        #if 0
        else if(obj == setting_sidebar_btn[SIDER_BTN_ACCOUNT])
        {
            settings_current_item=SIDER_BTN_ACCOUNT;
            enter_account_win();
        }
        #endif
        else if(obj == setting_sidebar_btn[SIDER_BTN_NETWORK])
        {
            settings_current_item=SIDER_BTN_NETWORK;   
            enter_network_win();
        }
        #if(APP_TYPE != APP_JUSBE_MIXER)
        else if(obj == setting_sidebar_btn[SIDER_BTN_MIC])
        {
            settings_current_item=SIDER_BTN_MIC;
            enter_mic_win();
        }
        else if(obj == setting_sidebar_btn[SIDER_BTN_LISTEN])
        {
            settings_current_item=SIDER_BTN_LISTEN;
            enter_listen_win();
        }
        #endif
        #if SUPPORT_AUTO_TRIGGER
        else if(obj == setting_sidebar_btn[SIDER_BTN_TRIGGER])
        {
             settings_current_item=SIDER_BTN_TRIGGER;
             enter_trigger_win(); 
        }
        #endif


        else if(obj == setting_sidebar_btn[SIDER_BTN_BELL])
        {
             settings_current_item=SIDER_BTN_BELL;
             enter_bell_win(); 
        }
        else if(obj == setting_sidebar_btn[SIDER_BTN_BACKLIGHT])
        {
             settings_current_item=SIDER_BTN_BACKLIGHT;   
             enter_backlight_win();
        }
        else if(obj == setting_sidebar_btn[SIDER_BTN_OTHER])
        {
             settings_current_item=SIDER_BTN_OTHER;
             enter_other_win();
        }

        //设置选中状态
        lv_obj_set_state(setting_sidebar_btn[settings_current_item], LV_STATE_CHECKED);
    }
}


void settings_main_win_start(void)
{
    enter_settings_language = language;
    //新建一个screen
    screen_settings = lv_obj_create(NULL, NULL);
    lv_scr_load(screen_settings);


    settings_all_cont = lv_cont_create(lv_scr_act(), NULL);
    lv_obj_set_size(settings_all_cont,LV_HOR_RES_MAX,LV_VER_RES_MAX);
    lv_obj_set_pos(settings_all_cont,0,0);		//此处为了解决默认情况下屏幕留边的问题
    lv_obj_set_style_local_pad_all(settings_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(settings_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_radius(settings_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_border_width(settings_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

    lv_obj_t *settings_head_box = lv_obj_create(settings_all_cont, NULL);


    if(IS_DISP_RES_1024)
	{
		lv_obj_set_size(settings_head_box, LV_HOR_RES_MAX, 50);
	}
    else if(IS_DISP_RES_1280)
    {
		lv_obj_set_size(settings_head_box, LV_HOR_RES_MAX, YRatio_600_to_800(50));
    }
	else if(IS_DISP_RES_800)
	{
		lv_obj_set_size(settings_head_box, LV_HOR_RES_MAX, 50);
	}
    lv_obj_set_style_local_pad_all(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_radius(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_border_width(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	lv_obj_align(settings_head_box, settings_all_cont, LV_ALIGN_IN_TOP_MID,0,0);
	settings_head_label=lv_label_create(settings_head_box, NULL);
	lv_obj_align(settings_head_label, settings_head_box, LV_ALIGN_CENTER,-2,-2);
	lv_label_set_align(settings_head_label, LV_LABEL_ALIGN_CENTER);
	lv_label_set_text(settings_head_label, language_settings_title_text);
	lv_obj_set_auto_realign(settings_head_label,true);
	lv_obj_set_width(settings_head_label, lv_obj_get_width_grid(settings_head_box, 2, 1));
	lv_obj_add_style(settings_head_box, LV_OBJ_PART_MAIN, &style_font_26);
	lv_obj_set_style_local_bg_color(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(79,91,106));
    //lv_obj_set_style_local_bg_grad_color(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(0x2f,0x61,0x9d));
	//lv_obj_set_style_local_bg_grad_dir(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_obj_set_style_local_text_color(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
	lv_obj_set_style_local_radius(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    /***20210413***降低边框颜色值（原来纯白色太亮）******/
    lv_obj_set_style_local_border_color(settings_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(0xC8, 0xC8, 0xC8));
    /***20210413***降低边框颜色值（原来纯白色太亮）******/

	lv_obj_add_protect(settings_head_box, LV_PROTECT_CLICK_FOCUS);



	settings_back_btn=lv_imgbtn_create(settings_head_box, NULL);
	lv_obj_align(settings_back_btn, settings_head_box, LV_ALIGN_IN_LEFT_MID,23,0);
	lv_imgbtn_set_src(settings_back_btn, LV_BTN_STATE_RELEASED, &pic_back);
	lv_imgbtn_set_src(settings_back_btn, LV_BTN_STATE_PRESSED, &pic_back);
	lv_obj_set_event_cb(settings_back_btn, settings_back_cb);


    //创建左侧边栏
    settings_sidebar = lv_cont_create(settings_all_cont, NULL);
    lv_obj_set_pos(settings_sidebar,0,50);
    if(IS_DISP_RES_1024)
	{
        lv_obj_set_size(settings_sidebar, 140, LV_HOR_RES_MAX-50);
    }
    else if(IS_DISP_RES_1280)
    {
		lv_obj_set_size(settings_sidebar, XRatio_1024_to_1280(140), LV_HOR_RES_MAX-YRatio_600_to_800(50));
        lv_obj_set_pos(settings_sidebar,0,YRatio_600_to_800(50));
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_set_size(settings_sidebar, 140, LV_HOR_RES_MAX-50);
    }

    lv_obj_set_style_local_pad_all(settings_sidebar, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(settings_sidebar, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_radius(settings_sidebar, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
/***20210413***降低边框颜色值（原来纯白色太亮）******/
    lv_obj_set_style_local_border_color(settings_sidebar, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(0xC8, 0xC8, 0xC8));
/***20210413***降低边框颜色值（原来纯白色太亮）******/
    lv_obj_set_style_local_border_side(settings_sidebar, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_BORDER_SIDE_BOTTOM | LV_BORDER_SIDE_RIGHT);

    int i,button_index=0;
    for(i=0;i<MAX_SIDEBAR_BTN_NUM;i++)
    {
        setting_sidebar_btn[i]=lv_cont_create(settings_sidebar, NULL);
        lv_obj_set_style_local_radius(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
        
        #if(APP_TYPE == APP_JUSBE_MIXER)
        lv_obj_set_pos(setting_sidebar_btn[i],0,111*button_index);
        lv_obj_set_size(setting_sidebar_btn[i],140, 111);
        #else
        #if SUPPORT_AUTO_TRIGGER
        lv_obj_set_pos(setting_sidebar_btn[i],0,69*button_index);
        lv_obj_set_size(setting_sidebar_btn[i],140, 69);
        #else

        if(IS_DISP_RES_1024)
	    {
            lv_obj_set_pos(setting_sidebar_btn[i],0,79*button_index);
            lv_obj_set_size(setting_sidebar_btn[i],140, 79);
        }
        else if(IS_DISP_RES_1280)
        {
            lv_obj_set_pos(setting_sidebar_btn[i],0,YRatio_600_to_800(79)*button_index);
            lv_obj_set_size(setting_sidebar_btn[i],XRatio_1024_to_1280(140), YRatio_600_to_800(79));
        }
        else if(IS_DISP_RES_800)
        {
            lv_obj_set_pos(setting_sidebar_btn[i],0,62*button_index);
            lv_obj_set_size(setting_sidebar_btn[i],140, 62);
        }

        #endif
        #endif
        lv_obj_set_style_local_pad_all(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
        lv_obj_set_style_local_margin_all(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
        lv_obj_set_style_local_radius(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
        lv_obj_set_style_local_border_side(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_BORDER_SIDE_RIGHT | LV_BORDER_SIDE_TOP | LV_BORDER_SIDE_BOTTOM);
        if(button_index == 0 || button_index == 2 || button_index == 4 || button_index == 6)
        {
            lv_obj_set_style_local_border_width(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
        }
        else
        {
            lv_obj_set_style_local_border_width(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 1);
        }

        if(i == MAX_SIDEBAR_BTN_NUM-1)
        {
            lv_obj_set_style_local_border_side(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_BORDER_SIDE_TOP | LV_BORDER_SIDE_BOTTOM);
        }
/***20210413***降低边框颜色值（原来纯白色太亮）******/
        lv_obj_set_style_local_border_color(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(0xC8, 0xC8, 0xC8));
/***20210413***降低边框颜色值（原来纯白色太亮）******/

        lv_obj_set_style_local_value_str(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, settings_btn_nameCN[i]);
        lv_obj_set_style_local_value_font(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font24);
        lv_obj_set_style_local_value_color(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);

        lv_obj_set_style_local_bg_color(setting_sidebar_btn[i],LV_OBJ_PART_MAIN,LV_STATE_DEFAULT,LV_COLOR_MAKE(0x5D,0x6D,0x7E));

        lv_obj_set_style_local_bg_color(setting_sidebar_btn[i],LV_OBJ_PART_MAIN,LV_STATE_CHECKED,LV_COLOR_OLIVE);
        lv_obj_set_style_local_value_color(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_CHECKED, LV_COLOR_WHITE);
        //默认点亮设备信息项
        if(i == DEFAULT_SETTINGS_ITEM )
        {
            lv_obj_set_state(setting_sidebar_btn[i], LV_STATE_CHECKED);
        }
        lv_obj_set_event_cb(setting_sidebar_btn[i], settings_sidebar_item_cb);

        button_index++;
    }


     //右侧显示区域(共同)
    settings_right_area_common= lv_cont_create(settings_all_cont, NULL);
    lv_obj_set_pos(settings_right_area_common,140,50);	
    if(IS_DISP_RES_1024)
	{
        lv_obj_set_size(settings_right_area_common, LV_HOR_RES_MAX-140, LV_HOR_RES_MAX-50);
    }
    else if(IS_DISP_RES_1280)
    {
        lv_obj_set_size(settings_right_area_common, LV_HOR_RES_MAX-XRatio_1024_to_1280(140), LV_HOR_RES_MAX-YRatio_600_to_800(50));
        lv_obj_set_pos(settings_right_area_common,XRatio_1024_to_1280(140),YRatio_600_to_800(50));	
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_set_size(settings_right_area_common, LV_HOR_RES_MAX-140, LV_HOR_RES_MAX-50);
    }	
    lv_obj_set_style_local_pad_all(settings_right_area_common, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(settings_right_area_common, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_radius(settings_right_area_common, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_border_width(settings_right_area_common, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

    lv_obj_set_style_local_bg_color(settings_right_area_common, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(0x5D,0x6D,0x7E));
    lv_obj_add_style(settings_right_area_common, LV_OBJ_PART_MAIN, &style_font_24);


    SetCurrentWin(WIN_SETTINGS_MAIN);

    enter_systemInfo_win();

}



/***设备信息项***/
void enter_systemInfo_win()
{

    //右侧显示区域
    setting_right_area[settings_current_item] = lv_cont_create(settings_all_cont, settings_right_area_common);


    lv_obj_t * systemInfo_version = lv_label_create(setting_right_area[settings_current_item], NULL);
    #if USE_PC_SIMULATOR
    lv_label_set_text_fmt(systemInfo_version,"%s :    %sC%d%s / %s",language_settings_device_version_text,VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION, "1.2.1");
    #elif USE_ASM9260
    if(g_bp1048_info.firmware_type == BP1048_FW_UDISK_TYPE)
    {
        lv_label_set_text_fmt(systemInfo_version,"%s :    %sC%d%s / %sU",language_settings_device_version_text,VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION, g_bp1048_info.version);
    }
    else
    {
        lv_label_set_text_fmt(systemInfo_version,"%s :    %sC%d%s / %s",language_settings_device_version_text,VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION, g_bp1048_info.version);
    }
    #elif defined(USE_SSD212) || defined(USE_SSD202)
        #if IS_TRANSMITTER
        if(!g_stc_IsInit)
        {
            lv_label_set_text_fmt(systemInfo_version,"%s :    %sC%d%s(ADC invalid)",language_settings_device_version_text,VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION);
        }
        else
        {
            lv_label_set_text_fmt(systemInfo_version,"%s :    %sC%d%s",language_settings_device_version_text,VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION);
            #if DEBUG_CORE_MODE
            lv_label_set_text_fmt(systemInfo_version,"%s :    %sC%d%s(Debug)",language_settings_device_version_text,VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION);
            #endif
        }
        #else
        	#if LZY_COMMERCIAL_VERISON
            lv_label_set_text_fmt(systemInfo_version,"%s :    %sL_C%d%s",language_settings_device_version_text,VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION);
            #else
            lv_label_set_text_fmt(systemInfo_version,"%s :    %sC%d%s",language_settings_device_version_text,VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION);
            #endif
            #if DEBUG_CORE_MODE
            lv_label_set_text_fmt(systemInfo_version,"%s :    %sC%d%s(Debug)",language_settings_device_version_text,VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION);
            #endif
            #if DISABLE_UDISK_FUNCTION
            lv_label_set_text_fmt(systemInfo_version,"%s :    %sC%d%s(No Udisk)",language_settings_device_version_text,VERSION,CUSTOMER_ID,CUSTOMER_FUNCTION);
            #endif
        #endif
    #endif

    #if NETWORK_VPN_INTERNET
    char version_label_text[128]={0};
    char *temp_label_text=lv_label_get_text(systemInfo_version);
    if(temp_label_text)
    {
        sprintf(version_label_text,"%s(VPN)",temp_label_text);
        lv_label_set_text_fmt(systemInfo_version,version_label_text);
    }
    #endif

    lv_obj_set_style_local_text_color(systemInfo_version, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    int posY=50;
    lv_obj_set_pos(systemInfo_version,120,posY);
    posY+=70;

    lv_obj_t * systemInfo_mac = lv_label_create(setting_right_area[settings_current_item], systemInfo_version);
    char mac[30]={0};
    sprintf(mac,"%02x-%02x-%02x-%02x-%02x-%02x",MAC_ADDR[0],MAC_ADDR[1],MAC_ADDR[2],MAC_ADDR[3],MAC_ADDR[4],MAC_ADDR[5]);
    char mac_upper[30]={0};
    strcpy(mac_upper,strupr(mac));
    lv_label_set_text_fmt(systemInfo_mac,"%s :    %s","MAC",mac_upper);
    lv_obj_set_pos(systemInfo_mac,120,posY);
    posY+=70;

    systemInfo_ip = lv_label_create(setting_right_area[settings_current_item], systemInfo_version);
    if(eth_link_status)
    {
        lv_label_set_text_fmt(systemInfo_ip,"%s :    %s","IP",ipAddress);
    }
    else
    {
        #if defined(USE_SSD212) || defined(USE_SSD202)
		if(g_module_4G_status <= MODULE_4G_OFF)
        {
            lv_label_set_text_fmt(systemInfo_ip,"%s :    %s","IP",language_settings_device_netCableNotInsert_text);
        }
        else
        {
            lv_label_set_text_fmt(systemInfo_ip,"%s :    %s","IP",language_settings_device_netCableNotInsertAndUse4G_text);
        }
        #else
        lv_label_set_text_fmt(systemInfo_ip,"%s :    %s","IP",language_settings_device_netCableNotInsert_text);
        #endif
    }
    lv_obj_set_pos(systemInfo_ip,120,posY);
    posY+=70;

    if(strlen(g_device_serialNum)>0)
    {
        systemInfo_SerialNum = lv_label_create(setting_right_area[settings_current_item], systemInfo_version);
        lv_label_set_text_fmt(systemInfo_SerialNum,"%s :  %s","SN",g_device_serialNum);
        lv_obj_set_pos(systemInfo_SerialNum,120,posY);
        posY+=70;
    }

    systemInfo_networkMode = lv_label_create(setting_right_area[settings_current_item], systemInfo_version);
    if(g_network_mode == NETWORK_MODE_LAN)
    {
        lv_label_set_text_fmt(systemInfo_networkMode,"%s :  UDP",language_settings_device_networkMode_text);
    }
    else
    {
        if(if_a_string_is_a_valid_ipv4_address(g_host_tcp_addr_domain2) || is_valid_domain(g_host_tcp_addr_domain2))
        {
            lv_label_set_text_fmt(systemInfo_networkMode,"%s :  TCP (%s、%s)",language_settings_device_networkMode_text,g_host_tcp_addr_domain,g_host_tcp_addr_domain2);
        }
        else
        {
            lv_label_set_text_fmt(systemInfo_networkMode,"%s :  TCP (%s)",language_settings_device_networkMode_text,g_host_tcp_addr_domain);
        }
    }
    lv_obj_set_pos(systemInfo_networkMode,120,posY);
    posY+=70;

    systemInfo_serverOnline = lv_label_create(setting_right_area[settings_current_item], systemInfo_version);
    lv_label_set_text_fmt(systemInfo_serverOnline,"%s :  %s",language_settings_device_connectStatus_text,(g_system_work_mode == WORK_MODE_CONCENTRATED) ?language_settings_device_connected_text:language_settings_device_disconnected_text);
    lv_obj_set_pos(systemInfo_serverOnline,120,posY);
    posY+=70;

}





static void bright_slider_event_cb(lv_obj_t * obj, lv_event_t e)
{
     if(e == LV_EVENT_VALUE_CHANGED) {
        if(lv_slider_get_type(obj) == LV_SLIDER_TYPE_NORMAL) {
            int val=lv_slider_get_value(obj);
            backlight_level=val;
            backlight_adjust(backlight_level);
            save_sysconf("Display","Brightness");
            printf("backlight_level=%d\n",backlight_level);
        }
    }
}


static void ScreenAutoClose_slider_event_cb(lv_obj_t * obj, lv_event_t e)
{
    if(e == LV_EVENT_VALUE_CHANGED) {
        if(lv_slider_get_type(obj) == LV_SLIDER_TYPE_NORMAL) {
            static char buf[16];
            int val=lv_slider_get_value(obj);
            printf("ScreenAutoClose_val=%d\n",val);
            g_backlightTimeout=val;
            save_sysconf("Display","ScreenTimeOut");

            if(val == 6)
                sprintf(buf,"%s",language_settings_display_disable_text);
            else if(val == 1)
                sprintf(buf,"5%s",language_settings_display_minute_text);
            else if(val == 2)
                sprintf(buf,"10%s",language_settings_display_minute_text);
            else if(val == 3)
                sprintf(buf,"30%s",language_settings_display_minute_text);
            else if(val == 4)
                sprintf(buf,"60%s",language_settings_display_minute_text);
            else if(val == 5)
                sprintf(buf,"120%s",language_settings_display_minute_text);

            lv_obj_t * lv_obj_temp = lv_obj_get_child(setting_right_area[settings_current_item],NULL);
            if(lv_obj_temp!=NULL)
            {
                lv_obj_temp = lv_obj_get_child(setting_right_area[settings_current_item],lv_obj_temp);
                if(lv_obj_temp!=NULL)
                {
                    lv_label_set_text_fmt(lv_obj_temp,"%s :  %s",language_settings_display_automatic_screenOff_text,buf);
                }
            }
        }
    }
}


static void language_dropdown_event_cb(lv_obj_t * ta, lv_event_t e)
{
    if(e == LV_EVENT_VALUE_CHANGED) {
        int tmp_language=lv_dropdown_get_selected(ta);
        //注意此处显示顺序与语言定义顺序不一样(0是简体中文，1是繁体中文，2是英文)
        switch(tmp_language)
        {
            case 0:
                tmp_language = CHINESE;
                break;
            case 1:
                tmp_language = ZHTW;
                break;
            case 2:
                tmp_language = ENGLISH;
                break;
        }
        //printf("dropdown_id=%d\n",tmp_language);

        //lv_obj_set_style_local_text_font(ta, LV_DROPDOWN_PART_MAIN,LV_STATE_DEFAULT, &font20);  //这个字体没有包含箭头
        if(tmp_language != language)
        {
            language = tmp_language;
            save_sysconf("Display","Language");
#if 0
            static const char * btns[] = {"OK", ""};
            lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
            lv_msgbox_add_btns(msg, btns);
            lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_24);
            char tips[128]={0};

           sprintf(tips,"%s",language_settings_network_settingsOK_text);
           lv_msgbox_set_text(msg,tips);

            //reboot
			System_Reboot();
#endif
            Language_Config();
        }
    }
}


/***背光设置***/
void enter_backlight_win()
{
    //右侧显示区域
    setting_right_area[settings_current_item] = lv_cont_create(settings_all_cont, settings_right_area_common);
   
    //语言
    lv_obj_t *language_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(language_label,"%s :",language_settings_display_language_text);
    lv_obj_set_style_local_text_color(language_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(language_label,120,50);


    language_DropDown = lv_dropdown_create(setting_right_area[settings_current_item], NULL);
    lv_obj_align(language_DropDown, language_label, LV_ALIGN_OUT_RIGHT_MID,120,0);
    lv_obj_set_event_cb(language_DropDown, language_dropdown_event_cb);
    lv_obj_set_width(language_DropDown, 100);
    lv_obj_set_height(language_DropDown, 35);

    lv_obj_set_style_local_text_font(language_DropDown, LV_DROPDOWN_PART_MAIN,LV_STATE_DEFAULT, &font20);  //这个字体没有包含箭头
    lv_obj_set_style_local_text_font(language_DropDown, LV_DROPDOWN_PART_LIST,LV_STATE_DEFAULT, &font20);  //这个字体没有包含箭头
    lv_dropdown_clear_options(language_DropDown);
    lv_dropdown_add_option(language_DropDown, "简体中文",0);
    lv_dropdown_add_option(language_DropDown, "繁體中文",1);
    lv_dropdown_add_option(language_DropDown, "English",2);
    if(language == CHINESE)
        lv_dropdown_set_selected(language_DropDown,0);
    else if(language == ZHTW)
        lv_dropdown_set_selected(language_DropDown,1);
    else if(language == ENGLISH)
        lv_dropdown_set_selected(language_DropDown,2);


    //亮度标签显示
    lv_obj_t * bright_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(bright_label,"%s :",language_settings_display_brightness_text);
    lv_obj_set_style_local_text_color(bright_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(bright_label,120,50+65);

    //创建亮度滑动条
	lv_obj_t *bright_slider = lv_slider_create(setting_right_area[settings_current_item], NULL);
	lv_slider_set_value(bright_slider, backlight_level, LV_ANIM_OFF);
    lv_slider_set_range(bright_slider,1,MAX_BRIGHT_LEVEL);
	lv_obj_set_pos(bright_slider,110,50+65+60);
	lv_obj_set_event_cb(bright_slider, bright_slider_event_cb);
	lv_obj_set_width(bright_slider, 350);
	lv_obj_set_height(bright_slider, 10);
    
    //自动息屏标签显示
    lv_obj_t * autoClose_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    static char buf[16]={0};
    if(g_backlightTimeout == 6)
        sprintf(buf,"%s",language_settings_display_disable_text);
    else if(g_backlightTimeout == 1)
        sprintf(buf,"5%s",language_settings_display_minute_text);
    else if(g_backlightTimeout == 2)
        sprintf(buf,"10%s",language_settings_display_minute_text);
    else if(g_backlightTimeout == 3)
        sprintf(buf,"30%s",language_settings_display_minute_text);
    else if(g_backlightTimeout == 4)
        sprintf(buf,"60%s",language_settings_display_minute_text);
    else if(g_backlightTimeout == 5)
        sprintf(buf,"120%s",language_settings_display_minute_text);
    lv_label_set_text_fmt(autoClose_label,"%s :  %s",language_settings_display_automatic_screenOff_text,buf);
    lv_obj_set_style_local_text_color(autoClose_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(autoClose_label,120,50+65+60+60);

    //创建自动息屏滑动条
	lv_obj_t *ScreenAutoClose_slider = lv_slider_create(setting_right_area[settings_current_item], NULL);
	lv_slider_set_value(ScreenAutoClose_slider, g_backlightTimeout, LV_ANIM_OFF);
    lv_slider_set_range(ScreenAutoClose_slider,1,6);
	lv_obj_set_pos(ScreenAutoClose_slider,110,50+65+60+60+60);
	lv_obj_set_event_cb(ScreenAutoClose_slider, ScreenAutoClose_slider_event_cb);
	lv_obj_set_width(ScreenAutoClose_slider, 350);
	lv_obj_set_height(ScreenAutoClose_slider, 10);
}




static void bell_switch_event_cb(lv_obj_t *sw, lv_event_t e)
{
    if (e == LV_EVENT_VALUE_CHANGED)
    {
        if (lv_switch_get_state(sw))
        {
            printf("switch on\n");
            paging_bell_selected=1;
        }
        else
        {
            printf("switch off\n");
            paging_bell_selected=0;
        }
        save_sysconf("Bell","Paging_Bell_Selected");
    }
}


/***钟声设置***/
void enter_bell_win()
{
    //右侧显示区域
    setting_right_area[settings_current_item] = lv_cont_create(settings_all_cont, settings_right_area_common);
   
    //亮度标签显示
    lv_obj_t * paging_bell_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(paging_bell_label,"%s",language_settings_ring_pagingRing_text);
    lv_obj_set_style_local_text_color(paging_bell_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(paging_bell_label,120,50);


    lv_obj_t *bell_switch_btn = lv_switch_create(setting_right_area[settings_current_item], NULL);
    lv_obj_align(bell_switch_btn, paging_bell_label, LV_ALIGN_OUT_RIGHT_MID,120,0);
    lv_obj_set_event_cb(bell_switch_btn, bell_switch_event_cb);
    lv_obj_set_width(bell_switch_btn, 65);
    lv_obj_set_height(bell_switch_btn, 30);
    if(paging_bell_selected)
        lv_switch_on(bell_switch_btn,LV_ANIM_OFF);
}

#if SUPPORT_AUTO_TRIGGER
static void auto_trigger_group_list_event_cb(lv_obj_t * obj, lv_event_t e);
void refresh_trigger_settings_win(int IsSelfcall,int reFreshList)
{
    if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}
    if( GetCurrentWin() == WIN_SETTINGS_MAIN )
    {
        if( settings_current_item == SIDER_BTN_TRIGGER )
        {
            int ori_index=Get_group_ori_index_by_realId(g_auto_trigger_groupId);
            if(ori_index>=0)
            {
                lv_label_set_text_fmt(trigger_group_label,"当前触发分组: %s",m_stGroup_Info.groupInfo[ori_index].g_group_name);
            }
            else
            {
                lv_label_set_text_fmt(trigger_group_label,"当前触发分组: %s","");
            }

            if(reFreshList)
            {
                lv_list_clean(trigger_group_list);
                int i=0;
                int real_list_index=-1;
                for(i = 0; i<m_stGroup_Info.TotalGroup; i++) 
                {
                    if( !m_stGroup_Info.groupInfo[i].g_group_isHide )
                    {
                        real_list_index++;
                        trigger_group_list_item[real_list_index] = lv_list_add_btn(trigger_group_list, NULL, m_stGroup_Info.groupInfo[i].g_group_name);
                        //lv_btn_set_checkable(btn, true); 开启后btn可以选中和取消,因列表项选中后不允许取消,故不开启
                        lv_obj_set_style_local_pad_top(trigger_group_list_item[real_list_index], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
                        lv_obj_set_style_local_pad_bottom(trigger_group_list_item[real_list_index], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
                        lv_obj_set_event_cb(trigger_group_list_item[real_list_index],auto_trigger_group_list_event_cb);
                    }
                }
            }
        }
    }
    if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}

void Paging_auto_trigger_change(int IsSignal,int ChangeSignal);
static void auto_trigger_switch_event_cb(lv_obj_t *sw, lv_event_t e)
{
    if (e == LV_EVENT_VALUE_CHANGED)
    {
        if (lv_switch_get_state(sw))
        {
            printf("switch on\n");
            if(!g_AuxIn_enable)
            {
                lv_switch_off(sw,LV_ANIM_ON);   //如果打开使用了动画（默认打开），则关闭也必须使用动画，否则异常

                static const char * btns[] = {"OK", ""};
                lv_obj_t *msg = lv_msgbox_create(settings_all_cont, NULL);
                lv_msgbox_add_btns(msg, btns);
                lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
                lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
                lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
                lv_msgbox_set_text(msg,"请开启线路输入!");
                return;
            }
            g_auto_trigger_enable=1;

            lv_obj_set_hidden(trigger_group_label,false);
            lv_obj_set_hidden(trigger_group_list,false);
            lv_obj_set_hidden(trigger_group_save_button,false);
            lv_obj_set_hidden(trigger_group_reset_button,false);
        }
        else
        {
            printf("switch off\n");
            g_auto_trigger_enable=0;

            lv_obj_set_hidden(trigger_group_label,true);
            lv_obj_set_hidden(trigger_group_list,true);
            lv_obj_set_hidden(trigger_group_save_button,true);
            lv_obj_set_hidden(trigger_group_reset_button,true);

            Paging_auto_trigger_change(0,0); //停止自动触发
        }
        save_sysconf("Trigger","IsEnable");
    }
}

//选择分组列表
static void auto_trigger_group_list_event_cb(lv_obj_t * obj, lv_event_t e)
{
	int i;
    if(e == LV_EVENT_CLICKED)
    {
    	lv_obj_set_state(obj, LV_STATE_CHECKED);
    	lv_obj_t * btn = lv_list_get_btn_selected(trigger_group_list);
    	if(!btn)
    		return;
      	int group_index=lv_list_get_btn_index(trigger_group_list,btn);
		printf("group_index=%d\n",group_index);
    }
}


static void auto_trigger_group_btn_event_cb(lv_obj_t * btn, lv_event_t e)
{
    if(e == LV_EVENT_CLICKED ) 
    {
        if(btn == trigger_group_save_button)
        {
            //判断当前所选分组
            lv_obj_t * btn = lv_list_get_btn_selected(trigger_group_list);
            if(!btn)
                return;
      	    int group_realIndex=lv_list_get_btn_index(trigger_group_list,btn);
            if(group_realIndex>=0)
            {
                int ori_index;
                if((ori_index=Get_Group_ori_index_by_realIndex(group_realIndex))>=0)
                {
                    int old_index = Get_group_ori_index_by_realId(g_auto_trigger_groupId);
                    if( ori_index != old_index )
                    {
                        sprintf(g_auto_trigger_groupId,m_stGroup_Info.groupInfo[ori_index].g_group_realId);
                        save_sysconf("Trigger","GroupId");
                        refresh_trigger_settings_win(1,0);

                        Paging_auto_trigger_change(0,0); //停止自动触发
                    }
                }
            }
        }
        else if(btn == trigger_group_reset_button)
        {
            //判断当前所选分组
            lv_obj_t * btn = lv_list_get_btn_selected(trigger_group_list);
            if(btn)
            {
      	        int group_realIndex=lv_list_get_btn_index(trigger_group_list,btn);
                if(group_realIndex>=0)
                {
                   lv_list_focus_btn(trigger_group_list,NULL);
                   lv_obj_set_state(trigger_group_list_item[group_realIndex], LV_STATE_DEFAULT);
                }
            }

            sprintf(g_auto_trigger_groupId,"");
            save_sysconf("Trigger","GroupId");
            refresh_trigger_settings_win(1,0);
            Paging_auto_trigger_change(0,0); //停止自动触发
        }
    }

}

/***触发设置***/
void enter_trigger_win()
{
    #if !defined(USE_PC_SIMULATOR) &&  !defined(USE_SSD212) && !defined(USE_SSD202)
    if( strcmp(g_bp1048_info.version,"1.2.1") <0 )
    {
        static const char * btns[] = {"OK", ""};
        lv_obj_t *msg = lv_msgbox_create(settings_all_cont, NULL);
        lv_msgbox_add_btns(msg, btns);
        lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
        lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
        lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
        lv_msgbox_set_text(msg,"当前DSP固件不支持,请升级!");
        return;
    }
    #endif
    
    //右侧显示区域
    setting_right_area[settings_current_item] = lv_cont_create(settings_all_cont, settings_right_area_common);
   
    //亮度标签显示
    lv_obj_t *paging_trigger_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(paging_trigger_label,"%s","开启自动触发");
    lv_obj_set_style_local_text_color(paging_trigger_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(paging_trigger_label,120,50);

    lv_obj_t *bell_switch_btn = lv_switch_create(setting_right_area[settings_current_item], NULL);
    lv_obj_align(bell_switch_btn, paging_trigger_label, LV_ALIGN_OUT_RIGHT_MID,120,0);
    lv_obj_set_event_cb(bell_switch_btn, auto_trigger_switch_event_cb);
    lv_obj_set_width(bell_switch_btn, 65);
    lv_obj_set_height(bell_switch_btn, 30);

    //列表形式，选择一个需要触发的分组
    trigger_group_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_obj_set_style_local_text_color(trigger_group_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(trigger_group_label,120,110);


    //创建列表
    trigger_group_list = lv_list_create(setting_right_area[settings_current_item], NULL);
    //lv_list_set_scroll_propagation(songlist, true);
    lv_obj_set_size(trigger_group_list, 380, 350);
    lv_obj_set_pos(trigger_group_list, 100, 160);
    lv_obj_add_style(trigger_group_list, LV_OBJ_PART_MAIN, &style_font_20);

#if 0
    int i=0;
    int real_list_index=-1;
    for(i = 0; i<m_stGroup_Info.TotalGroup; i++) {
		if( !m_stGroup_Info.groupInfo[i].g_group_isHide )
		{
            real_list_index++;
			trigger_group_list_item[real_list_index] = lv_list_add_btn(trigger_group_list, NULL, m_stGroup_Info.groupInfo[i].g_group_name);
			//lv_btn_set_checkable(btn, true); 开启后btn可以选中和取消,因列表项选中后不允许取消,故不开启
			lv_obj_set_style_local_pad_top(trigger_group_list_item[real_list_index], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
			lv_obj_set_style_local_pad_bottom(trigger_group_list_item[real_list_index], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 12);
            lv_obj_set_event_cb(trigger_group_list_item[real_list_index],auto_trigger_group_list_event_cb);
		}
    }
#else
    refresh_trigger_settings_win(1,1);
#endif

    int group_ori_index=-1;
    if( (group_ori_index=Get_group_ori_index_by_realId(g_auto_trigger_groupId)) >=0 )
    {
        if( m_stGroup_Info.groupInfo[group_ori_index].g_group_isHide  )
        {
            group_ori_index = -1;
        }
    }
    if(group_ori_index>=0)
    {
        lv_label_set_text_fmt(trigger_group_label,"当前触发分组: %s",m_stGroup_Info.groupInfo[group_ori_index].g_group_name);
        //点亮对应的项（实际项）
        int group_real_index=Get_group_real_index_by_realId(g_auto_trigger_groupId);
        lv_list_focus_btn(trigger_group_list,trigger_group_list_item[group_real_index]);
        lv_obj_set_state(trigger_group_list_item[group_real_index], LV_STATE_CHECKED);
    }
    else
    {
        lv_label_set_text_fmt(trigger_group_label,"当前触发分组: %s","");
    }


    //右侧按钮
    trigger_group_save_button = lv_btn_create(setting_right_area[settings_current_item], NULL);
    lv_btn_toggle(trigger_group_save_button);
    lv_obj_t *lv_group_save_label = lv_label_create(trigger_group_save_button, NULL);
    lv_label_set_text(lv_group_save_label ,"保存");
    lv_obj_align(trigger_group_save_button, trigger_group_list, LV_ALIGN_OUT_RIGHT_TOP,80,95);

    trigger_group_reset_button = lv_btn_create(setting_right_area[settings_current_item], NULL);
    lv_btn_toggle(trigger_group_reset_button);
    lv_obj_t *lv_group_reset_label = lv_label_create(trigger_group_reset_button, NULL);
    lv_label_set_text(lv_group_reset_label ,"重置");
    lv_obj_align(trigger_group_reset_button, trigger_group_list, LV_ALIGN_OUT_RIGHT_BOTTOM,80,-95);

    lv_obj_set_event_cb(trigger_group_save_button, auto_trigger_group_btn_event_cb);
    lv_obj_set_event_cb(trigger_group_reset_button, auto_trigger_group_btn_event_cb);


    if(g_auto_trigger_enable)
    {
        lv_switch_on(bell_switch_btn,LV_ANIM_OFF);
    }
    else
    {
        lv_obj_set_hidden(trigger_group_label,true);
        lv_obj_set_hidden(trigger_group_list,true);
        lv_obj_set_hidden(trigger_group_save_button,true);
        lv_obj_set_hidden(trigger_group_reset_button,true);
    }
}
#endif


static void listen_volume_event_cb(lv_obj_t * obj, lv_event_t e)
{
     if(e == LV_EVENT_VALUE_CHANGED) {
        if(lv_slider_get_type(obj) == LV_SLIDER_TYPE_NORMAL) {
            int val=lv_slider_get_value(obj);
            g_listen_vol=val;
            if(listen_volume_label!=NULL)
            {
                lv_label_set_text_fmt(listen_volume_label,"%s : %d",language_settings_output_listenVol_text,val);
            }
            save_sysconf("Other","Listen_Volume");
            set_output_volume();
        }
    }
}

#if ENABLE_CALL_FUNCTION
static void call_volume_event_cb(lv_obj_t * obj, lv_event_t e)
{
     if(e == LV_EVENT_VALUE_CHANGED) {
        if(lv_slider_get_type(obj) == LV_SLIDER_TYPE_NORMAL) {
            int val=lv_slider_get_value(obj);
            g_call_vol=val;
            if(call_volume_label!=NULL)
            {
                lv_label_set_text_fmt(call_volume_label,"%s : %d",language_settings_output_intercomVol_text,val);
            }
            save_sysconf("Other","Call_Volume");
            set_output_volume();
        }
    }
}

static void ring_volume_event_cb(lv_obj_t * obj, lv_event_t e)
{
     if(e == LV_EVENT_VALUE_CHANGED) {
        if(lv_slider_get_type(obj) == LV_SLIDER_TYPE_NORMAL) {
            int val=lv_slider_get_value(obj);
            g_ring_vol=val;
            if(ring_volume_label!=NULL)
            {
                lv_label_set_text_fmt(ring_volume_label,"%s : %d",language_settings_output_ringVol_text,val);
            }
            save_sysconf("Other","Ring_Volume");
            set_output_volume();
        }
    }
}
#endif


static void other_zone_volume_event_cb(lv_obj_t * obj, lv_event_t e)
{
     if(e == LV_EVENT_VALUE_CHANGED) {
        if(lv_slider_get_type(obj) == LV_SLIDER_TYPE_NORMAL) {
            int val=lv_slider_get_value(obj);
            g_paging_vol=val;
            if(paging_zone_volume_label!=NULL)
            {
                lv_label_set_text_fmt(paging_zone_volume_label,"%s : %d",language_settings_other_zone_paging_volume_text,val);
            }
            save_sysconf("Other","Paging_Zone_Volume");
        }
    }
}

static void SignalAutoClose_slider_event_cb(lv_obj_t * obj, lv_event_t e)
{
    if(e == LV_EVENT_VALUE_CHANGED) {
        if(lv_slider_get_type(obj) == LV_SLIDER_TYPE_NORMAL) {
            static char buf[16];
            int val=lv_slider_get_value(obj);
            g_Signal_Timeout_level=val;
            save_sysconf("Other","SignalTimeOut");
            #if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
            if(val == 6)
                sprintf(buf, "10%s", language_settings_display_minute_text);
            else if(val == 1)
                sprintf(buf, "1%s", language_settings_display_minute_text);
            else if(val == 2)
                sprintf(buf, "2%s", language_settings_display_minute_text);
            else if(val == 3)
                sprintf(buf, "4%s", language_settings_display_minute_text);
            else if(val == 4)
                sprintf(buf, "6%s", language_settings_display_minute_text);
            else if(val == 5)
                sprintf(buf, "8%s", language_settings_display_minute_text);
            #else
            if(val == 6)
                sprintf(buf, "%s", language_settings_display_disable_text);
            else if(val == 1)
                sprintf(buf, "5%s", language_settings_display_minute_text);
            else if(val == 2)
                sprintf(buf, "10%s", language_settings_display_minute_text);
            else if(val == 3)
                sprintf(buf, "30%s", language_settings_display_minute_text);
            else if(val == 4)
                sprintf(buf, "45%s", language_settings_display_minute_text);
            else if(val == 5)
                sprintf(buf, "60%s", language_settings_display_minute_text);
            #endif
            lv_label_set_text_fmt(signal_timeout_label,"%s :  %s",language_settings_other_paging_timeout_text,buf);

        }
    }
}



static void clean_zone_btn_event_cb(lv_obj_t * bt, lv_event_t e)
{
    if(e == LV_EVENT_CLICKED ) {
        Reset_Zone_proc();
    }
}


static void access_by_password_btn_event_cb(lv_obj_t *sw, lv_event_t e)
{
    if (e == LV_EVENT_VALUE_CHANGED)
    {
        if (lv_switch_get_state(sw))
        {
            printf("switch on\n");
            enable_password_access_settings=1;
        }
        else
        {
            printf("switch off\n");
            enable_password_access_settings=0;
        }
        save_sysconf("Other","Settings_Password_Access");
    }
}

static void msg_backToLogin_event_cb(lv_obj_t * btnm, lv_event_t e)
{
	if(e == LV_EVENT_DELETE) {//由close产生的事件

		//lv_obj_del_async(lv_obj_get_parent(msg));//删除父窗口则会删除父窗口和其所有子窗口
		//printf("delete\n");
	} else	if(e == LV_EVENT_VALUE_CHANGED) {
		const int *ex_data=lv_event_get_data();
		//printf("lv_event_get_data=%d",*ex_data);
		lv_msgbox_start_auto_close(btnm, 0);
		if(*ex_data == 1)	//确定
		{
            if(IsValidWin(WIN_SETTINGS_MAIN))
            {
                DeleteWin(WIN_SETTINGS_MAIN);
                lv_obj_del(screen_settings);
            }
            if(IsValidWin(WIN_CONTROL))
            {
                DeleteWin(WIN_CONTROL);
                lv_obj_del(control_all_cont);
                //重要，一定要加上这句
                lv_scr_load(screen_login);
                login_win_start();
            }
		}
	}
}

static void logout_btn_event_cb(lv_obj_t * bt, lv_event_t e)
{
    if(e == LV_EVENT_CLICKED ) 
    {
        if(Paging_status == PAGING_START || Paging_status == CALLING_START)
        {
            static const char * btns[] = {"OK", ""};
            lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
            lv_msgbox_add_btns(msg, btns);
            lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
            lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
            lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
            if(Paging_status == PAGING_START)
                lv_msgbox_set_text(msg,language_control_message_stopPaging_text);
            if(Paging_status == CALLING_START)
                lv_msgbox_set_text(msg,language_control_message_stopIntercom_text);
            return;
        }

        int i;
        for(i=0;i<MAX_SIDEBAR_BTN_NUM;i++)
        {
            setting_right_area[i]=NULL;

            if(i == SIDER_BTN_SYSINFO)
            {
                systemInfo_ip=NULL;
                systemInfo_serverOnline=NULL;
                systemInfo_networkMode=NULL;
                systemInfo_SerialNum=NULL;
            }
        }
        settings_current_item=DEFAULT_SETTINGS_ITEM;

        if(IsValidWin(WIN_CONTROL))
        {
            static char * btns[] = {"Cancel", "OK",""};
            btns[0] = language_messagebox_cancel_text;
            btns[1] = language_messagebox_confirm_text;
            lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
            lv_msgbox_add_btns(msg, btns);
            lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
            //lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
            lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
            lv_obj_set_event_cb(msg, msg_backToLogin_event_cb);
            lv_msgbox_start_auto_close(msg, 5000);
            lv_msgbox_set_text(msg,language_control_message_logout_text);
        }
        else if(IsValidWin(WIN_LOGIN))
        {
            DeleteWin(WIN_SETTINGS_MAIN);
            lv_scr_load(screen_login);
            lv_obj_del(screen_settings);
        }
    }
}


/***其他设置***/
void enter_other_win()
{
    //右侧显示区域
    setting_right_area[settings_current_item] = lv_cont_create(settings_all_cont, settings_right_area_common);
   
    //启用密码显示
    lv_obj_t * access_by_password_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(access_by_password_label,"%s",language_settings_other_password_access_text);
    lv_obj_set_style_local_text_color(access_by_password_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(access_by_password_label,120,50);


    lv_obj_t *access_by_password_btn = lv_switch_create(setting_right_area[settings_current_item], NULL);
    lv_obj_align(access_by_password_btn, access_by_password_label, LV_ALIGN_OUT_RIGHT_MID,80,0);
    lv_obj_set_event_cb(access_by_password_btn, access_by_password_btn_event_cb);
    lv_obj_set_width(access_by_password_btn, 65);
    lv_obj_set_height(access_by_password_btn, 30);
    if(enable_password_access_settings)
        lv_switch_on(access_by_password_btn,LV_ANIM_OFF);


    //分区寻呼默认音量标签显示
    paging_zone_volume_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(paging_zone_volume_label,"%s : %d",language_settings_other_zone_paging_volume_text,g_paging_vol);
    lv_obj_set_style_local_text_color(paging_zone_volume_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(paging_zone_volume_label,120,50+70);

    //创建分区寻呼默认音量滑动条
	lv_obj_t *paging_zone_volume_slider = lv_slider_create(setting_right_area[settings_current_item], NULL);
	lv_slider_set_value(paging_zone_volume_slider, g_paging_vol, LV_ANIM_OFF);
    lv_slider_set_range(paging_zone_volume_slider,30,100);
	lv_obj_set_pos(paging_zone_volume_slider,110,50+60+50);
	lv_obj_set_event_cb(paging_zone_volume_slider, other_zone_volume_event_cb);
	lv_obj_set_width(paging_zone_volume_slider, 350);
	lv_obj_set_height(paging_zone_volume_slider, 10);
    
    //信号超时时间标签显示
    signal_timeout_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    static char buf[16]={0};
    #if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
    if(g_Signal_Timeout_level == 6)
        sprintf(buf, "10%s", language_settings_display_minute_text);
    else if(g_Signal_Timeout_level == 1)
        sprintf(buf, "1%s", language_settings_display_minute_text);
    else if(g_Signal_Timeout_level == 2)
        sprintf(buf, "2%s", language_settings_display_minute_text);
    else if(g_Signal_Timeout_level == 3)
        sprintf(buf, "4%s", language_settings_display_minute_text);
    else if(g_Signal_Timeout_level == 4)
        sprintf(buf, "6%s", language_settings_display_minute_text);
    else if(g_Signal_Timeout_level == 5)
        sprintf(buf, "8%s", language_settings_display_minute_text);
    #else
    if(g_Signal_Timeout_level == 6)
        sprintf(buf, "%s", language_settings_display_disable_text);
    else if(g_Signal_Timeout_level == 1)
        sprintf(buf, "5%s", language_settings_display_minute_text);
    else if(g_Signal_Timeout_level == 2)
        sprintf(buf, "10%s", language_settings_display_minute_text);
    else if(g_Signal_Timeout_level == 3)
        sprintf(buf, "30%s", language_settings_display_minute_text);
    else if(g_Signal_Timeout_level == 4)
        sprintf(buf, "45%s", language_settings_display_minute_text);
    else if(g_Signal_Timeout_level == 5)
        sprintf(buf, "60%s", language_settings_display_minute_text);
    #endif
    lv_label_set_text_fmt(signal_timeout_label,"%s :  %s",language_settings_other_paging_timeout_text,buf);
    lv_obj_set_style_local_text_color(signal_timeout_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(signal_timeout_label,120,50+60+50+50);

    //创建信号超时滑动条
	lv_obj_t *SignalAutoClose_slider = lv_slider_create(setting_right_area[settings_current_item], NULL);
	lv_slider_set_value(SignalAutoClose_slider, g_Signal_Timeout_level, LV_ANIM_OFF);
    lv_slider_set_range(SignalAutoClose_slider,1,6);
	lv_obj_set_pos(SignalAutoClose_slider,110,50+60+50+50+50);
	lv_obj_set_event_cb(SignalAutoClose_slider, SignalAutoClose_slider_event_cb);
	lv_obj_set_width(SignalAutoClose_slider, 350);
	lv_obj_set_height(SignalAutoClose_slider, 10);


    //重置分区按钮
    lv_obj_t *clean_zone_btn = lv_btn_create(setting_right_area[settings_current_item], NULL);
    lv_btn_toggle(clean_zone_btn);
    lv_obj_t *lv_clean_zone_label = lv_label_create(clean_zone_btn, NULL);
    lv_label_set_text(lv_clean_zone_label ,language_settings_other_reset_zones_text);
    lv_obj_set_event_cb(clean_zone_btn, clean_zone_btn_event_cb);
    #if !HIDE_CONTROL_BACK_BUTTON
    lv_obj_set_pos(clean_zone_btn,210,50+60+50+50+50+70);
    #else
    lv_obj_set_pos(clean_zone_btn,110,50+60+50+50+50+70);

    lv_obj_t *logout_btn = lv_btn_create(setting_right_area[settings_current_item], NULL);
    lv_btn_toggle(logout_btn);
    lv_obj_t *lv_log_out_label = lv_label_create(logout_btn, NULL);
    lv_label_set_text(lv_log_out_label ,language_settings_other_logout_text);
    lv_obj_set_event_cb(logout_btn, logout_btn_event_cb);
    lv_obj_set_pos(logout_btn,320,50+60+50+50+50+70);
    #endif
}





static void mic_aux_switch_event_cb(lv_obj_t *sw, lv_event_t e)
{
    if (e == LV_EVENT_VALUE_CHANGED)
    {
        if (lv_switch_get_state(sw))
        {
            printf("switch on\n");
            g_AuxIn_enable=1;
        }
        else
        {
            printf("switch off\n");
            g_AuxIn_enable=0;
            #if SUPPORT_AUTO_TRIGGER
            g_auto_trigger_enable=0;
            Paging_auto_trigger_change(0,0); //停止自动触发
            #endif
        }
        #if USE_ASM9260
        Bp1048_Send_Set_Features();
        #endif
        save_sysconf("Mic","AuxIn_Enable");
    }
}


static void mic_sensitivity_slider_event_cb(lv_obj_t * obj, lv_event_t e)
{
     if(e == LV_EVENT_VALUE_CHANGED) {
        if(lv_slider_get_type(obj) == LV_SLIDER_TYPE_NORMAL) {
            int val=lv_slider_get_value(obj);
            g_mic_sensitivity=val;
            lv_label_set_text_fmt(mic_sensitivity_label,"%s : %d dB",language_settings_mic_sensitivity_text,g_mic_sensitivity-11);
            #if defined(USE_SSD212) || defined(USE_SSD202)
            set_mic_multiplier_val();
            #else
            Bp1048_Send_Set_Features();
            #endif
            save_sysconf("Mic","Mic_Sensitivity");
        }
    }
}


/***麦克风设置***/
void enter_mic_win()
{
    //右侧显示区域
    setting_right_area[settings_current_item] = lv_cont_create(settings_all_cont, settings_right_area_common);
   
    //启用线路输入显示
    lv_obj_t * mic_aux_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(mic_aux_label,"%s",language_settings_mic_mixAux_text);
    lv_obj_set_style_local_text_color(mic_aux_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(mic_aux_label,120,50);


    lv_obj_t *mic_aux_switch_btn = lv_switch_create(setting_right_area[settings_current_item], NULL);
    lv_obj_align(mic_aux_switch_btn, mic_aux_label, LV_ALIGN_OUT_RIGHT_MID,120,0);
    lv_obj_set_event_cb(mic_aux_switch_btn, mic_aux_switch_event_cb);
    lv_obj_set_width(mic_aux_switch_btn, 65);
    lv_obj_set_height(mic_aux_switch_btn, 30);
    if(g_AuxIn_enable)
        lv_switch_on(mic_aux_switch_btn,LV_ANIM_OFF);


    //麦克风灵敏度标签显示
    mic_sensitivity_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(mic_sensitivity_label,"%s : %d dB",language_settings_mic_sensitivity_text,g_mic_sensitivity-11);
    lv_obj_set_style_local_text_color(mic_sensitivity_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(mic_sensitivity_label,120,50+70);

    //创建麦克风灵敏度滑动条
	lv_obj_t *mic_sensitivity_slider = lv_slider_create(setting_right_area[settings_current_item], NULL);
	lv_slider_set_value(mic_sensitivity_slider, g_mic_sensitivity, LV_ANIM_OFF);   //default-10dB
    lv_slider_set_range(mic_sensitivity_slider,1,21);   //+-10dB
	lv_obj_set_pos(mic_sensitivity_slider,110,50+60+60);
	lv_obj_set_event_cb(mic_sensitivity_slider, mic_sensitivity_slider_event_cb);
	lv_obj_set_width(mic_sensitivity_slider, 350);
	lv_obj_set_height(mic_sensitivity_slider, 10);
}



/***监听设置***/
void enter_listen_win()
{
    //右侧显示区域
    setting_right_area[settings_current_item] = lv_cont_create(settings_all_cont, settings_right_area_common);
   
    //监听音量标签显示
    listen_volume_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(listen_volume_label,"%s : %d",language_settings_output_listenVol_text,g_listen_vol);
    lv_obj_set_style_local_text_color(listen_volume_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(listen_volume_label,120,50);

    //监听音量滑动条
	lv_obj_t *listen_volume_slider = lv_slider_create(setting_right_area[settings_current_item], NULL);
	lv_slider_set_value(listen_volume_slider, g_listen_vol, LV_ANIM_OFF);
    lv_slider_set_range(listen_volume_slider,0,100);
	lv_obj_set_pos(listen_volume_slider,110,50+60);
	lv_obj_set_event_cb(listen_volume_slider, listen_volume_event_cb);
	lv_obj_set_width(listen_volume_slider, 350);
	lv_obj_set_height(listen_volume_slider, 10);

#if ENABLE_CALL_FUNCTION
    if(g_isSupportCall)
    {
        //对讲音量标签显示
        call_volume_label = lv_label_create(setting_right_area[settings_current_item], NULL);
        lv_label_set_text_fmt(call_volume_label,"%s : %d",language_settings_output_intercomVol_text,g_call_vol);
        lv_obj_set_style_local_text_color(call_volume_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
        lv_obj_set_pos(call_volume_label,120,50+60+50);

        //对讲音量滑动条
        lv_obj_t *call_volume_slider = lv_slider_create(setting_right_area[settings_current_item], NULL);
        lv_slider_set_value(call_volume_slider, g_call_vol, LV_ANIM_OFF);
        lv_slider_set_range(call_volume_slider,0,100);
        lv_obj_set_pos(call_volume_slider,110,50+60+50+60);
        lv_obj_set_event_cb(call_volume_slider, call_volume_event_cb);
        lv_obj_set_width(call_volume_slider, 350);
        lv_obj_set_height(call_volume_slider, 10);


        //铃声音量标签显示
        ring_volume_label = lv_label_create(setting_right_area[settings_current_item], NULL);
        lv_label_set_text_fmt(ring_volume_label,"%s : %d",language_settings_output_ringVol_text,g_ring_vol);
        lv_obj_set_style_local_text_color(ring_volume_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
        lv_obj_set_pos(ring_volume_label,120,50+60+50+60+50);

        //铃声音量滑动条
        lv_obj_t *ring_volume_slider = lv_slider_create(setting_right_area[settings_current_item], NULL);
        lv_slider_set_value(ring_volume_slider, g_ring_vol, LV_ANIM_OFF);
        lv_slider_set_range(ring_volume_slider,0,100);
        lv_obj_set_pos(ring_volume_slider,110,50+60+50+60+50+60);
        lv_obj_set_event_cb(ring_volume_slider, ring_volume_event_cb);
        lv_obj_set_width(ring_volume_slider, 350);
        lv_obj_set_height(ring_volume_slider, 10);
    }
#endif
}



static void network_type_switch_event_cb(lv_obj_t *sw, lv_event_t e)
{
    if (e == LV_EVENT_VALUE_CHANGED)
    {
        if (lv_switch_get_state(sw))
        {
            temp_g_IP_Assign=IP_ASSIGN_DHCP;
        }
        else
        {
            temp_g_IP_Assign=IP_ASSIGN_STATIC;
        }

        if(temp_g_IP_Assign == IP_ASSIGN_DHCP)
        {
            lv_obj_set_state(network_ip_label,LV_STATE_DISABLED);
            lv_obj_set_state(network_subnet_label,LV_STATE_DISABLED);
            lv_obj_set_state(network_gateway_label,LV_STATE_DISABLED);
            lv_obj_set_state(network_ip_textarea,LV_STATE_DISABLED);
            lv_obj_set_state(network_subnet_textarea,LV_STATE_DISABLED);
            lv_obj_set_state(network_gateway_textarea,LV_STATE_DISABLED);
        }
        else
        {
            lv_obj_set_state(network_ip_label,LV_STATE_DEFAULT);
            lv_obj_set_state(network_subnet_label,LV_STATE_DEFAULT);
            lv_obj_set_state(network_gateway_label,LV_STATE_DEFAULT);
            lv_obj_set_state(network_ip_textarea,LV_STATE_DEFAULT);
            lv_obj_set_state(network_subnet_textarea,LV_STATE_DEFAULT);
            lv_obj_set_state(network_gateway_textarea,LV_STATE_DEFAULT);
        }

        if(temp_g_IP_Assign!=g_IP_Assign)
        {
            lv_obj_set_state(network_save_btn, LV_STATE_CHECKED);
        }
        else
        {
            lv_obj_set_state(network_save_btn, LV_STATE_DISABLED);
        }
        
    }
}

static lv_obj_t * kb;

static void kb_event_cb(lv_obj_t * _kb, lv_event_t e)
{
    lv_keyboard_def_event_cb(kb, e);

    if(e == LV_EVENT_CANCEL || e == LV_EVENT_APPLY) {
        if(kb) {

                lv_obj_set_hidden(network_ip_label,false);
                lv_obj_set_hidden(network_subnet_label,false);
                lv_obj_set_hidden(network_gateway_label,false);
                lv_obj_set_hidden(network_ip_textarea,false);
                lv_obj_set_hidden(network_subnet_textarea,false);
                lv_obj_set_hidden(network_gateway_textarea,false);


                lv_obj_set_y(network_subnet_label,network_subnet_label_posy);
                lv_obj_set_y(network_subnet_textarea,network_subnet_textarea_posy);
                lv_obj_set_y(network_gateway_label,network_gateway_label_posy);
                lv_obj_set_y(network_gateway_textarea,(network_gateway_textarea_posy));

                lv_obj_set_hidden(network_save_btn,false);

            lv_obj_del(kb);


            //判断数据是否变化
            const char *ip = lv_textarea_get_text(network_ip_textarea);
            const char *subnet = lv_textarea_get_text(network_subnet_textarea);
            const char *gateway = lv_textarea_get_text(network_gateway_textarea);
            if(strcmp(ip,g_Static_ip_address)!=0 || strcmp(subnet,g_Subnet_Mask)!=0 || strcmp(gateway,g_GateWay)!=0)
                lv_obj_set_state(network_save_btn, LV_STATE_CHECKED);
            else if(g_IP_Assign == temp_g_IP_Assign)
            {
                lv_obj_set_state(network_save_btn, LV_STATE_DISABLED);
            }

            kb = NULL;
        }
    }
}

static void network_ta_event_cb(lv_obj_t * ta, lv_event_t e)
{
    if(ta->state & LV_STATE_DISABLED)
        return;
    if(e == LV_EVENT_RELEASED) {
        if(kb == NULL) {
            if(ta == network_ip_textarea)
            {
                lv_obj_set_hidden(network_subnet_label,true);
                lv_obj_set_hidden(network_gateway_label,true);
                lv_obj_set_hidden(network_subnet_textarea,true);
                lv_obj_set_hidden(network_gateway_textarea,true);
            }
            else if(ta == network_subnet_textarea)
            {
                lv_obj_set_hidden(network_ip_label,true);
                lv_obj_set_hidden(network_gateway_label,true);
                lv_obj_set_hidden(network_ip_textarea,true);
                lv_obj_set_hidden(network_gateway_textarea,true);

                lv_obj_set_y(network_subnet_label,lv_obj_get_y(network_ip_label));
                lv_obj_set_y(network_subnet_textarea,lv_obj_get_y(network_ip_textarea));
            }
            else if(ta == network_gateway_textarea)
            {
                lv_obj_set_hidden(network_ip_label,true);
                lv_obj_set_hidden(network_subnet_label,true);
                lv_obj_set_hidden(network_ip_textarea,true);
                lv_obj_set_hidden(network_subnet_textarea,true);

                lv_obj_set_y(network_gateway_label,lv_obj_get_y(network_ip_label));
                lv_obj_set_y(network_gateway_textarea,lv_obj_get_y(network_ip_textarea));
            }

            lv_obj_set_hidden(network_save_btn,true);
        	
            kb = lv_keyboard_create(lv_scr_act(), NULL);
            lv_keyboard_set_mode(kb,LV_KEYBOARD_MODE_NUM);
            lv_obj_set_event_cb(kb, kb_event_cb);

            lv_indev_wait_release(lv_indev_get_act());
        }
        lv_textarea_set_cursor_hidden(ta, false);

        lv_keyboard_set_textarea(kb, ta);
    } else if(e == LV_EVENT_DEFOCUSED) {
        lv_textarea_set_cursor_hidden(ta, true);
    }
    else  if(e == LV_EVENT_VALUE_CHANGED) {
        //const char * txt = lv_textarea_get_text(ta);
       
    }
}

int isValidIP_GatewayByNetmask(const char *ip,const char *mask,const char *gw);
static void network_save_btn_event_cb(lv_obj_t * bt, lv_event_t e)
{
    if(e == LV_EVENT_CLICKED ) {
        int vaild=0;
        static const char * btns[] = {"OK", ""};
        lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
        lv_msgbox_add_btns(msg, btns);
        lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_24);
        char tips[128]={0};

        if(temp_g_IP_Assign == IP_ASSIGN_STATIC)
        {
            const char *ip = lv_textarea_get_text(network_ip_textarea);
            const char *subnet = lv_textarea_get_text(network_subnet_textarea);
            const char *gateway = lv_textarea_get_text(network_gateway_textarea);
            int result;
            if((result=isValidIP_GatewayByNetmask(ip,subnet,gateway)) == 0)
            {
                printf("IP info Succeed");
                vaild=1;
            }
            else
            {
                printf("IP info error:%d\n",result);
            }


            if( result == 0 )
            {
                sprintf(tips,"%s",language_settings_network_settingsOK_text);

                sprintf(g_Static_ip_address,"%s",ip);
                sprintf(g_Subnet_Mask,"%s",subnet);
                sprintf(g_GateWay,"%s",gateway);
            }
            else if(result == -1)
            {
                sprintf(tips,"%s",language_settings_network_inputCorrectIP_text);
            }
            else if(result == -2)
            {
                sprintf(tips,"%s",language_settings_network_inputCorrectSubNetMask_text);
            }
            else if(result == -3)
            {
                sprintf(tips,"%s",language_settings_network_inputCorrectGateWay_text);
            }
            
            lv_msgbox_set_text(msg,tips);
            
        }
        else
        {
           vaild=1;
           sprintf(tips,"%s",language_settings_network_settingsOK_text);
           lv_msgbox_set_text(msg,tips);
        }
        if(vaild)
        {
            g_IP_Assign=temp_g_IP_Assign;
            save_sysconf("Network",NULL);

            //reboot
			System_Reboot();
        }
    }
}


/***网络设置***/
void enter_network_win()
{
    temp_g_IP_Assign = g_IP_Assign;
    //右侧显示区域
    setting_right_area[settings_current_item] = lv_cont_create(settings_all_cont, settings_right_area_common);
   
    //启用线路输入显示
    network_type_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(network_type_label,"%s","DHCP");
    lv_obj_set_style_local_text_color(network_type_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    lv_obj_set_pos(network_type_label,120,50);


    network_type_switch = lv_switch_create(setting_right_area[settings_current_item], NULL);
    lv_obj_align(network_type_switch, network_type_label, LV_ALIGN_OUT_RIGHT_MID,120,0);
    lv_obj_set_event_cb(network_type_switch, network_type_switch_event_cb);
    lv_obj_set_width(network_type_switch, 65);
    lv_obj_set_height(network_type_switch, 30);


    network_ip_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(network_ip_label,"%s",language_settings_network_ip_text);
    lv_obj_set_style_local_text_color(network_ip_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    if(IS_DISP_RES_1024)
	{
        lv_obj_set_pos(network_ip_label,120,50+60+60);
    }
    else if(IS_DISP_RES_1280)
    {
        lv_obj_set_pos(network_ip_label,120,50+60+60);
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_set_pos(network_ip_label,120,50+30+60);
    }

    network_ip_textarea = lv_textarea_create(setting_right_area[settings_current_item], NULL);
    lv_textarea_set_text(network_ip_textarea,"");
    lv_textarea_set_one_line(network_ip_textarea, true);
    if(IS_DISP_RES_1024)
    {
        lv_obj_set_width(network_ip_textarea, 180);
    }
    else if(IS_DISP_RES_1280)
    {
        lv_obj_set_width(network_ip_textarea, 180);
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_set_width(network_ip_textarea, 160);
    }
    lv_textarea_set_cursor_hidden(network_ip_textarea, true);
    lv_obj_align(network_ip_textarea, network_ip_label, LV_ALIGN_OUT_RIGHT_MID,0,0);
    lv_obj_set_x(network_ip_textarea,lv_obj_get_x(network_type_switch));
    lv_obj_set_style_local_bg_color(network_ip_textarea, LV_OBJ_PART_MAIN, LV_STATE_DISABLED, LV_COLOR_GRAY);


    lv_obj_set_event_cb(network_ip_textarea, network_ta_event_cb);

    network_subnet_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(network_subnet_label,"%s",language_settings_network_subnetMask_text);
    lv_obj_set_style_local_text_color(network_subnet_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    if(IS_DISP_RES_1024)
	{
        lv_obj_set_pos(network_subnet_label,120,50+60+60+60);
    }
    else if(IS_DISP_RES_1280)
    {
        lv_obj_set_pos(network_subnet_label,120,50+60+60+80);
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_set_pos(network_subnet_label,120,50+30+60+60);
    }

    network_subnet_textarea = lv_textarea_create(setting_right_area[settings_current_item], network_ip_textarea);
    lv_obj_align(network_subnet_textarea, network_subnet_label, LV_ALIGN_OUT_RIGHT_MID,0,0);
    lv_obj_set_x(network_subnet_textarea,lv_obj_get_x(network_type_switch));

    network_gateway_label = lv_label_create(setting_right_area[settings_current_item], NULL);
    lv_label_set_text_fmt(network_gateway_label,"%s",language_settings_network_gateway_text);
    lv_obj_set_style_local_text_color(network_gateway_label, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
    if(IS_DISP_RES_1024)
	{
        lv_obj_set_pos(network_gateway_label,120,50+60+60+60+60);
    }
    else if(IS_DISP_RES_1280)
    {
        lv_obj_set_pos(network_gateway_label,120,50+60+60+80+80);
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_set_pos(network_gateway_label,120,50+30+60+60+60);
    }

    network_gateway_textarea = lv_textarea_create(setting_right_area[settings_current_item], network_ip_textarea);
    lv_obj_align(network_gateway_textarea, network_gateway_label, LV_ALIGN_OUT_RIGHT_MID,0,0);
    lv_obj_set_x(network_gateway_textarea,lv_obj_get_x(network_type_switch));

    network_subnet_label_posy=lv_obj_get_y(network_subnet_label);
    network_gateway_label_posy=lv_obj_get_y(network_gateway_label);
    network_subnet_textarea_posy=lv_obj_get_y(network_subnet_textarea);
    network_gateway_textarea_posy=lv_obj_get_y(network_gateway_textarea);


    //保存按钮
    network_save_btn = lv_btn_create(setting_right_area[settings_current_item], NULL);
    lv_btn_toggle(network_save_btn);
    lv_obj_t *lv_network_save_label = lv_label_create(network_save_btn, NULL);
    lv_label_set_text(lv_network_save_label ,language_settings_network_settings_text);
    if(IS_DISP_RES_1024)
	{
        lv_obj_align(network_save_btn, network_subnet_textarea, LV_ALIGN_OUT_RIGHT_MID,120,0);
    }
    else if(IS_DISP_RES_1280)
	{
        lv_obj_align(network_save_btn, network_subnet_textarea, LV_ALIGN_OUT_RIGHT_MID,150,0);
    }
    else if(IS_DISP_RES_800)
    {
        lv_obj_align(network_save_btn, network_subnet_textarea, LV_ALIGN_OUT_RIGHT_MID,50,0);
    }
    lv_obj_set_event_cb(network_save_btn, network_save_btn_event_cb);


    lv_obj_set_state(network_save_btn, LV_STATE_DISABLED);
    if(temp_g_IP_Assign == IP_ASSIGN_DHCP)
    {
        lv_switch_on(network_type_switch,LV_ANIM_OFF);
        /*
        lv_obj_set_hidden(network_ip_label,true);
        lv_obj_set_hidden(network_subnet_label,true);
        lv_obj_set_hidden(network_gateway_label,true);

        lv_obj_set_hidden(network_ip_textarea,true);
        lv_obj_set_hidden(network_subnet_textarea,true);
        lv_obj_set_hidden(network_gateway_textarea,true);
        */

       lv_obj_set_state(network_ip_label,LV_STATE_DISABLED);
       lv_obj_set_state(network_subnet_label,LV_STATE_DISABLED);
       lv_obj_set_state(network_gateway_label,LV_STATE_DISABLED);
       lv_obj_set_state(network_ip_textarea,LV_STATE_DISABLED);
       lv_obj_set_state(network_subnet_textarea,LV_STATE_DISABLED);
       lv_obj_set_state(network_gateway_textarea,LV_STATE_DISABLED);
    }
    else
    {
        lv_textarea_set_text(network_ip_textarea,g_Static_ip_address);
        lv_textarea_set_text(network_subnet_textarea,g_Subnet_Mask);
        lv_textarea_set_text(network_gateway_textarea,g_GateWay);
    }
    
}








static void account_kb_event_cb(lv_obj_t * _kb, lv_event_t e)
{
    lv_keyboard_def_event_cb(kb, e);

    if(e == LV_EVENT_CANCEL || e == LV_EVENT_APPLY) {
        if(kb) {

            lv_obj_set_y(page_account_modify,lv_obj_get_y(page_account_modify)+50);
            lv_obj_del(kb);
            kb = NULL;
        }
    }
}

static void account_ta_event_cb(lv_obj_t * ta, lv_event_t e)
{
    if(ta->state & LV_STATE_DISABLED)
        return;
    if(e == LV_EVENT_RELEASED) {
        if(kb == NULL) {

            lv_obj_set_y(page_account_modify,lv_obj_get_y(page_account_modify)-50);
        	
            kb = lv_keyboard_create(lv_scr_act(), NULL);
            if(ta == account_userName_ta)
                lv_keyboard_set_mode(kb,LV_KEYBOARD_MODE_TEXT_LOWER);
            else
                lv_keyboard_set_mode(kb,LV_KEYBOARD_MODE_NUM);
            lv_obj_set_event_cb(kb, account_kb_event_cb);

            lv_indev_wait_release(lv_indev_get_act());
        }
        lv_textarea_set_cursor_hidden(ta, false);

        lv_keyboard_set_textarea(kb, ta);
    } else if(e == LV_EVENT_DEFOCUSED) {
        lv_textarea_set_cursor_hidden(ta, true);
    }
    else  if(e == LV_EVENT_VALUE_CHANGED) {
       
    }
}


static void account_btn_event_cb(lv_obj_t * btn, lv_event_t e)
{
    int i,k;
    if(e == LV_EVENT_CLICKED ) {
        if(btn == account_add_btn)      //新增账户
        {
             //取消选中行
            if(account_user_selected>0)
            {
                for(k=0;k<lv_table_get_col_cnt(account_table);k++)
                {
                    lv_table_set_cell_type(account_table, account_user_selected, k, 1); //将这行全部取消
                }
                lv_obj_invalidate(account_table);
                account_user_selected=0;
            }
            lv_textarea_set_text(account_userName_ta,"");
            lv_textarea_set_text(account_passord_ta,"");
            lv_obj_set_style_local_value_str(acoount_modify_box, LV_CONT_PART_MAIN, LV_STATE_DEFAULT,"新增账户");
            lv_checkbox_set_checked(account_admin_cb,false);
            lv_obj_set_state(account_admin_cb,LV_STATE_DEFAULT);
            lv_obj_set_hidden(page_account_modify,false);
            lv_obj_set_state(account_userName_ta,LV_STATE_DEFAULT);

           
        }
        else  if(btn == account_del_btn)    //删除账户
        {
            //刷新账户表格
            lv_obj_set_hidden(page_account_modify,true);
            if(account_user_selected>1)
            {
                Delete_User_Info(account_user_selected-1);

                lv_table_set_row_cnt(account_table,1);
                account_table_refresh();
                account_user_selected=0;
            }
            else
            {
                //提示不能删除系统管理员账户
            }
            
        }
        else if(btn == account_save_btn)       //账户保存
        {
            //判断用户名跟密码是否符合规则
            const char *userName = lv_textarea_get_text(account_userName_ta);
            const char *passwd = lv_textarea_get_text(account_passord_ta);
            int Isadmin=lv_checkbox_is_checked(account_admin_cb);


            static const char * btns[] = {"OK", ""};
            lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
            lv_msgbox_add_btns(msg, btns);
            lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
		    lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
            lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_26);
            char tips[128]={0};
           
            
            if(strlen(userName)>0)
            {
                if(account_user_selected>1)
                {
                    Set_Save_User_Info(account_user_selected-1,userName,passwd,Isadmin,m_stUser_Info.userInfo[account_user_selected-1].passwordRemember);
                }

                account_table_refresh();
                sprintf(tips,"%s","保存成功");
            }
            else
            {
                printf("save error,strlen(userName) = 0\n");
                sprintf(tips,"%s","用户名不能为空，请重新输入");
            }
             lv_msgbox_set_text(msg,tips);
        }
    }
}




static void table_event_cb(lv_obj_t * table, lv_event_t e)
{
    if(e == LV_EVENT_CLICKED) {
        uint16_t row;
        uint16_t col;
        int i,k=0;
 
        lv_res_t r = lv_table_get_pressed_cell(table, &row, &col);
        if(r == LV_RES_OK && row) {  /*Skip the first row*/
#if 0
            //检测哪一行是被选中的
            for(i=0;i<lv_table_get_row_cnt(table);i++)
            {
                if(lv_table_get_cell_type(table,i,1) == 4)  //已被选中
                {
                    for(k=0;k<lv_table_get_col_cnt(table);k++)
                    {
                        lv_table_set_cell_type(table, i, k, 1); //将这行全部取消
                    }
                }
            }
#endif          
            if(account_user_selected)
            {
                for(k=0;k<lv_table_get_col_cnt(table);k++)
                {
                    lv_table_set_cell_type(table, account_user_selected, k, 1);
                }
            }
            if(account_user_selected!=row)
            {
                account_user_selected=row;
                for(k=0;k<lv_table_get_col_cnt(table);k++)
                {
                    lv_table_set_cell_type(table, row, k, 4);
                }
                lv_obj_set_style_local_value_str(acoount_modify_box, LV_CONT_PART_MAIN, LV_STATE_DEFAULT,"编辑账户");
                lv_textarea_set_text(account_userName_ta,m_stUser_Info.userInfo[account_user_selected-1].name);
                if(account_user_selected != 1)  //不是管理员才显示几秒钟的密码
                    lv_textarea_set_text(account_passord_ta,m_stUser_Info.userInfo[account_user_selected-1].password);
                else if(strlen(m_stUser_Info.userInfo[0].password)>0)
                    lv_textarea_set_text(account_passord_ta,"******");
                else
                     lv_textarea_set_text(account_passord_ta,"");
                
                
                lv_obj_set_hidden(page_account_modify,false);
                if(account_user_selected!=1)    //admin不允许删除
                {
                    //lv_obj_set_hidden(account_del_btn,false);
                    lv_checkbox_set_checked(account_admin_cb,m_stUser_Info.userInfo[account_user_selected-1].authority?true:false);
                    lv_obj_set_state(account_admin_cb,LV_STATE_DEFAULT);
                    lv_obj_set_state(account_userName_ta,LV_STATE_DEFAULT);
                }
                else
                {
                    lv_obj_set_hidden(account_del_btn,true);
                    lv_checkbox_set_checked(account_admin_cb,true);
                    lv_obj_set_state(account_admin_cb,LV_STATE_DISABLED);
                    lv_obj_set_state(account_userName_ta,LV_STATE_DISABLED);    //用户名不允许修改
                }
                
            }
            else
            {
                account_user_selected=0;
               lv_obj_set_hidden(page_account_modify,true);
               lv_obj_set_hidden(account_del_btn,true);
            }
            

            lv_obj_invalidate(table);
        }
    }
}



void account_table_refresh()
{
    int i;
    for(i=0;i<m_stUser_Info.TotalUser;i++)
    {
        char num[10]={0};
        sprintf(num,"%d",i+1);
        
        lv_table_set_cell_value(account_table, i+1, 0, num);
        lv_table_set_cell_value(account_table, i+1, 1, m_stUser_Info.userInfo[i].name);
        lv_table_set_cell_value(account_table, i+1, 2, m_stUser_Info.userInfo[i].authority?"是":"否");
        printf("userName=%s\n",m_stUser_Info.userInfo[i].name);
    }
}


/***账户设置***/
void enter_account_win()
{
    //右侧显示区域
    setting_right_area[settings_current_item] = lv_cont_create(settings_all_cont, settings_right_area_common);


    static lv_style_t style_cell4;
    lv_style_reset(&style_cell4);  //重置样式释放内存
    lv_style_init(&style_cell4);
    lv_style_set_bg_opa(&style_cell4, LV_STATE_DEFAULT, LV_OPA_50);
    lv_style_set_bg_color(&style_cell4, LV_STATE_DEFAULT, lv_color_make(0,161,181));
    lv_style_set_text_font(&style_cell4, LV_STATE_DEFAULT, &font20);


    page_account_list = lv_page_create(setting_right_area[settings_current_item] ,NULL);
    lv_obj_set_size(page_account_list, 400, 480);
    lv_obj_set_pos(page_account_list,30,40);
    lv_coord_t table_w_max = lv_page_get_width_fit(page_account_list);
    lv_page_set_scroll_propagation(page_account_list, true);

    account_add_btn = lv_btn_create(page_account_list, NULL);
    lv_btn_toggle(account_add_btn);
    lv_obj_t *account_add_btn_label = lv_label_create(account_add_btn, NULL);
    //lv_label_set_text(account_add_btn_label ,"新增账户");
    lv_label_set_text(account_add_btn_label ,"账户信息");
    lv_obj_add_style(account_add_btn_label, LV_OBJ_PART_MAIN, &style_font_20);
    //lv_obj_set_event_cb(account_add_btn, account_btn_event_cb);

    account_del_btn = lv_btn_create(page_account_list, NULL);
    lv_btn_toggle(account_del_btn);
    lv_obj_t *account_del_btn_label = lv_label_create(account_del_btn, NULL);
    lv_label_set_text(account_del_btn_label ,"删除账户");
    lv_obj_add_style(account_del_btn_label, LV_OBJ_PART_MAIN, &style_font_20);
    lv_obj_set_x(account_del_btn,200);
    //lv_obj_set_event_cb(account_del_btn, account_btn_event_cb);


    lv_obj_set_hidden(account_del_btn,true);


    account_table = lv_table_create(page_account_list, NULL);
    lv_obj_add_style(account_table, LV_TABLE_PART_CELL1, &style_font_20);
    lv_obj_add_style(account_table, LV_TABLE_PART_CELL4, &style_cell4);
    lv_obj_set_event_cb(account_table, table_event_cb);

    lv_obj_set_y(account_table,50);

    /*Clean the background style of the table because it is placed on a page*/
    lv_obj_clean_style_list(account_table, LV_TABLE_PART_BG);
    lv_obj_set_drag_parent(account_table, true);
    lv_table_set_col_cnt(account_table, 3);
#if 1
    lv_table_set_col_width(account_table, 0, LV_MATH_MAX(30, 1 * table_w_max / 5));
    lv_table_set_col_width(account_table, 1, LV_MATH_MAX(60, 2 * table_w_max / 5));
    lv_table_set_col_width(account_table, 2, LV_MATH_MAX(60, 2 * table_w_max / 5));
#endif

    lv_table_set_cell_value(account_table, 0, 0, "序号");
    lv_table_set_cell_value(account_table, 0, 1, "用户名");
    lv_table_set_cell_value(account_table, 0, 2, "管理权限");

    account_table_refresh();
    #if 0
    lv_table_set_cell_value(account_table, 1, 0, "1");
    lv_table_set_cell_value(account_table, 2, 0, "2");
    lv_table_set_cell_value(account_table, 3, 0, "3");
    lv_table_set_cell_value(account_table, 4, 0, "4");
    lv_table_set_cell_value(account_table, 5, 0, "5");
    lv_table_set_cell_value(account_table, 6, 0, "6");

    lv_table_set_cell_value(account_table, 0, 1, "用户名");
    lv_table_set_cell_value(account_table, 1, 1, "Mark");
    lv_table_set_cell_value(account_table, 2, 1, "Jacob");
    lv_table_set_cell_value(account_table, 3, 1, "John");
    lv_table_set_cell_value(account_table, 4, 1, "Emily");
    lv_table_set_cell_value(account_table, 5, 1, "Ivan");
    lv_table_set_cell_value(account_table, 6, 1, "George");


    lv_table_set_cell_value(account_table, 1, 2, "是");
    lv_table_set_cell_value(account_table, 2, 2, "是");
    lv_table_set_cell_value(account_table, 3, 2, "否");
    lv_table_set_cell_value(account_table, 4, 2, "否");
    lv_table_set_cell_value(account_table, 5, 2, "是");
    lv_table_set_cell_value(account_table, 6, 2, "是");
    
    //lv_table_ext_t * ext = lv_obj_get_ext_attr(table);
    //ext->row_h[0] = 20;
    #endif




    page_account_modify = lv_page_create(setting_right_area[settings_current_item] ,NULL);
    lv_obj_set_size(page_account_modify, 350, 400);
    lv_obj_set_pos(page_account_modify,470,80);
    lv_obj_add_style(page_account_modify, LV_OBJ_PART_MAIN, &style_font_20);

    //提示框
    acoount_modify_box = lv_obj_create(page_account_modify, NULL);
    lv_obj_set_size(acoount_modify_box, lv_obj_get_height_fit(page_account_modify)-50, 50);
    lv_obj_set_x(acoount_modify_box,0);
    lv_obj_set_style_local_value_str(acoount_modify_box, LV_CONT_PART_MAIN, LV_STATE_DEFAULT,"编辑账户");
    lv_obj_set_style_local_value_font(acoount_modify_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font20);
    lv_obj_set_click(acoount_modify_box,false);

    account_userName_ta = lv_textarea_create(page_account_modify, NULL);
    lv_textarea_set_text(account_userName_ta,"");
    lv_textarea_set_one_line(account_userName_ta, true);

    lv_obj_set_pos(account_userName_ta,100,100);
    lv_obj_set_width(account_userName_ta, lv_obj_get_width_grid(page_account_modify, 2, 1)+20);

    lv_textarea_set_cursor_hidden(account_userName_ta, true);
    lv_obj_set_style_local_value_str(account_userName_ta, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, "用户名");
    lv_obj_set_style_local_value_font(account_userName_ta, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font20);
    lv_obj_set_style_local_value_align(account_userName_ta,LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_ALIGN_OUT_LEFT_MID);
    lv_obj_set_style_local_value_ofs_x(account_userName_ta,LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, -30);

   //lv_obj_set_event_cb(account_userName_ta, account_ta_event_cb);

    account_passord_ta = lv_textarea_create(page_account_modify, NULL);
    lv_textarea_set_pwd_mode(account_passord_ta, true);
    lv_textarea_set_text(account_passord_ta,"");
    lv_textarea_set_one_line(account_passord_ta, true);

    lv_obj_set_pos(account_passord_ta,100,160);
    lv_obj_set_width(account_passord_ta, lv_obj_get_width_grid(page_account_modify, 2, 1)+20);

    lv_textarea_set_cursor_hidden(account_passord_ta, true);
    lv_obj_set_style_local_value_str(account_passord_ta, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, "密码");
    lv_obj_set_style_local_value_font(account_passord_ta, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font20);
    lv_obj_set_style_local_value_align(account_passord_ta,LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_ALIGN_OUT_LEFT_MID);
    lv_obj_set_style_local_value_ofs_x(account_passord_ta,LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, -30);

    //lv_obj_set_event_cb(account_passord_ta, account_ta_event_cb);

    account_admin_cb = lv_checkbox_create(page_account_modify,NULL);
    lv_obj_set_style_local_value_font(account_admin_cb, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font20);
    lv_checkbox_set_text(account_admin_cb,"管理权限");
    lv_obj_set_pos(account_admin_cb,100,220);


    account_save_btn = lv_btn_create(page_account_modify, NULL);
    lv_btn_toggle(account_save_btn);
    lv_obj_t *account_save_btn_label = lv_label_create(account_save_btn, NULL);
    lv_label_set_text(account_save_btn_label ,"保存设置");
    lv_obj_align(account_save_btn,page_account_modify,LV_ALIGN_IN_BOTTOM_MID,0,-40);
    lv_obj_set_event_cb(account_save_btn, account_btn_event_cb);

    lv_obj_set_hidden(page_account_modify,true);
    account_user_selected=0;
}



void settings_win_language_change() 
{   
    int i=0;
    if(GetCurrentWin() != WIN_SETTINGS_MAIN)
        return;
    lv_label_set_text(settings_head_label, language_settings_title_text);
    for(i=0;i<MAX_SIDEBAR_BTN_NUM;i++)
    {
        lv_obj_set_style_local_value_str(setting_sidebar_btn[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, settings_btn_nameCN[i]);
    }
    if(settings_current_item == SIDER_BTN_BACKLIGHT)
    {
        //删除原来的右侧显示区域
        if(setting_right_area[settings_current_item])
        {
            lv_obj_del(setting_right_area[settings_current_item]);
            setting_right_area[settings_current_item]=NULL;
        }
        enter_backlight_win();
    }
}