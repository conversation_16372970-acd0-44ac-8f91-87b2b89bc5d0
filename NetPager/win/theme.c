/*
 * cheme.c
 *
 *  Created on: Sep 22, 2020
 *      Author: king
 */

#include "theme.h"
#include "sysconf.h"


st_control_button_list control_button_list[MAX_CONTROL_NUM];
st_control_button_list musiclist_button_list[MAX_MUSICLIST_NUM];

#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
st_control_button_list	lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_MAX_MUSICLIST_NUM];
st_control_button_list	lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_MAX_MUSICLIST_NUM];
#endif

extern int g_PlayMode;
const lv_img_dsc_t *img_PlayMode[5]={&pic_singlePlay,&pic_singleRepeat,&pic_sequencyPlay,&pic_listLoop,&pic_shufflePlay};
char text_PlayMode[5][16]={{"单曲播放"},{"单曲循环"},{"顺序播放"},{"列表循环"},{"随机播放"}};

const lv_img_dsc_t *img_Udisk_PlayMode[5]={&pic_singlePlay,&pic_singleRepeat,&pic_listLoop,&pic_sequencyPlay,&pic_shufflePlay};
char text_Udisk_PlayMode[5][16]={{"单曲播放"},{"单曲循环"},{"列表循环"},{"顺序播放"},{"随机播放"}};


lv_color_t get_theme_color()
{
	#if (APP_TYPE == APP_ALL_GENERAL)
	return LV_COLOR_MAKE(76,175,80);
	#elif (APP_TYPE == APP_AISP_GENERAL)
	return LV_COLOR_MAKE(234,81,25);
	#elif (APP_TYPE == APP_SCGJ_GENERAL)
	return LV_COLOR_MAKE(0xF0,0x83,0x1E);
	#elif (APP_TYPE == APP_JUSBE_GENERAL || APP_TYPE == APP_JUSBE_MIXER)
	return LV_COLOR_MAKE(0xba,0x51,0xd0);
	#elif (APP_TYPE == APP_BLUE_GENERAL)
	return LV_COLOR_MAKE(30,175,196);
	#elif (APP_TYPE == APP_CYAN_GENERAL)
	return LV_COLOR_MAKE(00,202,227);
	#elif (APP_TYPE == APP_RED_GENERAL)
	return LV_COLOR_MAKE(233,30,99);
	#elif (APP_TYPE == APP_C6A1)
	return LV_COLOR_MAKE(00,202,227);

	#elif (APP_TYPE == APP_LZY_COMMERCIAL_STANDARD) || (APP_TYPE == APP_LZY_COMMERCIAL_TRANSMITTER)
	return LV_COLOR_MAKE(30,175,196);

	#elif (APP_TYPE == APP_AIPU_GENERAL || APP_TYPE == APP_AIPU_MIXER)
	return LV_COLOR_MAKE(71,125,213);

	#else
	return LV_COLOR_MAKE(76,175,80);
	#endif
}


void init_control_button()
{
	if(g_bp1048_info.firmware_type == BP1048_FW_NORMAL_TYPE)
	{
		control_button_list[CONTROL_BUTTON_ZONEGROUP].pic = pic_zoneGroup;
		control_button_list[CONTROL_BUTTON_SELECTALL].pic = pic_selectAll;
		//control_button_list[CONTROL_BUTTON_CANCEL].pic = pic_cancel;
		control_button_list[CONTROL_BUTTON_PAGING].pic = pic_paging;
		control_button_list[CONTROL_BUTTON_CALL].pic = pic_call;
		control_button_list[CONTROL_BUTTON_LISTEN].pic = pic_headphones;
		control_button_list[CONTROL_BUTTON_MUSIC].pic = pic_music;
		control_button_list[CONTROL_BUTTON_STOP].pic = pic_stop;
		control_button_list[CONTROL_BUTTON_VOLUME].pic = pic_volume;
		control_button_list[CONTROL_BUTTON_SETTINGS].pic = pic_settings;
		
		if(g_isSupportCall)
		{
			sprintf(control_button_list[CONTROL_BUTTON_ZONEGROUP].nameCN,language_control_button_controlType_text);
		}
		else
		{
			sprintf(control_button_list[CONTROL_BUTTON_ZONEGROUP].nameCN,language_control_button_zoneOrGroup_text);
		}

		sprintf(control_button_list[CONTROL_BUTTON_SELECTALL].nameCN,language_control_button_selectAll_text);
		//strcpy(control_button_list[CONTROL_BUTTON_CANCEL].nameCN,"取消");
		#if(IS_TRANSMITTER)
		sprintf(control_button_list[CONTROL_BUTTON_PAGING].nameCN,language_control_button_transmit_text);
		#else
		sprintf(control_button_list[CONTROL_BUTTON_PAGING].nameCN,language_control_button_paging_text);
		#endif
		sprintf(control_button_list[CONTROL_BUTTON_CALL].nameCN,language_control_button_intercom_text);
		sprintf(control_button_list[CONTROL_BUTTON_LISTEN].nameCN,language_control_button_listen_text);
		sprintf(control_button_list[CONTROL_BUTTON_MUSIC].nameCN,language_control_button_NetMusic_text);
		sprintf(control_button_list[CONTROL_BUTTON_STOP].nameCN,language_control_button_stop_text);
		sprintf(control_button_list[CONTROL_BUTTON_VOLUME].nameCN,language_control_button_volume_text);
		sprintf(control_button_list[CONTROL_BUTTON_SETTINGS].nameCN,language_control_button_settings_text);
	}
	else if(g_bp1048_info.firmware_type == BP1048_FW_UDISK_TYPE)
	{
		control_button_list[CONTROL_BUTTON_ZONEGROUP].pic = pic_zoneGroup;
		control_button_list[CONTROL_BUTTON_SELECTALL].pic = pic_selectAll;
		//control_button_list[CONTROL_BUTTON_CANCEL].pic = pic_cancel;
		control_button_list[CONTROL_BUTTON_PAGING].pic = pic_paging;
		control_button_list[CONTROL_BUTTON_CALL].pic = pic_call;
		control_button_list[CONTROL_BUTTON_LISTEN].pic = pic_headphones;
		control_button_list[CONTROL_BUTTON_MUSIC].pic = pic_music;
		control_button_list[CONTROL_BUTTON_UDISK].pic = pic_udisk;
		control_button_list[CONTROL_BUTTON_STOP].pic = pic_stop;
		control_button_list[CONTROL_BUTTON_VOLUME].pic = pic_volume;
		control_button_list[CONTROL_BUTTON_SETTINGS].pic = pic_settings;

		if(g_isSupportCall)
		{
			sprintf(control_button_list[CONTROL_BUTTON_ZONEGROUP].nameCN,language_control_button_controlType_text);
		}
		else
		{
			sprintf(control_button_list[CONTROL_BUTTON_ZONEGROUP].nameCN,language_control_button_zoneOrGroup_text);
		}

		sprintf(control_button_list[CONTROL_BUTTON_SELECTALL].nameCN,language_control_button_selectAll_text);
		//strcpy(control_button_list[CONTROL_BUTTON_CANCEL].nameCN,"取消");
		#if(IS_TRANSMITTER)
		sprintf(control_button_list[CONTROL_BUTTON_PAGING].nameCN,language_control_button_transmit_text);
		#else
		sprintf(control_button_list[CONTROL_BUTTON_PAGING].nameCN,language_control_button_paging_text);
		#endif
		sprintf(control_button_list[CONTROL_BUTTON_CALL].nameCN,language_control_button_intercom_text);
		sprintf(control_button_list[CONTROL_BUTTON_LISTEN].nameCN,language_control_button_listen_text);
		sprintf(control_button_list[CONTROL_BUTTON_MUSIC].nameCN,language_control_button_NetMusic_text);
		sprintf(control_button_list[CONTROL_BUTTON_UDISK].nameCN,language_control_button_udisk_text);
		sprintf(control_button_list[CONTROL_BUTTON_STOP].nameCN,language_control_button_stop_text);
		sprintf(control_button_list[CONTROL_BUTTON_VOLUME].nameCN,language_control_button_volume_text);
		sprintf(control_button_list[CONTROL_BUTTON_SETTINGS].nameCN,language_control_button_settings_text);
	}
	
}



void init_musiclist_button()
{
	sprintf(text_PlayMode[SINGLE_PLAY-1],language_playmode_singlePlay_text);
	sprintf(text_PlayMode[SINGLE_LOOP-1],language_playmode_singleLoop_text);
	sprintf(text_PlayMode[SEQUENCY_PLAY-1],language_playmode_sequencyPlay_text);
	sprintf(text_PlayMode[LIST_LOOP-1],language_playmode_listLoop_text);
	sprintf(text_PlayMode[SHUFFLE-1],language_playmode_shuffle_text);

	sprintf(text_Udisk_PlayMode[SINGLE_PLAY-1],language_playmode_singlePlay_text);
	sprintf(text_Udisk_PlayMode[SINGLE_LOOP-1],language_playmode_singleLoop_text);
	sprintf(text_Udisk_PlayMode[SEQUENCY_PLAY-1],language_playmode_listLoop_text);
	sprintf(text_Udisk_PlayMode[LIST_LOOP-1],language_playmode_sequencyPlay_text);
	sprintf(text_Udisk_PlayMode[SHUFFLE-1],language_playmode_shuffle_text);

	musiclist_button_list[MUSICLIST_BUTTON_PREPLAY].pic = pic_preplay;
	musiclist_button_list[MUSICLIST_BUTTON_PLAY].pic = pic_play;
	musiclist_button_list[MUSICLIST_BUTTON_STOP].pic = pic_stop;
	musiclist_button_list[MUSICLIST_BUTTON_NEXTPLAY].pic = pic_nextplay;

	sprintf(musiclist_button_list[MUSICLIST_BUTTON_PREPLAY].nameCN,language_musiclist_preSong_text);
	sprintf(musiclist_button_list[MUSICLIST_BUTTON_PLAY].nameCN,language_musiclist_play_text);
	sprintf(musiclist_button_list[MUSICLIST_BUTTON_STOP].nameCN,language_musiclist_stop_text);
	sprintf(musiclist_button_list[MUSICLIST_BUTTON_NEXTPLAY].nameCN,language_musiclist_nextSong_text);

	musiclist_button_list[MUSICLIST_BUTTON_PLAYMODE].pic = *img_PlayMode[g_PlayMode-1];
	sprintf(musiclist_button_list[MUSICLIST_BUTTON_PLAYMODE].nameCN,text_PlayMode[g_PlayMode-1]);
	


	#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
	lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_DELETE].pic = pic_delete;
	lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PREPLAY].pic = pic_preplay;
	lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PLAY].pic = pic_play;
	lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_STOP].pic = pic_stop;
	lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_NEXTPLAY].pic = pic_nextplay;
	lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PLAYMODE].pic = *img_PlayMode[g_PlayMode-1];
	sprintf(lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_DELETE].nameCN,language_musiclist_delete_text);
	sprintf(lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PREPLAY].nameCN,language_musiclist_preSong_text);
	sprintf(lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PLAY].nameCN,language_musiclist_play_text);
	sprintf(lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_STOP].nameCN,language_musiclist_stop_text);
	sprintf(lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_NEXTPLAY].nameCN,language_musiclist_nextSong_text);
	sprintf(lzy_server_musiclist_button_list[LZY_SERVER_MUSICLIST_BUTTON_PLAYMODE].nameCN,text_PlayMode[g_PlayMode-1]);

	lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_UPLOAD].pic = pic_upload;
	lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PREPLAY].pic = pic_preplay;
	lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAY].pic = pic_play;
	lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_STOP].pic = pic_stop;
	lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_NEXTPLAY].pic = pic_nextplay;
	lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAYMODE].pic = *img_PlayMode[g_PlayMode-1];
	sprintf(lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_UPLOAD].nameCN,language_musiclist_upload_text);
	sprintf(lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PREPLAY].nameCN,language_musiclist_preSong_text);
	sprintf(lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAY].nameCN,language_musiclist_play_text);
	sprintf(lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_STOP].nameCN,language_musiclist_stop_text);
	sprintf(lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_NEXTPLAY].nameCN,language_musiclist_nextSong_text);
	sprintf(lzy_udisk_musiclist_button_list[LZY_UDISK_MUSICLIST_BUTTON_PLAYMODE].nameCN,text_PlayMode[g_PlayMode-1]);
	#endif
}

