#ifndef LANGUAGE_TEXT_H_
#define LANGUAGE_TEXT_H_


extern char *language_playsource_offline_CN;
extern char *language_playsource_idle_CN;
extern char *language_playsource_localPlay_CN;
extern char *language_playsource_netPlay_CN;
extern char *language_playsource_timing_CN;
extern char *language_playsource_audioCollector_CN;
extern char *language_playsource_intercom_CN;
extern char *language_playsource_paging_CN;
extern char *language_playsource_alarm_CN;
extern char *language_playsource_100V_CN;
extern char *language_playsource_offline_TW;
extern char *language_playsource_idle_TW;
extern char *language_playsource_localPlay_TW;
extern char *language_playsource_netPlay_TW;
extern char *language_playsource_timing_TW;
extern char *language_playsource_audioCollector_TW;
extern char *language_playsource_intercom_TW;
extern char *language_playsource_paging_TW;
extern char *language_playsource_alarm_TW;
extern char *language_playsource_100V_TW;
extern char *language_playsource_offline_EN;
extern char *language_playsource_idle_EN;
extern char *language_playsource_localPlay_EN;
extern char *language_playsource_netPlay_EN;
extern char *language_playsource_timing_EN;
extern char *language_playsource_audioCollector_EN;
extern char *language_playsource_intercom_EN;
extern char *language_playsource_paging_EN;
extern char *language_playsource_alarm_EN;
extern char *language_playsource_100V_EN;

extern char *language_playmode_singlePlay_CN;
extern char *language_playmode_singleLoop_CN;
extern char *language_playmode_sequencyPlay_CN;
extern char *language_playmode_listLoop_CN;
extern char *language_playmode_shuffle_CN;
extern char *language_playmode_singlePlay_TW;
extern char *language_playmode_singleLoop_TW;
extern char *language_playmode_sequencyPlay_TW;
extern char *language_playmode_listLoop_TW;
extern char *language_playmode_shuffle_TW;
extern char *language_playmode_singlePlay_EN;
extern char *language_playmode_singleLoop_EN;
extern char *language_playmode_sequencyPlay_EN;
extern char *language_playmode_listLoop_EN;
extern char *language_playmode_shuffle_EN;

extern char *language_musiclist_root_CN;
extern char *language_musiclist_preSong_CN;
extern char *language_musiclist_play_CN;
extern char *language_musiclist_stop_CN;
extern char *language_musiclist_pause_CN;
extern char *language_musiclist_nextSong_CN;
extern char *language_musiclist_list_CN;
extern char *language_musiclist_song_CN;
extern char *language_musiclist_upload_CN;
extern char *language_musiclist_delete_CN;
extern char *language_musiclist_selectList_CN;
extern char *language_musiclist_selectSong_CN;
extern char *language_musiclist_insertUdisk_CN;
extern char *language_musiclist_udiskLoading_CN;
extern char *language_musiclist_root_TW;
extern char *language_musiclist_preSong_TW;
extern char *language_musiclist_play_TW;
extern char *language_musiclist_stop_TW;
extern char *language_musiclist_pause_TW;
extern char *language_musiclist_nextSong_TW;
extern char *language_musiclist_list_TW;
extern char *language_musiclist_song_TW;
extern char *language_musiclist_upload_TW;
extern char *language_musiclist_delete_TW;
extern char *language_musiclist_selectList_TW;
extern char *language_musiclist_selectSong_TW;
extern char *language_musiclist_insertUdisk_TW;
extern char *language_musiclist_udiskLoading_TW;
extern char *language_musiclist_root_EN;
extern char *language_musiclist_preSong_EN;
extern char *language_musiclist_play_EN;
extern char *language_musiclist_stop_EN;
extern char *language_musiclist_pause_EN;
extern char *language_musiclist_nextSong_EN;
extern char *language_musiclist_list_EN;
extern char *language_musiclist_song_EN;
extern char *language_musiclist_upload_EN;
extern char *language_musiclist_delete_EN;
extern char *language_musiclist_selectList_EN;
extern char *language_musiclist_selectSong_EN;
extern char *language_musiclist_insertUdisk_EN;
extern char *language_musiclist_udiskLoading_EN;


extern char *language_musiclist_song_upload_CN;
extern char *language_musiclist_song_delete_success_CN;
extern char *language_musiclist_song_delete_failed_CN;
extern char *language_musiclist_notSupported_upload_song_CN;
extern char *language_musiclist_insufficient_storage_CN;
extern char *language_musiclist_uploaded_CN;
extern char *language_musiclist_confirm_delete_CN;
extern char *language_musiclist_confirm_upload_head_CN;
extern char *language_musiclist_confirm_upload_tail_CN;
extern char *language_musiclist_upload_transferred_CN;
extern char *language_musiclist_upload_status_ready_CN;
extern char *language_musiclist_upload_status_uploading_CN;
extern char *language_musiclist_upload_status_succeed_CN;
extern char *language_musiclist_upload_status_failed_song_invaild_CN;
extern char *language_musiclist_upload_status_failed_storage_CN;
extern char *language_musiclist_upload_status_failed_CN;
extern char *language_musiclist_upload_close_CN;
extern char *language_musiclist_upload_stop_CN;
extern char *language_musiclist_storage_cacacity_CN; 
extern char *language_musiclist_storage_used_CN;
extern char *language_musiclist_storage_remaining_CN;
extern char *language_musiclist_song_upload_TW;
extern char *language_musiclist_song_delete_success_TW;
extern char *language_musiclist_song_delete_failed_TW;
extern char *language_musiclist_notSupported_upload_song_TW;
extern char *language_musiclist_insufficient_storage_TW;
extern char *language_musiclist_uploaded_TW;
extern char *language_musiclist_confirm_delete_TW;
extern char *language_musiclist_confirm_upload_head_TW;
extern char *language_musiclist_confirm_upload_tail_TW;
extern char *language_musiclist_upload_transferred_TW;
extern char *language_musiclist_upload_status_ready_TW;
extern char *language_musiclist_upload_status_uploading_TW;
extern char *language_musiclist_upload_status_succeed_TW;
extern char *language_musiclist_upload_status_failed_song_invaild_TW;
extern char *language_musiclist_upload_status_failed_storage_TW;
extern char *language_musiclist_upload_status_failed_TW;
extern char *language_musiclist_upload_close_TW;
extern char *language_musiclist_upload_stop_TW;
extern char *language_musiclist_storage_cacacity_TW; 
extern char *language_musiclist_storage_used_TW;
extern char *language_musiclist_storage_remaining_TW;
extern char *language_musiclist_song_upload_EN;
extern char *language_musiclist_song_delete_success_EN;
extern char *language_musiclist_song_delete_failed_EN;
extern char *language_musiclist_notSupported_upload_song_EN;
extern char *language_musiclist_insufficient_storage_EN;
extern char *language_musiclist_uploaded_EN;
extern char *language_musiclist_confirm_delete_EN;
extern char *language_musiclist_confirm_upload_head_EN;
extern char *language_musiclist_confirm_upload_tail_EN;
extern char *language_musiclist_upload_transferred_EN;
extern char *language_musiclist_upload_status_ready_EN;
extern char *language_musiclist_upload_status_uploading_EN;
extern char *language_musiclist_upload_status_succeed_EN;
extern char *language_musiclist_upload_status_failed_song_invaild_EN;
extern char *language_musiclist_upload_status_failed_storage_EN;
extern char *language_musiclist_upload_status_failed_EN;
extern char *language_musiclist_upload_close_EN;
extern char *language_musiclist_upload_stop_EN;
extern char *language_musiclist_storage_cacacity_EN; 
extern char *language_musiclist_storage_used_EN;
extern char *language_musiclist_storage_remaining_EN;

extern char *language_login_title_CN;
extern char *language_login_userName_CN;
extern char *language_login_password_CN;
extern char *language_login_remember_CN;
extern char *language_login_button_CN;
extern char *language_login_tips_password_error_CN;
extern char *language_login_tips_dsp_error_CN;
extern char *language_login_tips_init_network_error_CN;
extern char *language_login_tips_connect_server_error_CN;
extern char *language_login_tips_getUserInfo_error_CN;
extern char *language_login_tips_no_user_error_CN;
extern char *language_login_title_TW;
extern char *language_login_userName_TW;
extern char *language_login_password_TW;
extern char *language_login_remember_TW;
extern char *language_login_button_TW;
extern char *language_login_tips_password_error_TW;
extern char *language_login_tips_dsp_error_TW;
extern char *language_login_tips_init_network_error_TW;
extern char *language_login_tips_connect_server_error_TW;
extern char *language_login_tips_getUserInfo_error_TW;
extern char *language_login_tips_no_user_error_TW;
extern char *language_login_title_EN;
extern char *language_login_userName_EN;
extern char *language_login_password_EN;
extern char *language_login_remember_EN;
extern char *language_login_button_EN;
extern char *language_login_tips_password_error_EN;
extern char *language_login_tips_dsp_error_EN;
extern char *language_login_tips_init_network_error_EN;
extern char *language_login_tips_connect_server_error_EN;
extern char *language_login_tips_getUserInfo_error_EN;
extern char *language_login_tips_no_user_error_EN;

extern char *language_control_button_controlType_CN;
extern char *language_control_button_zoneOrGroup_CN;
extern char *language_control_button_selectAll_CN;
extern char *language_control_button_paging_CN;
extern char *language_control_button_transmit_CN;
extern char *language_control_button_intercom_CN;
extern char *language_control_button_listen_CN;
extern char *language_control_button_NetMusic_CN;
extern char *language_control_button_udisk_CN;
extern char *language_control_button_stop_CN;
extern char *language_control_button_volume_CN;
extern char *language_control_button_settings_CN;
extern char *language_control_button_controlType_TW;
extern char *language_control_button_zoneOrGroup_TW;
extern char *language_control_button_selectAll_TW;
extern char *language_control_button_paging_TW;
extern char *language_control_button_transmit_TW;
extern char *language_control_button_intercom_TW;
extern char *language_control_button_listen_TW;
extern char *language_control_button_NetMusic_TW;
extern char *language_control_button_udisk_TW;
extern char *language_control_button_stop_TW;
extern char *language_control_button_volume_TW;
extern char *language_control_button_settings_TW;
extern char *language_control_button_controlType_EN;
extern char *language_control_button_zoneOrGroup_EN;
extern char *language_control_button_selectAll_EN;
extern char *language_control_button_paging_EN;
extern char *language_control_button_transmit_EN;
extern char *language_control_button_intercom_EN;
extern char *language_control_button_listen_EN;
extern char *language_control_button_NetMusic_EN;
extern char *language_control_button_udisk_EN;
extern char *language_control_button_stop_EN;
extern char *language_control_button_volume_EN;
extern char *language_control_button_settings_EN;

extern char *language_control_button_stop_zone_CN;
extern char *language_control_button_stop_usb_CN;
extern char *language_control_button_intercom_volume_CN;
extern char *language_control_zone_cnt_CN;
extern char *language_control_online_zone_cnt_CN;
extern char *language_control_button_stop_zone_TW;
extern char *language_control_button_stop_usb_TW;
extern char *language_control_button_intercom_volume_TW;
extern char *language_control_zone_cnt_TW;
extern char *language_control_online_zone_cnt_TW;
extern char *language_control_button_stop_zone_EN;
extern char *language_control_button_stop_usb_EN;
extern char *language_control_button_intercom_volume_EN;
extern char *language_control_zone_cnt_EN;
extern char *language_control_online_zone_cnt_EN;


extern char *language_control_zoneInfo_zone_cnt_CN;
extern char *language_control_zoneInfo_online_zone_cnt_CN;
extern char *language_control_zoneInfo_selected_zone_cnt_CN;
extern char *language_control_groupInfo_group_cnt_CN;
extern char *language_control_groupInfo_selected_group_cnt_CN;
extern char *language_control_pagerInfo_pager_cnt_CN;
extern char *language_control_pagerInfo_online_pager_cnt_CN;
extern char *language_control_pagerInfo_selected_pager_cnt_CN;
extern char *language_control_zoneInfo_zone_cnt_TW;
extern char *language_control_zoneInfo_online_zone_cnt_TW;
extern char *language_control_zoneInfo_selected_zone_cnt_TW;
extern char *language_control_groupInfo_group_cnt_TW;
extern char *language_control_groupInfo_selected_group_cnt_TW;
extern char *language_control_pagerInfo_pager_cnt_TW;
extern char *language_control_pagerInfo_online_pager_cnt_TW;
extern char *language_control_pagerInfo_selected_pager_cnt_TW;
extern char *language_control_zoneInfo_zone_cnt_EN;
extern char *language_control_zoneInfo_online_zone_cnt_EN;
extern char *language_control_zoneInfo_selected_zone_cnt_EN;
extern char *language_control_groupInfo_group_cnt_EN;
extern char *language_control_groupInfo_selected_group_cnt_EN;
extern char *language_control_pagerInfo_pager_cnt_EN;
extern char *language_control_pagerInfo_online_pager_cnt_EN;
extern char *language_control_pagerInfo_selected_pager_cnt_EN;

extern char *language_control_type_zone_CN;
extern char *language_control_type_group_CN;
extern char *language_control_type_pager_CN;
extern char *language_control_type_zone_TW;
extern char *language_control_type_group_TW;
extern char *language_control_type_pager_TW;
extern char *language_control_type_zone_EN;
extern char *language_control_type_group_EN;
extern char *language_control_type_pager_EN;

extern char *language_messagebox_confirm_CN;
extern char *language_messagebox_cancel_CN;
extern char *language_messagebox_confirm_TW;
extern char *language_messagebox_cancel_TW;
extern char *language_messagebox_confirm_EN;
extern char *language_messagebox_cancel_EN;

extern char *language_control_message_cancelAlarm_CN;
extern char *language_control_message_cancelAlarm_TW;
extern char *language_control_message_cancelAlarm_EN;
extern char *language_control_message_stopPaging_CN;
extern char *language_control_message_stopPaging_TW;
extern char *language_control_message_stopPaging_EN;
extern char *language_control_message_stopIntercom_CN;
extern char *language_control_message_stopIntercom_TW;
extern char *language_control_message_stopIntercom_EN;
extern char *language_control_message_logout_CN;
extern char *language_control_message_logout_TW;
extern char *language_control_message_logout_EN;
extern char *language_control_message_selectOnlineZone_CN;
extern char *language_control_message_selectOnlineZone_TW;
extern char *language_control_message_selectOnlineZone_EN;
extern char *language_control_message_selectGroupNoOnlineZone_CN;
extern char *language_control_message_selectGroupNoOnlineZone_TW;
extern char *language_control_message_selectGroupNoOnlineZone_EN;
extern char *language_control_message_talkAfterRing_CN;
extern char *language_control_message_talkAfterRing_TW;
extern char *language_control_message_talkAfterRing_EN;
extern char *language_control_message_selectOneIntercomDevice_CN;
extern char *language_control_message_selectOneIntercomDevice_TW;
extern char *language_control_message_selectOneIntercomDevice_EN;
extern char *language_control_message_selectZoneNoIntercom_CN;
extern char *language_control_message_selectZoneNoIntercom_TW;
extern char *language_control_message_selectZoneNoIntercom_EN;
extern char *language_control_message_selectPager_CN;
extern char *language_control_message_selectPager_TW;
extern char *language_control_message_selectPager_EN;
extern char *language_control_message_selectOnePagerToTalk_CN;
extern char *language_control_message_selectOnePagerToTalk_TW;
extern char *language_control_message_selectOnePagerToTalk_EN;
extern char *language_control_message_selectPagerNoIntercom_CN;
extern char *language_control_message_selectPagerNoIntercom_TW;
extern char *language_control_message_selectPagerNoIntercom_EN;
extern char *language_control_message_calledBusy_CN;
extern char *language_control_message_calledBusy_TW;
extern char *language_control_message_calledBusy_EN;
extern char *language_control_message_resetZone_CN;
extern char *language_control_message_resetZone_TW;
extern char *language_control_message_resetZone_EN;

extern char *language_control_call_calling_CN;
extern char *language_control_call_wait_answer_CN;
extern char *language_control_call_wait_reply_CN;
extern char *language_control_call_hangUp_CN;
extern char *language_control_call_answer_CN;
extern char *language_control_call_hangedUp_CN;
extern char *language_control_call_no_answer_CN;
extern char *language_control_call_codecs_not_support_CN;
extern char *language_control_call_busy_CN;
extern char *language_control_call_refused_CN;
extern char *language_control_input_settings_password_CN;
extern char *language_control_call_calling_TW;
extern char *language_control_call_wait_answer_TW;
extern char *language_control_call_wait_reply_TW;
extern char *language_control_call_hangUp_TW;
extern char *language_control_call_answer_TW;
extern char *language_control_call_hangedUp_TW;
extern char *language_control_call_no_answer_TW;
extern char *language_control_call_codecs_not_support_TW;
extern char *language_control_call_busy_TW;
extern char *language_control_call_refused_TW;
extern char *language_control_input_settings_password_TW;
extern char *language_control_call_calling_EN;
extern char *language_control_call_wait_answer_EN;
extern char *language_control_call_wait_reply_EN;
extern char *language_control_call_hangUp_EN;
extern char *language_control_call_answer_EN;
extern char *language_control_call_hangedUp_EN;
extern char *language_control_call_no_answer_EN;
extern char *language_control_call_codecs_not_support_EN;
extern char *language_control_call_busy_EN;
extern char *language_control_call_refused_EN;
extern char *language_control_input_settings_password_EN;

extern char *language_settings_title_CN;
extern char *language_settings_tab_deviceInfo_CN;
extern char *language_settings_tab_network_CN;
extern char *language_settings_tab_mic_CN;
extern char *language_settings_tab_output_CN;
extern char *language_settings_tab_listen_CN;
extern char *language_settings_tab_trigger_CN;
extern char *language_settings_tab_ring_CN;
extern char *language_settings_tab_display_CN;
extern char *language_settings_tab_other_CN;
extern char *language_settings_title_TW;
extern char *language_settings_tab_deviceInfo_TW;
extern char *language_settings_tab_network_TW;
extern char *language_settings_tab_mic_TW;
extern char *language_settings_tab_output_TW;
extern char *language_settings_tab_listen_TW;
extern char *language_settings_tab_trigger_TW;
extern char *language_settings_tab_ring_TW;
extern char *language_settings_tab_display_TW;
extern char *language_settings_tab_other_TW;
extern char *language_settings_title_EN;
extern char *language_settings_tab_deviceInfo_EN;
extern char *language_settings_tab_network_EN;
extern char *language_settings_tab_mic_EN;
extern char *language_settings_tab_output_EN;
extern char *language_settings_tab_listen_EN;
extern char *language_settings_tab_trigger_EN;
extern char *language_settings_tab_ring_EN;
extern char *language_settings_tab_display_EN;
extern char *language_settings_tab_other_EN;

extern char *language_settings_device_version_CN;
extern char *language_settings_device_networkMode_CN;
extern char *language_settings_device_connectStatus_CN;
extern char *language_settings_device_connected_CN;
extern char *language_settings_device_disconnected_CN;
extern char *language_settings_device_serialNumber_CN;
extern char *language_settings_device_netCableNotInsert_CN;
extern char *language_settings_device_netCableNotInsertAndUse4G_CN;
extern char *language_settings_device_version_TW;
extern char *language_settings_device_networkMode_TW;
extern char *language_settings_device_connectStatus_TW;
extern char *language_settings_device_connected_TW;
extern char *language_settings_device_disconnected_TW;
extern char *language_settings_device_serialNumber_TW;
extern char *language_settings_device_netCableNotInsert_TW;
extern char *language_settings_device_netCableNotInsertAndUse4G_TW;
extern char *language_settings_device_version_EN;
extern char *language_settings_device_networkMode_EN;
extern char *language_settings_device_connectStatus_EN;
extern char *language_settings_device_connected_EN;
extern char *language_settings_device_disconnected_EN;
extern char *language_settings_device_serialNumber_EN;
extern char *language_settings_device_netCableNotInsert_EN;
extern char *language_settings_device_netCableNotInsertAndUse4G_EN;

extern char *language_settings_network_ip_CN;
extern char *language_settings_network_subnetMask_CN;
extern char *language_settings_network_gateway_CN;
extern char *language_settings_network_settings_CN;
extern char *language_settings_network_settingsOK_CN;
extern char *language_settings_network_inputCorrectIP_CN;
extern char *language_settings_network_inputCorrectSubNetMask_CN;
extern char *language_settings_network_inputCorrectGateWay_CN;
extern char *language_settings_network_ip_TW;
extern char *language_settings_network_subnetMask_TW;
extern char *language_settings_network_gateway_TW;
extern char *language_settings_network_settings_TW;
extern char *language_settings_network_settingsOK_TW;
extern char *language_settings_network_inputCorrectIP_TW;
extern char *language_settings_network_inputCorrectSubNetMask_TW;
extern char *language_settings_network_inputCorrectGateWay_TW;
extern char *language_settings_network_ip_EN;
extern char *language_settings_network_subnetMask_EN;
extern char *language_settings_network_gateway_EN;
extern char *language_settings_network_settings_EN;
extern char *language_settings_network_settingsOK_EN;
extern char *language_settings_network_inputCorrectIP_EN;
extern char *language_settings_network_inputCorrectSubNetMask_EN;
extern char *language_settings_network_inputCorrectGateWay_EN;

extern char *language_settings_mic_mixAux_CN;
extern char *language_settings_mic_sensitivity_CN;
extern char *language_settings_mic_mixAux_TW;
extern char *language_settings_mic_sensitivity_TW;
extern char *language_settings_mic_mixAux_EN;
extern char *language_settings_mic_sensitivity_EN;

extern char *language_settings_output_listenVol_CN;
extern char *language_settings_output_intercomVol_CN;
extern char *language_settings_output_ringVol_CN;
extern char *language_settings_output_listenVol_TW;
extern char *language_settings_output_intercomVol_TW;
extern char *language_settings_output_ringVol_TW;
extern char *language_settings_output_listenVol_EN;
extern char *language_settings_output_intercomVol_EN;
extern char *language_settings_output_ringVol_EN;

extern char *language_settings_ring_pagingRing_CN;
extern char *language_settings_ring_pagingRing_TW;
extern char *language_settings_ring_pagingRing_EN;

extern char *language_settings_display_brightness_CN;
extern char *language_settings_display_automatic_screenOff_CN;
extern char *language_settings_display_minute_CN;
extern char *language_settings_display_disable_CN;
extern char *language_settings_display_language_CN;
extern char *language_settings_display_brightness_TW;
extern char *language_settings_display_automatic_screenOff_TW;
extern char *language_settings_display_minute_TW;
extern char *language_settings_display_disable_TW;
extern char *language_settings_display_language_TW;
extern char *language_settings_display_brightness_EN;
extern char *language_settings_display_automatic_screenOff_EN;
extern char *language_settings_display_minute_EN;
extern char *language_settings_display_disable_EN;
extern char *language_settings_display_language_EN;

extern char *language_settings_other_zone_paging_volume_CN;
extern char *language_settings_other_paging_timeout_CN;
extern char *language_settings_other_reset_zones_CN;
extern char *language_settings_other_password_access_CN;
extern char *language_settings_other_logout_CN;
extern char *language_settings_other_zone_paging_volume_TW;
extern char *language_settings_other_paging_timeout_TW;
extern char *language_settings_other_reset_zones_TW;
extern char *language_settings_other_password_access_TW;
extern char *language_settings_other_logout_TW;
extern char *language_settings_other_zone_paging_volume_EN;
extern char *language_settings_other_paging_timeout_EN;
extern char *language_settings_other_reset_zones_EN;
extern char *language_settings_other_password_access_EN;
extern char *language_settings_other_logout_EN;
#endif