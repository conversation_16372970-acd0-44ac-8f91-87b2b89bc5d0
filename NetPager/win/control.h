/*
 * control.h
 *
 *  Created on: Aug 25, 2020
 *      Author: king
 */

#ifndef NETPAGER_CONTROL_H_
#define NETPAGER_CONTROL_H_

#include "lvgl/lvgl.h"
#include "sysconf.h"


typedef struct
{
	lv_obj_t* zone_name;
	lv_obj_t* source_name;
	lv_obj_t* volume_value;
	lv_obj_t* source_icon;
}zone_tx;


#define PAGE_ZONE 0
#define PAGE_GROUP 1
#if ENABLE_CALL_FUNCTION
#define PAGE_PAGER 2
#endif


#define CONTROL_PAGE_ZONE_SHOW_LINE_NUM		(g_screen_width == 1024? 5 : (g_screen_width == 1280 ? 6 : (g_screen_width == 600 ? 3 : 4)))	//控制页面每行显示的分区数量
#define CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM	(g_screen_width == 1024? 20 : (g_screen_width == 1280 ? 30 : (g_screen_width == 600 ? 18 : 12)))	//控制页面每页显示的分区数量

#define ZONE_POS_START_X  (g_screen_width == 600 ? 14 : 12)	//相对于顶栏
#define ZONE_POS_START_Y  12	//相对于顶栏
#define ZONE_WIDTH (g_screen_width == 1024 ? 183 : (g_screen_width == 1280 ? 189 : (g_screen_width == 600 ? 188 : 173)))
#define ZONE_HEIGHT (g_screen_width == 1024 ? 98 : (g_screen_width == 1280 ? 105 : (g_screen_width == 600 ? 98 : 98)))
#define ZONE_BLANK (g_screen_width == 1024 ? 5 : (g_screen_width == 1280 ? 6 : (g_screen_width == 600 ? 5 : 5)))


LV_IMG_DECLARE(audioh_25);
LV_IMG_DECLARE(audiol_25);
LV_IMG_DECLARE(pic_back);
LV_IMG_DECLARE(pic_micMute);
LV_IMG_DECLARE(pic_up);
LV_IMG_DECLARE(pic_down);
LV_IMG_DECLARE(pic_left);
LV_IMG_DECLARE(pic_right);
LV_IMG_DECLARE(mic_on);
LV_IMG_DECLARE(mic_off);

LV_IMG_DECLARE(pic_enter);
LV_IMG_DECLARE(pic_quit);


extern lv_obj_t *control_all_cont;

extern unsigned char control_page_type;

extern lv_obj_t *control_head_label;			//顶栏时间标签

extern lv_obj_t *screen_control;				//控制界面screen

extern lv_obj_t *mic_switch_btn;

#if ENABLE_CALL_FUNCTION
extern lv_obj_t *call_main_cont;				//对讲主容器
extern lv_obj_t *video_call_main_cont;			//视频对讲主容器
#endif

extern lv_obj_t *group_enter_btn;						//进入扩展按钮
extern int group_enter_index;					//进入的分组index

#endif /* NETPAGER_CONTROL_H_ */
