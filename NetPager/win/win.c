/*
 * win.c
 *
 *  Created on: Aug 27, 2020
 *      Author: king
 */

#include <stdio.h>
#include <malloc.h>
#include "pthread.h"
#include "win.h"

pthread_mutex_t lvglMutex=PTHREAD_MUTEX_INITIALIZER;
static pthread_mutex_t winChangeMutex=PTHREAD_MUTEX_INITIALIZER;

int lvglMutex_status=0;			//lvgl界面互斥锁状态，1表示已加锁

DoubleLink win_stack;
static char IsWin_init=0;

void InitWinStack()
{
	Dlist_Init(&win_stack);
	IsWin_init=1;
}

int GetCurrentWin()
{
	if(!IsWin_init)
		return -1;
	pthread_mutex_lock(&winChangeMutex);
	int win=win_stack.last->data;
	//printf("current_win=%d\n",win);
	pthread_mutex_unlock(&winChangeMutex);
	return win;

}

void SetCurrentWin(int win)
{
	if(!IsWin_init)
		return;
	pthread_mutex_lock(&winChangeMutex);
	Dlist_push_back(&win_stack,win);
	pthread_mutex_unlock(&winChangeMutex);
}

void DeleteWin(int win)
{
	if(!IsWin_init)
		return;
	pthread_mutex_lock(&winChangeMutex);
	Dlist_delete_val(&win_stack,win);
	pthread_mutex_unlock(&winChangeMutex);
}

int IsValidWin(int win)
{
	if(!IsWin_init)
		return 0;
	if(Dlist_find(&win_stack,win))
		return 1;
	else
		return 0;
}

