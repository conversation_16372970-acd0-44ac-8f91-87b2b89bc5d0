/*
 * zone.c
 *
 *  Created on: Aug 24, 2020
 *      Author: king
 */

#include <stdio.h>
#include "control.h"
#include "theme.h"
#include "win.h"
#include "SaveZoneInfo.h"
#include "SaveUserInfo.h"
#include "MusicFile_Alter.h"
#include "sysconf.h"


lv_obj_t *control_all_cont=NULL;				//控制界面总成
lv_obj_t *control_zone_cont=NULL;			//分区控制栏容器

lv_obj_t *control_zone_cont_arry[30];				//分区控制栏单容器集合
zone_tx control_zone_tx_arry[30];					//分区控制栏单个分区的描述
lv_obj_t *control_zone_bt_pre,*control_zone_bt_next;//分区控制栏上下页按钮
lv_obj_t *control_label_info=NULL;			//页码显示
lv_obj_t *control_head_label=NULL;			//顶栏时间标签

lv_obj_t *volume_slider=NULL;				//音量滑动条

lv_obj_t *back_btn=NULL;	//返回
lv_obj_t *group_enter_btn=NULL;//进入分组扩展按钮
lv_obj_t *mic_switch_btn=NULL;
#if ENABLE_CALL_FUNCTION
lv_obj_t *category_selected_cont=NULL;		//分区/分组/寻呼台类别选择
lv_obj_t *category_selected_btn[3];		//分区/分组/寻呼台类别选择按钮（共3个）

lv_obj_t *call_main_cont=NULL;				//对讲主容器
lv_obj_t *label_callDeviceName=NULL;			//对讲设备名称
lv_obj_t *label_callStatus=NULL;				//对讲提示label
lv_obj_t *call_bt_hangup=NULL;				//对讲挂断按钮
lv_obj_t *call_bt_answer=NULL;				//对讲接听按钮
lv_obj_t *call_bt_video=NULL;				//对讲视频按钮
#endif


lv_obj_t *video_call_main_cont=NULL;			//视频对讲主容器
lv_obj_t *video_call_exit_btn=NULL;			//视频对讲-退出按钮




unsigned char control_zone_click_arry[30];



static lv_style_t style_box_zone_cont;

int control_win_current_page=1;
int control_win_total_page=1;
unsigned char control_page_type = PAGE_ZONE;
int group_enter_index=-1;	//进入扩展分组index


lv_obj_t *screen_control;				//控制界面screen

extern lv_obj_t *screen_login;				//登录界面screen

lv_obj_t *control_password_kb_obj;
static lv_obj_t *password_dialog;
static lv_obj_t *password_textarea;


#if defined(USE_SSD212) || defined(USE_SSD202)
#define stUsbInfo 	udiskInfo
#else
#define stUsbInfo 	bp1048_usb_info
#endif

int control_win_get_vaild_Count()
{
	int i=0,k=0,valid_count=0;
	if(control_page_type == PAGE_ZONE)
	{
		for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
		{
		   if(!m_stZone_Info.zoneInfo[i].g_zone_isHide)
		   {
			   valid_count++;
		   }
		}
	}
	else if(control_page_type == PAGE_GROUP)
	{
		if(group_enter_index == -1)
		{
			for(i=0;i<m_stGroup_Info.TotalGroup;i++)
			{
				if(!m_stGroup_Info.groupInfo[i].g_group_isHide)
				{
					valid_count++;
				}
			}
		}
		else
		{
			//找出这个分组内的有效分区数量
			for(i=0;i<m_stGroup_Info.groupInfo[group_enter_index].ZoneNum;i++)
			{
				unsigned char mac_buf[6]={0};
				memset(mac_buf,0,sizeof(mac_buf));
				memcpy(mac_buf,m_stGroup_Info.groupInfo[group_enter_index].g_zone_mac+i*6,6);
				for(k=0;k<m_stZone_Info.ExistTotalZone;k++)
				{
					if(memcmp(mac_buf,m_stZone_Info.zoneInfo[k].g_zone_mac,6) == 0)
					{
						//找到
						valid_count++;
						break;
					}
				}
			}
		}
	}
	#if ENABLE_CALL_FUNCTION
	else if(control_page_type == PAGE_PAGER)
	{
		for(i=0;i<m_stPager_Info.TotalPager;i++)
		{
			valid_count++;
		}
	}
	#endif
	return valid_count;
}



int control_win_get_online_zone_Count()
{
	int i=0,valid_count=0;

	for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
	{
		if(!m_stZone_Info.zoneInfo[i].g_zone_isHide && m_stZone_Info.zoneInfo[i].g_zone_conection)
		{
			valid_count++;
		}
	}
	
	return valid_count;
}


int control_win_get_total_page()
{
	int i=0,total_page=0;
	int valid_count=control_win_get_vaild_Count();
	if(valid_count%CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM > 0)
		total_page=valid_count/CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM + 1;
	else
		total_page=valid_count/CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;

	return (total_page == 0 ? 1:total_page) ;
}



void control_win_labelInfo_update(int IsSelfcall)
{
	if(GetCurrentWin() != WIN_CONTROL)
			return;
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}
	int i,cnt=0;
	char labelInfo[256]={0};
	if(control_page_type == PAGE_ZONE || control_page_type == PAGE_GROUP)
	{
		for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
		{
			if(m_stZone_Info.zoneInfo[i].g_zone_isSelect && m_stZone_Info.zoneInfo[i].g_zone_conection)
			{
				cnt++;
			}
		}
	}
	#if ENABLE_CALL_FUNCTION
	else if(control_page_type == PAGE_PAGER)
	{
		for(i=0;i<m_stPager_Info.TotalPager;i++)
		{
			if(m_stPager_Info.pagerInfo[i].isSelect && m_stPager_Info.pagerInfo[i].conection)
			{
				cnt++;
			}
		}
	}
	#endif

	char *space_char=NULL;
	if(IS_DISP_RES_1024)
	{
		space_char="                                               ";
	}
	else if(IS_DISP_RES_1280)
    {
        space_char="                                                             ";
    }
	else if(IS_DISP_RES_800)
	{
		space_char="                               ";
	}
	else if(IS_DISP_RES_600)
	{
		space_char="                                               ";
	}

	if(control_page_type == PAGE_ZONE)
	{
		if(language == CHINESE)
		{   
			sprintf(labelInfo,"[ 第 %d 页 , 共 %d 页 ]%s[ %s : %d , %s : %d , %s : %d ]",\
				control_win_current_page,control_win_total_page,space_char,language_control_zoneInfo_zone_cnt_text,strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) == 0 ? m_stZone_Info.ExistTotalZone:m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneCount,\
				language_control_zoneInfo_online_zone_cnt_text,control_win_get_online_zone_Count(),language_control_zoneInfo_selected_zone_cnt_text,cnt);
		}
		else if(language == ZHTW)
		{
			sprintf(labelInfo,"[ 第 %d 頁 , 共 %d 頁 ]%s[ %s : %d , %s : %d , %s : %d ]",\
				control_win_current_page,control_win_total_page,space_char,language_control_zoneInfo_zone_cnt_text,strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) == 0 ? m_stZone_Info.ExistTotalZone:m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneCount,\
				language_control_zoneInfo_online_zone_cnt_text,control_win_get_online_zone_Count(),language_control_zoneInfo_selected_zone_cnt_text,cnt);
		}
		else if(language == ENGLISH)
		{
			sprintf(labelInfo,"[ page %d of %d ]%s[ %s : %d , %s : %d , %s : %d ]",\
			control_win_current_page,control_win_total_page,space_char,language_control_zoneInfo_zone_cnt_text,strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME) == 0 ? m_stZone_Info.ExistTotalZone:m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].ZoneCount,\
			language_control_zoneInfo_online_zone_cnt_text,control_win_get_online_zone_Count(),language_control_zoneInfo_selected_zone_cnt_text,cnt);
		}
	}
	else if(control_page_type == PAGE_GROUP)
	{
		//分组数、已选择的分组数
		int groupCnt=0,selectedGroupCnt = 0;
		for(i=0;i<m_stGroup_Info.TotalGroup;i++)
		{
			if(!m_stGroup_Info.groupInfo[i].g_group_isHide)
				groupCnt++;
			if(m_stGroup_Info.groupInfo[i].g_group_isSelect)
				selectedGroupCnt++;
		}
		if(group_enter_index == -1)
		{
			if(language == CHINESE)
			{
				sprintf(labelInfo,"[ 第 %d 页 , 共 %d 页 ]%s[ %s : %d , %s : %d , %s : %d ]",\
				control_win_current_page,control_win_total_page,space_char,language_control_groupInfo_group_cnt_text,groupCnt,\
				language_control_groupInfo_selected_group_cnt_text,selectedGroupCnt,language_control_zoneInfo_selected_zone_cnt_text,cnt);
			}
			else if(language == ZHTW)
			{
				sprintf(labelInfo,"[ 第 %d 頁 , 共 %d 頁 ]%s[ %s : %d , %s : %d , %s : %d ]",\
				control_win_current_page,control_win_total_page,space_char,language_control_groupInfo_group_cnt_text,groupCnt,\
				language_control_groupInfo_selected_group_cnt_text,selectedGroupCnt,language_control_zoneInfo_selected_zone_cnt_text,cnt);
			}
			else if(language == ENGLISH)
			{
				sprintf(labelInfo,"[ page %d of %d ]%s[ %s : %d , %s : %d , %s : %d ]",\
				control_win_current_page,control_win_total_page,space_char,language_control_groupInfo_group_cnt_text,groupCnt,\
				language_control_groupInfo_selected_group_cnt_text,selectedGroupCnt,language_control_zoneInfo_selected_zone_cnt_text,cnt);
			}
		}
		else
		{
			//分组内在线分区数
			int groupOnlineZoneCnt = 0;
			for(i=0;i<m_stGroup_Info.groupInfo[group_enter_index].ZoneNum;i++)
			{
				//找出分组内的分区index
				int index = Get_ZoneIndex_By_MAC(m_stGroup_Info.groupInfo[group_enter_index].g_zone_mac+6*i);
				if(index>=0)
				{
					if(!m_stZone_Info.zoneInfo[index].g_zone_isHide && m_stZone_Info.zoneInfo[index].g_zone_conection)
					{
						groupOnlineZoneCnt++;
					}
				}
			}
			if(language == CHINESE)
			{
				sprintf(labelInfo,"[ 第 %d 页 , 共 %d 页 ]%s[ %s : %d , %s : %d , %s : %d ]",\
				control_win_current_page,control_win_total_page,space_char,language_control_zoneInfo_zone_cnt_text,m_stGroup_Info.groupInfo[group_enter_index].ZoneNum,\
				language_control_zoneInfo_online_zone_cnt_text,groupOnlineZoneCnt,language_control_zoneInfo_selected_zone_cnt_text,cnt);
			}
			else if(language == ZHTW)
			{
				sprintf(labelInfo,"[ 第 %d 頁 , 共 %d 頁 ]%s[ %s : %d , %s : %d , %s : %d ]",\
				control_win_current_page,control_win_total_page,space_char,language_control_zoneInfo_zone_cnt_text,m_stGroup_Info.groupInfo[group_enter_index].ZoneNum,\
				language_control_zoneInfo_online_zone_cnt_text,groupOnlineZoneCnt,language_control_zoneInfo_selected_zone_cnt_text,cnt);
			}
			else if(language == ENGLISH)
			{
				sprintf(labelInfo,"[ page %d of %d ]%s[ %s : %d , %s : %d , %s : %d ]",\
				control_win_current_page,control_win_total_page,space_char,language_control_zoneInfo_zone_cnt_text,m_stGroup_Info.groupInfo[group_enter_index].ZoneNum,\
				language_control_zoneInfo_online_zone_cnt_text,groupOnlineZoneCnt,language_control_zoneInfo_selected_zone_cnt_text,cnt);
			}
		}
	}
	#if ENABLE_CALL_FUNCTION
	else if(control_page_type == PAGE_PAGER)
	{
		if(language == CHINESE)
		{
			sprintf(labelInfo,"[ 第 %d 页 , 共 %d 页 ]%s[ %s : %d , %s : %d , %s : %d ]",\
				control_win_current_page,control_win_total_page,space_char,language_control_pagerInfo_pager_cnt_text,m_stPager_Info.TotalPager,\
				language_control_pagerInfo_online_pager_cnt_text,m_stPager_Info.OnlinePager,language_control_pagerInfo_selected_pager_cnt_text,cnt);
		}
		else if(language == ZHTW)
		{
			sprintf(labelInfo,"[ 第 %d 頁 , 共 %d 頁 ]%s[ %s : %d , %s : %d , %s : %d ]",\
				control_win_current_page,control_win_total_page,space_char,language_control_pagerInfo_pager_cnt_text,m_stPager_Info.TotalPager,\
				language_control_pagerInfo_online_pager_cnt_text,m_stPager_Info.OnlinePager,language_control_pagerInfo_selected_pager_cnt_text,cnt);
		}
		else if(language == ENGLISH)
		{
			sprintf(labelInfo,"[ page %d of %d ]%s[ %s : %d , %s : %d , %s : %d ]",\
				control_win_current_page,control_win_total_page,space_char,language_control_pagerInfo_pager_cnt_text,m_stPager_Info.TotalPager,\
				language_control_pagerInfo_online_pager_cnt_text,m_stPager_Info.OnlinePager,language_control_pagerInfo_selected_pager_cnt_text,cnt);
		}
	}
	#endif
	lv_label_set_text_fmt(control_label_info, labelInfo);

	#if !LIMIT_MINIMUM_FUNCTION
	#if ENABLE_CALL_FUNCTION
	if(g_bp1048_info.firmware_type == BP1048_FW_UDISK_TYPE && control_page_type != PAGE_PAGER)
	#else
	if(g_bp1048_info.firmware_type == BP1048_FW_UDISK_TYPE)
	#endif
	{
		if(cnt>0)
		{
			//提示：停止分区
			lv_label_set_text(control_button_list[CONTROL_BUTTON_STOP].label, language_control_button_stop_zone_text);
		}
		else if(stUsbInfo.IsPlug)
		{
			//提示：停止USB
			lv_label_set_text(control_button_list[CONTROL_BUTTON_STOP].label, language_control_button_stop_usb_text);
		}
		else
		{
			//提示：停止
			lv_label_set_text(control_button_list[CONTROL_BUTTON_STOP].label, language_control_button_stop_text);
		}
	}
	#endif

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}


void control_win_update(int update_type,int IsSelfcall)
{
	if(GetCurrentWin() != WIN_CONTROL)
		return;
	if(update_type == 0XFF)
		update_type = control_page_type;
	if(update_type !=  control_page_type)
	{
		//处于分组展开页面，也刷新分区状态
		if( (update_type == PAGE_ZONE && control_page_type == PAGE_GROUP && group_enter_index!=-1) )
		{
			update_type = PAGE_GROUP;
		}
		else
		{
			return;
		}
	}

	//更新时间
	UI_UpdateSystemTime(IsSelfcall);

	if(g_bp1048_info.firmware_type == BP1048_FW_UDISK_TYPE)
	{
		//更新USB播放状态
		refresh_usbPlay(IsSelfcall);
	}

	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}

	#if HIDE_CONTROL_BACK_BUTTON
	lv_obj_set_hidden(back_btn,true);
	#endif

	//更新时间
	int i=0,k=0;
	control_win_total_page=control_win_get_total_page();
	if(control_win_current_page<=control_win_total_page && control_win_current_page>=1)
	{
		//OK
	}
	else if(control_win_current_page == 0)
	{
		control_win_current_page=control_win_total_page;
	}
	else
	{
		control_win_current_page=1;
		control_win_current_page=1;
	}

	//更新信息栏
	control_win_labelInfo_update(1);

	//获取当前页有效分区数量
	int page_vaild_count=0;
	if(control_win_current_page<control_win_total_page)
	{
		page_vaild_count=CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;
	}
	else if(control_win_current_page == control_win_total_page)
	{
		int valid_zoneCount=control_win_get_vaild_Count();
		if(valid_zoneCount>0)
		{
			page_vaild_count=valid_zoneCount%CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;
			if(page_vaild_count == 0)
				page_vaild_count=CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;
		}
		else
			page_vaild_count=0;
	}
	int first=(control_win_current_page-1)*CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;
	if(update_type == PAGE_ZONE && strcmp(m_stUser_Info.CurrentUserName,SUPER_USER_NAME)!=0)
	{
		//找到第一个有效分区
		int valid_cnt=-1;
		for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
		{
			if(!m_stZone_Info.zoneInfo[i].g_zone_isHide)
			{
				valid_cnt++;
				if(valid_cnt == first)
				{
					first = i;
					break;
				}
			}
		}
	}
	printf("update_type=%d,first=%d,page_vaild_count=%d\n",update_type,first,page_vaild_count);

	if(update_type == PAGE_ZONE)
	{
		int cnt=0;
		#if 0  //将后面的分区/分组隐藏,加快显示速度(不能一开始先将所有分区控件隐藏)
			//先全部隐藏
			for(i=0;i<9;i++)
			{
				lv_obj_set_hidden(control_zone_cont_arry[i],1);
			}
		#endif
		for(i=0;i<m_stZone_Info.ExistTotalZone && cnt<page_vaild_count;i++)
		{
			//找到当前页第一个可显示的分区所在的位置
			if(m_stZone_Info.zoneInfo[i].g_zone_isHide || i < first)
				continue;
			cnt++;

			int index=cnt-1;
			//printf("i=%d,zone_cnt=%d,index=%d\n",i,zone_cnt,index);

			if(strlen(m_stZone_Info.zoneInfo[i].g_zone_name)>0)
			{
				if(strcmp(lv_label_get_text(control_zone_tx_arry[index].zone_name),m_stZone_Info.zoneInfo[i].g_zone_name))
					lv_label_set_text(control_zone_tx_arry[index].zone_name ,m_stZone_Info.zoneInfo[i].g_zone_name);
			}
			else
			{
				char *ipchar=IPIntToChar(m_stZone_Info.zoneInfo[i].g_zone_ip);
				if(strcmp(lv_label_get_text(control_zone_tx_arry[index].zone_name),ipchar))
					lv_label_set_text(control_zone_tx_arry[index].zone_name ,ipchar);
			}

			if( m_stZone_Info.zoneInfo[i].g_zone_source == SOURCE_LOCAL_PLAY || m_stZone_Info.zoneInfo[i].g_zone_source == SOURCE_NET_PLAY || m_stZone_Info.zoneInfo[i].g_zone_source == SOURCE_TIMING )
			{
				lv_label_set_text_fmt(control_zone_tx_arry[index].source_name ,"%s",Check_FileName(m_stZone_Info.zoneInfo[i].g_zone_media_name));
				//if(strcmp(lv_label_get_text(control_zone_tx_arry[index].source_name),m_stZone_Info.zoneInfo[i].g_zone_media_name))
							//lv_label_set_text_fmt(control_zone_tx_arry[index].source_name ,"%s",m_stZone_Info.zoneInfo[i].g_zone_media_name);
			}
			else
			{
				int source_id=m_stZone_Info.zoneInfo[i].g_zone_source;
				char sourceName[128]={0};
				strcpy(sourceName,Get_Source_Name_ById(m_stZone_Info.zoneInfo[i].g_zone_source));

				if( strcmp(lv_label_get_text(control_zone_tx_arry[index].source_name),sourceName) )
					lv_label_set_text_fmt(control_zone_tx_arry[index].source_name ,"%s",sourceName);
			}

			char vol_char[6]={0};
			sprintf(vol_char,"%d%%",m_stZone_Info.zoneInfo[i].g_zone_vol);
			int source_id=m_stZone_Info.zoneInfo[i].g_zone_source;
			int play_status=m_stZone_Info.zoneInfo[i].g_zone_playStatus;
			if(	source_id == SOURCE_LOCAL_PLAY || source_id == SOURCE_TIMING || (source_id >= SOURCE_AUDIO_COLLECTOR_MIN && source_id <= SOURCE_AUDIO_COLLECTOR_MAX) )
			{
				if(source_id >= SOURCE_AUDIO_COLLECTOR_MIN && source_id <= SOURCE_AUDIO_COLLECTOR_MAX)
					lv_label_set_text_fmt(control_zone_tx_arry[index].volume_value,"%s    %s%d%%",language_playsource_audioCollector_text,language_control_button_volume_text,m_stZone_Info.zoneInfo[i].g_zone_vol);
				else
				{
					lv_label_set_text_fmt(control_zone_tx_arry[index].volume_value,"%s    %s%d%%",Get_Source_Name_ById(m_stZone_Info.zoneInfo[i].g_zone_source),language_control_button_volume_text,m_stZone_Info.zoneInfo[i].g_zone_vol);
					if(source_id == SOURCE_LOCAL_PLAY && play_status == SONG_SUSPEND)
					{
						lv_obj_set_hidden(control_zone_tx_arry[index].source_icon,false);
					}
					else
					{
						lv_obj_set_hidden(control_zone_tx_arry[index].source_icon,true);
					}
				}
			}
			else //if(strcmp(lv_label_get_text(control_zone_tx_arry[index].volume_value),vol_char))
			{
				lv_label_set_text_fmt(control_zone_tx_arry[index].volume_value,"%s%d%%",language_control_button_volume_text,m_stZone_Info.zoneInfo[i].g_zone_vol);
				lv_obj_set_hidden(control_zone_tx_arry[index].source_icon,true);
			}
			if(m_stZone_Info.zoneInfo[i].g_zone_conection && m_stZone_Info.zoneInfo[i].g_zone_isSelect)		//分区在线且已被选择
			{
				lv_obj_set_state(control_zone_cont_arry[index], LV_STATE_CHECKED);
				control_zone_click_arry[index]=1;
			}
			else if(m_stZone_Info.zoneInfo[i].g_zone_conection)							//分区在线但没被选择
			{
				lv_obj_set_state(control_zone_cont_arry[index], LV_STATE_DEFAULT);
				control_zone_click_arry[index]=0;	//加入词句,避免原来选中的分区离线后,再次重新上线第一次点击失败的问题
			}
			else			//分区不在线
			{
				lv_obj_set_state(control_zone_cont_arry[index], LV_STATE_DISABLED);
				control_zone_click_arry[index]=0;	//加入词句,避免原来选中的分区离线后,再次重新上线第一次点击失败的问题
			}

			if(control_zone_cont_arry[index]->hidden)	//如果原来是隐藏状态,则显示
				lv_obj_set_hidden(control_zone_cont_arry[index],0);
		}

		//将后面的分区隐藏,加快显示速度(不能一开始先将所有分区控件隐藏)
		for(i=cnt;i<CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;i++)
			lv_obj_set_hidden(control_zone_cont_arry[i],1);
	}
	else if(update_type == PAGE_GROUP)
	{
		int cnt=0;
		int temCnt=0;

		//如果是在扩展页面,应该显示分区
		if(group_enter_index == -1)
		{
			for(i=0;i<m_stGroup_Info.TotalGroup && cnt<page_vaild_count;i++)
			{
				//找到当前页第一个可显示的分组所在的位置
				if(m_stGroup_Info.groupInfo[i].g_group_isHide)
					continue;
				temCnt++;
				if(temCnt <= first)
				{
					continue;
				}
				cnt++;

				int index=cnt-1;


				if(strcmp(lv_label_get_text(control_zone_tx_arry[index].zone_name),m_stGroup_Info.groupInfo[i].g_group_name))
					lv_label_set_text(control_zone_tx_arry[index].zone_name,m_stGroup_Info.groupInfo[i].g_group_name);
				char cnt_char[32]={0};
				sprintf(cnt_char,"%s:%d",language_control_zone_cnt_text,m_stGroup_Info.groupInfo[i].AccountZoneNum);
				if(strcmp(lv_label_get_text(control_zone_tx_arry[index].source_name),cnt_char))
					lv_label_set_text(control_zone_tx_arry[index].source_name ,cnt_char);

				char onlineZoneCnt_char[32]={0};
				int onlineZoneCnt=0;
				for(k=0;k<m_stGroup_Info.groupInfo[i].ZoneNum;k++)
				{
					//找出分组内的分区index
					int zoneIndex = Get_ZoneIndex_By_MAC(m_stGroup_Info.groupInfo[i].g_zone_mac+6*k);
					if(zoneIndex>=0)
					{
						if(!m_stZone_Info.zoneInfo[zoneIndex].g_zone_isHide && m_stZone_Info.zoneInfo[zoneIndex].g_zone_conection)
						{
							onlineZoneCnt++;
						}
					}
				}
				sprintf(onlineZoneCnt_char,"%s:%d",language_control_online_zone_cnt_text,onlineZoneCnt);
				if(strcmp(lv_label_get_text(control_zone_tx_arry[index].volume_value),onlineZoneCnt_char))
					lv_label_set_text(control_zone_tx_arry[index].volume_value,onlineZoneCnt_char);

				if(m_stGroup_Info.groupInfo[i].g_group_isSelect)
				{
					lv_obj_set_state(control_zone_cont_arry[index], LV_STATE_CHECKED);
				}
				else
				{
					lv_obj_set_state(control_zone_cont_arry[index], LV_STATE_DEFAULT);
				}

				if(control_zone_cont_arry[index]->hidden)	//如果原来是隐藏状态,则显示
					lv_obj_set_hidden(control_zone_cont_arry[index],0);
			}

			//将后面的分组隐藏,加快显示速度(不能一开始先将所有分区控件隐藏)
			for(i=cnt;i<CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;i++)
			{
				lv_obj_set_hidden(control_zone_cont_arry[i],1);
			}

			//如果有且只选中了一个分组，那么显示扩展按钮
			int sectedGroupCnt=0;
			for(i = 0;i<m_stGroup_Info.TotalGroup;i++)
			{
				if(m_stGroup_Info.groupInfo[i].g_group_isSelect)
				{
					sectedGroupCnt++;
				}
			}
			if(sectedGroupCnt == 1)
			{
				//显示扩展按钮
				lv_obj_set_hidden(group_enter_btn,false);
			}
			else
			{
				//隐藏扩展按钮
				lv_obj_set_hidden(group_enter_btn,true);
			}
		}
		else
		{
			//显示扩展按钮
			lv_obj_set_hidden(group_enter_btn,false);

			for(i=0;i<m_stGroup_Info.groupInfo[group_enter_index].ZoneNum && cnt<page_vaild_count;i++)
			{
				//找出分组内的分区index
				int index = Get_ZoneIndex_By_MAC(m_stGroup_Info.groupInfo[group_enter_index].g_zone_mac+6*i+(6*(control_win_current_page-1)*CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM));
				//找到当前页第一个可显示的分区所在的位置
				if(index<0 || m_stZone_Info.zoneInfo[index].g_zone_isHide || index < first)
					continue;
				if(strlen(m_stZone_Info.zoneInfo[index].g_zone_name)>0)
				{
					if(strcmp(lv_label_get_text(control_zone_tx_arry[cnt].zone_name),m_stZone_Info.zoneInfo[index].g_zone_name))
						lv_label_set_text(control_zone_tx_arry[cnt].zone_name ,m_stZone_Info.zoneInfo[index].g_zone_name);
				}
				else
				{
					char *ipchar=IPIntToChar(m_stZone_Info.zoneInfo[index].g_zone_ip);
					if(strcmp(lv_label_get_text(control_zone_tx_arry[cnt].zone_name),ipchar))
						lv_label_set_text(control_zone_tx_arry[cnt].zone_name ,ipchar);
				}

				if( m_stZone_Info.zoneInfo[index].g_zone_source == SOURCE_LOCAL_PLAY || m_stZone_Info.zoneInfo[index].g_zone_source == SOURCE_NET_PLAY || m_stZone_Info.zoneInfo[index].g_zone_source == SOURCE_TIMING )
				{
					lv_label_set_text_fmt(control_zone_tx_arry[cnt].source_name ,"%s",Check_FileName(m_stZone_Info.zoneInfo[index].g_zone_media_name));
				}
				else
				{
					int source_id=m_stZone_Info.zoneInfo[index].g_zone_source;
					char sourceName[128]={0};
					strcpy(sourceName,Get_Source_Name_ById(m_stZone_Info.zoneInfo[index].g_zone_source));

					if( strcmp(lv_label_get_text(control_zone_tx_arry[cnt].source_name),sourceName) )
						lv_label_set_text_fmt(control_zone_tx_arry[cnt].source_name ,"%s",sourceName);
				}

				char vol_char[6]={0};
				sprintf(vol_char,"%d%%",m_stZone_Info.zoneInfo[index].g_zone_vol);
				int source_id=m_stZone_Info.zoneInfo[index].g_zone_source;
				int play_status=m_stZone_Info.zoneInfo[index].g_zone_playStatus;
				if(	source_id == SOURCE_LOCAL_PLAY || source_id == SOURCE_TIMING || (source_id >= SOURCE_AUDIO_COLLECTOR_MIN && source_id <= SOURCE_AUDIO_COLLECTOR_MAX) )
				{
					if(source_id >= SOURCE_AUDIO_COLLECTOR_MIN && source_id <= SOURCE_AUDIO_COLLECTOR_MAX)
						lv_label_set_text_fmt(control_zone_tx_arry[cnt].volume_value,"%s    %s%d%%",language_playsource_audioCollector_text,language_control_button_volume_text,m_stZone_Info.zoneInfo[index].g_zone_vol);
					else
					{
						lv_label_set_text_fmt(control_zone_tx_arry[cnt].volume_value,"%s    %s%d%%",Get_Source_Name_ById(m_stZone_Info.zoneInfo[index].g_zone_source),language_control_button_volume_text,m_stZone_Info.zoneInfo[index].g_zone_vol);
						if(source_id == SOURCE_LOCAL_PLAY && play_status == SONG_SUSPEND)
						{
							lv_obj_set_hidden(control_zone_tx_arry[cnt].source_icon,false);
						}
						else
						{
							lv_obj_set_hidden(control_zone_tx_arry[cnt].source_icon,true);
						}
					}
				}
				else //if(strcmp(lv_label_get_text(control_zone_tx_arry[index].volume_value),vol_char))
				{
					lv_label_set_text_fmt(control_zone_tx_arry[cnt].volume_value,"%s%d%%",language_control_button_volume_text,m_stZone_Info.zoneInfo[index].g_zone_vol);
					lv_obj_set_hidden(control_zone_tx_arry[cnt].source_icon,true);
				}
				if(m_stZone_Info.zoneInfo[index].g_zone_conection && m_stZone_Info.zoneInfo[index].g_zone_isSelect)		//分区在线且已被选择
				{
					lv_obj_set_state(control_zone_cont_arry[cnt], LV_STATE_CHECKED);
					control_zone_click_arry[cnt]=1;
				}
				else if(m_stZone_Info.zoneInfo[index].g_zone_conection)							//分区在线但没被选择
				{
					lv_obj_set_state(control_zone_cont_arry[cnt], LV_STATE_DEFAULT);
					control_zone_click_arry[cnt]=0;	//加入词句,避免原来选中的分区离线后,再次重新上线第一次点击失败的问题
				}
				else			//分区不在线
				{
					lv_obj_set_state(control_zone_cont_arry[cnt], LV_STATE_DISABLED);
					control_zone_click_arry[cnt]=0;	//加入词句,避免原来选中的分区离线后,再次重新上线第一次点击失败的问题
				}

				if(control_zone_cont_arry[cnt]->hidden)	//如果原来是隐藏状态,则显示
					lv_obj_set_hidden(control_zone_cont_arry[cnt],0);

				cnt++;
			}
			//将后面的分区隐藏,加快显示速度(不能一开始先将所有分区控件隐藏)
			for(i=cnt;i<CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;i++)
				lv_obj_set_hidden(control_zone_cont_arry[i],1);
		}
	}
	#if ENABLE_CALL_FUNCTION
	else if(update_type == PAGE_PAGER)
	{
		//刷新音量滑动条-对讲音量
		lv_slider_set_value(volume_slider,g_call_vol,0);

		printf("PAGE_PAGER:page_vaild_count=%d\n",page_vaild_count);
		int cnt=0;
		for(i=0;i<m_stPager_Info.TotalPager && cnt<page_vaild_count;i++)
		{
			//找到当前页第一个可显示的分组所在的位置
			if(i < first)
				continue;
			cnt++;

			int index=cnt-1;


			if(strcmp(lv_label_get_text(control_zone_tx_arry[index].zone_name),m_stPager_Info.pagerInfo[i].name))
				lv_label_set_text(control_zone_tx_arry[index].zone_name,m_stPager_Info.pagerInfo[i].name);
			char ip_char[24]={0};
			sprintf(ip_char,"%s",IPIntToChar(m_stPager_Info.pagerInfo[i].ip));
			if(strcmp(lv_label_get_text(control_zone_tx_arry[index].source_name),ip_char))
				lv_label_set_text(control_zone_tx_arry[index].source_name ,ip_char);

			if(strcmp(lv_label_get_text(control_zone_tx_arry[index].volume_value),""))
				lv_label_set_text(control_zone_tx_arry[index].volume_value,"");

			char sourceName[128]={0};
			strcpy(sourceName,Get_Source_Name_ById(m_stPager_Info.pagerInfo[i].source));

			if( strcmp(lv_label_get_text(control_zone_tx_arry[index].volume_value),sourceName) )
				lv_label_set_text_fmt(control_zone_tx_arry[index].volume_value ,"%s",sourceName);

			if(m_stPager_Info.pagerInfo[i].conection && m_stPager_Info.pagerInfo[i].isSelect)		//分区在线且已被选择
			{
				lv_obj_set_state(control_zone_cont_arry[index], LV_STATE_CHECKED);
			}
			else if(m_stPager_Info.pagerInfo[i].conection)							//分区在线但没被选择
			{
				lv_obj_set_state(control_zone_cont_arry[index], LV_STATE_DEFAULT);
				control_zone_click_arry[index]=0;	//加入词句,避免原来选中的分区离线后,再次重新上线第一次点击失败的问题
			}
			else			//分区不在线
			{
				lv_obj_set_state(control_zone_cont_arry[index], LV_STATE_DISABLED);
				control_zone_click_arry[index]=0;	//加入词句,避免原来选中的分区离线后,再次重新上线第一次点击失败的问题
			}
			if(control_zone_cont_arry[index]->hidden)	//如果原来是隐藏状态,则显示
				lv_obj_set_hidden(control_zone_cont_arry[index],0);
		}

		//将后面的分组隐藏,加快显示速度(不能一开始先将所有分区控件隐藏)
		for(i=cnt;i<CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;i++)
		{
			lv_obj_set_hidden(control_zone_cont_arry[i],1);
		}
	}
	#endif

	if(!IsSelfcall)
	{
		//如果不是在图形线程调用，那么需要刷新一次窗体，否则可能图形异常。注意！！！
		//lv_obj_invalidate(control_all_cont);
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}


void Zone_Group_SelectOrCancel(int isSelect,int id)
{
	int i,k,t,m;
	//printf("SelectOrCancel:id=%d,isSelect=%d\n",id,isSelect);
	if(control_page_type == PAGE_ZONE)
	{
		if(id == 0xFFFF)
		{
			for(i=0;i<CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;i++)
			{
				if(lv_obj_get_state(control_zone_cont_arry[i], LV_OBJ_PART_MAIN) !=LV_STATE_DISABLED)
				{
					control_zone_click_arry[i]=isSelect;
					if(isSelect)
						lv_obj_set_state(control_zone_cont_arry[i], LV_STATE_CHECKED);
					else
						lv_obj_set_state(control_zone_cont_arry[i], LV_STATE_DEFAULT);
				}
			}
			for(i = 0;i<m_stZone_Info.ExistTotalZone;i++)
			{
				if( m_stZone_Info.zoneInfo[i].g_zone_conection && !m_stZone_Info.zoneInfo[i].g_zone_isHide )
					m_stZone_Info.zoneInfo[i].g_zone_isSelect=isSelect;
			}
		}
		else
		{
			//找到有效的分区
			int vaild_cnt=-1;
			for(i = 0;i<m_stZone_Info.ExistTotalZone;i++)
			{
				if(!m_stZone_Info.zoneInfo[i].g_zone_isHide)
					vaild_cnt++;
				if(vaild_cnt == id)
				{
					printf("valid_cnt=%d,i=%d\n",vaild_cnt,i);
					m_stZone_Info.zoneInfo[i].g_zone_isSelect=isSelect;
					break;
				}
			}
		}
	}
	else if(control_page_type == PAGE_GROUP)
	{
		if(id == 0xFFFF)
		{
			if(group_enter_index==-1)
			{
				for(i=0;i<CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;i++)
				{
					control_zone_click_arry[i]=isSelect;
					if(isSelect)
						lv_obj_set_state(control_zone_cont_arry[i], LV_STATE_CHECKED);
					else
						lv_obj_set_state(control_zone_cont_arry[i], LV_STATE_DEFAULT);
				}
				for(m = 0;m<m_stGroup_Info.TotalGroup;m++)
				{
					if(!m_stGroup_Info.groupInfo[m].g_group_isHide)
					{
						m_stGroup_Info.groupInfo[m].g_group_isSelect=isSelect;

						unsigned char mac_buf[6]={0};
						for(i=0;i<m_stGroup_Info.groupInfo[m].ZoneNum;i++)
						{
							memset(mac_buf,0,sizeof(mac_buf));
							memcpy(mac_buf,m_stGroup_Info.groupInfo[m].g_zone_mac+i*6,6);
							for(k=0;k<m_stZone_Info.ExistTotalZone;k++)
							{
								for(t=0;t<6;t++)
								{
									if( mac_buf[t] != m_stZone_Info.zoneInfo[k].g_zone_mac[t] )
									{
										break;
									}
								}
								if(t == 6)
								{
									//找到
									if(m_stZone_Info.zoneInfo[k].g_zone_conection && !m_stZone_Info.zoneInfo[k].g_zone_isHide)
									{
										m_stZone_Info.zoneInfo[k].g_zone_isSelect = m_stGroup_Info.groupInfo[m].g_group_isSelect;
									}
									break;
								}
							}
						}
					}
				}
			}
			else
			{
				for(i=0;i<CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;i++)
				{
					if(lv_obj_get_state(control_zone_cont_arry[i], LV_OBJ_PART_MAIN) !=LV_STATE_DISABLED)
					{
						control_zone_click_arry[i]=isSelect;
						if(isSelect)
							lv_obj_set_state(control_zone_cont_arry[i], LV_STATE_CHECKED);
						else
							lv_obj_set_state(control_zone_cont_arry[i], LV_STATE_DEFAULT);
					}
				}
				
				for(i=0;i<m_stGroup_Info.groupInfo[group_enter_index].ZoneNum;i++)
				{
					int zoneIndex=Get_ZoneIndex_By_MAC(m_stGroup_Info.groupInfo[group_enter_index].g_zone_mac+6*i);
					if(zoneIndex>=0)
					{
						if(!m_stZone_Info.zoneInfo[zoneIndex].g_zone_isHide)
						{
							m_stZone_Info.zoneInfo[zoneIndex].g_zone_isSelect=isSelect;
						}
					}
				}
			}
		}
		else
		{
			if(group_enter_index==-1)
			{
				//找到有效的分组
				int vaild_cnt=-1;
				int g=0;
				for(g = 0;g<m_stGroup_Info.TotalGroup;g++)
				{
					if(!m_stGroup_Info.groupInfo[g].g_group_isHide)
						vaild_cnt++;
					if(vaild_cnt == id)
					{
						m_stGroup_Info.groupInfo[g].g_group_isSelect=isSelect;
						break;
					}
				}

				//先把所有分区设置为不选中
				for(k=0;k<m_stZone_Info.ExistTotalZone;k++)
				{
					m_stZone_Info.zoneInfo[k].g_zone_isSelect = 0;
				}

				for(m = 0;m<m_stGroup_Info.TotalGroup;m++)
				{
					if(!m_stGroup_Info.groupInfo[m].g_group_isHide)
					{
						unsigned char mac_buf[6]={0};
						for(i=0;i<m_stGroup_Info.groupInfo[m].ZoneNum;i++)
						{
							memset(mac_buf,0,sizeof(mac_buf));
							memcpy(mac_buf,m_stGroup_Info.groupInfo[m].g_zone_mac+i*6,6);
							for(k=0;k<m_stZone_Info.ExistTotalZone;k++)
							{
								if(memcmp(mac_buf,m_stZone_Info.zoneInfo[k].g_zone_mac,6) == 0)
								{
									//找到
									if(m_stZone_Info.zoneInfo[k].g_zone_conection && !m_stZone_Info.zoneInfo[k].g_zone_isHide)
									{
										if(m_stGroup_Info.groupInfo[m].g_group_isSelect)
											m_stZone_Info.zoneInfo[k].g_zone_isSelect = 1;
									}
									break;
								}
							}
						}
					}
				}

			}
			else
			{
				//找到有效的分区
				int vaild_cnt=-1;
				for(i = 0;i<m_stGroup_Info.groupInfo[group_enter_index].ZoneNum;i++)
				{
					int zoneIndex=Get_ZoneIndex_By_MAC(m_stGroup_Info.groupInfo[group_enter_index].g_zone_mac+6*i);
					if(!m_stZone_Info.zoneInfo[zoneIndex].g_zone_isHide)
						vaild_cnt++;
					if(vaild_cnt == id)
					{
						m_stZone_Info.zoneInfo[zoneIndex].g_zone_isSelect=isSelect;
						break;
					}
				}
			}
		}

		//未处于分组扩展时
		if(group_enter_index==-1)
		{
			//判断是否只选中了一个分组,且这个分组包含了有效分区,是的话显示扩展按钮进入，否则隐藏
			int sectedGroupCnt=0;
			int selectedGroupIndex=-1;
			for(i = 0;i<m_stGroup_Info.TotalGroup;i++)
			{
				if(m_stGroup_Info.groupInfo[i].g_group_isSelect)
				{
					sectedGroupCnt++;
					selectedGroupIndex=i;
				}
			}
			if(sectedGroupCnt == 1)
			{
				//显示扩展按钮
				if(m_stGroup_Info.groupInfo[selectedGroupIndex].ZoneNum>0)
					lv_obj_set_hidden(group_enter_btn,false);
			}
			else
			{
				//隐藏扩展按钮
				lv_obj_set_hidden(group_enter_btn,true);
			}
		}
	}
#if ENABLE_CALL_FUNCTION
	else if(control_page_type == PAGE_PAGER)
	{
		if(id == 0xFFFF)
		{
			for(i=0;i<CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;i++)
			{
				if(lv_obj_get_state(control_zone_cont_arry[i], LV_OBJ_PART_MAIN) !=LV_STATE_DISABLED)
				{
					control_zone_click_arry[i]=isSelect;
					if(isSelect)
						lv_obj_set_state(control_zone_cont_arry[i], LV_STATE_CHECKED);
					else
						lv_obj_set_state(control_zone_cont_arry[i], LV_STATE_DEFAULT);
				}
			}
			for(i = 0;i<m_stPager_Info.TotalPager;i++)
			{
				m_stPager_Info.pagerInfo[i].isSelect=m_stPager_Info.pagerInfo[i].conection?isSelect:0;
			}
		}
		else
		{
			//找到有效的寻呼台
			int vaild_cnt=-1;
			for(i = 0;i<m_stPager_Info.TotalPager;i++)
			{
				vaild_cnt++;
				if(vaild_cnt == id)
				{
					m_stPager_Info.pagerInfo[i].isSelect=m_stPager_Info.pagerInfo[i].conection?isSelect:0;
					break;
				}
			}
		}
	}
#endif
	if(control_page_type == PAGE_ZONE || control_page_type == PAGE_GROUP)
		Update_Selected_Zone();

	//更新信息栏
	control_win_labelInfo_update(1);
}




void Btn_SelectAllOrCancel()
{
	char IsSelectAll=1;
	int i=0,k=0;
	if(control_page_type == PAGE_ZONE)
	{
		for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
		{
			if(!m_stZone_Info.zoneInfo[i].g_zone_isHide && m_stZone_Info.zoneInfo[i].g_zone_conection)
			{
				if(!m_stZone_Info.zoneInfo[i].g_zone_isSelect)
				{
					IsSelectAll=0;
					break;
				}
			}
		}
	}
	else if(control_page_type == PAGE_GROUP)
	{
		if(group_enter_index == -1)
		{
			for(i=0;i<m_stGroup_Info.TotalGroup;i++)
			{
				if(!m_stGroup_Info.groupInfo[i].g_group_isHide)
				{
					if(!m_stGroup_Info.groupInfo[i].g_group_isSelect)
					{
						IsSelectAll=0;
						break;
					}
				}
			}
		}
		else
		{
			for(i=0;i<m_stGroup_Info.groupInfo[group_enter_index].ZoneNum;i++)
			{
				//找出分组内的分区index
				int index = Get_ZoneIndex_By_MAC(m_stGroup_Info.groupInfo[group_enter_index].g_zone_mac+6*i);
				if(index>=0)
				{
					if(!m_stZone_Info.zoneInfo[index].g_zone_isHide && m_stZone_Info.zoneInfo[index].g_zone_conection)
					{
						if(!m_stZone_Info.zoneInfo[index].g_zone_isSelect)
						{
							IsSelectAll=0;
							break;
						}
					}
				}
			}
		}
	}
	#if ENABLE_CALL_FUNCTION
	else if(control_page_type == PAGE_PAGER)
	{
		for(i=0;i<m_stPager_Info.TotalPager;i++)
		{
			if(m_stPager_Info.pagerInfo[i].conection)
			{
				if(!m_stPager_Info.pagerInfo[i].isSelect)
				{
					IsSelectAll=0;
					break;
				}
			}
		}
	}
	#endif

	if(IsSelectAll)
	{
		Zone_Group_SelectOrCancel(0,0xFFFF);
	}
	else
	{
		Zone_Group_SelectOrCancel(1,0xFFFF);
	}
}


int get_controlzone_id(lv_obj_t* obj)
{
	int i=0;
	int id=-1;
	for(i=0;i<CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;i++)
	{
		if(control_zone_cont_arry[i] == obj)
			id=i;
	}
	return id;
}


static void control_all_cont_event_cb(lv_obj_t * ta, lv_event_t e)
{
	#if ENABLE_CALL_FUNCTION
	if(lv_debug_check_obj_valid(category_selected_cont))
	{
		lv_obj_del_async(category_selected_cont);
		category_selected_cont=NULL;
	}
	#endif
}

static void zone_all_cont_event_cb(lv_obj_t * ta, lv_event_t e)
{
	#if ENABLE_CALL_FUNCTION
	if(lv_debug_check_obj_valid(category_selected_cont))
	{
		lv_obj_del_async(category_selected_cont);
		category_selected_cont=NULL;
	}
	#endif
}

static void zone_cont_event_cb(lv_obj_t * ta, lv_event_t e)
{
	#if ENABLE_CALL_FUNCTION
	if(lv_debug_check_obj_valid(category_selected_cont))
	{
		lv_obj_del_async(category_selected_cont);
		category_selected_cont=NULL;
	}
	#endif

    if(e == LV_EVENT_RELEASED) {
    	int id=get_controlzone_id(ta);
    	if(id == -1)
    		return;
    	control_zone_click_arry[id]=!control_zone_click_arry[id];
		//printf("zone_cont_event_cb:id=%d,realId=%d\n",id,id+(control_win_current_page-1)*20);
    	if(control_zone_click_arry[id])
    	{
			#if ENABLE_CALL_FUNCTION
			if(control_page_type == PAGE_PAGER)
			{
				//如果是pager，选择一个则取消其他所有
				Zone_Group_SelectOrCancel(0, 0xFFFF);
				control_zone_click_arry[id] = 1;
			}
			#endif
    		lv_obj_set_state(ta, LV_STATE_CHECKED);
    		Zone_Group_SelectOrCancel(1,id+(control_win_current_page-1)*CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM);
#if 0
    		//lv_obj_add_style(ta, LV_OBJ_PART_MAIN, &style_box_selected);
    		// lv_task_handler();	//如果是用add_style,一定要加这句,否则lv_style.c的_lv_style_get_color()函数会段错误line623,因为此函数系统不断循环执行
#endif
    	}
    	else
    	{
    		lv_obj_set_state(ta, LV_STATE_DEFAULT);
    		Zone_Group_SelectOrCancel(0,id+(control_win_current_page-1)*CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM);
    	}
    }
}


static void zone_cont_event_cb2(lv_obj_t * ta, lv_event_t e)
{

    if(e == LV_EVENT_GESTURE)
    {
    	lv_indev_t * pIndev = lv_indev_get_act();
    	lv_gesture_dir_t gesture_dir = lv_indev_get_gesture_dir(pIndev);
		  switch(gesture_dir ){
		  case 	LV_GESTURE_DIR_TOP:		/**< Gesture dir up. */
			  /*on led 1*/
			  printf("gestrue TOP...\n");
		  break;
		  case 	LV_GESTURE_DIR_BOTTOM:	/**< Gesture dir down. */
			  /*on led 2*/
			  printf("gestrue BOTTOM...\n");
		  break;
		  case 	LV_GESTURE_DIR_LEFT:	/**< Gesture dir left. */
			  /*on led 3*/
			  printf("gestrue LEFT...\n");
		  break;
		  case 	LV_GESTURE_DIR_RIGHT:	/**< Gesture dir right. */
			  /*on led 4*/
			  printf("gestrue RIGHT...\n");
		  break;
		 }
    }
}




static void page_pre_next_event_cb(lv_obj_t * obj, lv_event_t e)
{
	if(e == LV_EVENT_PRESSED || e == LV_EVENT_RELEASED || e == LV_EVENT_PRESS_LOST)
	{
		lv_obj_t *btn=lv_obj_get_child(obj, NULL);
		if(btn)
			lv_obj_set_state(btn, lv_obj_get_state(obj, LV_OBJ_PART_MAIN));
	}
	else if(e == LV_EVENT_CLICKED)	//有时候进入LV_EVENT_CLICKED后不再产生LV_EVENT_RELEASED,导致按钮颜色没有变回去,此处强制改变
	{
		lv_obj_t *btn=lv_obj_get_child(obj, NULL);
		if(btn)
			lv_obj_set_state(btn, LV_STATE_DEFAULT);
	}
    if(e == LV_EVENT_CLICKED) {
		printf("page_pre_next_event_cb...\n");
		control_win_total_page=control_win_get_total_page();
    	if(obj == control_zone_bt_pre || lv_obj_get_parent(obj) == control_zone_bt_pre)
    	{
    		control_win_current_page--;
    		if(control_win_current_page<=0)
    			control_win_current_page=control_win_total_page;
    		 control_win_update(control_page_type,1);
    	}
    	else
    	{
    		control_win_current_page++;
    	    if(control_win_current_page>control_win_total_page)
    	    {
    	    	control_win_current_page=1;
    	    }
    	    control_win_update(control_page_type,1);
    	}
    }
}


static void imgbutton_event_cb(lv_obj_t * obj, lv_event_t e)
{
#if 0
    if(e == LV_EVENT_PRESSED && obj !=  back_btn)
    {
    	lv_obj_set_pos(obj, lv_obj_get_x(obj)+5,lv_obj_get_y(obj)+5);
    }
    else if(e == LV_EVENT_RELEASED || e == LV_EVENT_PRESS_LOST)
    {
    	if(obj !=  back_btn)
    	{
    		lv_obj_set_pos(obj, lv_obj_get_x(obj)-5,lv_obj_get_y(obj)-5);
    	}
    	if(e == LV_EVENT_PRESS_LOST)
    		return;
		if(obj ==  back_btn)
		{
			int current_win=GetCurrentWin();
			if(current_win == WIN_CONTROL)
			{
				lv_obj_del(control_all_cont);
				DeleteWin(current_win);
				login_win_start();
			}
			else if(current_win == WIN_MUSICLIST)
			{
				musiclist_back_to_control_win(1);
			}
		}
		else if(obj == control_group)
		{
	    	Zone_Group_SelectOrCancel(0,0xFFFF);	//先取消当前界面类型的按钮选中状态
	    	control_page_type=!control_page_type;
	    	control_win_current_page=1;
	    	control_win_update(control_page_type,1);
		}
		else if(obj == control_music)
		{
			musiclist_win_start();
		}
		else if(obj == control_stop)
		{
			control_terminal_idle();
		}
		else if(obj == control_selectAll)
		{
			Zone_Group_SelectOrCancel(1,0xFFFF);
		}
		else if(obj == control_cancel)
		{
			Zone_Group_SelectOrCancel(0,0xFFFF);
		}
		else if(obj == control_paging)
		{
			Paging_proc(1);
		}
    }
#endif
}



void temp_test()
{
	int i;
	for(i=0;i<CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;i++)
	{
	    lv_label_set_text_fmt(control_zone_tx_arry[i].zone_name ,"%s%d班","高中六年级",i+1);
	    lv_label_set_text_fmt(control_zone_tx_arry[i].source_name,"%s%d","Just one last dance",i+1);
	    lv_label_set_text_fmt(control_zone_tx_arry[i].volume_value,"%d%%",80+i);
	}
	lv_obj_set_state(control_zone_cont_arry[0], LV_STATE_DISABLED);
	lv_obj_set_state(control_zone_cont_arry[1], LV_STATE_DISABLED);
	lv_obj_set_state(control_zone_cont_arry[2], LV_STATE_DISABLED);
}


static void slider_event_cb(lv_obj_t * slider, lv_event_t e)
{
    if(e == LV_EVENT_VALUE_CHANGED) {
        if(lv_slider_get_type(slider) == LV_SLIDER_TYPE_NORMAL) {
            static char buf[16];
            int vol=lv_slider_get_value(volume_slider);
            lv_snprintf(buf, sizeof(buf), "%d", vol);
            lv_obj_set_style_local_value_str(slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, buf);
        }
    }
    else if(e == LV_EVENT_RELEASED)	//释放后才发送音量
    {
    	int vol=lv_slider_get_value(volume_slider);
		#if ENABLE_CALL_FUNCTION
		if(control_page_type == PAGE_PAGER)
		{
			//if(Paging_status == CALLING_START)
			{
				g_call_vol=vol;
				set_output_volume();
				save_sysconf("Other","Call_Volume");
			}
		}
		else
		#endif
		{
			control_terminal_volume(vol);
		}
    }
}

#if ENABLE_CALL_FUNCTION
static void category_selected_cb(lv_obj_t * obj, lv_event_t e)
{
	int btn_id=-1;
	if(obj == category_selected_btn[0])
		btn_id=0;
	if(obj == category_selected_btn[1])
		btn_id=1;
	if(obj == category_selected_btn[2])
		btn_id=2;
	if(e == LV_EVENT_PRESSED)
	{
		printf("click,id=%d\n",btn_id);
	}
	else if(e == LV_EVENT_RELEASED)
	{
		lv_obj_del_async(category_selected_cont);
		category_selected_cont=NULL;
		if(btn_id!=control_page_type)
		{
			UI_Group_ExitExtraMode();

			Zone_Group_SelectOrCancel(0,0xFFFF);	//先取消当前界面类型的按钮选中状态
			control_win_current_page=1;
			switch(btn_id)
			{
				case 0:
				control_page_type=PAGE_ZONE;
				break;
				case 1:
				control_page_type=PAGE_GROUP;
				break;
				case 2:	//寻呼台
				//已经处于寻呼状态,不进入
				if(Paging_status == PAGING_START)
				{
					static const char * btns[] = {"OK", ""};
					lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
					lv_msgbox_add_btns(msg, btns);
					lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
					lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
					lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
					lv_msgbox_set_text(msg,language_control_message_stopPaging_text);
					return;
				}
				control_page_type=PAGE_PAGER;
				//查询所有寻呼台设备信息
				QueryAllPagerDeviceInfo();
				break;
			}

			if(control_page_type == PAGE_PAGER)
			{
				//进入对讲页面，首先禁止本地监听
				if(g_Enable_local_listening)
				{
					g_Enable_local_listening=0;
					//关闭功放
					Amp_Switch(0);
				}
				//todo 停止USB播放
				if(g_bp1048_info.firmware_type == BP1048_FW_UDISK_TYPE)
				{
					stop_udiskPlay(1);
				}

				//禁止底部按钮
				int i;
				for(i=1;i<MAX_CONTROL_NUM-1;i++)
				{
					if(g_bp1048_info.firmware_type == BP1048_FW_NORMAL_TYPE)
					{
						if( i == CONTROL_BUTTON_UDISK )
						{
							printf("CONTROL_BUTTON_UDISK continue.\n");
							continue;
						}
					}
					if(i==CONTROL_BUTTON_CALL)
					{
						continue;
					}
					else if(i==CONTROL_BUTTON_VOLUME)
					{
						lv_label_set_text(control_button_list[i].label, language_control_button_intercom_volume_text);
						//默认隐藏音量条
						lv_obj_set_hidden(volume_slider, true);

						//设置音量值为对讲音量值
						lv_slider_set_value(volume_slider,g_call_vol,0);
						
						lv_obj_set_state(control_button_list[i].obj, LV_STATE_DEFAULT);
						lv_obj_t *btn=lv_obj_get_child(control_button_list[i].obj, NULL);
						if(btn)
							lv_obj_set_state(btn,LV_STATE_DEFAULT);
					}
					else
					{
						lv_obj_t *btn=lv_obj_get_child(control_button_list[i].obj, NULL);
						lv_obj_set_state(control_button_list[i].obj,LV_STATE_DISABLED);
						if(btn)
							lv_obj_set_state(btn,LV_STATE_DISABLED);
					}
				}
			}
			else
			{
				//恢复底部按钮
				int i;
				for(i=1;i<MAX_CONTROL_NUM-1;i++)
				{
					if(g_bp1048_info.firmware_type == BP1048_FW_NORMAL_TYPE)
					{
						if( i == CONTROL_BUTTON_UDISK )
						{
							continue;
						}
					}
					if(i==CONTROL_BUTTON_CALL)
					{
						continue;
					}
					else if(i==CONTROL_BUTTON_VOLUME)
					{
						lv_label_set_text(control_button_list[i].label, control_button_list[i].nameCN);
						//默认隐藏音量条
						lv_obj_set_hidden(volume_slider, true);

						lv_obj_set_state(control_button_list[i].obj, LV_STATE_DEFAULT);
						lv_obj_t *btn=lv_obj_get_child(control_button_list[i].obj, NULL);
						if(btn)
							lv_obj_set_state(btn,LV_STATE_DEFAULT);
					}
					else if(i==CONTROL_BUTTON_PAGING)
					{
						lv_obj_t *btn=lv_obj_get_child(control_button_list[i].obj, NULL);
						if(Paging_status == PAGING_START)
						{
							if(btn)
								lv_obj_set_state(btn,LV_STATE_CHECKED);
						}
						else
						{
							if(btn)
								lv_obj_set_state(btn,LV_STATE_DEFAULT);
						}
					}
					else
					{
						lv_obj_t *btn=lv_obj_get_child(control_button_list[i].obj, NULL);
						lv_obj_set_state(control_button_list[i].obj,LV_STATE_DEFAULT);
						if(btn)
							lv_obj_set_state(btn,LV_STATE_DEFAULT);
					}
				}
			}
			control_win_update(control_page_type,1);
		}
	}
}


static void call_event_cb(lv_obj_t * obj, lv_event_t e)
{
	if(e == LV_EVENT_PRESSED)
	{

	}
	else if(e == LV_EVENT_RELEASED)
	{
		if(obj == call_bt_answer)
		{
			//接听
			m_stPager_Info.self_callStatus = CALL_STATUS_CONNECT;
			Send_callStatus_feedback(CALL_STATUS_CONNECT);

			lv_label_set_text(label_callStatus, language_control_call_calling_text);
			lv_obj_set_hidden(call_bt_answer,true);

			#if USE_SSD202
			if(m_stPager_Info.other_isSupportVideo)
			{
				lv_obj_set_hidden(call_bt_video,false);
			}
			else
			{
				lv_obj_align(call_bt_hangup,call_main_cont,LV_ALIGN_IN_BOTTOM_MID,0,-30);
			}
			#else
			lv_obj_align(call_bt_hangup,call_main_cont,LV_ALIGN_IN_BOTTOM_MID,0,-30);
			#endif


			//打开音频接口
			#if defined(USE_SSD212) || defined(USE_SSD202)
			Paging_status = CALLING_START;
			//此处需要再重新打开一次ao，否则之前里面有铃声缓存导致延时很大
			mi_audio_out_init(16000, 16, 1);
			mi_audio_in_init(AI_MODE_CALL_16K,16000, 16, 1);
			#else
			i2s_call_thread();
			#endif
		}
		else if(obj == call_bt_hangup)
		{
			//挂断
			m_stPager_Info.self_callStatus = CALL_STATUS_FREE;
			Send_callStatus_feedback(CALL_STATUS_HANGUP);
			//如果是挂断，需要立即响应，避免线程轮询后接收多个状态后覆盖
			close_calling_win_extern(1,1,1000);
		}
		#if USE_SSD202
		else if(obj == call_bt_video)
		{
			//主动点击视频按钮方为主动发起方
			Send_request_video_call(1,1);
		}
		#endif
	}
}





void create_call_cont(int IsSelfcall)
{
	if(call_main_cont)
		return;

	//找到那个设备名称
	int called_index=-1;
	int called_device=0;	//1代表寻呼台 2代表解码分区
	int i=0;
	for(i=0;i<m_stPager_Info.TotalPager;i++)
	{
		if( memcmp(m_stPager_Info.self_callMac,m_stPager_Info.pagerInfo[i].mac,6) == 0 )
		{
			called_index=i;
			called_device=1;
			break;
		}
	}
	if(called_index == -1)
	{
		printf("Not Found call pager,ready found zone...\n");

		for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
		{
			if( memcmp(m_stPager_Info.self_callMac,m_stZone_Info.zoneInfo[i].g_zone_mac,6) == 0 )
			{
				called_index=i;
				called_device=2;
				break;
			}
		}
		if(called_index == -1)
		{
			printf("Not Found call zone!\n");
			//挂断
			m_stPager_Info.self_callStatus = CALL_STATUS_FREE;
			Send_callStatus_feedback(CALL_STATUS_HANGUP);
			return;
		}
	}


	//进入对讲模式
	BP1048_Send_Enter_CALL(1,5,2);

	//打开对讲窗口，需要关闭本地监听
	if(g_Enable_local_listening)
	{
		g_Enable_local_listening=0;
		//关闭功放
		Amp_Switch(0);
	}
	//todo 停止USB播放
	if(g_bp1048_info.firmware_type == BP1048_FW_UDISK_TYPE)
	{
		stop_udiskPlay(IsSelfcall);
	}

	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}

	printf("create_call_cont4\n");

	call_main_cont =lv_cont_create(lv_scr_act(), NULL);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_size(call_main_cont,450,270);
	}
	else if(IS_DISP_RES_1280)
    {
        lv_obj_set_size(call_main_cont,XRatio_1024_to_1280(450),YRatio_600_to_800(270));
    }
	else if(IS_DISP_RES_800)
	{
		lv_obj_set_size(call_main_cont,350,210);
	}
	else if(IS_DISP_RES_600)
	{
		lv_obj_set_size(call_main_cont,450,270);
	}
	lv_obj_align(call_main_cont,NULL,LV_ALIGN_CENTER,0,0);
	lv_obj_set_style_local_border_color(call_main_cont,LV_OBJ_PART_MAIN,LV_STATE_DEFAULT,get_theme_color());


	lv_obj_t *call_head_box = lv_obj_create(call_main_cont, NULL);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_size(call_head_box, 450, 50);
	}
	else if(IS_DISP_RES_1280)
    {
		lv_obj_set_size(call_head_box, XRatio_1024_to_1280(450),YRatio_600_to_800(50));
    }
	else if(IS_DISP_RES_800)
	{
		lv_obj_set_size(call_head_box, 350, 50);
	}
	else if(IS_DISP_RES_600)
	{
		lv_obj_set_size(call_head_box, 450, 50);
	}
	lv_obj_align(call_head_box, call_main_cont, LV_ALIGN_IN_TOP_MID,0,0);
	lv_obj_set_style_local_pad_all(call_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(call_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    //lv_obj_set_style_local_radius(call_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(call_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_bg_color(call_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());

	label_callDeviceName=lv_label_create(call_head_box, NULL);
	lv_obj_align(label_callDeviceName, call_head_box, LV_ALIGN_IN_TOP_MID,0,5);
	lv_label_set_align(label_callDeviceName, LV_LABEL_ALIGN_CENTER);
	
	if(called_device == 1)
	{
		lv_label_set_text(label_callDeviceName, m_stPager_Info.pagerInfo[called_index].name);
	}
	else if(called_device == 2)
	{
		lv_label_set_text(label_callDeviceName, m_stZone_Info.zoneInfo[called_index].g_zone_name);
	}
	lv_obj_set_auto_realign(label_callDeviceName,true);
	lv_obj_set_width(label_callDeviceName, lv_obj_get_width_grid(call_head_box, 2, 1));

	lv_obj_set_style_local_text_font(label_callDeviceName, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font26);

	label_callStatus=lv_label_create(call_main_cont, NULL);
	if(IS_DISP_RES_1024)
	{
		lv_obj_align(label_callStatus, call_main_cont, LV_ALIGN_IN_TOP_MID,0,60);
	}
	else if(IS_DISP_RES_1280)
	{
		lv_obj_align(label_callStatus, call_main_cont, LV_ALIGN_IN_TOP_MID,0,80);
	}
	else if(IS_DISP_RES_800)
	{
		lv_obj_align(label_callStatus, call_main_cont, LV_ALIGN_IN_TOP_MID,0,50);
	}
	else if(IS_DISP_RES_600)
	{
		lv_obj_align(label_callStatus, call_main_cont, LV_ALIGN_IN_TOP_MID,0,60);
	}

	lv_label_set_align(label_callStatus, LV_LABEL_ALIGN_CENTER);
	lv_label_set_text(label_callStatus, language_control_call_wait_answer_text);
	lv_obj_set_auto_realign(label_callStatus,true);
	lv_obj_set_width(label_callStatus, lv_obj_get_width_grid(call_main_cont, 2, 1));

	lv_obj_set_style_local_text_font(label_callStatus, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font26);
	//lv_obj_set_style_local_bg_color(label_callStatus, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());

	
	call_bt_hangup=lv_btn_create(call_main_cont,NULL);
	lv_obj_align(call_bt_hangup,call_main_cont,LV_ALIGN_IN_BOTTOM_MID,0,-30);
	lv_obj_set_style_local_value_str(call_bt_hangup, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT,language_control_call_hangUp_text);
	lv_obj_set_style_local_value_font(call_bt_hangup, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font26);
	lv_obj_set_style_local_border_color(call_bt_hangup,LV_OBJ_PART_MAIN,LV_STATE_DEFAULT,get_theme_color());
	lv_obj_set_style_local_bg_color(call_bt_hangup, LV_OBJ_PART_MAIN, LV_STATE_CHECKED, get_theme_color());

	lv_obj_set_event_cb(call_bt_hangup, call_event_cb);

	if(!m_stPager_Info.self_isCallingParty)	//被叫
	{
		//显示接听按钮
		call_bt_answer=lv_btn_create(call_main_cont,call_bt_hangup);
		lv_obj_set_style_local_value_str(call_bt_answer, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT,language_control_call_answer_text);
		lv_obj_align(call_bt_answer,call_main_cont,LV_ALIGN_IN_BOTTOM_LEFT,80,-30);

		lv_obj_align(call_bt_hangup,call_main_cont,LV_ALIGN_IN_BOTTOM_RIGHT,-80,-30);

		lv_obj_set_event_cb(call_bt_answer, call_event_cb);
	}

	#if USE_SSD202
	call_bt_video=lv_btn_create(call_main_cont,call_bt_hangup);
	lv_obj_set_style_local_value_str(call_bt_video, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT,"视频");
	lv_obj_align(call_bt_video,call_main_cont,LV_ALIGN_IN_BOTTOM_LEFT,80,-30);
	lv_obj_set_hidden(call_bt_video,true);

	lv_obj_set_event_cb(call_bt_video, call_event_cb);
	#endif

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}

	//此处需要立刻刷新一遍状态，否则自动应答的设备状态变化太快，没有更新
	set_call_status_show(m_stPager_Info.other_callStatus,IsSelfcall);
}


#if USE_SSD202
// 延迟任务回调函数
static void delay_destroy_video_call_page(lv_task_t *task)
{
    // 销毁页面
	if(video_call_main_cont)
	{
    	lv_obj_del(video_call_main_cont);
		video_call_main_cont=NULL;
		
		m_stPager_Info.self_video_callStatus=VIDEO_CALL_STATUS_FREE;
		Send_video_call_status_feedback(m_stPager_Info.self_video_callStatus);

		VideoCallExit_clean();
	}
	lv_task_del(task);
}

static void video_call_event_cb(lv_obj_t * obj, lv_event_t e)
{
	if(e == LV_EVENT_PRESSED)
	{

	}
	else if(e == LV_EVENT_RELEASED)
	{
		if(obj == video_call_exit_btn)
		{
			// 创建延迟任务，在100毫秒后执行销毁页面的函数,并停止视频播放
        	lv_task_create(delay_destroy_video_call_page, 50, LV_TASK_PRIO_LOWEST, NULL);
		}
	}
}


void create_video_call_cont(int IsSelfcall)
{
	if(video_call_main_cont)
		return;

	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}

	printf("create_video_call_cont\n");

	video_call_main_cont =lv_cont_create(lv_scr_act(), NULL);

	lv_obj_set_size(video_call_main_cont,LV_HOR_RES_MAX,LV_VER_RES_MAX);

	//set colorkey to black
	lv_obj_set_style_local_bg_color(video_call_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_BLACK);
	
	lv_obj_set_style_local_pad_all(video_call_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(video_call_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(video_call_main_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_radius(video_call_main_cont, LV_IMG_PART_MAIN, LV_STATE_DEFAULT, 0);

	video_call_exit_btn=lv_btn_create(video_call_main_cont, NULL);
	lv_obj_set_style_local_border_width(video_call_exit_btn, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	//lv_obj_set_style_local_border_opa(exit_btn, LV_STATE_DEFAULT, LV_STATE_DEFAULT, LV_OPA_TRANSP);
	lv_obj_set_style_local_radius(video_call_exit_btn, LV_IMG_PART_MAIN, LV_STATE_DEFAULT, 0);
	
	lv_obj_set_event_cb(video_call_exit_btn, video_call_event_cb);

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}

#endif


//刷新对讲显示状态
void set_call_status_show(int status,int IsSelfcall)
{
	if(!call_main_cont)
		return;
	
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}
	switch(status)
	{
		case CALL_STATUS_FREE:
		{
			lv_label_set_text(label_callStatus, language_control_call_hangedUp_text);
			//1s后关闭对话框
		}
		break;
		case CALL_STATUS_WAIT_CALLED_ANSWER:
		{
			if(m_stPager_Info.self_isCallingParty)
			{
				lv_label_set_text(label_callStatus, language_control_call_wait_reply_text);
			}
			else
			{
				lv_label_set_text(label_callStatus, language_control_call_wait_answer_text);
			}
		}
		break;
		case CALL_STATUS_NO_RESPONSE:
		{
			lv_label_set_text(label_callStatus, language_control_call_no_answer_text);
		}
		break;
		case CALL_STATUS_CODECES_NOT_SUPPORT:
		{
			lv_label_set_text(label_callStatus, language_control_call_codecs_not_support_text);
		}
		break;
		case CALL_STATUS_BUSY:
		{
			lv_label_set_text(label_callStatus, language_control_call_busy_text);
			//1s后关闭对话框
		}
		break;
		case CALL_STATUS_REJECT:
		{
			lv_label_set_text(label_callStatus, language_control_call_refused_text);
			//1s后关闭对话框
		}
		break;
		case CALL_STATUS_CONNECT:
		{
			lv_label_set_text(label_callStatus, language_control_call_calling_text);
			//如果是SSD202，对方支持视频，且本机为主叫（因为被叫,点击接听按键时已经处理)，那么还需要显示视频按钮
			#if USE_SSD202
			if(m_stPager_Info.other_isSupportVideo)
			{
				lv_obj_set_hidden(call_bt_video,false);
				lv_obj_align(call_bt_hangup,call_main_cont,LV_ALIGN_IN_BOTTOM_RIGHT,-80,-30);
			}
			else
			{
				lv_obj_align(call_bt_hangup,call_main_cont,LV_ALIGN_IN_BOTTOM_MID,0,-30);
			}
			#endif
		}
		break;
		case CALL_STATUS_HANGUP:
		{
			lv_label_set_text(label_callStatus, language_control_call_hangedUp_text);
			//1s后关闭对话框
		}
		break;
	}
	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}





#endif



static void msg_backToLogin_event_cb(lv_obj_t * btnm, lv_event_t e)
{
	if(e == LV_EVENT_DELETE) {//由close产生的事件

		//lv_obj_del_async(lv_obj_get_parent(msg));//删除父窗口则会删除父窗口和其所有子窗口
		//printf("delete\n");
	} else	if(e == LV_EVENT_VALUE_CHANGED) {
		const int *ex_data=lv_event_get_data();
		//printf("lv_event_get_data=%d",*ex_data);
		lv_msgbox_start_auto_close(btnm, 0);
		if(*ex_data == 1)	//确定
		{
			if(IsValidWin(WIN_CONTROL))
			{
				lv_obj_del(control_all_cont);
				DeleteWin(WIN_CONTROL);
				//重要，一定要加上这句
				lv_scr_load(screen_login);
				login_win_start();
			}
		}
	}
}

static void kb_event_cb(lv_obj_t * _kb, lv_event_t e)
{
    lv_keyboard_def_event_cb(control_password_kb_obj, e);

    if(e == LV_EVENT_CANCEL || e == LV_EVENT_APPLY) {
        if(control_password_kb_obj) {
            if(IS_DISP_RES_1024)
            {
        	    lv_obj_set_pos(password_dialog,lv_obj_get_x(password_dialog),lv_obj_get_y(password_dialog)+180);
            }
			else if(IS_DISP_RES_1280)
			{
				lv_obj_set_pos(password_dialog, XRatio_1024_to_1280(lv_obj_get_x(password_dialog)),YRatio_600_to_800(lv_obj_get_y(password_dialog)+180));
			}
            else if(IS_DISP_RES_800)
            {
                lv_obj_set_pos(password_dialog,lv_obj_get_x(password_dialog),lv_obj_get_y(password_dialog)+180);
            }
            else if(IS_DISP_RES_600)
            {
                lv_obj_set_pos(password_dialog,lv_obj_get_x(password_dialog),lv_obj_get_y(password_dialog)+180);
            }
            lv_obj_del(control_password_kb_obj);
            control_password_kb_obj = NULL;
        }
    }
}
static void password_settings_event_cb(lv_obj_t * ta, lv_event_t e)
{
    if(e == LV_EVENT_RELEASED) {
        if(control_password_kb_obj == NULL) {
			if(IS_DISP_RES_1024)
            {
        	    lv_obj_set_pos(password_dialog,lv_obj_get_x(password_dialog),lv_obj_get_y(password_dialog)-180);
            }
			else if(IS_DISP_RES_1280)
			{
				lv_obj_set_pos(password_dialog, XRatio_1024_to_1280(lv_obj_get_x(password_dialog)),YRatio_600_to_800(lv_obj_get_y(password_dialog)-180));
			}
            else if(IS_DISP_RES_800)
            {
                lv_obj_set_pos(password_dialog,lv_obj_get_x(password_dialog),lv_obj_get_y(password_dialog)-180);
            }
            else if(IS_DISP_RES_600)
            {
                lv_obj_set_pos(password_dialog,lv_obj_get_x(password_dialog),lv_obj_get_y(password_dialog)-180);
            }
            control_password_kb_obj = lv_keyboard_create(lv_scr_act(), NULL);
            lv_keyboard_set_mode(control_password_kb_obj,LV_KEYBOARD_MODE_TEXT_LOWER);
            lv_obj_set_event_cb(control_password_kb_obj, kb_event_cb);

            lv_indev_wait_release(lv_indev_get_act());
        }
        lv_textarea_set_cursor_hidden(ta, false);

        lv_keyboard_set_textarea(control_password_kb_obj, ta);
    } else if(e == LV_EVENT_DEFOCUSED) {
        lv_textarea_set_cursor_hidden(ta, true);
    }
}
static void msg_password_to_settings_event_cb(lv_obj_t * btnm, lv_event_t e)
{
	if(e == LV_EVENT_DELETE) {//由close产生的事件

		//lv_obj_del_async(lv_obj_get_parent(msg));//删除父窗口则会删除父窗口和其所有子窗口
		//printf("delete\n");
	} else	if(e == LV_EVENT_VALUE_CHANGED) {
		const int *ex_data=lv_event_get_data();
		//printf("lv_event_get_data=%d",*ex_data);
		lv_msgbox_start_auto_close(btnm, 0);
		if(*ex_data == 1)	//确定
		{
			//判断账户密码是否匹配
       		const char *password = lv_textarea_get_text(password_textarea);
			int super_user_index=GetUserIndexByUserName(SUPER_USER_NAME);
			if(m_stUser_Info.CurrentUserIndex>=0 && super_user_index>=0 && m_stUser_Info.CurrentUserIndex<m_stUser_Info.TotalUser && super_user_index<m_stUser_Info.TotalUser)
			{
				if( strcmp(m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].password,password) == 0 || strcmp(m_stUser_Info.userInfo[super_user_index].password,password) == 0)
				{
					settings_main_win_start();
				}
			}
		}
		if(control_password_kb_obj)
		{
			lv_obj_del(control_password_kb_obj);
			control_password_kb_obj=NULL;
		}
	}
}


static void round_btn_cb(lv_obj_t * obj, lv_event_t e)
{
	int i=0;
	if(e == LV_EVENT_PRESSED || e == LV_EVENT_RELEASED || e == LV_EVENT_PRESS_LOST)
	{
		lv_obj_t *btn=lv_obj_get_child(obj, NULL);
		if(btn)
			lv_obj_set_state(btn, lv_obj_get_state(obj, LV_OBJ_PART_MAIN));
	}

    if(e == LV_EVENT_RELEASED)
	{
		#if ENABLE_CALL_FUNCTION
		if( call_main_cont )	//如果存在对讲窗口，那么只能处理音量按钮
		{
			if( !(obj == control_button_list[CONTROL_BUTTON_VOLUME].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_VOLUME].obj) )
				return;
		}
		#endif

		if(obj == control_button_list[CONTROL_BUTTON_ZONEGROUP].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_ZONEGROUP].obj)
		{
			#if ENABLE_CALL_FUNCTION
				if(g_isSupportCall)
				{
					//对讲忙碌状态不允许切换
					if(m_stPager_Info.self_callStatus != CALL_STATUS_FREE)
					{
						return;
					}
					if(category_selected_cont != NULL)
					{
						lv_obj_del(category_selected_cont);
						category_selected_cont=NULL;
					}
					else
					{
						category_selected_cont =lv_cont_create(control_all_cont, NULL);
						lv_obj_set_size(category_selected_cont,460,140);
						lv_obj_align(category_selected_cont,NULL,LV_ALIGN_CENTER,0,10);
						lv_obj_set_style_local_border_color(category_selected_cont,LV_OBJ_PART_MAIN,LV_STATE_DEFAULT,get_theme_color());
						//创建三个按钮
						lv_obj_t *btn1=lv_btn_create(category_selected_cont,NULL);
						lv_obj_align(btn1,category_selected_cont,LV_ALIGN_IN_LEFT_MID,10,0);
						lv_obj_set_style_local_value_str(btn1, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT,language_control_type_zone_text);
						lv_obj_set_style_local_value_font(btn1, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font30);
						lv_obj_set_style_local_border_color(btn1,LV_OBJ_PART_MAIN,LV_STATE_DEFAULT,get_theme_color());
						lv_obj_set_style_local_bg_color(btn1, LV_OBJ_PART_MAIN, LV_STATE_CHECKED, get_theme_color());
						lv_obj_set_event_cb(btn1, category_selected_cb);
						lv_obj_t *btn2=lv_btn_create(category_selected_cont,btn1);
						lv_obj_align(btn2,btn1,LV_ALIGN_OUT_RIGHT_MID,25,0);
						lv_obj_set_style_local_value_str(btn2, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT,language_control_type_group_text);
						//lv_obj_set_style_local_value_font(btn2, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font20);
						lv_obj_t *btn3=lv_btn_create(category_selected_cont,btn1);
						lv_obj_align(btn3,btn2,LV_ALIGN_OUT_RIGHT_MID,25,0);
						lv_obj_set_style_local_value_str(btn3, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT,language_control_type_pager_text);
						//lv_obj_set_style_local_value_font(btn3, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &font20);

						//如果不支持寻呼台间对讲，那么需要隐藏btn3
						if(!g_isSupportPagerCall)
						{
							lv_obj_set_hidden(btn3,true);
							lv_obj_set_size(category_selected_cont,320,140);
							lv_obj_set_pos(category_selected_cont, lv_obj_get_x(category_selected_cont)+40,lv_obj_get_y(category_selected_cont));
						}

						category_selected_btn[0]=btn1;
						category_selected_btn[1]=btn2;
						category_selected_btn[2]=btn3;

						if(control_page_type == PAGE_ZONE)
						{
							lv_obj_set_state(btn1, LV_STATE_CHECKED);
						}
						else if(control_page_type == PAGE_GROUP)
						{
							lv_obj_set_state(btn2, LV_STATE_CHECKED);
						}
						else if(control_page_type == PAGE_PAGER)
						{
							lv_obj_set_state(btn3, LV_STATE_CHECKED);
						}
					}
				}
				else
				{
					UI_Group_ExitExtraMode();
					Zone_Group_SelectOrCancel(0,0xFFFF);	//先取消当前界面类型的按钮选中状态
					control_page_type=!control_page_type;
					control_win_current_page=1;
					control_win_update(control_page_type,1);
				}
			#else
				UI_Group_ExitExtraMode();
				Zone_Group_SelectOrCancel(0,0xFFFF);	//先取消当前界面类型的按钮选中状态
				control_page_type=!control_page_type;
				control_win_current_page=1;
				control_win_update(control_page_type,1);
			#endif
		}
		else if(obj == control_button_list[CONTROL_BUTTON_MUSIC].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_MUSIC].obj)
		{
			#if USE_SSD202
			//create_video_call_cont(1);
			//start_ffplayer_video_thread("rtsp://192.168.3.22:554/user=admin&password=&channel=1&stream=0.sdp?");
			startRTCPClient_thread(1);
			return;
			#endif

			#if ENABLE_LISTEN_FUNCTION
			int i=0;
			for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
			{
				if(m_stZone_Info.zoneInfo[i].g_zone_isSelect)
				{
					break;
				}
			}
			if(m_stListen_Info.self_listenStatus == LISTEN_STATUS_STOP)
				Send_listen_event(m_stZone_Info.zoneInfo[i].g_zone_mac,LISTEN_STATUS_START,LISTEN_TYPE_OUTPUT);
			else
				Send_listen_event(m_stZone_Info.zoneInfo[i].g_zone_mac,LISTEN_STATUS_STOP,LISTEN_TYPE_OUTPUT);
			#else
			musiclist_win_start();
			#endif
		}
		else if(obj == control_button_list[CONTROL_BUTTON_UDISK].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_UDISK].obj)
		{
			udisk_win_start();
		}
		else if(obj == control_button_list[CONTROL_BUTTON_STOP].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_STOP].obj)
		{
			if(g_bp1048_info.firmware_type == BP1048_FW_UDISK_TYPE)
			{
				int hasSelectZone=0;
				int i=0;
				for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
				{
					if(m_stZone_Info.zoneInfo[i].g_zone_isSelect)
					{
						hasSelectZone++;
					}
				}
				if(!hasSelectZone)
				{
					//如果没有选中分区，那么点击停止按钮将停止U盘播放
					stop_udiskPlay(1);
				}
				else
				{
					control_terminal_idle();
				}
			}
			else
			{
				control_terminal_idle();
			}
		}
		else if(obj == control_button_list[CONTROL_BUTTON_SELECTALL].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_SELECTALL].obj)
		{
			//判断是不是选择了全部?如果不是则选择全部。否则重置所有选择项
			//Zone_Group_SelectOrCancel(1,0xFFFF);
			Btn_SelectAllOrCancel();
		}
		#if 0
		else if(obj == control_button_list[CONTROL_BUTTON_CANCEL].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_CANCEL].obj)
		{
			Zone_Group_SelectOrCancel(0,0xFFFF);
		}
		#endif
		else if(obj == control_button_list[CONTROL_BUTTON_PAGING].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_PAGING].obj)
		{
			if(!paging_alarm_press_flag)	//消防警报键已经按下的情况下不响应
			{
				Paging_proc(1);
			}
			else
			{
				static const char * btns[] = {"OK", ""};
				lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
				lv_msgbox_add_btns(msg, btns);
				lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
				lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
				lv_msgbox_set_text(msg,language_control_message_cancelAlarm_text);
				return;
			}
		}
		else if(obj == control_button_list[CONTROL_BUTTON_CALL].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_CALL].obj)
		{
			if(!paging_alarm_press_flag)	//消防警报键已经按下的情况下不响应
			{
				#if ENABLE_CALL_FUNCTION
				call_proc(1);
				#endif
			}
			else
			{
				static const char * btns[] = {"OK", ""};
				lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
				lv_msgbox_add_btns(msg, btns);
				lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
				lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
				lv_msgbox_set_text(msg,language_control_message_cancelAlarm_text);
				return;
			}
		}
		else if(obj == control_button_list[CONTROL_BUTTON_LISTEN].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_LISTEN].obj)
		{
			lv_obj_t *btn=lv_obj_get_child(obj, NULL);

			//监听
			g_Enable_local_listening = !g_Enable_local_listening;
			if(g_Enable_local_listening)
			{
				lv_obj_set_state(btn!=NULL?btn:obj, LV_STATE_CHECKED);
				//已经处于寻呼状态
				if(Paging_status == PAGING_START)
				{
					if(g_paging_type == PAGING_TYPE_MIC)	//mic
					{
						set_dsp_source(g_Mic_enable?BP1048_SOURCE_DECODE_PAGER_MIC_LISTENING:BP1048_SOURCE_DECODE_PAGER_ONLY_LINE_LISTENING);
					}
					else	//music
					{
						set_dsp_source(BP1048_SOURCE_DECODE_PAGER_MUSIC_LISTENING);
					}
				}
				else
				{
					set_dsp_source(BP1048_SOURCE_DECODE_PAGER_MIC_MUSIC_LISTENING);
				}
			}
			else
			{
				lv_obj_set_state(btn!=NULL?btn:obj, LV_STATE_DEFAULT);
				//已经处于寻呼状态
				if(Paging_status == PAGING_START)
				{
					if(g_paging_type == PAGING_TYPE_MIC)	//mic
					{
						set_dsp_source(g_Mic_enable?BP1048_SOURCE_DECODE_PAGER_MIC:BP1048_SOURCE_DECODE_PAGER_ONLY_LINE);
					}
					else	//music
					{
						set_dsp_source(BP1048_SOURCE_DECODE_PAGER_MUSIC);
					}
				}
				else
				{
					set_dsp_source(BP1048_SOURCE_DECODE_PAGER_MIC_MUSIC);
				}
			}
			//打开或关闭功放
			if(g_Enable_local_listening)
				set_output_volume();
			Amp_Switch(g_Enable_local_listening);
		}
		else if(obj == control_button_list[CONTROL_BUTTON_SETTINGS].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_SETTINGS].obj)
		{
			#if 0
			if(m_stUser_Info.CurrentUserIndex!=0 && !m_stUser_Info.userInfo[m_stUser_Info.CurrentUserIndex].authority)
			{
				static const char * btns[] = {"OK", ""};
				lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
				lv_msgbox_add_btns(msg, btns);
				lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
				lv_msgbox_set_text(msg,"当前账户没有管理权限");
				return;
			}
			#endif

			if(enable_password_access_settings)
			{
				//static char user_password[64]; // 用于存储用户输入的密码

				static char * btns[] = {"Cancel", "OK",""};
				btns[0] = language_messagebox_cancel_text;
				btns[1] = language_messagebox_confirm_text;

				if(control_password_kb_obj)
				{
					lv_obj_del(control_password_kb_obj);
					control_password_kb_obj=NULL;
				}
				password_dialog = lv_msgbox_create(control_all_cont, NULL);
				lv_msgbox_add_btns(password_dialog, btns);
				lv_obj_add_style(password_dialog, LV_OBJ_PART_MAIN, &style_font_20);
				//lv_obj_set_event_cb(password_dialog, password_dialog_event_cb);
				lv_msgbox_set_text(password_dialog, language_control_input_settings_password_text);
				lv_obj_t *password_dialog_btnmatrix = lv_msgbox_get_btnmatrix(password_dialog);
				lv_btnmatrix_set_btn_ctrl(password_dialog_btnmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_set_event_cb(password_dialog, msg_password_to_settings_event_cb);

				password_textarea = lv_textarea_create(password_dialog, NULL);
				lv_obj_set_width(password_textarea, 200);
				lv_textarea_set_pwd_mode(password_textarea, true);
				lv_textarea_set_pwd_show_time(password_textarea,0);
				lv_textarea_set_one_line(password_textarea, true);
				//lv_textarea_set_accepted_chars(password_textarea, lv_textarea_accepted_chars_password);
				lv_textarea_set_text(password_textarea,"");
        		lv_textarea_set_placeholder_text(password_textarea, "Password");
				lv_obj_set_event_cb(password_textarea, password_settings_event_cb);

				//lv_msgbox_start_auto_close(password_dialog, 5000);
			}
			else
			{
				settings_main_win_start();
			}
		}
		else if(obj == control_button_list[CONTROL_BUTTON_VOLUME].obj || lv_obj_get_parent(obj) == control_button_list[CONTROL_BUTTON_VOLUME].obj)
		{
			bool isvolumeHidden=lv_obj_get_hidden(volume_slider);

			lv_obj_t *btn=lv_obj_get_child(obj, NULL);

			if(isvolumeHidden)
			{
				lv_obj_set_hidden(volume_slider, false);
				lv_obj_set_state(btn!=NULL?btn:obj, LV_STATE_CHECKED);
			}
			else
			{
				lv_obj_set_hidden(volume_slider, true);
				lv_obj_set_state(btn!=NULL?btn:obj, LV_STATE_DEFAULT);
			}
			if(isvolumeHidden)
			{
//				lv_obj_set_style_local_image_recolor_opa(obj, LV_IMGBTN_PART_MAIN, LV_BTN_STATE_RELEASED, LV_OPA_COVER);
//				lv_obj_set_style_local_image_recolor(obj, LV_IMGBTN_PART_MAIN, LV_BTN_STATE_RELEASED, LV_COLOR_YELLOW);
			}
			else
			{
//				lv_obj_set_style_local_image_recolor_opa(obj, LV_IMGBTN_PART_MAIN, LV_BTN_STATE_RELEASED, LV_OPA_COVER);
//				lv_obj_set_style_local_image_recolor(obj, LV_IMGBTN_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
			}
		}
		else if(obj == back_btn)
		{
			int current_win=GetCurrentWin();
			if(current_win == WIN_CONTROL)
			{
				if(Paging_status == PAGING_START || Paging_status == CALLING_START)
				{
					static const char * btns[] = {"OK", ""};
					lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
					lv_msgbox_add_btns(msg, btns);
					lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
					lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
					lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
					if(Paging_status == PAGING_START)
						lv_msgbox_set_text(msg,language_control_message_stopPaging_text);
					if(Paging_status == CALLING_START)
						lv_msgbox_set_text(msg,language_control_message_stopIntercom_text);
					return;
				}

				static char * btns[] = {"Cancel", "OK",""};
				btns[0] = language_messagebox_cancel_text;
				btns[1] = language_messagebox_confirm_text;
				lv_obj_t *msg = lv_msgbox_create(lv_scr_act(), NULL);
				lv_msgbox_add_btns(msg, btns);
				lv_obj_t *msg_btmatrix = lv_msgbox_get_btnmatrix(msg);
				//lv_btnmatrix_set_btn_ctrl(msg_btmatrix, 1, LV_BTNMATRIX_CTRL_CHECK_STATE);
				lv_obj_add_style(msg, LV_OBJ_PART_MAIN, &style_font_20);
				lv_obj_set_event_cb(msg, msg_backToLogin_event_cb);
				lv_msgbox_start_auto_close(msg, 5000);
				lv_msgbox_set_text(msg,language_control_message_logout_text);
				return;
			}
			else if(current_win == WIN_MUSICLIST)
			{
				musiclist_back_to_control_win(1);
			}
			else if(current_win == WIN_UDISK)
			{
				udisk_back_to_control_win(1);
			}
		}
		else if(obj == mic_switch_btn)
		{
			if(g_Mic_enable)
			{
				g_Mic_enable=0;
				lv_obj_set_state(obj, LV_STATE_CHECKED);
			}
			else
			{
				g_Mic_enable=1;
				lv_obj_set_state(obj, LV_STATE_DEFAULT);
			}
			lv_obj_invalidate(obj);
			#if (APP_TYPE != APP_JUSBE_MIXER)
			if(g_paging_type == PAGING_TYPE_MIC)	//当前是寻呼类型才改变，否则开关MIC没有意义
			{
				if(g_Mic_enable)
				{
					if(g_Enable_local_listening)
					{
						set_dsp_source(BP1048_SOURCE_DECODE_PAGER_MIC_LISTENING);
					}
					else
					{
						set_dsp_source(BP1048_SOURCE_DECODE_PAGER_MIC);
					}
				}
				else
				{
					if(g_Enable_local_listening)
					{
						set_dsp_source(BP1048_SOURCE_DECODE_PAGER_ONLY_LINE_LISTENING);
					}
					else
					{
						set_dsp_source(BP1048_SOURCE_DECODE_PAGER_ONLY_LINE);
					}
				}
			}
			#endif
			printf("mic_switch_btn...\n");
		}
		else if(obj == group_enter_btn)
		{
			if(group_enter_index == -1)
			{
				int selectedGroupIndex=-1;
				for(i = 0;i<m_stGroup_Info.TotalGroup;i++)
				{
					if(m_stGroup_Info.groupInfo[i].g_group_isSelect)
					{
						selectedGroupIndex=i;
						break;
					}
				}
				if(selectedGroupIndex != -1)
				{
					Zone_Group_SelectOrCancel(0,0xFFFF);	//先取消当前界面类型的按钮选中状态
					group_enter_index = selectedGroupIndex;
					control_win_current_page=1;
					lv_obj_set_state(group_enter_btn, LV_STATE_CHECKED);
					//刷新
					control_win_update(0XFF,1);
				}
			}
			else
			{
				UI_Group_ExitExtraMode();
				control_win_update(0XFF,1);
			}
		}
	}
}


void paing_status_show(int IsSelfcall)
{
	if(GetCurrentWin() != WIN_CONTROL)
			return;
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}
	lv_obj_t *btn=lv_obj_get_child(control_button_list[CONTROL_BUTTON_PAGING].obj, NULL);
	if(btn)
	{
		if(Paging_status == PAGING_START)
		{
			lv_obj_set_state(btn, LV_STATE_CHECKED);
		}
		else
		{
			lv_obj_set_state(btn, LV_STATE_DEFAULT);
		}
	}

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}

void call_Btn_status_show(int IsSelfcall)
{
	if(GetCurrentWin() != WIN_CONTROL)
			return;
	if(!IsSelfcall)
	{
		pthread_mutex_lock(&lvglMutex);
		lvglMutex_status=1;
	}
	lv_obj_t *btn=lv_obj_get_child(control_button_list[CONTROL_BUTTON_CALL].obj, NULL);
	if(Paging_status == CALLING_START)
	{
		lv_obj_set_state(btn, LV_STATE_CHECKED);
	}
	else
	{
		lv_obj_set_state(btn, LV_STATE_DEFAULT);
	}

	if(!IsSelfcall)
	{
		pthread_mutex_unlock(&lvglMutex);
		lvglMutex_status=0;
	}
}


void control_win_start(void)
{
	static char hasInitButton=0;
	if(!hasInitButton)
	{
		hasInitButton=1;
		if(g_bp1048_info.firmware_type == 0)
		{
			g_bp1048_info.firmware_type = BP1048_FW_NORMAL_TYPE;
		}
		init_control_button();	//初始化控制按钮信息
		init_musiclist_button();  //初始化音乐列表按钮信息
	}
	Account_Zone_Update();
	Account_Group_Update();

	control_all_cont = lv_cont_create(lv_scr_act(), NULL);
    lv_obj_set_size(control_all_cont,LV_HOR_RES_MAX,LV_VER_RES_MAX);
    lv_obj_set_pos(control_all_cont,0,0);		//此处为了解决默认情况下屏幕留边的问题
    lv_obj_set_style_local_pad_all(control_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(control_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_radius(control_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(control_all_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	lv_obj_set_event_cb(control_all_cont, control_all_cont_event_cb);

    lv_obj_t *control_head_box = lv_obj_create(control_all_cont, NULL);
	lv_obj_set_style_local_pad_all(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_margin_all(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    lv_obj_set_style_local_radius(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_border_width(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	if(IS_DISP_RES_1024)
	{
		lv_obj_set_size(control_head_box, LV_HOR_RES_MAX, 50);
	}
	else if(IS_DISP_RES_1280)
	{
		lv_obj_set_size(control_head_box, LV_HOR_RES_MAX, YRatio_600_to_800(50));
	}
	else if(IS_DISP_RES_800)
	{
		lv_obj_set_size(control_head_box, LV_HOR_RES_MAX, 50);
	}
	else if(IS_DISP_RES_600)
	{
		lv_obj_set_size(control_head_box, LV_HOR_RES_MAX, 50);
	}
	lv_obj_align(control_head_box, control_all_cont, LV_ALIGN_IN_TOP_MID,0,0);
	//lv_obj_set_style_local_value_str(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT,"Login分区");
	//lv_obj_set_style_local_value_font(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &style_font_20);
	control_head_label=lv_label_create(control_head_box, NULL);
	lv_obj_align(control_head_label, control_head_box, LV_ALIGN_CENTER,-2,-2);
	lv_label_set_align(control_head_label, LV_LABEL_ALIGN_CENTER);
	lv_label_set_text(control_head_label, "2021-05-10 14:45");
	lv_obj_set_auto_realign(control_head_label,true);
	lv_obj_set_width(control_head_label, lv_obj_get_width_grid(control_head_box, 2, 1));
	//lv_obj_add_style(control_head_label, LV_OBJ_PART_MAIN, &style_font_20);
	lv_obj_set_style_local_text_font(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &lv_font_montserrat_24);
	lv_obj_set_style_local_bg_color(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
	//lv_obj_set_style_local_bg_grad_color(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(0x1f,0x61,0x8d));
	//lv_obj_set_style_local_bg_grad_dir(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_obj_set_style_local_text_color(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_WHITE);
	lv_obj_set_style_local_radius(control_head_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	lv_obj_add_protect(control_head_box, LV_PROTECT_CLICK_FOCUS);
	

	mic_switch_btn=lv_imgbtn_create(control_head_box, NULL);
	lv_obj_align(mic_switch_btn, control_head_box, LV_ALIGN_IN_RIGHT_MID,35,5);
	lv_imgbtn_set_src(mic_switch_btn, LV_BTN_STATE_RELEASED, &mic_on);
	lv_imgbtn_set_src(mic_switch_btn, LV_BTN_STATE_PRESSED, &mic_on);
	lv_imgbtn_set_src(mic_switch_btn, LV_BTN_STATE_CHECKED_RELEASED, &mic_off);
	lv_imgbtn_set_src(mic_switch_btn, LV_BTN_STATE_CHECKED_PRESSED, &mic_off);
	lv_obj_set_event_cb(mic_switch_btn, round_btn_cb);

	lv_obj_set_hidden(mic_switch_btn,true);


	back_btn=lv_imgbtn_create(control_head_box, NULL);
	lv_obj_align(back_btn, control_head_box, LV_ALIGN_IN_LEFT_MID,23,0);
	lv_imgbtn_set_src(back_btn, LV_BTN_STATE_RELEASED, &pic_back);
	lv_imgbtn_set_src(back_btn, LV_BTN_STATE_PRESSED, &pic_back);
	lv_obj_set_event_cb(back_btn, round_btn_cb);

	group_enter_btn=lv_imgbtn_create(control_head_box, NULL);
	lv_obj_align(group_enter_btn, back_btn, LV_ALIGN_IN_LEFT_MID,170,7);
	lv_imgbtn_set_src(group_enter_btn, LV_BTN_STATE_RELEASED, &pic_enter);
	lv_imgbtn_set_src(group_enter_btn, LV_BTN_STATE_PRESSED, &pic_enter);
	lv_imgbtn_set_src(group_enter_btn, LV_BTN_STATE_CHECKED_PRESSED, &pic_quit);
	lv_imgbtn_set_src(group_enter_btn, LV_BTN_STATE_CHECKED_RELEASED, &pic_quit);
#if 0
	lv_obj_set_style_local_image_recolor_opa(group_enter_btn, LV_IMGBTN_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_60);
	lv_obj_set_style_local_image_recolor(group_enter_btn, LV_IMGBTN_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(255, 60, 60));
	lv_obj_set_style_local_image_recolor_opa(group_enter_btn, LV_IMGBTN_PART_MAIN, LV_STATE_CHECKED, LV_OPA_60);
	lv_obj_set_style_local_image_recolor(group_enter_btn, LV_IMGBTN_PART_MAIN, LV_STATE_CHECKED, LV_COLOR_MAKE(255, 60, 60));
#endif
	lv_obj_set_event_cb(group_enter_btn, round_btn_cb);
	//默认隐藏
	lv_obj_set_hidden(group_enter_btn,true);
	group_enter_index=-1;


    control_zone_cont = lv_obj_create(control_all_cont, NULL);

	lv_obj_set_event_cb(control_zone_cont, zone_all_cont_event_cb);

    //lv_obj_set_pos(control_zone_cont, 0, 0);
    lv_obj_set_y(control_zone_cont,50);

	if(IS_DISP_RES_1024)
	{
    	lv_obj_set_size(control_zone_cont, ZONE_POS_START_X+(5)*(ZONE_WIDTH+ZONE_BLANK)+6, LV_VER_RES_MAX-50-84);
	}
	else if(IS_DISP_RES_1280)
	{
		lv_obj_set_size(control_zone_cont,ZONE_POS_START_X+(6)*(ZONE_WIDTH+ZONE_BLANK)+6, LV_VER_RES_MAX-YRatio_600_to_800(50)-YRatio_600_to_800(86));
		lv_obj_set_y(control_zone_cont,YRatio_600_to_800(50));
	}
	else if(IS_DISP_RES_800)
	{
		lv_obj_set_size(control_zone_cont, ZONE_POS_START_X+(4)*(ZONE_WIDTH+ZONE_BLANK)+6, LV_VER_RES_MAX-50-74);
	}
	else if(IS_DISP_RES_600)
	{
		lv_obj_set_size(control_zone_cont, ZONE_POS_START_X+(3)*(ZONE_WIDTH+ZONE_BLANK)+6, LV_VER_RES_MAX-50-84);
	}
    //lv_obj_set_size(control_zone_cont, 1024, 600-50-84);
    lv_obj_set_style_local_pad_all(control_zone_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_margin_all(control_zone_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_radius(control_zone_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_bg_color(control_zone_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(250,250,250));
	lv_obj_set_style_local_border_width(control_zone_cont, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 1);
	//lv_obj_set_click(control_main_cont, false);
	lv_obj_add_protect(control_zone_cont, LV_PROTECT_CLICK_FOCUS);

	lv_style_reset(&style_box_zone_cont);  //重置样式释放内存
    lv_style_init(&style_box_zone_cont);
    int i;
    for(i = 0;i<CONTROL_PAGE_ZONE_SHOW_TOTAL_NUM;i++)
    {
    	control_zone_cont_arry[i]=lv_cont_create(control_zone_cont, NULL);

    	lv_obj_set_style_local_pad_all(control_zone_cont_arry[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
    	lv_obj_set_style_local_margin_all(control_zone_cont_arry[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

    	int pos_x=ZONE_POS_START_X+(i%CONTROL_PAGE_ZONE_SHOW_LINE_NUM)*(ZONE_WIDTH+ZONE_BLANK);
    	int pos_y=ZONE_POS_START_Y+(i/CONTROL_PAGE_ZONE_SHOW_LINE_NUM)*(ZONE_HEIGHT+ZONE_BLANK);
    	//printf("POS_X=%d,POS_Y=%d\n",pos_x,pos_y);
		lv_obj_set_pos(control_zone_cont_arry[i],pos_x,pos_y);
		lv_obj_set_size(control_zone_cont_arry[i],ZONE_WIDTH,ZONE_HEIGHT);

    	lv_obj_set_event_cb(control_zone_cont_arry[i], zone_cont_event_cb);

		control_zone_tx_arry[i].zone_name = lv_label_create(control_zone_cont_arry[i], NULL);
		control_zone_tx_arry[i].source_name = lv_label_create(control_zone_cont_arry[i], NULL);
		control_zone_tx_arry[i].volume_value = lv_label_create(control_zone_cont_arry[i], NULL);

		//创建底部音源图标
		control_zone_tx_arry[i].source_icon = lv_img_create(control_zone_cont_arry[i], NULL);
		lv_img_set_src(control_zone_tx_arry[i].source_icon, &LV_SYMBOL_PAUSE);
		lv_obj_set_style_local_text_font(control_zone_tx_arry[i].source_icon, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, &lv_font_montserrat_16);
		lv_obj_set_style_local_image_recolor_opa(control_zone_tx_arry[i].source_icon, LV_IMG_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_COVER);
		lv_obj_set_style_local_image_recolor(control_zone_tx_arry[i].source_icon, LV_IMG_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_MAKE(0x75, 0x75, 0x75));
		//lv_img_set_zoom(control_zone_tx_arry[i].source_icon,155);

		 lv_obj_align(control_zone_tx_arry[i].zone_name, control_zone_cont_arry[i], LV_ALIGN_IN_TOP_MID,0,5);
		 lv_obj_align(control_zone_tx_arry[i].source_name, control_zone_cont_arry[i], LV_ALIGN_CENTER,0,1);
		 //lv_obj_align(control_zone_tx_arry[i].volume_value, control_zone_tx_arry[i].source_icon, LV_ALIGN_OUT_RIGHT_MID,0,0);

		 lv_obj_align(control_zone_tx_arry[i].source_icon, control_zone_cont_arry[i], LV_ALIGN_IN_BOTTOM_LEFT,6,-7);
		 lv_obj_align(control_zone_tx_arry[i].volume_value, control_zone_cont_arry[i], LV_ALIGN_IN_BOTTOM_MID,4,-5);

		 lv_obj_set_hidden(control_zone_tx_arry[i].source_icon,true);

		  lv_label_set_align(control_zone_tx_arry[i].zone_name, LV_LABEL_ALIGN_CENTER);
		  lv_label_set_align(control_zone_tx_arry[i].source_name, LV_LABEL_ALIGN_CENTER);
		  lv_label_set_align(control_zone_tx_arry[i].volume_value, LV_LABEL_ALIGN_CENTER);
		  lv_obj_set_auto_realign(control_zone_tx_arry[i].zone_name,true);
		  lv_obj_set_auto_realign(control_zone_tx_arry[i].source_name,true);
		  lv_obj_set_auto_realign(control_zone_tx_arry[i].volume_value,true);

		  //lv_label_set_long_mode(control_zone_tx_arry[i].source_name, LV_LABEL_LONG_SROLL_CIRC);
		  lv_label_set_long_mode(control_zone_tx_arry[i].source_name,LV_LABEL_LONG_DOT);

		  lv_style_set_bg_color(&style_box_zone_cont, LV_STATE_DEFAULT, LV_COLOR_WHITE);
		  lv_style_set_bg_color(&style_box_zone_cont, LV_STATE_CHECKED, get_theme_color());
		  lv_style_set_bg_color(&style_box_zone_cont, LV_STATE_DISABLED, LV_COLOR_MAKE(177,177,177));
		  lv_obj_add_style(control_zone_cont_arry[i], LV_OBJ_PART_MAIN, &style_box_zone_cont);

		  lv_obj_set_size(control_zone_tx_arry[i].source_name,ZONE_WIDTH,28);	//高度尺寸不够的话会上下滚动.

		  lv_obj_add_style(control_zone_cont_arry[i], LV_OBJ_PART_MAIN, &style_font_20);

		
		  lv_obj_add_style(control_zone_tx_arry[i].volume_value, LV_OBJ_PART_MAIN, &style_font_15);			

		  lv_obj_set_style_local_radius(control_zone_cont_arry[i], LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 7);
		  //lv_obj_set_hidden(control_zone_cont_arry[i],1);
    }

    //创建标签栏,显示
    control_label_info=lv_label_create(control_zone_cont, NULL);
	if(IS_DISP_RES_1024)
	{
		lv_obj_align(control_label_info, control_zone_cont, LV_ALIGN_IN_BOTTOM_MID,8,-13);
	}
	else if(IS_DISP_RES_1280)
	{
		lv_obj_align(control_label_info, control_zone_cont, LV_ALIGN_IN_BOTTOM_MID,XRatio_1024_to_1280(8),YRatio_600_to_800(-13));
	}
	else if(IS_DISP_RES_800)
	{
		lv_obj_align(control_label_info, control_zone_cont, LV_ALIGN_IN_BOTTOM_LEFT,50,-8);
	}
	else if(IS_DISP_RES_600)
	{
		lv_obj_align(control_label_info, control_zone_cont, LV_ALIGN_IN_BOTTOM_MID,8,-13);
	}
	lv_label_set_align(control_label_info, LV_LABEL_ALIGN_CENTER);
	//lv_label_set_text(control_label_info, "[ 第 1 页 , 共 20 页 ]                                               [ 分区总数 : 400 , 在线分区数量 : 380 , 已选择分区数量 : 2 ]");
	lv_obj_set_auto_realign(control_label_info,true);
	lv_obj_add_style(control_label_info, LV_OBJ_PART_MAIN, &style_font_15);
	lv_obj_set_style_local_text_color(control_label_info, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());

#if 0
	lv_obj_t *lable_info2=lv_label_create(control_zone_cont, NULL);
	lv_obj_align(lable_info2, control_zone_cont, LV_ALIGN_IN_BOTTOM_RIGHT,-50,-10);
	lv_label_set_align(lable_info2, LV_LABEL_ALIGN_CENTER);
	lv_label_set_text(lable_info2, "[ 分区总数 : 400 , 在线分区数量 : 380 , 已选择分区数量 : 2 ]");
	lv_obj_set_auto_realign(lable_info2,true);
	lv_obj_add_style(lable_info2, LV_OBJ_PART_MAIN, &style_font_20);
	lv_obj_set_style_local_text_color(lable_info2, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
#endif

#if 0
    //lv_obj_set_gesture_parent(control_zone_cont, false);
    //lv_obj_set_event_cb(control_zone_cont, zone_cont_event_cb2);

    control_zone_bt_pre=lv_imgbtn_create(control_zone_cont, NULL);
    control_zone_bt_next=lv_imgbtn_create(control_zone_cont, NULL);
    lv_obj_align(control_zone_bt_pre, control_zone_cont, LV_ALIGN_IN_BOTTOM_LEFT,120,-8);
    lv_obj_align(control_zone_bt_next, control_zone_cont, LV_ALIGN_IN_BOTTOM_RIGHT,-35,-8);
   // lv_imgbtn_set_src(control_zone_bt_pre, LV_STATE_DEFAULT, &previous_30);
    //lv_imgbtn_set_src(control_zone_bt_next, LV_STATE_DEFAULT, &next_30);

	lv_imgbtn_set_src(control_zone_bt_pre, LV_BTN_STATE_RELEASED, &previous_30);
	lv_imgbtn_set_src(control_zone_bt_pre, LV_BTN_STATE_PRESSED, &previous_30);
	lv_imgbtn_set_src(control_zone_bt_next, LV_BTN_STATE_RELEASED, &next_30);
	lv_imgbtn_set_src(control_zone_bt_next, LV_BTN_STATE_PRESSED, &next_30);

	lv_style_reset(&style_box_bt_zone_page);  //重置样式释放内存
	lv_style_init(&style_box_bt_zone_page);
	//lv_style_set_bg_color(&style_box_bt_zone_page, LV_BTN_STATE_PRESSED, LV_COLOR_SILVER);
	//lv_style_set_bg_opa(&style_box_bt_zone_page, LV_BTN_STATE_PRESSED, LV_OPA_50);
	lv_style_set_image_recolor_opa(&style_box_bt_zone_page, LV_STATE_PRESSED, LV_OPA_COVER);
	lv_style_set_image_recolor(&style_box_bt_zone_page, LV_STATE_PRESSED, LV_COLOR_GREEN);

#if 0
    lv_style_set_shadow_ofs_x(&style_box_bt_zone_page, LV_STATE_PRESSED, 5);
    lv_style_set_shadow_ofs_y(&style_box_bt_zone_page, LV_STATE_PRESSED, 5);
    lv_style_set_shadow_width(&style_box_bt_zone_page, LV_STATE_DEFAULT, 8);
    lv_style_set_shadow_color(&style_box_bt_zone_page, LV_STATE_DEFAULT, LV_COLOR_GREEN);
#endif
	lv_obj_add_style(control_zone_bt_pre,LV_IMGBTN_PART_MAIN,&style_box_bt_zone_page);
	lv_obj_add_style(control_zone_bt_next,LV_IMGBTN_PART_MAIN,&style_box_bt_zone_page);
	lv_obj_set_event_cb(control_zone_bt_pre, page_pre_next_event_cb);
	lv_obj_set_event_cb(control_zone_bt_next, page_pre_next_event_cb);

	control_label_page=lv_label_create(control_zone_cont, NULL);
	lv_label_set_text_fmt(control_label_page, "%d/%d",1,1);
	lv_obj_align(control_label_page, control_zone_cont, LV_ALIGN_IN_BOTTOM_MID, 0, -26);


	//音量滑动条
    volume_slider = lv_slider_create(control_main_cont, NULL);
    lv_slider_set_value(volume_slider, 50, LV_ANIM_OFF);
    lv_obj_align(volume_slider, control_main_cont, LV_ALIGN_IN_BOTTOM_MID,-32,-35);
    lv_obj_set_event_cb(volume_slider, slider_event_cb);
    lv_obj_set_width(volume_slider, 300);
   // lv_event_send(volume_slider, LV_EVENT_VALUE_CHANGED, NULL);      /*To refresh the text*/
    /*Use the knobs style value the display the current value in focused state*/
    lv_obj_set_style_local_margin_top(volume_slider, LV_SLIDER_PART_BG, LV_STATE_DEFAULT, LV_DPX(25));
    lv_obj_set_style_local_value_font(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, lv_theme_get_font_small());
    lv_obj_set_style_local_value_ofs_y(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_FOCUSED, - LV_DPX(25));
    lv_obj_set_style_local_value_opa(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, LV_OPA_TRANSP);
    lv_obj_set_style_local_value_opa(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_FOCUSED, LV_OPA_COVER);
    lv_obj_set_style_local_transition_time(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, 300);
    lv_obj_set_style_local_transition_prop_5(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, LV_STYLE_VALUE_OFS_Y);
    lv_obj_set_style_local_transition_prop_6(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, LV_STYLE_VALUE_OPA);

	volume_high=lv_img_create(control_main_cont, NULL);
	volume_low=lv_img_create(control_main_cont, NULL);
    lv_img_set_src(volume_high, &audioh_25);
    lv_img_set_src(volume_low, &audiol_25);
    lv_obj_align(volume_low, volume_slider, LV_ALIGN_IN_LEFT_MID,-35,0);
    lv_obj_align(volume_high, volume_slider, LV_ALIGN_IN_RIGHT_MID,42,0);

#endif


	//创建右侧两个翻页图标
#if 0
	control_zone_bt_pre=lv_imgbtn_create(control_all_cont, NULL);
	control_zone_bt_next=lv_imgbtn_create(control_all_cont, NULL);
	lv_obj_set_pos(control_zone_bt_pre,974,70);
	lv_obj_set_pos(control_zone_bt_next,974,468);

	lv_imgbtn_set_src(control_zone_bt_pre, LV_BTN_STATE_PRESSED, &pic_up);
	lv_imgbtn_set_src(control_zone_bt_pre, LV_BTN_STATE_RELEASED, &pic_up);
	lv_imgbtn_set_src(control_zone_bt_next, LV_BTN_STATE_PRESSED, &pic_down);
	lv_imgbtn_set_src(control_zone_bt_next, LV_BTN_STATE_RELEASED, &pic_down);

	lv_obj_set_style_local_image_recolor_opa(control_zone_bt_pre, LV_IMGBTN_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_COVER);
	lv_obj_set_style_local_image_recolor(control_zone_bt_pre, LV_IMGBTN_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
	lv_obj_set_style_local_image_recolor_opa(control_zone_bt_next, LV_IMGBTN_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_COVER);
	lv_obj_set_style_local_image_recolor(control_zone_bt_next, LV_IMGBTN_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
#endif
	control_zone_bt_pre=lv_obj_create(control_all_cont, NULL);
	lv_obj_set_style_local_border_width(control_zone_bt_pre, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_pad_all(control_zone_bt_pre, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_margin_all(control_zone_bt_pre, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_size(control_zone_bt_pre, 50, 50);
		lv_obj_set_pos(control_zone_bt_pre,970, 60);
	}
	else if(IS_DISP_RES_1280)
    {
		lv_obj_set_size(control_zone_bt_pre, XRatio_1024_to_1280(50), YRatio_600_to_800(50));
		lv_obj_set_pos(control_zone_bt_pre,XRatio_1024_to_1280(970), YRatio_600_to_800(60));
    }
	else if(IS_DISP_RES_800)
	{
		lv_obj_set_size(control_zone_bt_pre, 42, 42);
		lv_obj_set_pos(control_zone_bt_pre,742, 60);
	}
	else if(IS_DISP_RES_600)
	{
		lv_obj_set_size(control_zone_bt_pre, 50, 50);
		lv_obj_set_pos(control_zone_bt_pre,540, 60);
	}
	//lv_obj_set_style_local_bg_color(control_zone_bt_pre, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_BLACK);

	lv_obj_t *prebtn=lv_imgbtn_create(control_zone_bt_pre, NULL);
	lv_obj_align(prebtn, control_zone_bt_pre, LV_ALIGN_IN_LEFT_MID,6,7);
	lv_imgbtn_set_src(prebtn, LV_BTN_STATE_RELEASED, &pic_up);
	lv_imgbtn_set_src(prebtn, LV_BTN_STATE_PRESSED, &pic_up);
	lv_obj_set_style_local_image_recolor_opa(prebtn, LV_IMGBTN_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_COVER);
	lv_obj_set_style_local_image_recolor(prebtn, LV_IMGBTN_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
	lv_obj_set_style_local_image_recolor_opa(prebtn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_OPA_COVER);
	lv_obj_set_style_local_image_recolor(prebtn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_COLOR_BLACK);

	control_zone_bt_next=lv_obj_create(control_all_cont, NULL);
	lv_obj_set_style_local_border_width(control_zone_bt_next, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_pad_all(control_zone_bt_next, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	lv_obj_set_style_local_margin_all(control_zone_bt_next, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_size(control_zone_bt_next, 50, 50);
		lv_obj_set_pos(control_zone_bt_next,970, 450);
	}
	else if(IS_DISP_RES_1280)
    {
		lv_obj_set_size(control_zone_bt_next, XRatio_1024_to_1280(50), YRatio_600_to_800(50));
		lv_obj_set_pos(control_zone_bt_next,XRatio_1024_to_1280(970), YRatio_600_to_800(450));
    }
	else if(IS_DISP_RES_800)
	{
		lv_obj_set_size(control_zone_bt_next, 42, 42);
		lv_obj_set_pos(control_zone_bt_next,742, 350);
	}
	else if(IS_DISP_RES_600)
	{
		lv_obj_set_size(control_zone_bt_next, 50, 50);
		lv_obj_set_pos(control_zone_bt_next,540, 850);
	}
	//lv_obj_set_style_local_bg_color(control_zone_bt_pre, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_BLACK);

	lv_obj_t *nextbtn=lv_imgbtn_create(control_zone_bt_next, prebtn);
	lv_obj_align(nextbtn, control_zone_bt_next, LV_ALIGN_IN_LEFT_MID,6,7);
	lv_imgbtn_set_src(nextbtn, LV_BTN_STATE_RELEASED, &pic_down);
	lv_imgbtn_set_src(nextbtn, LV_BTN_STATE_PRESSED, &pic_down);

	lv_obj_set_event_cb(control_zone_bt_pre, page_pre_next_event_cb);
	lv_obj_set_event_cb(control_zone_bt_next, page_pre_next_event_cb);
	lv_obj_set_event_cb(prebtn, page_pre_next_event_cb);
	lv_obj_set_event_cb(nextbtn, page_pre_next_event_cb);


	//创建音量滑动条
	volume_slider = lv_slider_create(control_all_cont, NULL);
	lv_slider_set_value(volume_slider, 50, LV_ANIM_OFF);
	lv_obj_set_event_cb(volume_slider, slider_event_cb);
	if(IS_DISP_RES_1024)
	{
		lv_obj_align(volume_slider, control_zone_cont, LV_ALIGN_OUT_RIGHT_MID,30,-115);
		lv_obj_set_width(volume_slider, 10);
		lv_obj_set_height(volume_slider, 240);
	}
	else if(IS_DISP_RES_1280)
    {
		lv_obj_align(volume_slider, control_zone_cont, LV_ALIGN_OUT_RIGHT_MID,XRatio_1024_to_1280(30)+4,YRatio_600_to_800(-115)-1);
		lv_obj_set_width(volume_slider, XRatio_1024_to_1280(12));
		lv_obj_set_height(volume_slider, YRatio_600_to_800(240)+5);
    }
	else if(IS_DISP_RES_800)
	{
		lv_obj_align(volume_slider, control_zone_cont, LV_ALIGN_OUT_RIGHT_MID,30,-107);
		lv_obj_set_width(volume_slider, 10);
		lv_obj_set_height(volume_slider, 225);
	}
	else if(IS_DISP_RES_600)
	{
		lv_obj_align(volume_slider, control_zone_cont, LV_ALIGN_OUT_RIGHT_MID,30,-200);
		lv_obj_set_width(volume_slider, 10);
		lv_obj_set_height(volume_slider, 400);
	}
   // lv_event_send(volume_slider, LV_EVENT_VALUE_CHANGED, NULL);      /*To refresh the text*/
	/*Use the knobs style value the display the current value in focused state*/
	lv_obj_set_style_local_margin_top(volume_slider, LV_SLIDER_PART_BG, LV_STATE_DEFAULT, LV_DPX(25));

	lv_obj_set_style_local_value_font(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, lv_theme_get_font_small());
	//lv_obj_set_style_local_value_ofs_y(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_FOCUSED, - LV_DPX(25));
	lv_obj_set_style_local_value_opa(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, LV_OPA_TRANSP);
	lv_obj_set_style_local_value_opa(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_FOCUSED, LV_OPA_COVER);

	lv_obj_set_style_local_transition_time(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, 300);
	lv_obj_set_style_local_transition_prop_5(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, LV_STYLE_VALUE_OFS_Y);
	lv_obj_set_style_local_transition_prop_6(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, LV_STYLE_VALUE_OPA);

	lv_obj_set_style_local_bg_color(volume_slider, LV_SLIDER_PART_INDIC, LV_STATE_DEFAULT, get_theme_color());
	lv_obj_set_style_local_bg_color(volume_slider, LV_SLIDER_PART_KNOB, LV_STATE_DEFAULT, get_theme_color());

	//默认隐藏音量滑动条
	lv_obj_set_hidden(volume_slider, true);



	//创建底部控制栏
	lv_obj_t *control_bottom_box = lv_obj_create(control_all_cont, control_all_cont);
	//lv_obj_set_pos(control_zone_cont, 0, 0);
	if(IS_DISP_RES_1024)
	{
		lv_obj_set_y(control_bottom_box,516);
		lv_obj_set_size(control_bottom_box, LV_HOR_RES_MAX, LV_VER_RES_MAX-516);
	}
	else if(IS_DISP_RES_1280)
    {
		lv_obj_set_y(control_bottom_box,700);
		lv_obj_set_size(control_bottom_box, LV_HOR_RES_MAX, LV_VER_RES_MAX-(700));
    }
	else if(IS_DISP_RES_800)
	{
		lv_obj_set_y(control_bottom_box,416);
		lv_obj_set_size(control_bottom_box, LV_HOR_RES_MAX, LV_VER_RES_MAX-416);
	}
	else if(IS_DISP_RES_600)
	{
		lv_obj_set_y(control_bottom_box,940);
		lv_obj_set_size(control_bottom_box, LV_HOR_RES_MAX, LV_VER_RES_MAX-940);
	}
	lv_obj_set_style_local_border_width(control_bottom_box, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	//底部控制按钮
	int button_index=0;
	for(i=0;i<MAX_CONTROL_NUM;i++)
	{
		#if(APP_TYPE == APP_JUSBE_MIXER)
		{
			if( i == CONTROL_BUTTON_LISTEN || i == CONTROL_BUTTON_UDISK || i == CONTROL_BUTTON_CALL )
			{
				continue;
			}
		}
		#endif

		#if LIMIT_MINIMUM_FUNCTION
		if( i == CONTROL_BUTTON_ZONEGROUP || i == CONTROL_BUTTON_UDISK || i == CONTROL_BUTTON_CALL || i == CONTROL_BUTTON_LISTEN || i == CONTROL_BUTTON_MUSIC )
		{
			continue;
		}
		#endif

		if(g_bp1048_info.firmware_type == BP1048_FW_NORMAL_TYPE)
		{
			if( i == CONTROL_BUTTON_UDISK )
			{
				continue;
			}
		}
		if(!g_isSupportCall)
		{
			if( i == CONTROL_BUTTON_CALL )
			{
				continue;
			}
		}
		control_button_list[i].obj=lv_obj_create(control_bottom_box, NULL);
	   // lv_obj_align(test_area, control_bottom_box, LV_ALIGN_IN_TOP_MID,0,40);
	    lv_obj_set_style_local_bg_color(control_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, get_theme_color());
	    lv_obj_set_style_local_radius(control_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_RADIUS_CIRCLE);
		if(IS_DISP_RES_1024)
		{
	    	lv_obj_set_size(control_button_list[i].obj, 48, 48);
		}
		else if(IS_DISP_RES_1280)
		{
			lv_obj_set_size(control_button_list[i].obj, 56, 56);
		}
		else if(IS_DISP_RES_800)
		{
			lv_obj_set_size(control_button_list[i].obj, 42, 42);
		}
		else if(IS_DISP_RES_600)
		{
			lv_obj_set_size(control_button_list[i].obj, 48, 48);
		}
		#if(APP_TYPE == APP_JUSBE_MIXER)
		lv_obj_set_pos(control_button_list[i].obj, 40+button_index*140, 8);
		#else
		if(g_bp1048_info.firmware_type == BP1048_FW_NORMAL_TYPE)
		{
			if(!g_isSupportCall)
			{
				if(IS_DISP_RES_1024)
				{
					lv_obj_set_pos(control_button_list[i].obj, 40+button_index*120, 8);
				}
				else if(IS_DISP_RES_1280)
				{
					lv_obj_set_pos(control_button_list[i].obj, 46+button_index*145, 8);
				}
				else if(IS_DISP_RES_800)
				{
					lv_obj_set_pos(control_button_list[i].obj, 20+button_index*107, 0);
				}
				else if(IS_DISP_RES_600)
				{
					lv_obj_set_pos(control_button_list[i].obj, 40+button_index*120, 8);
				}
			}
			else
			{
				if(IS_DISP_RES_1024)
				{
					lv_obj_set_pos(control_button_list[i].obj, 40+button_index*106, 8);
				}
				else if(IS_DISP_RES_1280)
				{
					lv_obj_set_pos(control_button_list[i].obj, 46+button_index*130, 8);
				}
				else if(IS_DISP_RES_800)
				{
					lv_obj_set_pos(control_button_list[i].obj, 20+button_index*84, 0);
				}
				else if(IS_DISP_RES_600)
				{
					lv_obj_set_pos(control_button_list[i].obj, 40+button_index*106, 8);
				}
			}
		}
		else if(g_bp1048_info.firmware_type == BP1048_FW_UDISK_TYPE)
		{
			if(!g_isSupportCall)
			{
				if(IS_DISP_RES_1024)
				{
					lv_obj_set_pos(control_button_list[i].obj, 40+button_index*106, 8);
				}
				else if(IS_DISP_RES_1280)
				{
					lv_obj_set_pos(control_button_list[i].obj, 46+button_index*130, 8);
				}
				else if(IS_DISP_RES_800)
				{
					lv_obj_set_pos(control_button_list[i].obj, 20+button_index*83, 0);
				}
				else if(IS_DISP_RES_600)
				{
					lv_obj_set_pos(control_button_list[i].obj, 40+button_index*106, 8);
				}
			}
			else
			{
				if(IS_DISP_RES_1024)
				{
					lv_obj_set_pos(control_button_list[i].obj, 40+button_index*95, 8);
				}
				else if(IS_DISP_RES_1280)
				{
					lv_obj_set_pos(control_button_list[i].obj, 46+button_index*120, 8);
				}
				else if(IS_DISP_RES_800)
				{
					lv_obj_set_pos(control_button_list[i].obj, 20+button_index*74, 0);
				}
				else if(IS_DISP_RES_600)
				{
					lv_obj_set_pos(control_button_list[i].obj, 40+button_index*95, 8);
				}
			}
		}
		#endif

		#if LIMIT_MINIMUM_FUNCTION
		lv_obj_set_pos(control_button_list[i].obj, 120+button_index*175, 8);
		#endif

	    lv_obj_set_style_local_border_width(control_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	    lv_obj_set_style_local_pad_all(control_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
	    lv_obj_set_style_local_margin_all(control_button_list[i].obj, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);

	    lv_obj_t *imgbtn=lv_imgbtn_create(control_button_list[i].obj, NULL);
		lv_obj_align(imgbtn, control_button_list[i].obj, LV_ALIGN_CENTER,50,5);
		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_RELEASED, &control_button_list[i].pic);
		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_PRESSED, &control_button_list[i].pic);

		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_CHECKED_RELEASED, &control_button_list[i].pic);
		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_CHECKED_PRESSED, &control_button_list[i].pic);

		lv_imgbtn_set_src(imgbtn, LV_BTN_STATE_DISABLED, &control_button_list[i].pic);

		lv_obj_set_style_local_image_recolor_opa(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_OPA_COVER);
		lv_obj_set_style_local_image_recolor(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_PRESSED, LV_COLOR_BLACK);

		lv_obj_set_style_local_image_recolor_opa(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_CHECKED, LV_OPA_COVER);
		lv_obj_set_style_local_image_recolor(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_CHECKED, LV_COLOR_YELLOW);

		#if ENABLE_CALL_FUNCTION
		lv_obj_set_style_local_image_recolor_opa(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_DISABLED, LV_OPA_COVER);
		lv_obj_set_style_local_image_recolor(imgbtn, LV_IMGBTN_PART_MAIN, LV_STATE_DISABLED, LV_COLOR_MAKE(177,177,177));
		#endif


		//创建label显示按钮标签
		control_button_list[i].label=lv_label_create(control_all_cont, NULL);
		lv_obj_align(control_button_list[i].label, control_button_list[i].obj, LV_ALIGN_OUT_BOTTOM_MID,0,1);
		lv_label_set_align(control_button_list[i].label, LV_LABEL_ALIGN_CENTER);
		lv_label_set_text(control_button_list[i].label, control_button_list[i].nameCN);
		lv_obj_set_auto_realign(control_button_list[i].label,true);
		lv_obj_set_width(control_button_list[i].label, lv_obj_get_width_grid(control_button_list[i].obj, 1, 1));
		lv_obj_add_style(control_button_list[i].label, LV_OBJ_PART_MAIN, &style_font_15);


	    lv_obj_set_event_cb(control_button_list[i].obj, round_btn_cb);
	    lv_obj_set_event_cb(imgbtn, round_btn_cb);

		button_index++;
	}


	//temp_test();

	//如果进入control界面时消防警报键已经按下，那么此次按键无效
	if(paging_alarm_press_flag)
	{
		paging_alarm_valid_flag = 0;
		paging_alarm_press_flag = 0;
	}

    SetCurrentWin(WIN_CONTROL);
    control_page_type = PAGE_ZONE;
    control_win_current_page=1;

    //取消选择所有分区
    for(i=0;i<m_stZone_Info.ExistTotalZone;i++)
    {
    	m_stZone_Info.zoneInfo[i].g_zone_isSelect=0;
    }
    memset(control_zone_click_arry,0,sizeof(control_zone_click_arry));

	//发送账户信息给服务器
	SendTo_Host_Account_Info();

	screen_control=lv_scr_act();

    control_win_update(PAGE_ZONE,1);

	//龙之音V1版本，切换用户登录后，需要清空上传存储信息
	#if (IS_LZY_NEW_TRANSMITTER_OR_PAGER)
	memset(&songUploadInfo,0,sizeof(songUploadInfo));
	//每次重新登录到控制页面，重新获取存储信息
	Send_host_get_account_storageCapacity(m_stUser_Info.CurrentUserName);
	#endif

}
