#!/bin/sh
# udhcpc script edited by <PERSON> <<EMAIL>>

RESOLV_CONF="/customer/resolv.conf"

[ -n "$1" ] || { echo "Error: should be called from udhcpc"; exit 1; }

NETMASK=""
[ -n "$subnet" ] && NETMASK="netmask $subnet"
BROADCAST="broadcast +"
[ -n "$broadcast" ] && BROADCAST="broadcast $broadcast"

case "$1" in
	deconfig)
		echo "Setting IP address 0.0.0.0 on $interface"
		ifconfig $interface 0.0.0.0
		;;
		
	#leasefail|nak)
	leasefail)
		ip=`ifconfig -a|grep inet|grep -v 127.0.0.1|grep -v inet6|awk '{print $2}'|tr -d "addr:"​`
		echo "autoip leasefail:"$ip
		#if [[ "$ip" =~ ************* ]]; then
		#if [[ $(expr match "$ip" '169.254*') != 0 ]]; then
			#echo "该网段是169.254.*.*网段"
		if [ -n "$ip" ]; then
			echo ${#ip}
			echo "IP exists,not handing!"
		else
			echo "使用临时IP"
			zcip -f -q eth0 /customer/App/zcip.script
			route add -net ********* netmask ********* eth0
		fi
		;;

	renew|bound)
		echo "Setting IP address $ip on $interface"
		ifconfig $interface $ip $NETMASK $BROADCAST

		if [ -n "$router" ] ; then
			echo "Deleting routers"
			while route del default gw 0.0.0.0 dev $interface ; do
				:
			done

			metric=0
			for i in $router ; do
				echo "Adding router $i"
				route add default gw $i dev $interface metric $metric
				: $(( metric += 1 ))
			done
		fi

		echo "Recreating $RESOLV_CONF"
		# If the file is a symlink somewhere (like /etc/resolv.conf
		# pointing to /run/resolv.conf), make sure things work.
		realconf=$(readlink -f "$RESOLV_CONF" 2>/dev/null || echo "$RESOLV_CONF")
		tmpfile="$realconf-$$"
		> "$tmpfile"
		[ -n "$domain" ] && echo "search $domain" >> "$tmpfile"
		for i in $dns ; do
			echo " Adding DNS server $i"
			echo "nameserver $i" >> "$tmpfile"
		done
		mv "$tmpfile" "$realconf"
		;;
esac

exit 0
