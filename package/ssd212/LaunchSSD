#!/bin/sh
APP_DIR_PATH=/customer/App

#coredump文件最大限制25MB
ulimit -c 25600
echo "| /customer/App/core.sh" > /proc/sys/kernel/core_pattern

cd $APP_DIR_PATH
if [ -e $APP_DIR_PATH/PREWORK ]; then
	echo "INIT PREWORK"
	$APP_DIR_PATH/PREWORK
	rm $APP_DIR_PATH/PREWORK
else
	echo "Not Found PREWORK"
fi


echo "Init Environment"

export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/config/lib:$APP_DIR_PATH/resources/lib:$APP_DIR_PATH/resources/lib/ffmpeg:$APP_DIR_PATH/resources/lib/curl:/customer/libdns

mkdir -p /customer/App/mnt/udisk
mount --bind /customer/App/mnt /mnt



RESET_COUNT_FILE=$APP_DIR_PATH/reset_count
if [ ! -e $RESET_COUNT_FILE ]; then
	echo "reset_count is not exist!,touch it!"
	touch $RESET_COUNT_FILE
	echo 0 > $RESET_COUNT_FILE
fi

resetCount=`cat $RESET_COUNT_FILE`

echo "reset_count:$resetCount"
if expr "$resetCount" : [0-9]*$; then
	if [ "$resetCount" -lt 7 ]; then
		echo "reset_count<7,normal start!"
		echo $((++resetCount)) >reset_count
		sync
		echo "RUN NetSpeaker"
		$APP_DIR_PATH/PagerSSD &
	else
		echo "reset_count>=6,reboot and restore!"
		echo 0 >reset_count
		rm $APP_DIR_PATH/appInit
		sync
		reboot
	fi
else
	echo "read reset_count error!"
	$APP_DIR_PATH/PagerSSD &
fi
