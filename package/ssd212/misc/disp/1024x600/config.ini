[ROOT]
m_pnlList = {SAT070AT50H18BH_Demo1,}
m_hdmiList = {\
    DACOUT_576I,\
    DACOUT_480I,\
    DACOUT_1080P_24,\
    DACOUT_1080P_25,\
    DACOUT_1080P_30,\
    DACOUT_1080P_50,\
    DACOUT_720P_50,\
    DACOUT_720P_60,\
    DACOUT_1080I_50,\
    DACOUT_1080I_60,\
    DACOUT_1080P_60,\
    DACOUT_576P,\
    DACOUT_480P,\
    DACOUT_2K2KP_30,\
    DACOUT_2K2KP_60,\
    DACOUT_4K2KP_24,\
    DACOUT_4K2KP_25,\
    DACOUT_4K2KP_30,\
    DACOUT_4K2KP_50,\
    DACOUT_4K2KP_60,\
    VGAOUT_1024x768P_60,\
    VGAOUT_1280x1024P_60,\
    VGAOUT_1366x768P_60,\
    VGAOUT_1440x900P_60,\
    VGAOUT_1280x800P_60,\
    VGAOUT_1680x1050P_60,\
    VGAOUT_1600x1200P_60,\
}

m_fbdevList = {\
    FB_DEVICE,\
}

[DISP0_MISC]
;sample m_wColorMetrixId=0, m_wGopDstId=0
;m_wColorMetrixId=1, m_wGopDstId=1;
m_wColorMetrixId = 1;
m_wGopDstId = 0;

[LOGO]
m_eDeviceType = 1; #0 None, 1 LCD, 2 HDMI, 3 VGA
m_sParaTarget = SAT070AT50H18BH_Demo1
m_wDispWidth = 0
m_wDispHeight = 0
m_wDispFps = 0
m_sLogoFile0 = logo.jpg
m_sLogoFile1 = upgrade.jpg
m_sPQFile0 = PQ.bin

[DACOUT_576I]
m_pPanelName            =   DACOUT_576I;
m_ePanelIntfType        =   7;#LINK_DAC_I;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   64;
m_wPanelHSyncBackPorch =   68;
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   39;
m_wPanelHStart          =   132;
m_wPanelVStart          =   0;
m_wPanelWidth           =   720;
m_wPanelHeight          =   576;

m_wPanelMaxHTotal       =   964;
m_wPanelHTotal          =   864;
m_wPanelMinHTotal       =   764;

m_wPanelMaxVTotal       =   725;
m_wPanelVTotal          =   625;
m_wPanelMinVTotal       =   525;

m_wPanelMaxDCLK        =   37;
m_wPanelDCLK           =   27;
m_wPanelMinDCLK        =   17;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_480I]
m_pPanelName            =   DACOUT_480I;

m_ePanelIntfType        =   7;#LINK_DAC_I;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   62;
m_wPanelHSyncBackPorch =   60;
m_wPanelVSyncWidth     =   6;
m_wPanelVBackPorch     =   30;
m_wPanelHStart          =   122;
m_wPanelVStart          =   0;
m_wPanelWidth           =   720;
m_wPanelHeight          =   480;

m_wPanelMaxHTotal       =   958;
m_wPanelHTotal          =   858;
m_wPanelMinHTotal       =   758;

m_wPanelMaxVTotal       =   625;
m_wPanelVTotal          =   525;
m_wPanelMinVTotal       =   425;

m_wPanelMaxDCLK        =   37;
m_wPanelDCLK           =   27;
m_wPanelMinDCLK        =   17;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_1080P_24]
m_pPanelName            =   DACOUT_1080P_24;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   44;
m_wPanelHSyncBackPorch =   148;
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   36;
m_wPanelHStart          =   192;
m_wPanelVStart          =   41;
m_wPanelWidth           =   1920;
m_wPanelHeight          =   1080;

m_wPanelMaxHTotal       =   2850;
m_wPanelHTotal          =   2750;
m_wPanelMinHTotal       =   2650;

m_wPanelMaxVTotal       =   1225;
m_wPanelVTotal          =   1125;
m_wPanelMinVTotal       =   1025;

m_wPanelMaxDCLK        =   85;
m_wPanelDCLK           =   74;
m_wPanelMinDCLK        =   65;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_1080P_25]
m_pPanelName            =   DACOUT_1080P_25;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   44;
m_wPanelHSyncBackPorch =   148;
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   36;
m_wPanelHStart          =   192;
m_wPanelVStart          =   41;
m_wPanelWidth           =   1920;
m_wPanelHeight          =   1080;

m_wPanelMaxHTotal       =   2740;
m_wPanelHTotal          =   2640;
m_wPanelMinHTotal       =   2540;

m_wPanelMaxVTotal       =   1225;
m_wPanelVTotal          =   1125;
m_wPanelMinVTotal       =   1025;

m_wPanelMaxDCLK        =   85;
m_wPanelDCLK           =   74;
m_wPanelMinDCLK        =   65;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_1080P_30]
m_pPanelName            =   DACOUT_1080P_30;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   44;
m_wPanelHSyncBackPorch =   148;
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   36;
m_wPanelHStart          =   192;
m_wPanelVStart          =   41;
m_wPanelWidth           =   1920;
m_wPanelHeight          =   1080;

m_wPanelMaxHTotal       =   2300;
m_wPanelHTotal          =   2200;
m_wPanelMinHTotal       =   2100;

m_wPanelMaxVTotal       =   1225;
m_wPanelVTotal          =   1125;
m_wPanelMinVTotal       =   1025;

m_wPanelMaxDCLK        =   85;
m_wPanelDCLK           =   74;
m_wPanelMinDCLK        =   65;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_1080P_50]
m_pPanelName            =   DACOUT_1080P_50;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   44;
m_wPanelHSyncBackPorch =   148;
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   36;
m_wPanelHStart          =   192;
m_wPanelVStart          =   41;
m_wPanelWidth           =   1920;
m_wPanelHeight          =   1080;

m_wPanelMaxHTotal       =   2740;
m_wPanelHTotal          =   2640;
m_wPanelMinHTotal       =   2540;

m_wPanelMaxVTotal       =   1225;
m_wPanelVTotal          =   1125;
m_wPanelMinVTotal       =   1025;

m_wPanelMaxDCLK        =   167;
m_wPanelDCLK           =   148;
m_wPanelMinDCLK        =   130;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_720P_50]
m_pPanelName            =   DACOUT_720P_50;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   40;
m_wPanelHSyncBackPorch =   220;
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   20;
m_wPanelHStart          =   260;
m_wPanelVStart          =   25;
m_wPanelWidth           =   1280;
m_wPanelHeight          =   720;

m_wPanelMaxHTotal       =   2080;
m_wPanelHTotal          =   1980;
m_wPanelMinHTotal       =   1880;

m_wPanelMaxVTotal       =   850;
m_wPanelVTotal          =   750;
m_wPanelMinVTotal       =   650;

m_wPanelMaxDCLK        =   88;
m_wPanelDCLK           =   74;
m_wPanelMinDCLK        =   61;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_720P_60]
m_pPanelName            =   DACOUT_720P_60;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   40;
m_wPanelHSyncBackPorch =   220;
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   20;
m_wPanelHStart          =   260;
m_wPanelVStart          =   25;
m_wPanelWidth           =   1280;
m_wPanelHeight          =   720;

m_wPanelMaxHTotal       =   1750;
m_wPanelHTotal          =   1650;
m_wPanelMinHTotal       =   1550;

m_wPanelMaxVTotal       =   850;
m_wPanelVTotal          =   750;
m_wPanelMinVTotal       =   650;

m_wPanelMaxDCLK        =   89;
m_wPanelDCLK           =   74;
m_wPanelMinDCLK        =   60;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_1080I_50]
m_pPanelName            =   DACOUT_1080I_50;
m_ePanelIntfType        =   7;#LINK_DAC_I;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   44;
m_wPanelHSyncBackPorch =   148;
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   15;
m_wPanelHStart          =   192;
m_wPanelVStart          =   0;
m_wPanelWidth           =   1920;
m_wPanelHeight          =   1080;

m_wPanelMaxHTotal       =   2740;
m_wPanelHTotal          =   2640;
m_wPanelMinHTotal       =   2540;

m_wPanelMaxVTotal       =   1225;
m_wPanelVTotal          =   1125;
m_wPanelMinVTotal       =   1025;

m_wPanelMaxDCLK        =   158;
m_wPanelDCLK           =   148;
m_wPanelMinDCLK        =   138;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_1080I_60]
m_pPanelName            =   DACOUT_1080I_60;
m_ePanelIntfType        =   7;#LINK_DAC_I;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   44;
m_wPanelHSyncBackPorch =   148;
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   15;
m_wPanelHStart          =   192;
m_wPanelVStart          =   0;
m_wPanelWidth           =   1920;
m_wPanelHeight          =   1080;

m_wPanelMaxHTotal       =   2300;
m_wPanelHTotal          =   2200;
m_wPanelMinHTotal       =   2100;

m_wPanelMaxVTotal       =   1225;
m_wPanelVTotal          =   1125;
m_wPanelMinVTotal       =   1025;

m_wPanelMaxDCLK        =   158;
m_wPanelDCLK           =   148;
m_wPanelMinDCLK        =   138;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_1080P_60]
m_pPanelName            =   DACOUT_1080P_60;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   44;
m_wPanelHSyncBackPorch =   148;
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   36;
m_wPanelHStart          =   192;
m_wPanelVStart          =   41;
m_wPanelWidth           =   1920;
m_wPanelHeight          =   1080;

m_wPanelMaxHTotal       =   2300;
m_wPanelHTotal          =   2200
m_wPanelMinHTotal       =   2100;

m_wPanelMaxVTotal       =   1225;
m_wPanelVTotal          =   1125;
m_wPanelMinVTotal       =   1025;

m_wPanelMaxDCLK        =   169;
m_wPanelDCLK           =   148;
m_wPanelMinDCLK        =   130;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_576P]
m_pPanelName            = DACOUT_576P;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   64;
m_wPanelHSyncBackPorch =   68;
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   39;
m_wPanelHStart          =   132;
m_wPanelVStart          =   44;
m_wPanelWidth           =   720;
m_wPanelHeight          =   576;

m_wPanelMaxHTotal       =   964;
m_wPanelHTotal          =   864;
m_wPanelMinHTotal       =   764;

m_wPanelMaxVTotal       =   725;
m_wPanelVTotal          =   625;
m_wPanelMinVTotal       =   525;

m_wPanelMaxDCLK        =   35;
m_wPanelDCLK           =   27;
m_wPanelMinDCLK        =   20;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_480P]
m_pPanelName            =   DACOUT_480P;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   62;
m_wPanelHSyncBackPorch =   60;
m_wPanelVSyncWidth     =   6;
m_wPanelVBackPorch     =   30;
m_wPanelHStart          =   122;
m_wPanelVStart          =   36;
m_wPanelWidth           =   720;
m_wPanelHeight          =   480;

m_wPanelMaxHTotal       =   958;
m_wPanelHTotal          =   858;
m_wPanelMinHTotal       =   758;

m_wPanelMaxVTotal       =   625;
m_wPanelVTotal          =   525;
m_wPanelMinVTotal       =   425;

m_wPanelMaxDCLK        =   36;
m_wPanelDCLK           =   27;
m_wPanelMinDCLK        =   19;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_2K2KP_30]
m_pPanelName            =   DACOUT_2K2KP_30;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   272;
m_wPanelHSyncBackPorch =   464;#overflow, 304
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   45;
m_wPanelHStart          =   736;#272+464
m_wPanelVStart          =   50;#5+45
m_wPanelWidth           =   2560;
m_wPanelHeight          =   1440;

m_wPanelMaxHTotal       =   3588;
m_wPanelHTotal          =   3488;
m_wPanelMinHTotal       =   3388;

m_wPanelMaxVTotal       =   1593;
m_wPanelVTotal          =   1493;
m_wPanelMinVTotal       =   1393;

m_wPanelMaxDCLK        =   166;
m_wPanelDCLK           =   156;
m_wPanelMinDCLK        =   146;

m_wSpreadSpectrumFreq   =   25;
m_wSpreadSpectrumRatio   =   192;

[DACOUT_2K2KP_60]
m_pPanelName            =   DACOUT_2K2KP_60;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   272;
m_wPanelHSyncBackPorch =   464;#overflow, 304
m_wPanelVSyncWidth     =   5;
m_wPanelVBackPorch     =   45;
m_wPanelHStart          =   736;#272+464
m_wPanelVStart          =   50;#5+45
m_wPanelWidth           =   2560;
m_wPanelHeight          =   1440;

m_wPanelMaxHTotal       =   3588;
m_wPanelHTotal          =   3488;
m_wPanelMinHTotal       =   3388;

m_wPanelMaxVTotal       =   1593;
m_wPanelVTotal          =   1493;
m_wPanelMinVTotal       =   1393;

m_wPanelMaxDCLK        =   322;
m_wPanelDCLK           =   312;
m_wPanelMinDCLK        =   302;

m_wSpreadSpectrumFreq   =   25;
m_wSpreadSpectrumRatio   =   192;

[DACOUT_4K2KP_24]
m_pPanelName            =   DACOUT_4K2KP_24;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   88;
m_wPanelHSyncBackPorch =   296;
m_wPanelVSyncWidth     =   11;
m_wPanelVBackPorch     =   72;
m_wPanelHStart          =   384;
m_wPanelVStart          =   0;
m_wPanelWidth           =   3840;
m_wPanelHeight          =   2160;

m_wPanelMaxHTotal       =   5600;
m_wPanelHTotal          =   5500;
m_wPanelMinHTotal       =   5400;

m_wPanelMaxVTotal       =   2350;
m_wPanelVTotal          =   2250;
m_wPanelMinVTotal       =   2150;

m_wPanelMaxDCLK        =   307;
m_wPanelDCLK           =   297;
m_wPanelMinDCLK        =   287;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_4K2KP_25]
m_pPanelName            =   DACOUT_4K2KP_25;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   88;
m_wPanelHSyncBackPorch =   296;
m_wPanelVSyncWidth     =   11;
m_wPanelVBackPorch     =   72;
m_wPanelHStart          =   384;
m_wPanelVStart          =   0;
m_wPanelWidth           =   3840;
m_wPanelHeight          =   2160;

m_wPanelMaxHTotal       =   5380;
m_wPanelHTotal          =   5280;
m_wPanelMinHTotal       =   5180;

m_wPanelMaxVTotal       =   2350;
m_wPanelVTotal          =   2250;
m_wPanelMinVTotal       =   2150;

m_wPanelMaxDCLK        =   307;
m_wPanelDCLK           =   297;
m_wPanelMinDCLK        =   287;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_4K2KP_30]
m_pPanelName            =   DACOUT_4K2KP_30;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   88;
m_wPanelHSyncBackPorch =   296;
m_wPanelVSyncWidth     =   10;
m_wPanelVBackPorch     =   72;
m_wPanelHStart          =   384;
m_wPanelVStart          =   0;
m_wPanelWidth           =   3840;
m_wPanelHeight          =   2160;

m_wPanelMaxHTotal       =   4500;
m_wPanelHTotal          =   4400;
m_wPanelMinHTotal       =   4300;

m_wPanelMaxVTotal       =   2350;
m_wPanelVTotal          =   2250;
m_wPanelMinVTotal       =   2150;

m_wPanelMaxDCLK        =   307;
m_wPanelDCLK           =   297;
m_wPanelMinDCLK        =   287;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_4K2KP_50]
m_pPanelName            =   DACOUT_4K2KP_50;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   88;
m_wPanelHSyncBackPorch =   296;
m_wPanelVSyncWidth     =   11;
m_wPanelVBackPorch     =   72;
m_wPanelHStart          =   384;
m_wPanelVStart          =   0;
m_wPanelWidth           =   3840;
m_wPanelHeight          =   2160;

m_wPanelMaxHTotal       =   5380;
m_wPanelHTotal          =   5280;
m_wPanelMinHTotal       =   5180;

m_wPanelMaxVTotal       =   2350;
m_wPanelVTotal          =   2250;
m_wPanelMinVTotal       =   2150;

m_wPanelMaxDCLK        =   607;
m_wPanelDCLK           =   594;
m_wPanelMinDCLK        =   584;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[DACOUT_4K2KP_60]
m_pPanelName            =   DACOUT_4K2KP_60;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   88;
m_wPanelHSyncBackPorch =   296;
m_wPanelVSyncWidth     =   11;
m_wPanelVBackPorch     =   72;
m_wPanelHStart          =   384;
m_wPanelVStart          =   0;
m_wPanelWidth           =   3840;
m_wPanelHeight          =   2160;

m_wPanelMaxHTotal       =   4500;
m_wPanelHTotal          =   4400;
m_wPanelMinHTotal       =   4300;

m_wPanelMaxVTotal       =   2350;
m_wPanelVTotal          =   2250;
m_wPanelMinVTotal       =   2150;

m_wPanelMaxDCLK        =   604;
m_wPanelDCLK           =   594;
m_wPanelMinDCLK        =   584;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[VGAOUT_1024x768P_60]
m_pPanelName            =   VGAOUT_1024x768P_60;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   136;
m_wPanelHSyncBackPorch =   160;
m_wPanelVSyncWidth     =   6;
m_wPanelVBackPorch     =   29;
m_wPanelHStart          =   296;#136+160
m_wPanelVStart          =   35;#6+29
m_wPanelWidth           =   1024;
m_wPanelHeight          =   768;

m_wPanelMaxHTotal       =   1444;
m_wPanelHTotal          =   1344;
m_wPanelMinHTotal       =   1244;

m_wPanelMaxVTotal       =   906;
m_wPanelVTotal          =   806;
m_wPanelMinVTotal       =   706;

m_wPanelMaxDCLK        =   78;
m_wPanelDCLK           =   65;
m_wPanelMinDCLK        =   52;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[VGAOUT_1280x1024P_60]
m_pPanelName            =   VGAOUT_1280x1024P_60;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   112;
m_wPanelHSyncBackPorch =   248;
m_wPanelVSyncWidth     =   3;
m_wPanelVBackPorch     =   38;
m_wPanelHStart          =   360;#112+248
m_wPanelVStart          =   41;#3+38
m_wPanelWidth           =   1280;
m_wPanelHeight          =   1024;

m_wPanelMaxHTotal       =   1788;
m_wPanelHTotal          =   1688;
m_wPanelMinHTotal       =   1588;

m_wPanelMaxVTotal       =   1166;
m_wPanelVTotal          =   1066;
m_wPanelMinVTotal       =   906;

m_wPanelMaxDCLK        =   125;
m_wPanelDCLK           =   108;
m_wPanelMinDCLK        =   86;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[VGAOUT_1366x768P_60]
m_pPanelName            =   VGAOUT_1366x768P_60;
m_ePanelIntfType        =   8 #0:TTL,1:LVDS,8:DAC_P

m_bPanelInvDCLK         =   0
m_bPanelInvDE           =   0
m_bPanelInvHSync        =   0
m_bPanelInvVSync        =   0

m_wPanelHSyncWidth     =   143
m_wPanelHSyncBackPorch =   215
m_wPanelVSyncWidth     =   3
m_wPanelVBackPorch     =   24
m_wPanelHStart          =   358
m_wPanelVStart          =   27
m_wPanelWidth           =   1366
m_wPanelHeight          =   768

m_wPanelMaxHTotal       =   1892
m_wPanelHTotal          =   1792
m_wPanelMinHTotal       =   1692

m_wPanelMaxVTotal       =   898
m_wPanelVTotal          =   798
m_wPanelMinVTotal       =   698

m_wPanelMaxDCLK        =   101
m_wPanelDCLK           =   86
m_wPanelMinDCLK        =   70

m_wSpreadSpectrumFreq   =   0x0
m_wSpreadSpectrumRatio   =   0x0

[VGAOUT_1440x900P_60]
m_pPanelName            =   VGAOUT_1440x900P_60;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   152;
m_wPanelHSyncBackPorch =   232;
m_wPanelVSyncWidth     =   6;
m_wPanelVBackPorch     =   25;
m_wPanelHStart          =   384;#152+232
m_wPanelVStart          =   31;#6+25
m_wPanelWidth           =   1440;
m_wPanelHeight          =   900;

m_wPanelMaxHTotal       =   2004;
m_wPanelHTotal          =   1904;
m_wPanelMinHTotal       =   1804;

m_wPanelMaxVTotal       =   1034;
m_wPanelVTotal          =   934;
m_wPanelMinVTotal       =   834;

m_wPanelMaxDCLK        =   124;
m_wPanelDCLK           =   106;
m_wPanelMinDCLK        =   90;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[VGAOUT_1280x800P_60]
m_pPanelName            =   VGAOUT_1280x800P_60;
m_ePanelIntfType        =   8 #0:TTL,1:LVDS,8:DAC_P

m_bPanelInvDCLK         =   0
m_bPanelInvDE           =   0
m_bPanelInvHSync        =   0
m_bPanelInvVSync        =   0

m_wPanelHSyncWidth     =   128
m_wPanelHSyncBackPorch =   200
m_wPanelVSyncWidth     =   6
m_wPanelVBackPorch     =   22
m_wPanelHStart          =   328
m_wPanelVStart          =   28
m_wPanelWidth           =   1280
m_wPanelHeight          =   800

m_wPanelMaxHTotal       =   1780
m_wPanelHTotal          =   1680
m_wPanelMinHTotal       =   1580

m_wPanelMaxVTotal       =   931
m_wPanelVTotal          =   831
m_wPanelMinVTotal       =   731

m_wPanelMaxDCLK        =   94
m_wPanelDCLK           =   84
m_wPanelMinDCLK        =   74

m_wSpreadSpectrumFreq   =   0x0
m_wSpreadSpectrumRatio   =   0x0

[VGAOUT_1680x1050P_60]
m_pPanelName            =   VGAOUT_1680x1050P_60
m_ePanelIntfType        =   8 #0:TTL,1:LVDS,8:DAC_P

m_bPanelInvDCLK         =   0
m_bPanelInvDE           =   0
m_bPanelInvHSync        =   0
m_bPanelInvVSync        =   0

m_wPanelHSyncWidth     =   176
m_wPanelHSyncBackPorch =   280
m_wPanelVSyncWidth     =   6
m_wPanelVBackPorch     =   30
m_wPanelHStart          =   456
m_wPanelVStart          =   36
m_wPanelWidth           =   1680
m_wPanelHeight          =   1050

m_wPanelMaxHTotal       =   2340
m_wPanelHTotal          =   2240
m_wPanelMinHTotal       =   2140

m_wPanelMaxVTotal       =   1139
m_wPanelVTotal          =   1089
m_wPanelMinVTotal       =   1039

m_wPanelMaxDCLK        =   159
m_wPanelDCLK           =   146
m_wPanelMinDCLK        =   133

m_wSpreadSpectrumFreq   =   0x0
m_wSpreadSpectrumRatio   =   0x0

[VGAOUT_1600x1200P_60]
m_pPanelName            =   VGAOUT_1600x1200P_60;
m_ePanelIntfType        =   8;#LINK_DAC_P;

m_bPanelInvDCLK         =   0;
m_bPanelInvDE           =   0;
m_bPanelInvHSync        =   0;
m_bPanelInvVSync        =   0;

m_wPanelHSyncWidth     =   192;
m_wPanelHSyncBackPorch =   304;
m_wPanelVSyncWidth     =   3;
m_wPanelVBackPorch     =   46;
m_wPanelHStart          =   496;#192+304
m_wPanelVStart          =   49;#6+26
m_wPanelWidth           =   1600;
m_wPanelHeight          =   1200;

m_wPanelMaxHTotal       =   2260;
m_wPanelHTotal          =   2160;
m_wPanelMinHTotal       =   2060;

m_wPanelMaxVTotal       =   1350;
m_wPanelVTotal          =   1250;
m_wPanelMinVTotal       =   1150;

m_wPanelMaxDCLK        =   183;
m_wPanelDCLK           =   162;
m_wPanelMinDCLK        =   142;

m_wSpreadSpectrumFreq   =   0x0;
m_wSpreadSpectrumRatio   =   0x0;

[ST7710S]
m_pPanelName = "ST7701S_480x480";
m_bPanelDither = 0;
m_ePanelIntfType = 11; #0:ttl 1:lvds 11:mipi 12:bt656

m_bPanelInvDCLK = 0;
m_bPanelInvDE = 0;
m_bPanelInvHSync = 0;
m_bPanelInvVSync = 0;

m_wPanelHSyncWidth = 10;
m_wPanelHSyncBackPorch = 160;
m_wPanelVSyncWidth = 1;
m_wPanelVBackPorch = 13;
m_wPanelHStart = 170;
m_wPanelVStart = 14;
m_wPanelWidth = 480;
m_wPanelHeight = 480;

m_wPanelHTotal = 810;
m_wPanelVTotal = 496;
m_wPanelDCLK = 24;

m_wSpreadSpectrumFreq = 0;
m_wSpreadSpectrumRatio = 0;

m_eOutputFormatBitMode = 2; #0:10bit 1:6bit 2:8bit 3:565bit

m_ucPanelSwapChnR = 0;
m_ucPanelSwapChnG = 0;
m_ucPanelSwapChnB = 0;
m_ucPanelSwapRgbML = 0;


m_wHsTrail = 5;
m_wHsPrpr = 3;
m_wHsZero = 5;
m_wClkHsPrpr = 10;
m_wClkHsExit = 14;
m_wClkTrail = 3;
m_wClkZero = 12;
m_wClkHsPost = 10;
m_wDaHsExit = 5;
m_wContDet = 0;
m_wLpx = 16;
m_wTaGet = 26;
m_wTaSure = 24;
m_wTaGo = 50;
m_wBllp = 0x0;

m_wHactive = 480;
m_wHpw = 10;
m_wHbp = 160;
m_wHfp = 160;
m_wVactive = 480;
m_wVpw = 1;
m_wVbp = 13;
m_wVfp = 2;
m_wFps = 60;

m_eLaneNum = 2; #1:one lane, 2:two lane, 3:three lane, 4:four lane
m_eFormat = 3; #0:rgb565, 1:rgb666, 2:loosely rgb666, 3:rgb888
m_eCtrlMode = 1; #0:cmd mode, 1:sync pluse, 2:sync event, 3:burst mode

m_ucClkLane = 2;
m_ucDataLane0 = 4;
m_ucDataLane1 = 3;
m_ucDataLane2 = 1;
m_ucDataLane3 = 0;

m_ucPolCh0 = 0;
m_ucPolCh1 = 0;
m_ucPolCh2 = 0;
m_ucPolCh3 = 0;
m_ucPolCh4 = 0;


#(0xfe,0xfe,val): delay flag of cmd table
#(0xff,0xff): end flag of cmd table

m_pCmdBuff = \
{\
    0xFF, 0x5, 0x77,0x01,0x00,0x00,0x10,\
    0xC0, 0x2, 0x3B,0x00,\
    0xC1, 0x2, 0x0D,0x02,\
    0xC2, 0x2, 0x21,0x08,\
    0xB0, 0x10,0x00,0x11,0x18,0x0E,0x11,0x06,0x07,0x08,0x07,0x22,0x04,0x12,0x0F,0xAA,0x31,0x18,\
    0xB1, 0x10,0x00,0x11,0x19,0x0E,0x12,0x07,0x08,0x08,0x08,0x22,0x04,0x11,0x11,0xA9,0x32,0x18,\
    0xFF, 0x5, 0x77,0x01,0x00,0x00,0x11,\
    0xB0, 0x1, 0x68,\
    0xB1, 0x1, 0x30,\
    0xB2, 0x1, 0x87,\
    0xB3, 0x1, 0x80,\
    0xB5, 0x1, 0x49,\
    0xB7, 0x1, 0x85,\
    0xB8, 0x1, 0x21,\
    0xC1, 0x1, 0x78,\
    0xC2, 0x1, 0x78,\
    0xFE, 0xFE,0x64,\
    0xE0, 0x3, 0x00,0x1B,0x02,\
    0xE1, 0xb, 0x08,0xA0,0x00,0x00,0x07,0xA0,0x00,0x00,0x00,0x44,0x44,\
    0xE2, 0xc, 0x11,0x11,0x44,0x44,0xED,0xA0,0x00,0x00,0xEC,0xA0,0x00,0x00,\
    0xE3, 0x4, 0x00,0x00,0x11,0x11,\
    0xE4, 0x2, 0x44,0x44,\
    0xE5, 0x10,0x0A,0xE9,0xD8,0xA0,0x0C,0xEB,0xD8,0xA0,0x0E,0xED,0xD8,0xA0,0x10,0xEF,0xD8,0xA0,\
    0xE6, 0x4, 0x00,0x00,0x11,0x11,\
    0xE7, 0x2, 0x44,0x44,\
    0xE8, 0x10,0x09,0xE8,0xD8,0xA0,0x0B,0xEA,0xD8,0xA0,0x0D,0xEC,0xD8,0xA0,0x0F,0xEE,0xD8,0xA0,\
    0xEB, 0x7, 0x02,0x00,0xE4,0xE4,0x88,0x00,0x40,\
    0xEC, 0x2, 0x3C,0x00,\
    0xED, 0x10,0xAB,0x89,0x76,0x54,0x02,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x20,0x45,0x67,0x98,0xBA,\
    0xEF, 0x6, 0x08,0x08,0x08,0x08,0x3F,0x1F,\
    0xFF, 0x5, 0x77,0x01,0x00,0x00,0x00,\
    0x11, 0x0, 0x00,\
    0xFE, 0xFE,0x64,\
    0x29, 0x0, 0x00,\
    0xFE, 0xFE,0x64,\
    0xFF, 0xFF,\
};

[SAT070AT50H18BH]
m_pPanelName = "SAT070AT50H18BH_1024x600";
m_bPanelDither = 0;
m_ePanelIntfType = 0; #0:ttl 1:lvds 11:mipi 12:bt656

m_bPanelInvDCLK = 0;
m_bPanelInvDE = 0;
m_bPanelInvHSync = 0;
m_bPanelInvVSync = 0;

m_wPanelHSyncWidth = 48;
m_wPanelHSyncBackPorch = 46;
m_wPanelVSyncWidth = 4;
m_wPanelVBackPorch = 23;
m_wPanelHStart = 98;
m_wPanelVStart = 27;
m_wPanelWidth = 1024;
m_wPanelHeight = 600;

m_wPanelHTotal = 1344;
m_wPanelVTotal = 635;
m_wPanelDCLK = 51;

m_wSpreadSpectrumFreq = 0;
m_wSpreadSpectrumRatio = 0;

m_eOutputFormatBitMode = 3; #0:10bit 1:6bit 2:8bit 3:565bit

m_wPadmux = 2; #ttl16 mode 2
m_ePanelPwBlCtrl = 0;
m_wPanelPwGpioNum = 7;
m_wPanelPwmNum = 0;         #pwm num not gpio index!!!
m_wPanelDutyVal = 200000;
m_wPanelDlyTm2Data = 0;
m_wPanelDlyTmData2Bl = 0;
m_wPanelPeriodVal = 200000;
m_ucPanelSwapChnR = 3;
m_ucPanelSwapChnG = 2;
m_ucPanelSwapChnB = 1;
m_ucPanelSwapRgbML = 0;

[SAT070AT50H18BH_Demo1]
m_pPanelName = "SAT070AT50H18BH_1024x600";
m_bPanelDither = 0;
m_ePanelIntfType = 0; #0:ttl 1:lvds 11:mipi 12:bt656

m_bPanelInvDCLK = 1;
m_bPanelInvDE = 0;
m_bPanelInvHSync = 0;
m_bPanelInvVSync = 0;

m_wPanelHSyncWidth = 100;
m_wPanelHSyncBackPorch = 110;
m_wPanelVSyncWidth = 5;
m_wPanelVBackPorch = 15;
m_wPanelHStart = 210;
m_wPanelVStart = 20;
m_wPanelWidth = 1024;
m_wPanelHeight = 600;

m_wPanelHTotal = 1344;
m_wPanelVTotal = 635;
m_wPanelDCLK = 51;

m_wSpreadSpectrumFreq = 0;
m_wSpreadSpectrumRatio = 0;

m_eOutputFormatBitMode = 2; #0:10bit 1:6bit 2:8bit 3:565bit

m_wPadmux = 4; #ttl24 mode 4

m_ePanelPwBlCtrl = 1;
m_wPanelPwGpioNum = 88;     #DEMOBOARD:PAD_GPIO1(GpioNum=62),Pager:88(GPIO_NR))
m_wPanelPwmNum = 0;         #pwm num not gpio index!!! DEMOBOARD:PAD_GPIO2(PWM1) Pager:SD_CMD(PWM0),NEW=PAD_KEY10
m_wPanelDutyVal = 50000;
m_wPanelDlyTm2Data = 0;
m_wPanelDlyTmData2Bl = 0;
m_wPanelPeriodVal = 50000;

m_ucPanelSwapChnR = 3;
m_ucPanelSwapChnG = 2;
m_ucPanelSwapChnB = 1;
m_ucPanelSwapRgbML = 0;

[SAT070AT50H18BH_Demo2]
m_pPanelName = "SAT070AT50H18BH_1024x600";
m_bPanelDither = 0;
m_ePanelIntfType = 0; #0:ttl 1:lvds 11:mipi 12:bt656

m_bPanelInvDCLK = 0;
m_bPanelInvDE = 0;
m_bPanelInvHSync = 0;
m_bPanelInvVSync = 0;

m_wPanelHSyncWidth = 48;
m_wPanelHSyncBackPorch = 46;
m_wPanelVSyncWidth = 4;
m_wPanelVBackPorch = 23;
m_wPanelHStart = 98;
m_wPanelVStart = 27;
m_wPanelWidth = 1024;
m_wPanelHeight = 600;

m_wPanelHTotal = 1344;
m_wPanelVTotal = 635;
m_wPanelDCLK = 51;

m_wSpreadSpectrumFreq = 0;
m_wSpreadSpectrumRatio = 0;

m_eOutputFormatBitMode = 2; #0:10bit 1:6bit 2:8bit 3:565bit

m_wPadmux = 4; #ttl24 mode 4

m_ePanelPwBlCtrl = 1;
m_wPanelPwGpioNum = 62;
m_wPanelPwmNum = 1;         #pwm num not gpio index!!!
m_wPanelDutyVal = 200000;
m_wPanelDlyTm2Data = 0;
m_wPanelDlyTmData2Bl = 100;
m_wPanelPeriodVal = 200000;

m_ucPanelSwapChnR = 3;
m_ucPanelSwapChnG = 2;
m_ucPanelSwapChnB = 1;
m_ucPanelSwapRgbML = 0;

[SAT070AT50H18BH_Demo3]
m_pPanelName = "SAT070AT50H18BH_1024x600";
m_bPanelDither = 0;
m_ePanelIntfType = 0; #0:ttl 1:lvds 11:mipi 12:bt656

m_bPanelInvDCLK = 0;
m_bPanelInvDE = 0;
m_bPanelInvHSync = 0;
m_bPanelInvVSync = 0;

m_wPanelHSyncWidth = 48;
m_wPanelHSyncBackPorch = 46;
m_wPanelVSyncWidth = 4;
m_wPanelVBackPorch = 23;
m_wPanelHStart = 98;
m_wPanelVStart = 27;
m_wPanelWidth = 1024;
m_wPanelHeight = 600;

m_wPanelHTotal = 1344;
m_wPanelVTotal = 635;
m_wPanelDCLK = 51;

m_wSpreadSpectrumFreq = 0;
m_wSpreadSpectrumRatio = 0;

m_eOutputFormatBitMode = 3; #0:10bit 1:6bit 2:8bit 3:565bit

m_wPadmux = 3; #ttl24 mode 4

m_ePanelPwBlCtrl = 1;
m_wPanelPwGpioNum = 62;
m_wPanelPwmNum = 1;         #pwm num not gpio index!!!
m_wPanelDutyVal = 200000;
m_wPanelDlyTm2Data = 0;
m_wPanelDlyTmData2Bl = 100;
m_wPanelPeriodVal = 200000;

m_ucPanelSwapChnR = 1;
m_ucPanelSwapChnG = 2;
m_ucPanelSwapChnB = 3;
m_ucPanelSwapRgbML = 1;

[SAT070AT50]
m_pPanelName = "SAT070AT50";
m_bPanelDither = 0;
m_ePanelIntfType = 0; #0:ttl 1:lvds 11:mipi 12:bt656

m_bPanelInvDCLK = 1;
m_bPanelInvDE = 0;
m_bPanelInvHSync = 1;
m_bPanelInvVSync = 1;

m_wPanelHSyncWidth = 48;
m_wPanelHSyncBackPorch = 46;
m_wPanelVSyncWidth = 4;
m_wPanelVBackPorch = 23;
m_wPanelHStart = 98;
m_wPanelVStart = 27;
m_wPanelWidth = 800;
m_wPanelHeight = 480;

m_wPanelHTotal = 928;
m_wPanelVTotal = 525;
m_wPanelDCLK = 29;

m_wSpreadSpectrumFreq = 0;
m_wSpreadSpectrumRatio = 0;

m_eOutputFormatBitMode = 2; #0:10bit 1:6bit 2:8bit 3:565bit

m_ucPanelSwapChnR = 3;
m_ucPanelSwapChnG = 2;
m_ucPanelSwapChnB = 1;
m_ucPanelSwapRgbML = 0;

[ST7789V]
m_pPanelName = "ST7789V";
m_bPanelDither = 0;
m_ePanelIntfType = 16; #0:ttl 1:lvds 11:mipi 12:bt656 16:sRGB

m_bPanelInvDCLK = 1;
m_bPanelInvDE = 0;
m_bPanelInvHSync = 1;
m_bPanelInvVSync = 1;

m_wPanelHSyncWidth = 20;
m_wPanelHSyncBackPorch = 20;
m_wPanelVSyncWidth = 4;
m_wPanelVBackPorch = 4;
m_wPanelHStart = 0;
m_wPanelVStart = 0;
m_wPanelWidth = 240;
m_wPanelHeight = 320;

m_wPanelHTotal = 306;
m_wPanelVTotal = 336;
m_wPanelDCLK = 18;

m_wSpreadSpectrumFreq = 0;
m_wSpreadSpectrumRatio = 0;

m_eOutputFormatBitMode = 2; #0:10bit 1:6bit 2:8bit 3:565bit

m_ePanelRgbDataType = 0;
m_ePanelRgbDeltaOddMode = 0;
m_ePanelRgbDeltaEvenMode = 0;

m_ucPanelSwapChnR = 3;
m_ucPanelSwapChnG = 2;
m_ucPanelSwapChnB = 1;
m_ucPanelSwapRgbML = 0;

[ST7789V2]
m_pPanelName = "ST7789V2_TTL240x320";
m_bPanelDither = 0;
m_ePanelIntfType = 0; #0:ttl 1:lvds 11:mipi 12:bt656

m_bPanelInvDCLK = 0;
m_bPanelInvDE = 0;
m_bPanelInvHSync = 0;
m_bPanelInvVSync = 0;

m_wPanelHSyncWidth = 10;
m_wPanelHSyncBackPorch = 10;
m_wPanelVSyncWidth = 6;
m_wPanelVBackPorch = 6;
m_wPanelHStart = 20;
m_wPanelVStart = 12;
m_wPanelWidth = 240;
m_wPanelHeight = 320;

m_wPanelHTotal = 332;
m_wPanelVTotal = 360;
m_wPanelDCLK = 7;

m_wSpreadSpectrumFreq = 0;
m_wSpreadSpectrumRatio = 0;

m_eOutputFormatBitMode = 3; #0:10bit 1:6bit 2:8bit 3:565bit

m_ucPanelSwapChnR = 3;
m_ucPanelSwapChnG = 2;
m_ucPanelSwapChnB = 1;
m_ucPanelSwapRgbML = 0;

[ST7789V3]
m_pPanelName = "ST7789V3";
m_bPanelDither = 0;
m_ePanelIntfType = 16; #0:ttl 1:lvds 11:mipi 12:bt656 16:sRGB

m_bPanelInvDCLK = 0;
m_bPanelInvDE = 0;
m_bPanelInvHSync = 0;
m_bPanelInvVSync = 0;

m_wPanelHSyncWidth = 12;
m_wPanelHSyncBackPorch =8;
m_wPanelVSyncWidth = 2;
m_wPanelVBackPorch = 6;
m_wPanelHStart = 0;
m_wPanelVStart = 0;
m_wPanelWidth = 240;
m_wPanelHeight = 320;

m_wPanelHTotal = 332;
m_wPanelVTotal = 336;
m_wPanelDCLK = 20;

m_wSpreadSpectrumFreq = 0;
m_wSpreadSpectrumRatio = 0;

m_eOutputFormatBitMode = 2; #0:10bit 1:6bit 2:8bit 3:565bit

m_ePanelPwBlCtrl = 1;
m_wPanelPwGpioNum = 8;
m_wPanelPwmNum = 1;         #pwm num not gpio index!!!
m_wPanelDutyVal = 200000;
m_wPanelDlyTm2Data = 0;
m_wPanelDlyTmData2Bl = 100;
m_wPanelPeriodVal = 200000;

m_ePanelRgbDataType = 0;
m_ePanelRgbDeltaOddMode = 0;
m_ePanelRgbDeltaEvenMode = 0;

m_ucPanelSwapChnR = 3;
m_ucPanelSwapChnG = 2;
m_ucPanelSwapChnB = 1;
m_ucPanelSwapRgbML = 0;


[FB_DEVICE]
FB_HWLAYER_ID = 0
FB_HWWIN_ID = 0
FB_HWLAYER_DST = 0
FB_HWWIN_FORMAT = 5
FB_HWLAYER_OUTPUTCOLOR = 1
FB_WIDTH = 1024
FB_HEIGHT = 600
FB_TIMMING_WIDTH = 1024
FB_TIMMING_HEIGHT = 600
FB_MMAP_NAME = E_MMAP_ID_FB
FB_BUFFER_LEN = 4800
#unit:Kbyte,4096=4M, fbdev.ko alloc size = FB_BUFFER_LEN*1024

