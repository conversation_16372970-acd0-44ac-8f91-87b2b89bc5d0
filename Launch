#!/bin/sh

touch /etc/passwd
touch /etc/shadow
echo root::0:0:root:/root:/bin/sh > /etc/passwd
touch /etc/chpass
echo "root:123456" > /etc/chpass
chpasswd < /etc/chpass

echo "telnetd start:9127"
telnetd -l /bin/login -p 9127


echo "Init Environment"

echo 0 > /proc/sys/vm/overcommit_memory

export LD_LIBRARY_PATH=:/mnt/yaffs2/resources/lib
#/mnt/yaffs2/update_dhcp
cd /mnt/yaffs2/

echo "INIT PREWORK"
/mnt/yaffs2/PREWORK
rm /mnt/yaffs2/PREWORK



#check update_kernel.sh
update_image=
if [ -e /mnt/yaffs2/update_image.sh ] ; then
	update_image=/mnt/yaffs2/update_image.sh
fi
if [ -n "$update_image" ] ; then
	#check image_file_update
	image_file1=`find /mnt/yaffs2/ -name "*.kernel"`
	if [ -z "$image_file1" ]
	then
		echo "can't find image_file"
	else
		echo "Ready to upgrade the kernel!"
		$update_image $image_file1
		rm $image_file1
		reboot
	fi
else
	echo "update_image.sh is not exist!"
fi





if [ ! -e reset_count ]
then
echo "reset_count is not exist!,touch it!"
touch reset_count
echo 0 >reset_count
fi

resetCount=`cat reset_count`

echo "reset_count:$resetCount"

if [ "$resetCount" -lt 7 ]
then
echo "reset_count<7,normal start!"
echo $((++resetCount)) >reset_count
echo "RUN NetPager"
/mnt/yaffs2/Pager &
else
echo "reset_count>=6,reboot and restore!"
echo 0 >reset_count
rm /mnt/yaffs2/Launch
rm /mnt/yaffs2/*info
reboot
fi

